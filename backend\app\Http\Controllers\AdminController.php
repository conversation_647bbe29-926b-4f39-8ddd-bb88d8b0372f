<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use App\Models\Admin;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;
use Illuminate\Foundation\Auth\User as Authenticatable;


class AdminController extends Controller
{
    //signup
    public function register(Request $request)
    {

       
       
        $validate=$request->validate([
            'name'=>'required',
            'email'=>'required|email',
            'password'=>'required',
            // 'role'=>'required',
        ]);
        if(!$validate){
            return response()->json(['ok' => false]);
        }
        $admin=new Admin();
        $admin->name=$request->name;
        $admin->email=$request->email;
        $admin->role=$request->role;
        if($request->hasFile('profile_image')){
            $photo = $request->file('profile_image');
            $photo->store('admin', 'public');
            $admin->profile_image = $photo->hashName();
        }
        $admin->password=bcrypt($request->password);
        $admin->save();
        return response()->json(['ok' => true]);
        return $request;
    }

    //login
    public function login(Request $request)
    {
       
     
       $validate=$request->validate([
        'email'=>'required|email',
        'password'=>'required',
       ]);
       if(!$validate){
        return response()->json(['ok' => false,'error'=>validate->errors()]);
       }
       $admin=Admin::where('email',$request->email)->first();
       if($admin){
        if(Hash::check($request->password,$admin->password)){
            $token=$admin->createToken($admin->email)->plainTextToken;
            Session::put("auth",$admin->id);
            Session::put("token",$token);
            return response()->json(['ok' => true,'token'=>$token]);
        }
       }
       return response()->json(['ok' => false,'error'=>'Invalid credentials']);

    }

    public function logout(Request $request)
    {
        Session::flush();
        return redirect()->route('login');
    }
    
}
