
"use client"
import { useState, useEffect } from 'react';
import ProgramTypeCard from './ProgramTypeCard';
import { programsData } from '../../../data/programsData';
import { Sparkles, Target, Users, Award, TrendingUp, Filter, Search, ArrowRight } from 'lucide-react';

const ProgramTypesSection = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [filteredPrograms, setFilteredPrograms] = useState(programsData);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    setIsVisible(true);
  }, []);

  useEffect(() => {
    let filtered = programsData;

    // Filter by category
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(program => program.category === selectedCategory);
    }

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(program =>
        program.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        program.shortDescription.toLowerCase().includes(searchTerm.toLowerCase()) ||
        program.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    setFilteredPrograms(filtered);
  }, [selectedCategory, searchTerm]);

  const categories = ['all', ...Array.from(new Set(programsData.map(p => p.category)))];

  return (
    <section className="py-20 md:py-28 bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-400/10 to-purple-400/10 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-indigo-400/10 to-pink-400/10 rounded-full blur-3xl"></div>

        {/* Floating Elements */}
        {[...Array(20)].map((_, i) => (
          <div
            key={i}
            className={`absolute rounded-full ${
              i % 4 === 0 ? 'bg-blue-400/20' :
              i % 4 === 1 ? 'bg-purple-400/20' :
              i % 4 === 2 ? 'bg-indigo-400/20' : 'bg-pink-400/20'
            }`}
            style={{
              width: `${Math.random() * 4 + 2}px`,
              height: `${Math.random() * 4 + 2}px`,
              top: `${Math.random() * 100}%`,
              left: `${Math.random() * 100}%`,
              animation: `float-gentle ${
                8 + Math.random() * 12
              }s infinite ease-in-out`,
              animationDelay: `${Math.random() * 8}s`,
            }}
          ></div>
        ))}
      </div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Enhanced Section Header */}
        <div className={`text-center mb-16 transition-all duration-1000 transform ${
          isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
        }`}>
          <div className="inline-block mb-6">
            <div className="flex items-center justify-center space-x-3 bg-white/80 backdrop-blur-md px-6 py-3 rounded-2xl border border-white/50 shadow-lg">
              <Sparkles className="text-blue-600" size={20} />
              <span className="text-blue-600 font-semibold tracking-wide">INNOVATE & GROW</span>
              <Target className="text-purple-600" size={16} />
            </div>
          </div>

          <h2 className="text-4xl md:text-5xl lg:text-6xl font-black mb-6 leading-tight">
            <span className="text-gray-900">FWU </span>
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600">
              Incubation
            </span>
            <br />
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-purple-600 via-indigo-600 to-blue-600">
              Programs
            </span>
          </h2>

          <p className="text-xl text-gray-700 max-w-3xl mx-auto leading-relaxed mb-8">
            Transform your innovative ideas into successful ventures with our comprehensive
            <span className="font-semibold text-blue-600"> incubation programs</span>.
            Join a vibrant community of entrepreneurs and change-makers.
          </p>

          {/* Program Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto">
            {[
              { icon: Users, number: "500+", label: "Entrepreneurs", color: "text-blue-600" },
              { icon: Award, number: "50+", label: "Success Stories", color: "text-purple-600" },
              { icon: TrendingUp, number: "95%", label: "Success Rate", color: "text-indigo-600" },
              { icon: Target, number: "20+", label: "Active Programs", color: "text-pink-600" }
            ].map((stat, index) => (
              <div
                key={index}
                className={`bg-white/80 backdrop-blur-md p-4 rounded-2xl border border-white/50 shadow-lg hover:shadow-xl transition-all duration-300 group ${
                  isVisible ? 'animate-fade-in-up' : 'opacity-0'
                }`}
                style={{ animationDelay: `${600 + index * 150}ms` }}
              >
                <stat.icon className={`${stat.color} mx-auto mb-2 group-hover:scale-110 transition-transform duration-300`} size={24} />
                <div className={`text-2xl font-bold ${stat.color} mb-1`}>{stat.number}</div>
                <div className="text-gray-600 text-sm font-medium">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>

        {/* Enhanced Search and Filter Section */}
        <div className={`mb-16 transition-all duration-1000 delay-300 transform ${
          isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
        }`}>
          <div className="bg-white/80 backdrop-blur-md rounded-3xl p-8 border border-white/50 shadow-xl">
            <div className="flex flex-col lg:flex-row gap-6">
              {/* Search Bar */}
              <div className="relative flex-grow">
                <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                  <Search className="text-blue-500" size={20} />
                </div>
                <input
                  type="text"
                  placeholder="Search programs, skills, or topics..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="block w-full pl-12 pr-4 py-4 border-2 border-gray-200 rounded-2xl focus:ring-2 focus:ring-blue-400 focus:border-blue-400 transition-all duration-300 bg-white/90 backdrop-blur-sm text-gray-900 placeholder-gray-500"
                />
              </div>

              {/* Category Filter */}
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                  <Filter className="text-purple-500" size={20} />
                </div>
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="block w-full lg:w-64 pl-12 pr-10 py-4 border-2 border-gray-200 rounded-2xl focus:ring-2 focus:ring-purple-400 focus:border-purple-400 transition-all duration-300 appearance-none bg-white/90 backdrop-blur-sm text-gray-900"
                >
                  <option value="all">All Categories</option>
                  {categories.filter(cat => cat !== 'all').map(category => (
                    <option key={category} value={category}>
                      {category.charAt(0).toUpperCase() + category.slice(1).replace('-', ' ')}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            {/* Active Filters Display */}
            {(selectedCategory !== 'all' || searchTerm) && (
              <div className="mt-4 flex flex-wrap gap-2">
                {selectedCategory !== 'all' && (
                  <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-purple-100 text-purple-800">
                    Category: {selectedCategory.charAt(0).toUpperCase() + selectedCategory.slice(1)}
                    <button
                      onClick={() => setSelectedCategory('all')}
                      className="ml-2 text-purple-600 hover:text-purple-800"
                    >
                      ×
                    </button>
                  </span>
                )}
                {searchTerm && (
                  <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                      Search: {searchTerm}
                    <button
                      onClick={() => setSearchTerm('')}
                      className="ml-2 text-blue-600 hover:text-blue-800"
                    >
                      ×
                    </button>
                  </span>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Enhanced Program Cards */}
        <div className="grid md:grid-cols-2 lg:grid-cols-2 gap-8">
          {filteredPrograms.length > 0 ? (
            filteredPrograms.map((program, index) => (
              <div
                key={program.id}
                className={`transition-all duration-700 transform ${
                  isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
                }`}
                style={{
                  animationDelay: `${800 + index * 200}ms`,
                  transitionDelay: `${800 + index * 200}ms`
                }}
              >
                <ProgramTypeCard
                  program={program}
                />
              </div>
            ))
          ) : (
            <div className="col-span-full text-center py-20">
              <div className="w-24 h-24 mx-auto bg-gray-100 rounded-full flex items-center justify-center mb-6">
                <Search className="text-gray-400" size={32} />
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-3">No Programs Found</h3>
              <p className="text-gray-600 max-w-md mx-auto mb-6">
                No programs match your current search criteria. Try adjusting your filters or search terms.
              </p>
              <button
                onClick={() => {
                  setSearchTerm('');
                  setSelectedCategory('all');
                }}
                className="px-6 py-3 bg-blue-600 text-white rounded-xl hover:bg-blue-700 transition-colors duration-300"
              >
                Clear Filters
              </button>
            </div>
          )}
        </div>

        {/* Enhanced CTA Section */}
        <div className={`mt-24 transition-all duration-1000 delay-700 transform ${
          isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
        }`}>
          <div className="relative bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900 rounded-3xl shadow-2xl overflow-hidden">
            {/* Background Elements */}
            <div className="absolute inset-0">
              <div className="absolute top-20 right-20 w-64 h-64 bg-blue-500/10 rounded-full blur-3xl"></div>
              <div className="absolute bottom-20 left-20 w-64 h-64 bg-purple-500/10 rounded-full blur-3xl"></div>

              {/* Floating particles */}
              {[...Array(15)].map((_, i) => (
                <div
                  key={i}
                  className="absolute w-1 h-1 bg-white/30 rounded-full"
                  style={{
                    top: `${Math.random() * 100}%`,
                    left: `${Math.random() * 100}%`,
                    animation: `float-gentle ${
                      6 + Math.random() * 8
                    }s infinite ease-in-out`,
                    animationDelay: `${Math.random() * 6}s`,
                  }}
                ></div>
              ))}
            </div>

            <div className="relative z-10 p-12 lg:p-16">
              <div className="grid lg:grid-cols-2 gap-12 items-center">
                {/* Left Content */}
                <div>
                  <div className="inline-block mb-6">
                    <div className="flex items-center space-x-3 bg-white/10 backdrop-blur-md px-6 py-3 rounded-2xl border border-white/20">
                      <Sparkles className="text-yellow-400" size={20} />
                      <span className="text-white font-semibold">Join Our Community</span>
                      <Target className="text-yellow-400" size={16} />
                    </div>
                  </div>

                  <h3 className="text-3xl lg:text-4xl font-bold text-white mb-6 leading-tight">
                    Ready to Transform Your
                    <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-400"> Ideas</span> into
                    <span className="text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-pink-400"> Reality</span>?
                  </h3>

                  <p className="text-blue-100 text-lg mb-8 leading-relaxed">
                    Join the Far Western University Incubation Center and become part of a vibrant ecosystem of
                    innovators, entrepreneurs, and change-makers. We welcome students, faculty, alumni, and
                    external entrepreneurs with groundbreaking ideas.
                  </p>

                  {/* Features */}
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-8">
                    {[
                      { icon: Users, text: "Expert Mentorship", color: "text-blue-400" },
                      { icon: Award, text: "Funding Opportunities", color: "text-purple-400" },
                      { icon: Target, text: "Workspace Access", color: "text-indigo-400" },
                      { icon: TrendingUp, text: "Network Building", color: "text-pink-400" }
                    ].map((feature, index) => (
                      <div key={index} className="flex items-center space-x-3 p-3 bg-white/5 backdrop-blur-sm rounded-xl border border-white/10">
                        <feature.icon className={feature.color} size={20} />
                        <span className="text-white font-medium">{feature.text}</span>
                      </div>
                    ))}
                  </div>

                  {/* Action Buttons */}
                  <div className="flex flex-col sm:flex-row gap-4">
                    <a
                      href="/apply"
                      className="group relative px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-bold rounded-2xl transition-all duration-300 hover:shadow-2xl hover:shadow-blue-500/25 hover:-translate-y-1 flex items-center justify-center overflow-hidden"
                    >
                      <div className="absolute inset-0 bg-gradient-to-r from-blue-700 to-purple-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                      <span className="relative z-10">Apply Now</span>
                      <ArrowRight className="relative z-10 ml-2 group-hover:translate-x-1 transition-transform duration-300" />
                    </a>

                    <a
                      href="/contact"
                      className="group relative px-8 py-4 bg-white/10 hover:bg-white/20 backdrop-blur-md text-white font-bold rounded-2xl transition-all duration-300 border-2 border-white/30 hover:border-white/50 hover:shadow-2xl hover:shadow-white/10 hover:-translate-y-1 flex items-center justify-center overflow-hidden"
                    >
                      <div className="absolute inset-0 bg-white/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                      <span className="relative z-10">Contact Us</span>
                    </a>
                  </div>
                </div>

                {/* Right Content - Stats Card */}
                <div className="relative">
                  <div className="bg-white/10 backdrop-blur-md rounded-3xl p-8 border border-white/20 shadow-2xl">
                    <h4 className="text-2xl font-bold text-white mb-6 text-center">Our Impact</h4>

                    <div className="grid grid-cols-2 gap-6">
                      <div className="text-center">
                        <div className="text-3xl font-bold text-blue-400 mb-2">500+</div>
                        <div className="text-blue-200 text-sm">Entrepreneurs Supported</div>
                      </div>
                      <div className="text-center">
                        <div className="text-3xl font-bold text-purple-400 mb-2">50+</div>
                        <div className="text-purple-200 text-sm">Successful Startups</div>
                      </div>
                      <div className="text-center">
                        <div className="text-3xl font-bold text-indigo-400 mb-2">95%</div>
                        <div className="text-indigo-200 text-sm">Success Rate</div>
                      </div>
                      <div className="text-center">
                        <div className="text-3xl font-bold text-pink-400 mb-2">$2M+</div>
                        <div className="text-pink-200 text-sm">Funding Raised</div>
                      </div>
                    </div>

                    <div className="mt-8 p-4 bg-white/5 rounded-2xl border border-white/10">
                      <p className="text-white text-center text-sm leading-relaxed">
                        &quot;The FWU Incubation Center transformed my idea into a thriving business.
                        The mentorship and resources were invaluable.&quot;
                      </p>
                      <div className="text-center mt-3">
                        <div className="text-blue-300 font-semibold text-sm">- Sarah Chen</div>
                        <div className="text-blue-200 text-xs">TechStart Founder</div>
                      </div>
                    </div>
                  </div>

                  {/* Floating Elements */}
                  <div className="absolute -top-4 -right-4 w-16 h-16 bg-gradient-to-r from-yellow-400 to-orange-400 rounded-2xl flex items-center justify-center shadow-lg">
                    <Award className="text-white" size={24} />
                  </div>

                  <div className="absolute -bottom-4 -left-4 w-12 h-12 bg-gradient-to-r from-green-400 to-blue-400 rounded-xl flex items-center justify-center shadow-lg">
                    <Target className="text-white" size={16} />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Custom CSS for animations */}
        <style jsx global>{`
          @keyframes float-gentle {
            0%, 100% { transform: translate(0, 0) rotate(0deg); }
            25% { transform: translate(5px, -5px) rotate(45deg); }
            50% { transform: translate(-3px, -8px) rotate(90deg); }
            75% { transform: translate(-5px, 3px) rotate(135deg); }
          }

          @keyframes fade-in-up {
            from {
              opacity: 0;
              transform: translateY(30px);
            }
            to {
              opacity: 1;
              transform: translateY(0);
            }
          }

          .animate-fade-in-up {
            animation: fade-in-up 0.6s ease-out forwards;
          }

          .line-clamp-3 {
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
          }
        `}</style>
      </div>
    </section>
  );
};

export default ProgramTypesSection;