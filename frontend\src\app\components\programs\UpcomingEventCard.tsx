"use client"
import { Calendar, Clock, MapPin, ChevronRight, Users, Star, ExternalLink } from 'lucide-react';
import { useState } from 'react';

export interface UpcomingEvent {
  id: string;
  date: string; // e.g., "Oct 25"
  fullDate: string; // e.g., "October 25, 2024"
  time?: string; // e.g., "10:00 AM - 04:00 PM"
  title: string;
  type: string; // e.g., "Workshop", "Deadline", "Networking"
  location?: string; // e.g., "Online" or "FWU Auditorium"
  description: string;
  color?: string; // Tailwind color class e.g. 'bg-blue-500'
  detailedDescription?: string; // More details for expanded view
  registrationLink?: string; // Link to register for the event
  capacity?: number; // Number of available spots
  status?: 'upcoming' | 'ongoing' | 'completed' | 'cancelled'; // Event status
}

interface UpcomingEventCardProps {
  event: UpcomingEvent;
}

const UpcomingEventCard: React.FC<UpcomingEventCardProps> = ({ event }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const typeColor = event.color || 'bg-blue-600';

  const toggleExpand = () => {
    setIsExpanded(!isExpanded);
  };

  const getStatusColor = (status?: string) => {
    const colors = {
      'upcoming': 'bg-green-100 text-green-800',
      'ongoing': 'bg-blue-100 text-blue-800',
      'completed': 'bg-gray-100 text-gray-800',
      'cancelled': 'bg-red-100 text-red-800'
    };
    return colors[status as keyof typeof colors] || 'bg-green-100 text-green-800';
  };

  return (
    <div className="group relative bg-white rounded-3xl shadow-xl border border-gray-100 overflow-hidden hover:shadow-2xl transition-all duration-500 hover:-translate-y-2">
      {/* Gradient Accent */}
      <div className={`absolute top-0 left-0 right-0 h-1 ${typeColor} transform origin-left scale-x-0 group-hover:scale-x-100 transition-transform duration-700`}></div>

      {/* Status Badge */}
      {event.status && (
        <div className="absolute top-4 right-4 z-10">
          <span className={`px-3 py-1 rounded-full text-xs font-semibold ${getStatusColor(event.status)}`}>
            {event.status.toUpperCase()}
          </span>
        </div>
      )}

      <div className="flex flex-col lg:flex-row">
        {/* Enhanced Date Column */}
        <div className={`p-8 lg:w-1/4 flex flex-col items-center justify-center text-white ${typeColor} relative overflow-hidden group-hover:scale-105 transition-transform duration-300`}>
          {/* Background Elements */}
          <div className="absolute inset-0 overflow-hidden">
            <div className="absolute top-4 right-4 w-16 h-16 bg-white/10 rounded-full blur-xl"></div>
            <div className="absolute bottom-4 left-4 w-12 h-12 bg-white/10 rounded-full blur-lg"></div>

            {/* Pattern */}
            <div className="absolute inset-0 opacity-20"
                 style={{
                   backgroundImage: 'radial-gradient(#ffffff 1px, transparent 1px)',
                   backgroundSize: '15px 15px'
                 }}>
            </div>
          </div>

          <div className="relative z-10 text-center">
            <div className="text-4xl lg:text-5xl font-black mb-2 drop-shadow-lg">
              {event.date.split(' ')[1] || event.date.split(' ')[0]}
            </div>
            <div className="text-lg uppercase font-bold tracking-wider opacity-90">
              {event.date.split(' ')[0]}
            </div>

            {/* Event Type Badge */}
            <div className="mt-4 px-3 py-1 bg-white/20 backdrop-blur-sm rounded-full border border-white/30">
              <span className="text-xs font-semibold uppercase tracking-wide">{event.type}</span>
            </div>
          </div>
        </div>

        {/* Enhanced Content Column */}
        <div className="p-8 lg:p-10 flex-grow">
          <div className="flex justify-between items-start mb-6">
            {/* Priority/Featured Badge */}
            <div className="flex items-center space-x-2">
              <Star className="text-yellow-500" size={16} />
              <span className="text-sm font-semibold text-gray-700">Featured Event</span>
            </div>

            {/* Expand Button */}
            <button
              onClick={toggleExpand}
              className="w-10 h-10 flex items-center justify-center bg-gradient-to-r from-blue-50 to-purple-50 hover:from-blue-100 hover:to-purple-100 text-blue-600 rounded-2xl focus:outline-none transition-all duration-300 hover:scale-110 active:scale-95 shadow-md"
              aria-label={isExpanded ? "Collapse details" : "Expand details"}
            >
              <div className={`transform transition-transform duration-300 ${isExpanded ? 'rotate-90' : 'rotate-0'}`}>
                <ChevronRight size={20} />
              </div>
            </button>
          </div>

          <h3 className="text-2xl lg:text-3xl font-bold text-gray-900 mb-4 group-hover:text-transparent group-hover:bg-clip-text group-hover:bg-gradient-to-r group-hover:from-blue-600 group-hover:to-purple-600 transition-all duration-300 leading-tight">
            {event.title}
          </h3>

          <p className="text-gray-600 mb-6 leading-relaxed text-lg">{event.description}</p>

          {/* Enhanced Event Details */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
            <div className="flex items-center space-x-3 p-3 bg-gradient-to-r from-blue-50 to-blue-100 rounded-xl border border-blue-200">
              <Calendar className="text-blue-600" size={18} />
              <div>
                <div className="text-xs font-semibold text-blue-700 uppercase tracking-wide">Date</div>
                <div className="text-sm font-medium text-blue-900">{event.fullDate}</div>
              </div>
            </div>

            {event.time && (
              <div className="flex items-center space-x-3 p-3 bg-gradient-to-r from-purple-50 to-purple-100 rounded-xl border border-purple-200">
                <Clock className="text-purple-600" size={18} />
                <div>
                  <div className="text-xs font-semibold text-purple-700 uppercase tracking-wide">Time</div>
                  <div className="text-sm font-medium text-purple-900">{event.time}</div>
                </div>
              </div>
            )}

            {event.location && (
              <div className="flex items-center space-x-3 p-3 bg-gradient-to-r from-indigo-50 to-indigo-100 rounded-xl border border-indigo-200">
                <MapPin className="text-indigo-600" size={18} />
                <div>
                  <div className="text-xs font-semibold text-indigo-700 uppercase tracking-wide">Location</div>
                  <div className="text-sm font-medium text-indigo-900">{event.location}</div>
                </div>
              </div>
            )}

            {event.capacity && (
              <div className="flex items-center space-x-3 p-3 bg-gradient-to-r from-green-50 to-green-100 rounded-xl border border-green-200">
                <Users className="text-green-600" size={18} />
                <div>
                  <div className="text-xs font-semibold text-green-700 uppercase tracking-wide">Capacity</div>
                  <div className="text-sm font-medium text-green-900">{event.capacity} spots</div>
                </div>
              </div>
            )}
          </div>

          {/* Quick Action Button */}
          {event.registrationLink && (
            <div className="flex justify-end">
              <a
                href={event.registrationLink}
                className="group/btn inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-2xl transition-all duration-300 hover:shadow-lg hover:shadow-blue-500/25 hover:-translate-y-1"
              >
                <span className="mr-2">Learn More</span>
                <ExternalLink className="group-hover/btn:translate-x-1 transition-transform duration-300" size={16} />
              </a>
            </div>
          )}
        </div>
      </div>

      {/* Enhanced Expandable Details Section */}
      <div
        className={`px-8 lg:px-10 pb-8 pt-0 border-t border-gray-200 overflow-hidden transition-all duration-700 ease-in-out ${
          isExpanded ? 'max-h-[500px] opacity-100' : 'max-h-0 opacity-0'
        }`}
      >
        <div className="pt-8">
          {/* Detailed Description */}
          <div className="mb-8">
            <h4 className="text-lg font-bold text-gray-900 mb-4 flex items-center">
              <div className="w-6 h-6 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center mr-3">
                <Star className="text-white" size={12} />
              </div>
              Event Details
            </h4>
            <div className="bg-gradient-to-r from-gray-50 to-blue-50 rounded-2xl p-6 border border-gray-200">
              <p className="text-gray-700 leading-relaxed">
                {event.detailedDescription || "More details about this event will be announced soon. Stay tuned for updates and prepare for an exciting learning experience!"}
              </p>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4">
            {event.registrationLink && (
              <a
                href={event.registrationLink}
                className="group relative flex-1 inline-flex items-center justify-center bg-gradient-to-r from-blue-600 to-purple-600 text-white font-bold py-4 px-6 rounded-2xl transition-all duration-300 hover:shadow-xl hover:shadow-blue-500/25 hover:-translate-y-1 overflow-hidden"
              >
                <div className="absolute inset-0 bg-gradient-to-r from-blue-700 to-purple-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <ExternalLink className="relative z-10 mr-2" size={20} />
                <span className="relative z-10">Register Now</span>
              </a>
            )}

            <button
              onClick={() => {
                // Add to calendar functionality
                alert('Add to calendar functionality would be implemented here');
              }}
              className="group relative flex-1 inline-flex items-center justify-center bg-white border-2 border-gray-300 text-gray-700 font-bold py-4 px-6 rounded-2xl transition-all duration-300 hover:border-blue-400 hover:text-blue-600 hover:shadow-lg hover:-translate-y-1"
            >
              <Calendar className="mr-2" size={20} />
              <span>Add to Calendar</span>
            </button>
          </div>

          {/* Additional Info */}
          <div className="mt-6 p-4 bg-gradient-to-r from-yellow-50 to-orange-50 rounded-xl border border-yellow-200">
            <div className="flex items-start space-x-3">
              <div className="w-6 h-6 bg-yellow-400 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                <Star className="text-white" size={12} />
              </div>
              <div>
                <h5 className="font-semibold text-yellow-800 mb-1">Pro Tip</h5>
                <p className="text-yellow-700 text-sm leading-relaxed">
                  Register early to secure your spot and receive exclusive pre-event materials and networking opportunities!
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UpcomingEventCard;