(()=>{var e={};e.id=547,e.ids=[547],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},59685:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});var n=t(37413);t(61120);let a=function(){return(0,n.jsx)("div",{children:"ProgramSection"})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},79551:e=>{"use strict";e.exports=require("url")},82682:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>p,pages:()=>l,routeModule:()=>c,tree:()=>d});var n=t(65239),a=t(48088),s=t(88170),i=t.n(s),o=t(30893),u={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(u[e]=()=>o[e]);t.d(r,u);let d={children:["",{children:["fwu",{children:["programs",{children:["[slug]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,59685)),"D:\\fwu_incubation(react+laravel)\\incubation\\frontend\\src\\app\\fwu\\programs\\[slug]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,45689)),"D:\\fwu_incubation(react+laravel)\\incubation\\frontend\\src\\app\\fwu\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,53862)),"D:\\fwu_incubation(react+laravel)\\incubation\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54413)),"D:\\fwu_incubation(react+laravel)\\incubation\\frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,l=["D:\\fwu_incubation(react+laravel)\\incubation\\frontend\\src\\app\\fwu\\programs\\[slug]\\page.tsx"],p={require:t,loadChunk:()=>Promise.resolve()},c=new n.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/fwu/programs/[slug]/page",pathname:"/fwu/programs/[slug]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),n=r.X(0,[447,693,585,171,305],()=>t(82682));module.exports=n})();