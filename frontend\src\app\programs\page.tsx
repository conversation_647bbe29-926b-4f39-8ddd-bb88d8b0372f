"use client"
import { useState, useEffect, useRef } from 'react';
import PastEventsGallerySection from "../components/programs/PastEventsGallerySection ";
import ProgramCalendarSection from "../components/programs/ProgramCalendarSection ";
import ProgramTypesSection from "../components/programs/ProgramTypesSection";
import {
  ArrowDown,
  Sparkles,
  Target,
  Users,
  Award,

  Rocket,
  Star,
  ChevronRight,
  Play
} from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import ProgramContent from '../components/programs/slug/ProgramContent';
import { programsData } from '@/data/programsData';

export default function ProgramsPage() {
  const [isVisible, setIsVisible] = useState(false);
  const [activeFeature, setActiveFeature] = useState(0);
  const heroRef = useRef(null);

  useEffect(() => {
    setIsVisible(true);

    // Auto-rotate features
    const interval = setInterval(() => {
      setActiveFeature((prev) => (prev + 1) % 4);
    }, 3000);

    return () => clearInterval(interval);
  }, []);

  // Function to scroll to the next section smoothly
  const scrollToNextSection = () => {
    const programsSection = document.getElementById('program-types');
    if (programsSection) {
      programsSection.scrollIntoView({ behavior: 'smooth' });
    }
  };

 
  return (
    <>
      {/* Enhanced Hero Section with Modern Design */}
      <section
        ref={heroRef}
        className="relative min-h-[100vh] flex items-center overflow-hidden bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900"
      >
        {/* Dynamic Background Elements */}
        <div className="absolute inset-0">
          {/* Hero Background Image */}
          <Image
            src="https://images.unsplash.com/photo-1522202176988-66273c2fd55f?q=80&w=2000&auto=format&fit=crop"
            alt="FWU Incubation Programs"
            fill
            priority
            className="object-cover opacity-20"
          />

          {/* Gradient Overlays */}
          <div className="absolute inset-0 bg-gradient-to-br from-blue-900/95 via-indigo-900/90 to-purple-900/95"></div>
          <div className="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent"></div>

          {/* Animated Mesh Pattern */}
          <div
            className="absolute inset-0 opacity-10"
            style={{
              backgroundImage: `
                linear-gradient(rgba(59, 130, 246, 0.3) 1px, transparent 1px),
                linear-gradient(90deg, rgba(59, 130, 246, 0.3) 1px, transparent 1px)
              `,
              backgroundSize: '60px 60px',
              animation: 'mesh-drift 25s linear infinite'
            }}
          ></div>
        </div>

        {/* Floating Elements */}
        <div className="absolute inset-0 overflow-hidden">
          {/* Animated Particles */}
          {[...Array(40)].map((_, i) => (
            <div
              key={i}
              className={`absolute rounded-full ${
                i % 5 === 0 ? 'bg-blue-400/30' :
                i % 5 === 1 ? 'bg-purple-400/30' :
                i % 5 === 2 ? 'bg-indigo-400/30' :
                i % 5 === 3 ? 'bg-pink-400/30' : 'bg-cyan-400/30'
              }`}
              style={{
                width: `${Math.random() * 8 + 3}px`,
                height: `${Math.random() * 8 + 3}px`,
                top: `${Math.random() * 100}%`,
                left: `${Math.random() * 100}%`,
                animation: `float-drift ${
                  10 + Math.random() * 20
                }s infinite ease-in-out`,
                animationDelay: `${Math.random() * 10}s`,
              }}
            ></div>
          ))}

          {/* Glowing Orbs */}
          {[...Array(6)].map((_, i) => (
            <div
              key={`orb-${i}`}
              className={`absolute rounded-full blur-2xl ${
                i % 3 === 0 ? 'bg-blue-500/15' :
                i % 3 === 1 ? 'bg-purple-500/15' : 'bg-indigo-500/15'
              }`}
              style={{
                width: `${Math.random() * 300 + 150}px`,
                height: `${Math.random() * 300 + 150}px`,
                top: `${Math.random() * 100}%`,
                left: `${Math.random() * 100}%`,
                animation: `pulse-glow ${
                  15 + Math.random() * 10
                }s infinite ease-in-out`,
                animationDelay: `${Math.random() * 8}s`,
              }}
            ></div>
          ))}
        </div>

        {/* Geometric Decorations */}
        <div className="absolute inset-0">
          <div className="absolute top-32 right-32 w-40 h-40 border-2 border-white/10 rounded-full animate-spin-slow"></div>
          <div className="absolute bottom-40 left-32 w-32 h-32 border border-purple-400/20 rotate-45 animate-pulse"></div>
          <div className="absolute top-1/2 right-1/4 w-20 h-20 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-xl rotate-12 animate-float"></div>
        </div>

        {/* Main Content */}
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-20">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Left Content */}
            <div className={`text-center lg:text-left transition-all duration-1000 transform ${
              isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
            }`}>
              {/* Enhanced Badge */}
              <div className="inline-block mb-8">
                <div className="flex items-center justify-center lg:justify-start space-x-3 bg-gradient-to-r from-white/20 to-white/10 backdrop-blur-md px-6 py-3 rounded-2xl border border-white/30 shadow-2xl">
                  <Sparkles className="text-yellow-300" size={20} />
                  <span className="text-white font-semibold text-sm tracking-wider uppercase">
                    Innovation Hub
                  </span>
                  <Star className="text-yellow-300" size={16} />
                </div>
              </div>

              {/* Main Heading */}
              <h1 className="text-5xl sm:text-6xl md:text-7xl lg:text-8xl font-black mb-8 leading-tight">
                <span className="block text-white mb-2">FWU</span>
                <span className="block text-transparent bg-clip-text bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 animate-gradient-x">
                  Incubation
                </span>
                <span className="block text-transparent bg-clip-text bg-gradient-to-r from-purple-400 via-pink-400 to-blue-400 animate-gradient-x-reverse">
                  Programs
                </span>
              </h1>

              {/* Description */}
              <p className="text-xl sm:text-2xl text-blue-100 max-w-2xl mx-auto lg:mx-0 mb-6 leading-relaxed">
                Empowering <span className="font-semibold text-white">innovation</span> and
                <span className="text-purple-300 font-semibold"> entrepreneurship</span> at Far Western University
              </p>

              <p className="text-lg text-blue-200 max-w-2xl mx-auto lg:mx-0 mb-10 leading-relaxed">
                Discover our range of specialized programs designed to support entrepreneurs at every stage of their journey,
                from ideation to market launch. Join our vibrant community of innovators and change-makers.
              </p>

              {/* Action Buttons */}
              <div className="flex flex-col sm:flex-row items-center justify-center lg:justify-start gap-6 mb-12">
                <button
                  onClick={scrollToNextSection}
                  className="group relative px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-bold rounded-2xl transition-all duration-300 hover:shadow-2xl hover:shadow-blue-500/25 hover:-translate-y-1 flex items-center overflow-hidden"
                >
                  <div className="absolute inset-0 bg-gradient-to-r from-blue-700 to-purple-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  <span className="relative z-10">Explore Programs</span>
                  <ArrowDown className="relative z-10 ml-2 group-hover:translate-y-1 transition-transform duration-300" />
                </button>

                <Link
                  href="/apply"
                  className="group relative px-8 py-4 bg-white/10 hover:bg-white/20 backdrop-blur-md text-white font-bold rounded-2xl transition-all duration-300 border-2 border-white/30 hover:border-white/50 hover:shadow-2xl hover:shadow-white/10 hover:-translate-y-1 flex items-center overflow-hidden"
                >
                  <div className="absolute inset-0 bg-white/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  <span className="relative z-10">Apply Now</span>
                  <ChevronRight className="relative z-10 ml-2 group-hover:translate-x-1 transition-transform duration-300" />
                </Link>
              </div>

              {/* Feature Highlights */}
              <div className="grid grid-cols-2 gap-4 max-w-md mx-auto lg:mx-0">
                {[
                  { icon: Target, text: "Expert Mentorship", color: "text-blue-400" },
                  { icon: Users, text: "Vibrant Community", color: "text-purple-400" },
                  { icon: Rocket, text: "Launch Support", color: "text-pink-400" },
                  { icon: Award, text: "Industry Recognition", color: "text-yellow-400" }
                ].map((item, index) => (
                  <div
                    key={index}
                    className={`flex items-center space-x-3 p-3 bg-white/5 backdrop-blur-sm rounded-xl border border-white/10 hover:bg-white/10 transition-all duration-300 group ${
                      activeFeature === index ? 'bg-white/15 border-white/30' : ''
                    }`}
                  >
                    <item.icon className={`${item.color} group-hover:scale-110 transition-transform duration-300`} size={20} />
                    <span className="text-white font-medium text-sm">{item.text}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Right Content - Video/Image */}
            <div className={`relative transition-all duration-1000 delay-300 transform ${
              isVisible ? 'opacity-100 translate-x-0' : 'opacity-0 translate-x-10'
            }`}>
              <div className="relative rounded-3xl overflow-hidden shadow-2xl group">
                {/* Main Image */}
                <div className="relative h-[500px] lg:h-[600px] w-full">
                  <Image
                    src="https://images.unsplash.com/photo-1521737604893-d14cc237f11d?q=80&w=1000&auto=format&fit=crop"
                    alt="FWU Incubation Programs"
                    fill
                    className="object-cover transform group-hover:scale-105 transition-transform duration-700"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-indigo-900/70 via-transparent to-transparent"></div>

                  {/* Play Button Overlay */}
                  <div className="absolute inset-0 flex items-center justify-center">
                    <button className="w-20 h-20 bg-white/20 backdrop-blur-md rounded-full flex items-center justify-center border-2 border-white/30 hover:bg-white/30 transition-all duration-300 group-hover:scale-110">
                      <Play className="text-white ml-1" size={32} />
                    </button>
                  </div>
                </div>

                {/* Floating Stats Cards */}
                <div className="absolute -bottom-6 -left-6 bg-white/90 backdrop-blur-md p-4 rounded-2xl shadow-xl border border-white/50">
                  <div className="text-2xl font-bold text-indigo-600 mb-1">20+</div>
                  <div className="text-sm text-gray-600">Active Programs</div>
                </div>

                <div className="absolute -top-6 -right-6 bg-white/90 backdrop-blur-md p-4 rounded-2xl shadow-xl border border-white/50">
                  <div className="text-2xl font-bold text-purple-600 mb-1">500+</div>
                  <div className="text-sm text-gray-600">Participants</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Enhanced Stats Section */}
        <div className="absolute bottom-20 left-1/2 transform -translate-x-1/2 w-full max-w-4xl px-4">
          <div className={`grid grid-cols-2 md:grid-cols-4 gap-4 transition-all duration-1000 delay-500 transform ${
            isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
          }`}>
            {[
              { number: "20+", label: "Active Programs", color: "from-blue-500 to-blue-600" },
              { number: "500+", label: "Participants", color: "from-purple-500 to-purple-600" },
              { number: "50+", label: "Expert Mentors", color: "from-indigo-500 to-indigo-600" },
              { number: "95%", label: "Success Rate", color: "from-pink-500 to-pink-600" }
            ].map((stat, index) => (
              <div
                key={index}
                className="bg-white/10 backdrop-blur-md p-4 rounded-2xl border border-white/20 hover:bg-white/20 transition-all duration-300 group"
              >
                <div className={`text-2xl md:text-3xl font-bold bg-gradient-to-r ${stat.color} bg-clip-text text-transparent mb-1 group-hover:scale-110 transition-transform duration-300`}>
                  {stat.number}
                </div>
                <div className="text-white/80 text-sm font-medium">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>

        {/* Scroll Indicator */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2">
          <button
            onClick={scrollToNextSection}
            className="w-12 h-12 bg-white/10 backdrop-blur-md rounded-full flex items-center justify-center border border-white/20 hover:bg-white/20 transition-all duration-300 group animate-bounce"
            aria-label="Scroll down to programs"
          >
            <ArrowDown className="text-white group-hover:translate-y-1 transition-transform duration-300" size={24} />
          </button>
        </div>
      </section>

      {/* Program Types Section with ID for scroll targeting */}
      <div id="program-types">
        {/* <ProgramTypesSection /> */}
      </div>
      <ProgramContent program={pro} />

      {/* Calendar Section */}
      <ProgramCalendarSection />

      {/* Past Events Gallery */}
      <PastEventsGallerySection />

      {/* Custom CSS for animations */}
      <style jsx global>{`
        @keyframes mesh-drift {
          0% { transform: translate(0, 0); }
          100% { transform: translate(-60px, -60px); }
        }

        @keyframes float-drift {
          0%, 100% { transform: translate(0, 0) rotate(0deg); }
          25% { transform: translate(10px, -10px) rotate(90deg); }
          50% { transform: translate(-5px, -15px) rotate(180deg); }
          75% { transform: translate(-10px, 5px) rotate(270deg); }
        }

        @keyframes pulse-glow {
          0%, 100% { opacity: 0.3; transform: scale(1); }
          50% { opacity: 0.6; transform: scale(1.1); }
        }

        @keyframes gradient-x {
          0%, 100% { background-position: 0% 50%; }
          50% { background-position: 100% 50%; }
        }

        @keyframes gradient-x-reverse {
          0%, 100% { background-position: 100% 50%; }
          50% { background-position: 0% 50%; }
        }

        .animate-gradient-x {
          background-size: 200% 200%;
          animation: gradient-x 3s ease infinite;
        }

        .animate-gradient-x-reverse {
          background-size: 200% 200%;
          animation: gradient-x-reverse 3s ease infinite;
        }

        .animate-float {
          animation: float 6s ease-in-out infinite;
        }

        .animate-spin-slow {
          animation: spin 20s linear infinite;
        }

        @keyframes float {
          0%, 100% { transform: translateY(0px) rotate(0deg); }
          50% { transform: translateY(-20px) rotate(180deg); }
        }
      `}</style>
    </>
  );
}