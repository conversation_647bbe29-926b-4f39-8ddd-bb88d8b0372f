"use client"
import React, { useState, useEffect } from 'react'
import Link from 'next/link'
import { useTheme } from 'next-themes'
import {
  ArrowLeft,
  Search,
  Compass,
  Sparkles,
  Star,

  Rocket,
  Heart,
  Coffee,
  MapPin,
  Lightbulb,
  RefreshCw
} from 'lucide-react'
import { useRouter } from 'next/navigation'

export default function NotFound() {
  const [mounted, setMounted] = useState(false)
  const [isAnimating, setIsAnimating] = useState(false)
  const { theme } = useTheme()
   const  navigation=useRouter();
  useEffect(() => {
    setMounted(true)
    // Trigger animation after component mounts
    setTimeout(() => setIsAnimating(true), 100)
  }, [])

  if (!mounted) {
    return null
  }

  const isDark = theme === 'dark'

  const handleRefresh = () => {
    window.location.reload()
  }

  return (
    <div className={`min-h-screen flex items-center justify-center p-4 relative overflow-hidden transition-all duration-1000 ${
      isDark
        ? 'bg-gradient-to-br from-gray-900 via-slate-900 to-black'
        : 'bg-gradient-to-br from-indigo-900 via-purple-900 to-blue-900'
    }`}>
      {/* Enhanced Animated Background */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Large floating orbs */}
        <div className={`absolute -top-40 -right-40 w-80 h-80 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse ${
          isDark ? 'bg-blue-600' : 'bg-purple-500'
        }`}></div>
        <div className={`absolute -bottom-40 -left-40 w-80 h-80 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse ${
          isDark ? 'bg-purple-600' : 'bg-blue-500'
        }`} style={{ animationDelay: '2s' }}></div>
        <div className={`absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 rounded-full mix-blend-multiply filter blur-xl opacity-15 animate-pulse ${
          isDark ? 'bg-indigo-600' : 'bg-indigo-500'
        }`} style={{ animationDelay: '4s' }}></div>

        {/* Floating particles */}
        {[...Array(20)].map((_, i) => (
          <div
            key={i}
            className={`absolute w-2 h-2 rounded-full opacity-30 ${
              isDark ? 'bg-white' : 'bg-white'
            }`}
            style={{
              top: `${Math.random() * 100}%`,
              left: `${Math.random() * 100}%`,
              animation: `float-gentle ${3 + Math.random() * 4}s ease-in-out infinite ${Math.random() * 2}s`
            }}
          ></div>
        ))}

        {/* Sparkle effects */}
        {[...Array(12)].map((_, i) => (
          <div
            key={`sparkle-${i}`}
            className={`absolute ${isDark ? 'text-blue-400/40' : 'text-yellow-400/60'}`}
            style={{
              top: `${20 + Math.random() * 60}%`,
              left: `${20 + Math.random() * 60}%`,
              animation: `sparkle-rotate ${2 + Math.random() * 3}s ease-in-out infinite ${Math.random() * 2}s`
            }}
          >
            <Sparkles size={8 + Math.random() * 8} />
          </div>
        ))}
      </div>

      {/* Main Content */}
      <div className="relative z-10 text-center max-w-4xl mx-auto">
        {/* Animated 404 SVG */}
        <div className={`mb-8 transition-all duration-1000 ${isAnimating ? 'scale-100 opacity-100' : 'scale-75 opacity-0'}`}>
          <svg
            width="400"
            height="200"
            viewBox="0 0 400 200"
            className="mx-auto"
            xmlns="http://www.w3.org/2000/svg"
          >
            <defs>
              <linearGradient id="gradient404" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor={isDark ? "#3B82F6" : "#8B5CF6"} />
                <stop offset="50%" stopColor={isDark ? "#8B5CF6" : "#EC4899"} />
                <stop offset="100%" stopColor={isDark ? "#EC4899" : "#F59E0B"} />
              </linearGradient>
              <filter id="glow">
                <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
                <feMerge>
                  <feMergeNode in="coloredBlur"/>
                  <feMergeNode in="SourceGraphic"/>
                </feMerge>
              </filter>
            </defs>

            {/* 404 Text */}
            <text
              x="200"
              y="120"
              fontSize="120"
              fontWeight="bold"
              textAnchor="middle"
              fill="url(#gradient404)"
              filter="url(#glow)"
              className="animate-pulse"
            >
              404
            </text>

            {/* Decorative elements */}
            <circle cx="80" cy="60" r="8" fill={isDark ? "#60A5FA" : "#A78BFA"} className="animate-bounce" style={{ animationDelay: '0.5s' }} />
            <circle cx="320" cy="60" r="6" fill={isDark ? "#F472B6" : "#FBBF24"} className="animate-bounce" style={{ animationDelay: '1s' }} />
            <circle cx="60" cy="140" r="4" fill={isDark ? "#34D399" : "#10B981"} className="animate-bounce" style={{ animationDelay: '1.5s' }} />
            <circle cx="340" cy="140" r="5" fill={isDark ? "#FBBF24" : "#F59E0B"} className="animate-bounce" style={{ animationDelay: '2s' }} />
          </svg>
        </div>

        {/* Astronaut SVG Illustration */}
        <div className={`mb-12 transition-all duration-1000 delay-300 ${isAnimating ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'}`}>
          <svg
            width="300"
            height="200"
            viewBox="0 0 300 200"
            className="mx-auto"
            xmlns="http://www.w3.org/2000/svg"
          >
            <defs>
              <linearGradient id="astronautGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor={isDark ? "#60A5FA" : "#8B5CF6"} />
                <stop offset="100%" stopColor={isDark ? "#A78BFA" : "#EC4899"} />
              </linearGradient>
              <linearGradient id="planetGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor={isDark ? "#F59E0B" : "#FBBF24"} />
                <stop offset="100%" stopColor={isDark ? "#EF4444" : "#F97316"} />
              </linearGradient>
            </defs>

            {/* Stars */}
            <circle cx="50" cy="30" r="2" fill={isDark ? "#FBBF24" : "#FDE047"} className="animate-pulse" />
            <circle cx="250" cy="40" r="1.5" fill={isDark ? "#60A5FA" : "#93C5FD"} className="animate-pulse" style={{ animationDelay: '0.5s' }} />
            <circle cx="80" cy="180" r="1" fill={isDark ? "#F472B6" : "#FBBF24"} className="animate-pulse" style={{ animationDelay: '1s' }} />
            <circle cx="220" cy="170" r="2" fill={isDark ? "#34D399" : "#6EE7B7"} className="animate-pulse" style={{ animationDelay: '1.5s' }} />

            {/* Planet */}
            <circle cx="240" cy="60" r="25" fill="url(#planetGradient)" className="animate-spin" style={{ animationDuration: '20s' }} />
            <circle cx="235" cy="55" r="3" fill={isDark ? "#1F2937" : "#374151"} opacity="0.3" />
            <circle cx="245" cy="65" r="2" fill={isDark ? "#1F2937" : "#374151"} opacity="0.3" />

            {/* Astronaut */}
            <g className="animate-bounce" style={{ animationDuration: '3s' }}>
              {/* Body */}
              <ellipse cx="150" cy="120" rx="25" ry="35" fill="url(#astronautGradient)" />

              {/* Helmet */}
              <circle cx="150" cy="80" r="20" fill={isDark ? "#E5E7EB" : "#F3F4F6"} opacity="0.9" />
              <circle cx="150" cy="80" r="18" fill="none" stroke={isDark ? "#9CA3AF" : "#D1D5DB"} strokeWidth="2" />

              {/* Face */}
              <circle cx="145" cy="78" r="2" fill={isDark ? "#1F2937" : "#374151"} />
              <circle cx="155" cy="78" r="2" fill={isDark ? "#1F2937" : "#374151"} />
              <path d="M 145 85 Q 150 88 155 85" stroke={isDark ? "#1F2937" : "#374151"} strokeWidth="1.5" fill="none" />

              {/* Arms */}
              <ellipse cx="125" cy="110" rx="8" ry="20" fill="url(#astronautGradient)" transform="rotate(-20 125 110)" />
              <ellipse cx="175" cy="110" rx="8" ry="20" fill="url(#astronautGradient)" transform="rotate(20 175 110)" />

              {/* Legs */}
              <ellipse cx="140" cy="160" rx="8" ry="25" fill="url(#astronautGradient)" />
              <ellipse cx="160" cy="160" rx="8" ry="25" fill="url(#astronautGradient)" />

              {/* Jetpack */}
              <rect x="135" y="100" width="30" height="40" rx="5" fill={isDark ? "#6B7280" : "#9CA3AF"} />
              <circle cx="140" cy="145" r="3" fill={isDark ? "#EF4444" : "#F87171"} className="animate-pulse" />
              <circle cx="160" cy="145" r="3" fill={isDark ? "#3B82F6" : "#60A5FA"} className="animate-pulse" style={{ animationDelay: '0.5s' }} />
            </g>

            {/* Floating elements */}
            <g className="animate-pulse">
              <circle cx="100" cy="50" r="1" fill={isDark ? "#A78BFA" : "#C084FC"} />
              <circle cx="200" cy="150" r="1.5" fill={isDark ? "#34D399" : "#6EE7B7"} />
            </g>
          </svg>
        </div>

        {/* Main Content */}
        <div className={`space-y-8 transition-all duration-1000 delay-500 ${isAnimating ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'}`}>
          {/* Main Heading */}
          <div className="space-y-4">
            <h1 className={`text-4xl md:text-6xl font-bold bg-gradient-to-r bg-clip-text text-transparent ${
              isDark
                ? 'from-blue-400 via-purple-400 to-pink-400'
                : 'from-purple-400 via-pink-400 to-red-400'
            }`}>
              Oops! Lost in Space
            </h1>
            <p className={`text-xl md:text-2xl font-medium transition-colors duration-500 ${
              isDark ? 'text-gray-300' : 'text-white'
            }`}>
              The page you&apos;re looking for has drifted away
            </p>
            <p className={`text-lg max-w-2xl mx-auto leading-relaxed transition-colors duration-500 ${
              isDark ? 'text-gray-400' : 'text-blue-200'
            }`}>
              Don&apos;t worry, our astronaut is here to help you navigate back to safety.
              Let&apos;s get you back to exploring amazing opportunities!
            </p>
          </div>

          {/* Fun Facts */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-3xl mx-auto">
            <div className={`p-6 rounded-2xl backdrop-blur-sm border transition-all duration-300 hover:scale-105 ${
              isDark
                ? 'bg-gray-800/40 border-gray-700/50 hover:bg-gray-800/60'
                : 'bg-white/10 border-white/20 hover:bg-white/15'
            }`}>
              <div className="flex items-center justify-center mb-4">
                <div className={`p-3 rounded-full ${
                  isDark ? 'bg-blue-500/20' : 'bg-blue-500/20'
                }`}>
                  <Rocket className={`w-6 h-6 ${isDark ? 'text-blue-400' : 'text-blue-300'}`} />
                </div>
              </div>
              <h3 className={`font-bold text-lg mb-2 ${isDark ? 'text-white' : 'text-white'}`}>
                500+ Startups
              </h3>
              <p className={`text-sm ${isDark ? 'text-gray-400' : 'text-blue-200'}`}>
                Successfully launched from our incubator
              </p>
            </div>

            <div className={`p-6 rounded-2xl backdrop-blur-sm border transition-all duration-300 hover:scale-105 ${
              isDark
                ? 'bg-gray-800/40 border-gray-700/50 hover:bg-gray-800/60'
                : 'bg-white/10 border-white/20 hover:bg-white/15'
            }`}>
              <div className="flex items-center justify-center mb-4">
                <div className={`p-3 rounded-full ${
                  isDark ? 'bg-purple-500/20' : 'bg-purple-500/20'
                }`}>
                  <Star className={`w-6 h-6 ${isDark ? 'text-purple-400' : 'text-purple-300'}`} />
                </div>
              </div>
              <h3 className={`font-bold text-lg mb-2 ${isDark ? 'text-white' : 'text-white'}`}>
                95% Success Rate
              </h3>
              <p className={`text-sm ${isDark ? 'text-gray-400' : 'text-blue-200'}`}>
                Of our incubated startups thrive
              </p>
            </div>

            <div className={`p-6 rounded-2xl backdrop-blur-sm border transition-all duration-300 hover:scale-105 ${
              isDark
                ? 'bg-gray-800/40 border-gray-700/50 hover:bg-gray-800/60'
                : 'bg-white/10 border-white/20 hover:bg-white/15'
            }`}>
              <div className="flex items-center justify-center mb-4">
                <div className={`p-3 rounded-full ${
                  isDark ? 'bg-green-500/20' : 'bg-green-500/20'
                }`}>
                  <Heart className={`w-6 h-6 ${isDark ? 'text-green-400' : 'text-green-300'}`} />
                </div>
              </div>
              <h3 className={`font-bold text-lg mb-2 ${isDark ? 'text-white' : 'text-white'}`}>
                Community Driven
              </h3>
              <p className={`text-sm ${isDark ? 'text-gray-400' : 'text-blue-200'}`}>
                Built with love for entrepreneurs
              </p>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row items-center justify-center gap-4 pt-8">
            <button
               onClick={()=>{
                navigation.back();
               }}
              
              className={`group flex items-center space-x-3 px-8 py-4 rounded-2xl font-bold text-lg transition-all duration-300 hover:scale-105 hover:shadow-2xl ${
                isDark
                  ? 'bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white'
                  : 'bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white'
              }`}
            >
              {/* <Home className="w-6 h-6 group-hover:scale-110 transition-transform duration-300" /> */}
              <span>Back to Previous Page </span>
              <ArrowLeft className="w-5 h-5 group-hover:-translate-x-1 transition-transform duration-300" />
            </button>

            <button
              onClick={handleRefresh}
              className={`group flex items-center space-x-3 px-8 py-4 rounded-2xl font-bold text-lg border-2 transition-all duration-300 hover:scale-105 ${
                isDark
                  ? 'border-gray-600 text-gray-300 hover:border-blue-500 hover:text-blue-400 hover:bg-blue-500/10'
                  : 'border-white/30 text-white hover:border-white hover:bg-white/10'
              }`}
            >
              <RefreshCw className="w-6 h-6 group-hover:rotate-180 transition-transform duration-500" />
              <span>Try Again</span>
            </button>

            <Link
              href="/search"
              className={`group flex items-center space-x-3 px-8 py-4 rounded-2xl font-bold text-lg border-2 transition-all duration-300 hover:scale-105 ${
                isDark
                  ? 'border-gray-600 text-gray-300 hover:border-purple-500 hover:text-purple-400 hover:bg-purple-500/10'
                  : 'border-white/30 text-white hover:border-white hover:bg-white/10'
              }`}
            >
              <Search className="w-6 h-6 group-hover:scale-110 transition-transform duration-300" />
              <span>Search</span>
            </Link>
          </div>

          {/* Additional Help */}
          <div className={`pt-8 transition-colors duration-500 ${
            isDark ? 'text-gray-400' : 'text-blue-200'
          }`}>
            <p className="text-sm mb-4">
              <Coffee className="inline w-4 h-4 mr-2" />
              Need help? Our team is here to assist you 24/7
            </p>
            <div className="flex items-center justify-center space-x-6 text-xs">
              <span className="flex items-center space-x-1">
                <MapPin className="w-3 h-3" />
                <span>Kanchanpur, Nepal</span>
              </span>
              <span className="flex items-center space-x-1">
                <Lightbulb className="w-3 h-3" />
                <span>Innovation Hub</span>
              </span>
              <span className="flex items-center space-x-1">
                <Compass className="w-3 h-3" />
                <span>Guiding Startups</span>
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced CSS Animations */}
      <style jsx>{`
        @keyframes float-gentle {
          0%, 100% { transform: translate(0, 0) rotate(0deg); }
          25% { transform: translate(5px, -5px) rotate(45deg); }
          50% { transform: translate(-3px, -8px) rotate(90deg); }
          75% { transform: translate(-5px, 3px) rotate(135deg); }
        }
        @keyframes sparkle-rotate {
          0%, 100% { opacity: 0.4; transform: rotate(0deg) scale(1); }
          25% { opacity: 0.8; transform: rotate(90deg) scale(1.1); }
          50% { opacity: 1; transform: rotate(180deg) scale(1.2); }
          75% { opacity: 0.8; transform: rotate(270deg) scale(1.1); }
        }
        @keyframes glow-pulse {
          0%, 100% { filter: drop-shadow(0 0 5px rgba(59, 130, 246, 0.5)); }
          50% { filter: drop-shadow(0 0 20px rgba(59, 130, 246, 0.8)); }
        }
      `}</style>
    </div>
  )
}