"use client"
import React, { useState, useEffect } from 'react'
import Link from 'next/link'
import { useTheme } from 'next-themes'
import {
  ArrowLeft,
  Search,
  Compass,
  Sparkles,
  Star,

  Rocket,
  Heart,
  Coffee,
  MapPin,
  Lightbulb,
  RefreshCw
} from 'lucide-react'
import { useRouter } from 'next/navigation'

export default function NotFound() {
  const [mounted, setMounted] = useState(false)
  const [isAnimating, setIsAnimating] = useState(false)
  const { theme } = useTheme()
   const  navigation=useRouter();
  useEffect(() => {
    setMounted(true)
    // Trigger animation after component mounts
    setTimeout(() => setIsAnimating(true), 100)
  }, [])

  if (!mounted) {
    return null
  }

  const isDark = theme === 'dark'

  const handleRefresh = () => {
    window.location.reload()
  }

  return (
    <div className={`min-h-screen flex items-center justify-center p-4 relative overflow-hidden transition-all duration-1000 ${
      isDark
        ? 'bg-gradient-to-br from-gray-900 via-slate-900 to-black'
        : 'bg-gradient-to-br from-indigo-900 via-purple-900 to-blue-900'
    }`}>
      {/* Enhanced Animated Background */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Large floating orbs */}
        <div className={`absolute -top-40 -right-40 w-80 h-80 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse ${
          isDark ? 'bg-blue-600' : 'bg-purple-500'
        }`}></div>
        <div className={`absolute -bottom-40 -left-40 w-80 h-80 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse ${
          isDark ? 'bg-purple-600' : 'bg-blue-500'
        }`} style={{ animationDelay: '2s' }}></div>
        <div className={`absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 rounded-full mix-blend-multiply filter blur-xl opacity-15 animate-pulse ${
          isDark ? 'bg-indigo-600' : 'bg-indigo-500'
        }`} style={{ animationDelay: '4s' }}></div>

        {/* Floating particles */}
        {[...Array(20)].map((_, i) => (
          <div
            key={i}
            className={`absolute w-2 h-2 rounded-full opacity-30 ${
              isDark ? 'bg-white' : 'bg-white'
            }`}
            style={{
              top: `${Math.random() * 100}%`,
              left: `${Math.random() * 100}%`,
              animation: `float-gentle ${3 + Math.random() * 4}s ease-in-out infinite ${Math.random() * 2}s`
            }}
          ></div>
        ))}

        {/* Sparkle effects */}
        {[...Array(12)].map((_, i) => (
          <div
            key={`sparkle-${i}`}
            className={`absolute ${isDark ? 'text-blue-400/40' : 'text-yellow-400/60'}`}
            style={{
              top: `${20 + Math.random() * 60}%`,
              left: `${20 + Math.random() * 60}%`,
              animation: `sparkle-rotate ${2 + Math.random() * 3}s ease-in-out infinite ${Math.random() * 2}s`
            }}
          >
            <Sparkles size={8 + Math.random() * 8} />
          </div>
        ))}
      </div>

      {/* Main Content */}
      <div className="relative z-10 text-center max-w-4xl mx-auto">
        {/* Animated 404 SVG */}
        <div className={`mb-8 transition-all duration-1000 ${isAnimating ? 'scale-100 opacity-100' : 'scale-75 opacity-0'}`}>
          <svg
            width="400"
            height="200"
            viewBox="0 0 400 200"
            className="mx-auto"
            xmlns="http://www.w3.org/2000/svg"
          >
            <defs>
              <linearGradient id="gradient404" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor={isDark ? "#3B82F6" : "#8B5CF6"} />
                <stop offset="50%" stopColor={isDark ? "#8B5CF6" : "#EC4899"} />
                <stop offset="100%" stopColor={isDark ? "#EC4899" : "#F59E0B"} />
              </linearGradient>
              <filter id="glow">
                <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
                <feMerge>
                  <feMergeNode in="coloredBlur"/>
                  <feMergeNode in="SourceGraphic"/>
                </feMerge>
              </filter>
            </defs>

            {/* 404 Text */}
            <text
              x="200"
              y="120"
              fontSize="120"
              fontWeight="bold"
              textAnchor="middle"
              fill="url(#gradient404)"
              filter="url(#glow)"
              className="animate-pulse"
            >
              404
            </text>

            {/* Decorative elements */}
            <circle cx="80" cy="60" r="8" fill={isDark ? "#60A5FA" : "#A78BFA"} className="animate-bounce" style={{ animationDelay: '0.5s' }} />
            <circle cx="320" cy="60" r="6" fill={isDark ? "#F472B6" : "#FBBF24"} className="animate-bounce" style={{ animationDelay: '1s' }} />
            <circle cx="60" cy="140" r="4" fill={isDark ? "#34D399" : "#10B981"} className="animate-bounce" style={{ animationDelay: '1.5s' }} />
            <circle cx="340" cy="140" r="5" fill={isDark ? "#FBBF24" : "#F59E0B"} className="animate-bounce" style={{ animationDelay: '2s' }} />
          </svg>
        </div>

        {/* Lost Way - Beautiful Forest Scene SVG */}
        <div className={`mb-12 transition-all duration-1000 delay-300 ${isAnimating ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'}`}>
          <svg
            width="500"
            height="350"
            viewBox="0 0 500 350"
            className="mx-auto"
            xmlns="http://www.w3.org/2000/svg"
          >
            <defs>
              <linearGradient id="skyGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                <stop offset="0%" stopColor={isDark ? "#1E293B" : "#3B82F6"} />
                <stop offset="50%" stopColor={isDark ? "#334155" : "#60A5FA"} />
                <stop offset="100%" stopColor={isDark ? "#475569" : "#93C5FD"} />
              </linearGradient>
              <linearGradient id="mountainGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                <stop offset="0%" stopColor={isDark ? "#6B7280" : "#8B5CF6"} />
                <stop offset="100%" stopColor={isDark ? "#374151" : "#6366F1"} />
              </linearGradient>
              <linearGradient id="treeGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                <stop offset="0%" stopColor={isDark ? "#059669" : "#10B981"} />
                <stop offset="100%" stopColor={isDark ? "#047857" : "#059669"} />
              </linearGradient>
              <linearGradient id="characterGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor={isDark ? "#F59E0B" : "#FBBF24"} />
                <stop offset="100%" stopColor={isDark ? "#EF4444" : "#F97316"} />
              </linearGradient>
              <linearGradient id="pathGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor={isDark ? "#92400E" : "#A16207"} />
                <stop offset="100%" stopColor={isDark ? "#78350F" : "#92400E"} />
              </linearGradient>
              <filter id="glowEffect">
                <feGaussianBlur stdDeviation="4" result="coloredBlur"/>
                <feMerge>
                  <feMergeNode in="coloredBlur"/>
                  <feMergeNode in="SourceGraphic"/>
                </feMerge>
              </filter>
              <filter id="dropShadow">
                <feDropShadow dx="2" dy="4" stdDeviation="3" floodColor={isDark ? "#000000" : "#1F2937"} floodOpacity="0.3"/>
              </filter>
            </defs>

            {/* Sky Background */}
            <rect width="500" height="200" fill="url(#skyGradient)" />

            {/* Clouds */}
            <g opacity="0.7">
              <ellipse cx="100" cy="60" rx="25" ry="12" fill={isDark ? "#64748B" : "#E2E8F0"} className="animate-pulse" />
              <ellipse cx="115" cy="55" rx="20" ry="10" fill={isDark ? "#64748B" : "#E2E8F0"} className="animate-pulse" />
              <ellipse cx="85" cy="55" rx="15" ry="8" fill={isDark ? "#64748B" : "#E2E8F0"} className="animate-pulse" />

              <ellipse cx="350" cy="40" rx="30" ry="15" fill={isDark ? "#64748B" : "#E2E8F0"} className="animate-pulse" style={{ animationDelay: '1s' }} />
              <ellipse cx="370" cy="35" rx="25" ry="12" fill={isDark ? "#64748B" : "#E2E8F0"} className="animate-pulse" style={{ animationDelay: '1s' }} />

              <ellipse cx="200" cy="30" rx="20" ry="10" fill={isDark ? "#64748B" : "#E2E8F0"} className="animate-pulse" style={{ animationDelay: '2s' }} />
            </g>

            {/* Mountains in background */}
            <g>
              <polygon points="0,200 80,120 160,200" fill="url(#mountainGradient)" opacity="0.8" />
              <polygon points="120,200 200,100 280,200" fill="url(#mountainGradient)" opacity="0.9" />
              <polygon points="240,200 320,110 400,200" fill="url(#mountainGradient)" opacity="0.7" />
              <polygon points="360,200 440,130 500,200" fill="url(#mountainGradient)" opacity="0.8" />

              {/* Mountain peaks with snow */}
              <polygon points="190,100 200,90 210,100" fill={isDark ? "#F1F5F9" : "#FFFFFF"} />
              <polygon points="310,110 320,95 330,110" fill={isDark ? "#F1F5F9" : "#FFFFFF"} />
            </g>

            {/* Forest Ground */}
            <rect x="0" y="200" width="500" height="150" fill={isDark ? "#166534" : "#22C55E"} opacity="0.3" />

            {/* Winding Path */}
            <g>
              <path d="M 50 350 Q 150 280 250 300 Q 350 320 450 250" stroke="url(#pathGradient)" strokeWidth="25" fill="none" opacity="0.8" />
              <path d="M 50 350 Q 150 280 250 300 Q 350 320 450 250" stroke={isDark ? "#A16207" : "#D97706"} strokeWidth="20" fill="none" opacity="0.6" />
              <path d="M 50 350 Q 150 280 250 300 Q 350 320 450 250" stroke={isDark ? "#CA8A04" : "#EAB308"} strokeWidth="15" fill="none" opacity="0.4" />

              {/* Path stones */}
              <circle cx="120" cy="295" r="3" fill={isDark ? "#78716C" : "#A8A29E"} />
              <circle cx="180" cy="285" r="2.5" fill={isDark ? "#78716C" : "#A8A29E"} />
              <circle cx="280" cy="305" r="3.5" fill={isDark ? "#78716C" : "#A8A29E"} />
              <circle cx="350" cy="315" r="2" fill={isDark ? "#78716C" : "#A8A29E"} />
            </g>

            {/* Forest Trees */}
            <g>
              {/* Large trees in background */}
              <g opacity="0.7">
                <rect x="45" y="180" width="8" height="40" fill={isDark ? "#92400E" : "#A16207"} />
                <circle cx="49" cy="175" r="18" fill="url(#treeGradient)" />
                <circle cx="42" cy="170" r="12" fill="url(#treeGradient)" />
                <circle cx="56" cy="168" r="14" fill="url(#treeGradient)" />
              </g>

              <g opacity="0.8">
                <rect x="420" y="170" width="10" height="50" fill={isDark ? "#92400E" : "#A16207"} />
                <circle cx="425" cy="165" r="22" fill="url(#treeGradient)" />
                <circle cx="415" cy="160" r="15" fill="url(#treeGradient)" />
                <circle cx="435" cy="158" r="17" fill="url(#treeGradient)" />
              </g>

              {/* Medium trees */}
              <g>
                <rect x="380" y="190" width="6" height="30" fill={isDark ? "#92400E" : "#A16207"} />
                <circle cx="383" cy="185" r="15" fill="url(#treeGradient)" />
                <circle cx="375" cy="180" r="10" fill="url(#treeGradient)" />
                <circle cx="391" cy="178" r="12" fill="url(#treeGradient)" />
              </g>

              <g>
                <rect x="90" y="200" width="5" height="25" fill={isDark ? "#92400E" : "#A16207"} />
                <circle cx="92.5" cy="195" r="12" fill="url(#treeGradient)" />
                <circle cx="86" cy="190" r="8" fill="url(#treeGradient)" />
                <circle cx="99" cy="188" r="10" fill="url(#treeGradient)" />
              </g>

              {/* Small trees and bushes */}
              <circle cx="150" cy="210" r="8" fill="url(#treeGradient)" opacity="0.9" />
              <circle cx="320" cy="220" r="6" fill="url(#treeGradient)" opacity="0.8" />
              <circle cx="200" cy="230" r="5" fill="url(#treeGradient)" opacity="0.7" />
              <circle cx="400" cy="240" r="7" fill="url(#treeGradient)" opacity="0.9" />
            </g>

            {/* Lost Traveler Character */}
            <g className="animate-bounce" style={{ animationDuration: '4s' }} filter="url(#dropShadow)">
              {/* Character shadow */}
              <ellipse cx="250" cy="340" rx="20" ry="6" fill={isDark ? "#000000" : "#1F2937"} opacity="0.3" />

              {/* Backpack */}
              <rect x="235" y="260" width="18" height="25" rx="3" fill={isDark ? "#7C2D12" : "#92400E"} />
              <rect x="237" y="262" width="14" height="20" rx="2" fill={isDark ? "#92400E" : "#A16207"} />
              <circle cx="240" cy="267" r="2" fill={isDark ? "#A16207" : "#CA8A04"} />
              <rect x="241" y="275" width="3" height="8" fill={isDark ? "#78716C" : "#A8A29E"} />

              {/* Body */}
              <ellipse cx="250" cy="300" rx="15" ry="25" fill="url(#characterGradient)" />

              {/* Head */}
              <circle cx="250" cy="260" r="12" fill={isDark ? "#FEF3C7" : "#FEF3C7"} />

              {/* Hat */}
              <ellipse cx="250" cy="250" rx="14" ry="4" fill={isDark ? "#7C2D12" : "#92400E"} />
              <rect x="242" y="245" width="16" height="8" rx="8" fill={isDark ? "#7C2D12" : "#92400E"} />

              {/* Confused/Lost face */}
              <circle cx="246" cy="258" r="1.5" fill={isDark ? "#1F2937" : "#374151"} />
              <circle cx="254" cy="258" r="1.5" fill={isDark ? "#1F2937" : "#374151"} />
              <path d="M 246 265 Q 250 268 254 265" stroke={isDark ? "#1F2937" : "#374151"} strokeWidth="1.5" fill="none" />

              {/* Arms */}
              <ellipse cx="230" cy="285" rx="5" ry="12" fill="url(#characterGradient)" transform="rotate(-20 230 285)" />
              <ellipse cx="270" cy="285" rx="5" ry="12" fill="url(#characterGradient)" transform="rotate(20 270 285)" />

              {/* Hands */}
              <circle cx="225" cy="295" r="3" fill={isDark ? "#FEF3C7" : "#FEF3C7"} />
              <circle cx="275" cy="295" r="3" fill={isDark ? "#FEF3C7" : "#FEF3C7"} />

              {/* Map in hand */}
              <rect x="270" y="290" width="12" height="8" rx="1" fill={isDark ? "#FEF3C7" : "#FFFBEB"} transform="rotate(15 276 294)" />
              <line x1="272" y1="292" x2="280" y2="294" stroke={isDark ? "#EF4444" : "#F87171"} strokeWidth="0.5" transform="rotate(15 276 294)" />
              <line x1="272" y1="294" x2="278" y2="295" stroke={isDark ? "#3B82F6" : "#60A5FA"} strokeWidth="0.5" transform="rotate(15 276 294)" />
              <line x1="272" y1="296" x2="280" y2="298" stroke={isDark ? "#10B981" : "#34D399"} strokeWidth="0.5" transform="rotate(15 276 294)" />

              {/* Legs */}
              <ellipse cx="245" cy="330" rx="5" ry="15" fill="url(#characterGradient)" />
              <ellipse cx="255" cy="330" rx="5" ry="15" fill="url(#characterGradient)" />

              {/* Boots */}
              <ellipse cx="245" cy="342" rx="6" ry="3" fill={isDark ? "#1F2937" : "#374151"} />
              <ellipse cx="255" cy="342" rx="6" ry="3" fill={isDark ? "#1F2937" : "#374151"} />
            </g>

            {/* Floating Question Marks and Confusion Elements */}
            <g>
              <text x="200" y="220" fontSize="20" fontWeight="bold" fill={isDark ? "#F59E0B" : "#FBBF24"} className="animate-pulse" opacity="0.8">?</text>
              <text x="280" y="240" fontSize="16" fontWeight="bold" fill={isDark ? "#EF4444" : "#F87171"} className="animate-pulse" style={{ animationDelay: '0.5s' }} opacity="0.7">?</text>
              <text x="220" y="200" fontSize="14" fontWeight="bold" fill={isDark ? "#8B5CF6" : "#A78BFA"} className="animate-pulse" style={{ animationDelay: '1s' }} opacity="0.6">?</text>
              <text x="300" y="220" fontSize="12" fontWeight="bold" fill={isDark ? "#10B981" : "#34D399"} className="animate-pulse" style={{ animationDelay: '1.5s' }} opacity="0.5">?</text>
            </g>

            {/* Flying Birds */}
            <g>
              <g className="animate-bounce" style={{ animationDuration: '6s', animationDelay: '0s' }}>
                <path d="M 80 80 Q 85 75 90 80 Q 85 85 80 80" fill={isDark ? "#374151" : "#6B7280"} />
                <path d="M 85 80 Q 90 75 95 80 Q 90 85 85 80" fill={isDark ? "#374151" : "#6B7280"} />
              </g>
              <g className="animate-bounce" style={{ animationDuration: '5s', animationDelay: '2s' }}>
                <path d="M 400 70 Q 405 65 410 70 Q 405 75 400 70" fill={isDark ? "#374151" : "#6B7280"} />
                <path d="M 405 70 Q 410 65 415 70 Q 410 75 405 70" fill={isDark ? "#374151" : "#6B7280"} />
              </g>
              <g className="animate-bounce" style={{ animationDuration: '7s', animationDelay: '4s' }}>
                <path d="M 300 50 Q 305 45 310 50 Q 305 55 300 50" fill={isDark ? "#374151" : "#6B7280"} />
                <path d="M 305 50 Q 310 45 315 50 Q 310 55 305 50" fill={isDark ? "#374151" : "#6B7280"} />
              </g>
            </g>

            {/* Magical Fireflies/Lights */}
            <g>
              <circle cx="150" cy="180" r="2" fill={isDark ? "#FBBF24" : "#FDE047"} className="animate-ping" opacity="0.8" />
              <circle cx="350" cy="200" r="1.5" fill={isDark ? "#34D399" : "#6EE7B7"} className="animate-ping" style={{ animationDelay: '1s' }} opacity="0.7" />
              <circle cx="100" cy="220" r="1" fill={isDark ? "#F472B6" : "#FBBF24"} className="animate-ping" style={{ animationDelay: '2s' }} opacity="0.6" />
              <circle cx="400" cy="160" r="2" fill={isDark ? "#60A5FA" : "#93C5FD"} className="animate-ping" style={{ animationDelay: '0.5s' }} opacity="0.8" />
              <circle cx="180" cy="150" r="1.5" fill={isDark ? "#A78BFA" : "#C084FC"} className="animate-ping" style={{ animationDelay: '3s' }} opacity="0.7" />
              <circle cx="320" cy="170" r="1" fill={isDark ? "#FB7185" : "#FDA4AF"} className="animate-ping" style={{ animationDelay: '1.5s' }} opacity="0.6" />
            </g>

            {/* Compass Rose (broken/spinning) */}
            <g className="animate-spin" style={{ animationDuration: '10s', transformOrigin: '450px 300px' }}>
              <circle cx="450" cy="300" r="15" fill={isDark ? "#92400E" : "#A16207"} opacity="0.8" />
              <polygon points="450,290 455,300 450,310 445,300" fill={isDark ? "#EF4444" : "#F87171"} />
              <polygon points="440,300 450,295 460,300 450,305" fill={isDark ? "#1F2937" : "#374151"} />
              <circle cx="450" cy="300" r="3" fill={isDark ? "#FBBF24" : "#FDE047"} />
              <text x="450" y="280" fontSize="8" fontWeight="bold" textAnchor="middle" fill={isDark ? "#F87171" : "#EF4444"}>N</text>
            </g>

            {/* Floating confusion elements */}
            <g>
              <circle cx="120" cy="100" r="2" fill={isDark ? "#A78BFA" : "#C084FC"} className="animate-ping" />
              <circle cx="280" cy="110" r="1.5" fill={isDark ? "#34D399" : "#6EE7B7"} className="animate-ping" style={{ animationDelay: '1s' }} />
              <circle cx="150" cy="80" r="1" fill={isDark ? "#F472B6" : "#FBBF24"} className="animate-ping" style={{ animationDelay: '2s' }} />
              <circle cx="250" cy="90" r="2" fill={isDark ? "#60A5FA" : "#93C5FD"} className="animate-ping" style={{ animationDelay: '0.5s' }} />
            </g>

            {/* Swirling confusion lines */}
            <g className="animate-spin" style={{ animationDuration: '8s', transformOrigin: '200px 180px' }}>
              <path d="M 180 160 Q 200 140 220 160 Q 200 180 180 160" stroke={isDark ? "#8B5CF6" : "#A78BFA"} strokeWidth="2" fill="none" opacity="0.6" />
              <path d="M 185 165 Q 200 150 215 165 Q 200 175 185 165" stroke={isDark ? "#F59E0B" : "#FBBF24"} strokeWidth="1.5" fill="none" opacity="0.4" />
            </g>
          </svg>
        </div>

        {/* Main Content */}
        <div className={`space-y-8 transition-all duration-1000 delay-500 ${isAnimating ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'}`}>
          {/* Main Heading */}
          <div className="space-y-4">
            <h1 className={`text-4xl md:text-6xl font-bold bg-gradient-to-r bg-clip-text text-transparent ${
              isDark
                ? 'from-emerald-400 via-blue-400 to-purple-400'
                : 'from-green-400 via-blue-400 to-purple-400'
            }`}>
              Lost in the Wilderness
            </h1>
            <p className={`text-xl md:text-2xl font-medium transition-colors duration-500 ${
              isDark ? 'text-gray-300' : 'text-white'
            }`}>
              The path seems to have vanished... 🗺️✨
            </p>
            <p className={`text-lg max-w-2xl mx-auto leading-relaxed transition-colors duration-500 ${
              isDark ? 'text-gray-400' : 'text-blue-200'
            }`}>
              Every great adventure has moments of uncertainty. Our lost traveler is checking the map,
              but don&apos;t worry - we&apos;ll help you find your way back to familiar territory!
            </p>
          </div>

          {/* Fun Facts */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-3xl mx-auto">
            <div className={`p-6 rounded-2xl backdrop-blur-sm border transition-all duration-300 hover:scale-105 ${
              isDark
                ? 'bg-gray-800/40 border-gray-700/50 hover:bg-gray-800/60'
                : 'bg-white/10 border-white/20 hover:bg-white/15'
            }`}>
              <div className="flex items-center justify-center mb-4">
                <div className={`p-3 rounded-full ${
                  isDark ? 'bg-blue-500/20' : 'bg-blue-500/20'
                }`}>
                  <Rocket className={`w-6 h-6 ${isDark ? 'text-blue-400' : 'text-blue-300'}`} />
                </div>
              </div>
              <h3 className={`font-bold text-lg mb-2 ${isDark ? 'text-white' : 'text-white'}`}>
                500+ Adventurers
              </h3>
              <p className={`text-sm ${isDark ? 'text-gray-400' : 'text-blue-200'}`}>
                Found their way through our wilderness
              </p>
            </div>

            <div className={`p-6 rounded-2xl backdrop-blur-sm border transition-all duration-300 hover:scale-105 ${
              isDark
                ? 'bg-gray-800/40 border-gray-700/50 hover:bg-gray-800/60'
                : 'bg-white/10 border-white/20 hover:bg-white/15'
            }`}>
              <div className="flex items-center justify-center mb-4">
                <div className={`p-3 rounded-full ${
                  isDark ? 'bg-purple-500/20' : 'bg-purple-500/20'
                }`}>
                  <Star className={`w-6 h-6 ${isDark ? 'text-purple-400' : 'text-purple-300'}`} />
                </div>
              </div>
              <h3 className={`font-bold text-lg mb-2 ${isDark ? 'text-white' : 'text-white'}`}>
                95% Safe Return
              </h3>
              <p className={`text-sm ${isDark ? 'text-gray-400' : 'text-blue-200'}`}>
                Explorers make it back home safely
              </p>
            </div>

            <div className={`p-6 rounded-2xl backdrop-blur-sm border transition-all duration-300 hover:scale-105 ${
              isDark
                ? 'bg-gray-800/40 border-gray-700/50 hover:bg-gray-800/60'
                : 'bg-white/10 border-white/20 hover:bg-white/15'
            }`}>
              <div className="flex items-center justify-center mb-4">
                <div className={`p-3 rounded-full ${
                  isDark ? 'bg-green-500/20' : 'bg-green-500/20'
                }`}>
                  <Heart className={`w-6 h-6 ${isDark ? 'text-green-400' : 'text-green-300'}`} />
                </div>
              </div>
              <h3 className={`font-bold text-lg mb-2 ${isDark ? 'text-white' : 'text-white'}`}>
                Expert Guides
              </h3>
              <p className={`text-sm ${isDark ? 'text-gray-400' : 'text-blue-200'}`}>
                Always ready to help lost travelers
              </p>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row items-center justify-center gap-4 pt-8">
            <button
               onClick={()=>{
                navigation.back();
               }}

              className={`group flex items-center space-x-3 px-8 py-4 rounded-2xl font-bold text-lg transition-all duration-300 hover:scale-105 hover:shadow-2xl ${
                isDark
                  ? 'bg-gradient-to-r from-emerald-600 to-blue-600 hover:from-emerald-700 hover:to-blue-700 text-white'
                  : 'bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white'
              }`}
            >
              <Compass className="w-6 h-6 group-hover:scale-110 transition-transform duration-300" />
              <span>Return to Base Camp</span>
              <ArrowLeft className="w-5 h-5 group-hover:-translate-x-1 transition-transform duration-300" />
            </button>

            <button
              onClick={handleRefresh}
              className={`group flex items-center space-x-3 px-8 py-4 rounded-2xl font-bold text-lg border-2 transition-all duration-300 hover:scale-105 ${
                isDark
                  ? 'border-gray-600 text-gray-300 hover:border-blue-500 hover:text-blue-400 hover:bg-blue-500/10'
                  : 'border-white/30 text-white hover:border-white hover:bg-white/10'
              }`}
            >
              <RefreshCw className="w-6 h-6 group-hover:rotate-180 transition-transform duration-500" />
              <span>Try Again</span>
            </button>

            <Link
              href="/search"
              className={`group flex items-center space-x-3 px-8 py-4 rounded-2xl font-bold text-lg border-2 transition-all duration-300 hover:scale-105 ${
                isDark
                  ? 'border-gray-600 text-gray-300 hover:border-purple-500 hover:text-purple-400 hover:bg-purple-500/10'
                  : 'border-white/30 text-white hover:border-white hover:bg-white/10'
              }`}
            >
              <Search className="w-6 h-6 group-hover:scale-110 transition-transform duration-300" />
              <span>Search</span>
            </Link>
          </div>

          {/* Additional Help */}
          <div className={`pt-8 transition-colors duration-500 ${
            isDark ? 'text-gray-400' : 'text-blue-200'
          }`}>
            <p className="text-sm mb-4">
              <Coffee className="inline w-4 h-4 mr-2" />
              Lost? Our rescue team is always ready to guide you home 🏕️
            </p>
            <div className="flex items-center justify-center space-x-6 text-xs">
              <span className="flex items-center space-x-1">
                <MapPin className="w-3 h-3" />
                <span>Base Camp, Nepal</span>
              </span>
              <span className="flex items-center space-x-1">
                <Lightbulb className="w-3 h-3" />
                <span>Adventure Hub</span>
              </span>
              <span className="flex items-center space-x-1">
                <Compass className="w-3 h-3" />
                <span>Guiding Explorers</span>
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced CSS Animations */}
      <style jsx>{`
        @keyframes float-gentle {
          0%, 100% { transform: translate(0, 0) rotate(0deg); }
          25% { transform: translate(5px, -5px) rotate(45deg); }
          50% { transform: translate(-3px, -8px) rotate(90deg); }
          75% { transform: translate(-5px, 3px) rotate(135deg); }
        }
        @keyframes sparkle-rotate {
          0%, 100% { opacity: 0.4; transform: rotate(0deg) scale(1); }
          25% { opacity: 0.8; transform: rotate(90deg) scale(1.1); }
          50% { opacity: 1; transform: rotate(180deg) scale(1.2); }
          75% { opacity: 0.8; transform: rotate(270deg) scale(1.1); }
        }
        @keyframes glow-pulse {
          0%, 100% { filter: drop-shadow(0 0 5px rgba(59, 130, 246, 0.5)); }
          50% { filter: drop-shadow(0 0 20px rgba(59, 130, 246, 0.8)); }
        }
      `}</style>
    </div>
  )
}