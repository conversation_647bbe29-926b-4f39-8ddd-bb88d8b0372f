exports.id=171,exports.ids=[171],exports.modules={409:(e,t,a)=>{Promise.resolve().then(a.t.bind(a,86346,23)),Promise.resolve().then(a.t.bind(a,27924,23)),Promise.resolve().then(a.t.bind(a,35656,23)),Promise.resolve().then(a.t.bind(a,40099,23)),Promise.resolve().then(a.t.bind(a,38243,23)),Promise.resolve().then(a.t.bind(a,28827,23)),Promise.resolve().then(a.t.bind(a,62763,23)),Promise.resolve().then(a.t.bind(a,97173,23))},47609:(e,t,a)=>{Promise.resolve().then(a.t.bind(a,16444,23)),Promise.resolve().then(a.t.bind(a,16042,23)),Promise.resolve().then(a.t.bind(a,88170,23)),Promise.resolve().then(a.t.bind(a,49477,23)),Promise.resolve().then(a.t.bind(a,29345,23)),Promise.resolve().then(a.t.bind(a,12089,23)),Promise.resolve().then(a.t.bind(a,46577,23)),Promise.resolve().then(a.t.bind(a,31307,23))},48482:(e,t,a)=>{"use strict";a.d(t,{Toaster:()=>s});let s=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\fwu_incubation(react+laravel)\\incubation\\frontend\\src\\components\\ui\\sonner.tsx","Toaster")},53862:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>i,metadata:()=>n});var s=a(37413),r=a(18556),o=a.n(r);a(61135),a(61120);var l=a(48482);let d=function({children:e}){return(0,s.jsxs)("div",{children:[(0,s.jsx)(l.Toaster,{position:"top-right"}),e]})},n={title:"Fwu Incubation",description:"Fwu Incubation center , farwestern university ,kanchanpur ,mahendranagar , kata-18 ,nepal"};function i({children:e}){return(0,s.jsx)("html",{lang:"en",suppressHydrationWarning:!0,children:(0,s.jsx)("body",{className:o()?.className,suppressHydrationWarning:!0,children:(0,s.jsx)(d,{children:e})})})}},54413:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>s});let s=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\fwu_incubation(react+laravel)\\\\incubation\\\\frontend\\\\src\\\\app\\\\not-found.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\fwu_incubation(react+laravel)\\incubation\\frontend\\src\\app\\not-found.tsx","default")},54458:(e,t,a)=>{Promise.resolve().then(a.bind(a,54413))},57347:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>w});var s=a(60687),r=a(76180),o=a.n(r),l=a(43210),d=a(85814),n=a.n(d),i=a(10218),c=a(56085),x=a(54786),m=a(64398),p=a(67760),b=a(28559),f=a(78122),u=a(99270),g=a(13166),h=a(97992),j=a(15807),y=a(56748),v=a(16189);function w(){let[e,t]=(0,l.useState)(!1),[a,r]=(0,l.useState)(!1),{theme:d}=(0,i.D)(),w=(0,v.useRouter)();if(!e)return null;let N="dark"===d;return(0,s.jsxs)("div",{className:`jsx-db24365e3de6d177 min-h-screen flex items-center justify-center p-4 relative overflow-hidden transition-all duration-1000 ${N?"bg-gradient-to-br from-gray-900 via-slate-900 to-black":"bg-gradient-to-br from-indigo-900 via-purple-900 to-blue-900"}`,children:[(0,s.jsxs)("div",{className:"jsx-db24365e3de6d177 absolute inset-0 overflow-hidden",children:[(0,s.jsx)("div",{className:`jsx-db24365e3de6d177 absolute -top-40 -right-40 w-80 h-80 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse ${N?"bg-blue-600":"bg-purple-500"}`}),(0,s.jsx)("div",{style:{animationDelay:"2s"},className:`jsx-db24365e3de6d177 absolute -bottom-40 -left-40 w-80 h-80 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse ${N?"bg-purple-600":"bg-blue-500"}`}),(0,s.jsx)("div",{style:{animationDelay:"4s"},className:`jsx-db24365e3de6d177 absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 rounded-full mix-blend-multiply filter blur-xl opacity-15 animate-pulse ${N?"bg-indigo-600":"bg-indigo-500"}`}),[...Array(20)].map((e,t)=>(0,s.jsx)("div",{style:{top:`${100*Math.random()}%`,left:`${100*Math.random()}%`,animation:`float-gentle ${3+4*Math.random()}s ease-in-out infinite ${2*Math.random()}s`},className:"jsx-db24365e3de6d177 absolute w-2 h-2 rounded-full opacity-30 bg-white"},t)),[...Array(12)].map((e,t)=>(0,s.jsx)("div",{style:{top:`${20+60*Math.random()}%`,left:`${20+60*Math.random()}%`,animation:`sparkle-rotate ${2+3*Math.random()}s ease-in-out infinite ${2*Math.random()}s`},className:`jsx-db24365e3de6d177 absolute ${N?"text-blue-400/40":"text-yellow-400/60"}`,children:(0,s.jsx)(c.A,{size:8+8*Math.random()})},`sparkle-${t}`))]}),(0,s.jsxs)("div",{className:"jsx-db24365e3de6d177 relative z-10 text-center max-w-4xl mx-auto",children:[(0,s.jsx)("div",{className:`jsx-db24365e3de6d177 mb-8 transition-all duration-1000 ${a?"scale-100 opacity-100":"scale-75 opacity-0"}`,children:(0,s.jsxs)("svg",{width:"400",height:"200",viewBox:"0 0 400 200",xmlns:"http://www.w3.org/2000/svg",className:"jsx-db24365e3de6d177 mx-auto",children:[(0,s.jsxs)("defs",{className:"jsx-db24365e3de6d177",children:[(0,s.jsxs)("linearGradient",{id:"gradient404",x1:"0%",y1:"0%",x2:"100%",y2:"100%",className:"jsx-db24365e3de6d177",children:[(0,s.jsx)("stop",{offset:"0%",stopColor:N?"#3B82F6":"#8B5CF6",className:"jsx-db24365e3de6d177"}),(0,s.jsx)("stop",{offset:"50%",stopColor:N?"#8B5CF6":"#EC4899",className:"jsx-db24365e3de6d177"}),(0,s.jsx)("stop",{offset:"100%",stopColor:N?"#EC4899":"#F59E0B",className:"jsx-db24365e3de6d177"})]}),(0,s.jsxs)("filter",{id:"glow",className:"jsx-db24365e3de6d177",children:[(0,s.jsx)("feGaussianBlur",{stdDeviation:"3",result:"coloredBlur",className:"jsx-db24365e3de6d177"}),(0,s.jsxs)("feMerge",{className:"jsx-db24365e3de6d177",children:[(0,s.jsx)("feMergeNode",{in:"coloredBlur",className:"jsx-db24365e3de6d177"}),(0,s.jsx)("feMergeNode",{in:"SourceGraphic",className:"jsx-db24365e3de6d177"})]})]})]}),(0,s.jsx)("text",{x:"200",y:"120",fontSize:"120",fontWeight:"bold",textAnchor:"middle",fill:"url(#gradient404)",filter:"url(#glow)",className:"jsx-db24365e3de6d177 animate-pulse",children:"404"}),(0,s.jsx)("circle",{cx:"80",cy:"60",r:"8",fill:N?"#60A5FA":"#A78BFA",style:{animationDelay:"0.5s"},className:"jsx-db24365e3de6d177 animate-bounce"}),(0,s.jsx)("circle",{cx:"320",cy:"60",r:"6",fill:N?"#F472B6":"#FBBF24",style:{animationDelay:"1s"},className:"jsx-db24365e3de6d177 animate-bounce"}),(0,s.jsx)("circle",{cx:"60",cy:"140",r:"4",fill:N?"#34D399":"#10B981",style:{animationDelay:"1.5s"},className:"jsx-db24365e3de6d177 animate-bounce"}),(0,s.jsx)("circle",{cx:"340",cy:"140",r:"5",fill:N?"#FBBF24":"#F59E0B",style:{animationDelay:"2s"},className:"jsx-db24365e3de6d177 animate-bounce"})]})}),(0,s.jsx)("div",{className:`jsx-db24365e3de6d177 mb-12 transition-all duration-1000 delay-300 ${a?"translate-y-0 opacity-100":"translate-y-10 opacity-0"}`,children:(0,s.jsxs)("svg",{width:"300",height:"200",viewBox:"0 0 300 200",xmlns:"http://www.w3.org/2000/svg",className:"jsx-db24365e3de6d177 mx-auto",children:[(0,s.jsxs)("defs",{className:"jsx-db24365e3de6d177",children:[(0,s.jsxs)("linearGradient",{id:"astronautGradient",x1:"0%",y1:"0%",x2:"100%",y2:"100%",className:"jsx-db24365e3de6d177",children:[(0,s.jsx)("stop",{offset:"0%",stopColor:N?"#60A5FA":"#8B5CF6",className:"jsx-db24365e3de6d177"}),(0,s.jsx)("stop",{offset:"100%",stopColor:N?"#A78BFA":"#EC4899",className:"jsx-db24365e3de6d177"})]}),(0,s.jsxs)("linearGradient",{id:"planetGradient",x1:"0%",y1:"0%",x2:"100%",y2:"100%",className:"jsx-db24365e3de6d177",children:[(0,s.jsx)("stop",{offset:"0%",stopColor:N?"#F59E0B":"#FBBF24",className:"jsx-db24365e3de6d177"}),(0,s.jsx)("stop",{offset:"100%",stopColor:N?"#EF4444":"#F97316",className:"jsx-db24365e3de6d177"})]})]}),(0,s.jsx)("circle",{cx:"50",cy:"30",r:"2",fill:N?"#FBBF24":"#FDE047",className:"jsx-db24365e3de6d177 animate-pulse"}),(0,s.jsx)("circle",{cx:"250",cy:"40",r:"1.5",fill:N?"#60A5FA":"#93C5FD",style:{animationDelay:"0.5s"},className:"jsx-db24365e3de6d177 animate-pulse"}),(0,s.jsx)("circle",{cx:"80",cy:"180",r:"1",fill:N?"#F472B6":"#FBBF24",style:{animationDelay:"1s"},className:"jsx-db24365e3de6d177 animate-pulse"}),(0,s.jsx)("circle",{cx:"220",cy:"170",r:"2",fill:N?"#34D399":"#6EE7B7",style:{animationDelay:"1.5s"},className:"jsx-db24365e3de6d177 animate-pulse"}),(0,s.jsx)("circle",{cx:"240",cy:"60",r:"25",fill:"url(#planetGradient)",style:{animationDuration:"20s"},className:"jsx-db24365e3de6d177 animate-spin"}),(0,s.jsx)("circle",{cx:"235",cy:"55",r:"3",fill:N?"#1F2937":"#374151",opacity:"0.3",className:"jsx-db24365e3de6d177"}),(0,s.jsx)("circle",{cx:"245",cy:"65",r:"2",fill:N?"#1F2937":"#374151",opacity:"0.3",className:"jsx-db24365e3de6d177"}),(0,s.jsxs)("g",{style:{animationDuration:"3s"},className:"jsx-db24365e3de6d177 animate-bounce",children:[(0,s.jsx)("ellipse",{cx:"150",cy:"120",rx:"25",ry:"35",fill:"url(#astronautGradient)",className:"jsx-db24365e3de6d177"}),(0,s.jsx)("circle",{cx:"150",cy:"80",r:"20",fill:N?"#E5E7EB":"#F3F4F6",opacity:"0.9",className:"jsx-db24365e3de6d177"}),(0,s.jsx)("circle",{cx:"150",cy:"80",r:"18",fill:"none",stroke:N?"#9CA3AF":"#D1D5DB",strokeWidth:"2",className:"jsx-db24365e3de6d177"}),(0,s.jsx)("circle",{cx:"145",cy:"78",r:"2",fill:N?"#1F2937":"#374151",className:"jsx-db24365e3de6d177"}),(0,s.jsx)("circle",{cx:"155",cy:"78",r:"2",fill:N?"#1F2937":"#374151",className:"jsx-db24365e3de6d177"}),(0,s.jsx)("path",{d:"M 145 85 Q 150 88 155 85",stroke:N?"#1F2937":"#374151",strokeWidth:"1.5",fill:"none",className:"jsx-db24365e3de6d177"}),(0,s.jsx)("ellipse",{cx:"125",cy:"110",rx:"8",ry:"20",fill:"url(#astronautGradient)",transform:"rotate(-20 125 110)",className:"jsx-db24365e3de6d177"}),(0,s.jsx)("ellipse",{cx:"175",cy:"110",rx:"8",ry:"20",fill:"url(#astronautGradient)",transform:"rotate(20 175 110)",className:"jsx-db24365e3de6d177"}),(0,s.jsx)("ellipse",{cx:"140",cy:"160",rx:"8",ry:"25",fill:"url(#astronautGradient)",className:"jsx-db24365e3de6d177"}),(0,s.jsx)("ellipse",{cx:"160",cy:"160",rx:"8",ry:"25",fill:"url(#astronautGradient)",className:"jsx-db24365e3de6d177"}),(0,s.jsx)("rect",{x:"135",y:"100",width:"30",height:"40",rx:"5",fill:N?"#6B7280":"#9CA3AF",className:"jsx-db24365e3de6d177"}),(0,s.jsx)("circle",{cx:"140",cy:"145",r:"3",fill:N?"#EF4444":"#F87171",className:"jsx-db24365e3de6d177 animate-pulse"}),(0,s.jsx)("circle",{cx:"160",cy:"145",r:"3",fill:N?"#3B82F6":"#60A5FA",style:{animationDelay:"0.5s"},className:"jsx-db24365e3de6d177 animate-pulse"})]}),(0,s.jsxs)("g",{className:"jsx-db24365e3de6d177 animate-pulse",children:[(0,s.jsx)("circle",{cx:"100",cy:"50",r:"1",fill:N?"#A78BFA":"#C084FC",className:"jsx-db24365e3de6d177"}),(0,s.jsx)("circle",{cx:"200",cy:"150",r:"1.5",fill:N?"#34D399":"#6EE7B7",className:"jsx-db24365e3de6d177"})]})]})}),(0,s.jsxs)("div",{className:`jsx-db24365e3de6d177 space-y-8 transition-all duration-1000 delay-500 ${a?"translate-y-0 opacity-100":"translate-y-10 opacity-0"}`,children:[(0,s.jsxs)("div",{className:"jsx-db24365e3de6d177 space-y-4",children:[(0,s.jsx)("h1",{className:`jsx-db24365e3de6d177 text-4xl md:text-6xl font-bold bg-gradient-to-r bg-clip-text text-transparent ${N?"from-blue-400 via-purple-400 to-pink-400":"from-purple-400 via-pink-400 to-red-400"}`,children:"Oops! Lost in Space"}),(0,s.jsx)("p",{className:`jsx-db24365e3de6d177 text-xl md:text-2xl font-medium transition-colors duration-500 ${N?"text-gray-300":"text-white"}`,children:"The page you're looking for has drifted away"}),(0,s.jsx)("p",{className:`jsx-db24365e3de6d177 text-lg max-w-2xl mx-auto leading-relaxed transition-colors duration-500 ${N?"text-gray-400":"text-blue-200"}`,children:"Don't worry, our astronaut is here to help you navigate back to safety. Let's get you back to exploring amazing opportunities!"})]}),(0,s.jsxs)("div",{className:"jsx-db24365e3de6d177 grid grid-cols-1 md:grid-cols-3 gap-6 max-w-3xl mx-auto",children:[(0,s.jsxs)("div",{className:`jsx-db24365e3de6d177 p-6 rounded-2xl backdrop-blur-sm border transition-all duration-300 hover:scale-105 ${N?"bg-gray-800/40 border-gray-700/50 hover:bg-gray-800/60":"bg-white/10 border-white/20 hover:bg-white/15"}`,children:[(0,s.jsx)("div",{className:"jsx-db24365e3de6d177 flex items-center justify-center mb-4",children:(0,s.jsx)("div",{className:"jsx-db24365e3de6d177 p-3 rounded-full bg-blue-500/20",children:(0,s.jsx)(x.A,{className:`w-6 h-6 ${N?"text-blue-400":"text-blue-300"}`})})}),(0,s.jsx)("h3",{className:"jsx-db24365e3de6d177 font-bold text-lg mb-2 text-white",children:"500+ Startups"}),(0,s.jsx)("p",{className:`jsx-db24365e3de6d177 text-sm ${N?"text-gray-400":"text-blue-200"}`,children:"Successfully launched from our incubator"})]}),(0,s.jsxs)("div",{className:`jsx-db24365e3de6d177 p-6 rounded-2xl backdrop-blur-sm border transition-all duration-300 hover:scale-105 ${N?"bg-gray-800/40 border-gray-700/50 hover:bg-gray-800/60":"bg-white/10 border-white/20 hover:bg-white/15"}`,children:[(0,s.jsx)("div",{className:"jsx-db24365e3de6d177 flex items-center justify-center mb-4",children:(0,s.jsx)("div",{className:"jsx-db24365e3de6d177 p-3 rounded-full bg-purple-500/20",children:(0,s.jsx)(m.A,{className:`w-6 h-6 ${N?"text-purple-400":"text-purple-300"}`})})}),(0,s.jsx)("h3",{className:"jsx-db24365e3de6d177 font-bold text-lg mb-2 text-white",children:"95% Success Rate"}),(0,s.jsx)("p",{className:`jsx-db24365e3de6d177 text-sm ${N?"text-gray-400":"text-blue-200"}`,children:"Of our incubated startups thrive"})]}),(0,s.jsxs)("div",{className:`jsx-db24365e3de6d177 p-6 rounded-2xl backdrop-blur-sm border transition-all duration-300 hover:scale-105 ${N?"bg-gray-800/40 border-gray-700/50 hover:bg-gray-800/60":"bg-white/10 border-white/20 hover:bg-white/15"}`,children:[(0,s.jsx)("div",{className:"jsx-db24365e3de6d177 flex items-center justify-center mb-4",children:(0,s.jsx)("div",{className:"jsx-db24365e3de6d177 p-3 rounded-full bg-green-500/20",children:(0,s.jsx)(p.A,{className:`w-6 h-6 ${N?"text-green-400":"text-green-300"}`})})}),(0,s.jsx)("h3",{className:"jsx-db24365e3de6d177 font-bold text-lg mb-2 text-white",children:"Community Driven"}),(0,s.jsx)("p",{className:`jsx-db24365e3de6d177 text-sm ${N?"text-gray-400":"text-blue-200"}`,children:"Built with love for entrepreneurs"})]})]}),(0,s.jsxs)("div",{className:"jsx-db24365e3de6d177 flex flex-col sm:flex-row items-center justify-center gap-4 pt-8",children:[(0,s.jsxs)("button",{onClick:()=>{w.back()},className:`jsx-db24365e3de6d177 group flex items-center space-x-3 px-8 py-4 rounded-2xl font-bold text-lg transition-all duration-300 hover:scale-105 hover:shadow-2xl ${N?"bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white":"bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white"}`,children:[(0,s.jsx)("span",{className:"jsx-db24365e3de6d177",children:"Back to Previous Page "}),(0,s.jsx)(b.A,{className:"w-5 h-5 group-hover:-translate-x-1 transition-transform duration-300"})]}),(0,s.jsxs)("button",{onClick:()=>{window.location.reload()},className:`jsx-db24365e3de6d177 group flex items-center space-x-3 px-8 py-4 rounded-2xl font-bold text-lg border-2 transition-all duration-300 hover:scale-105 ${N?"border-gray-600 text-gray-300 hover:border-blue-500 hover:text-blue-400 hover:bg-blue-500/10":"border-white/30 text-white hover:border-white hover:bg-white/10"}`,children:[(0,s.jsx)(f.A,{className:"w-6 h-6 group-hover:rotate-180 transition-transform duration-500"}),(0,s.jsx)("span",{className:"jsx-db24365e3de6d177",children:"Try Again"})]}),(0,s.jsxs)(n(),{href:"/search",className:`group flex items-center space-x-3 px-8 py-4 rounded-2xl font-bold text-lg border-2 transition-all duration-300 hover:scale-105 ${N?"border-gray-600 text-gray-300 hover:border-purple-500 hover:text-purple-400 hover:bg-purple-500/10":"border-white/30 text-white hover:border-white hover:bg-white/10"}`,children:[(0,s.jsx)(u.A,{className:"w-6 h-6 group-hover:scale-110 transition-transform duration-300"}),(0,s.jsx)("span",{className:"jsx-db24365e3de6d177",children:"Search"})]})]}),(0,s.jsxs)("div",{className:`jsx-db24365e3de6d177 pt-8 transition-colors duration-500 ${N?"text-gray-400":"text-blue-200"}`,children:[(0,s.jsxs)("p",{className:"jsx-db24365e3de6d177 text-sm mb-4",children:[(0,s.jsx)(g.A,{className:"inline w-4 h-4 mr-2"}),"Need help? Our team is here to assist you 24/7"]}),(0,s.jsxs)("div",{className:"jsx-db24365e3de6d177 flex items-center justify-center space-x-6 text-xs",children:[(0,s.jsxs)("span",{className:"jsx-db24365e3de6d177 flex items-center space-x-1",children:[(0,s.jsx)(h.A,{className:"w-3 h-3"}),(0,s.jsx)("span",{className:"jsx-db24365e3de6d177",children:"Kanchanpur, Nepal"})]}),(0,s.jsxs)("span",{className:"jsx-db24365e3de6d177 flex items-center space-x-1",children:[(0,s.jsx)(j.A,{className:"w-3 h-3"}),(0,s.jsx)("span",{className:"jsx-db24365e3de6d177",children:"Innovation Hub"})]}),(0,s.jsxs)("span",{className:"jsx-db24365e3de6d177 flex items-center space-x-1",children:[(0,s.jsx)(y.A,{className:"w-3 h-3"}),(0,s.jsx)("span",{className:"jsx-db24365e3de6d177",children:"Guiding Startups"})]})]})]})]})]}),(0,s.jsx)(o(),{id:"db24365e3de6d177",children:"@-webkit-keyframes float-gentle{0%,100%{-webkit-transform:translate(0,0)rotate(0deg);transform:translate(0,0)rotate(0deg)}25%{-webkit-transform:translate(5px,-5px)rotate(45deg);transform:translate(5px,-5px)rotate(45deg)}50%{-webkit-transform:translate(-3px,-8px)rotate(90deg);transform:translate(-3px,-8px)rotate(90deg)}75%{-webkit-transform:translate(-5px,3px)rotate(135deg);transform:translate(-5px,3px)rotate(135deg)}}@-moz-keyframes float-gentle{0%,100%{-moz-transform:translate(0,0)rotate(0deg);transform:translate(0,0)rotate(0deg)}25%{-moz-transform:translate(5px,-5px)rotate(45deg);transform:translate(5px,-5px)rotate(45deg)}50%{-moz-transform:translate(-3px,-8px)rotate(90deg);transform:translate(-3px,-8px)rotate(90deg)}75%{-moz-transform:translate(-5px,3px)rotate(135deg);transform:translate(-5px,3px)rotate(135deg)}}@-o-keyframes float-gentle{0%,100%{-o-transform:translate(0,0)rotate(0deg);transform:translate(0,0)rotate(0deg)}25%{-o-transform:translate(5px,-5px)rotate(45deg);transform:translate(5px,-5px)rotate(45deg)}50%{-o-transform:translate(-3px,-8px)rotate(90deg);transform:translate(-3px,-8px)rotate(90deg)}75%{-o-transform:translate(-5px,3px)rotate(135deg);transform:translate(-5px,3px)rotate(135deg)}}@keyframes float-gentle{0%,100%{-webkit-transform:translate(0,0)rotate(0deg);-moz-transform:translate(0,0)rotate(0deg);-o-transform:translate(0,0)rotate(0deg);transform:translate(0,0)rotate(0deg)}25%{-webkit-transform:translate(5px,-5px)rotate(45deg);-moz-transform:translate(5px,-5px)rotate(45deg);-o-transform:translate(5px,-5px)rotate(45deg);transform:translate(5px,-5px)rotate(45deg)}50%{-webkit-transform:translate(-3px,-8px)rotate(90deg);-moz-transform:translate(-3px,-8px)rotate(90deg);-o-transform:translate(-3px,-8px)rotate(90deg);transform:translate(-3px,-8px)rotate(90deg)}75%{-webkit-transform:translate(-5px,3px)rotate(135deg);-moz-transform:translate(-5px,3px)rotate(135deg);-o-transform:translate(-5px,3px)rotate(135deg);transform:translate(-5px,3px)rotate(135deg)}}@-webkit-keyframes sparkle-rotate{0%,100%{opacity:.4;-webkit-transform:rotate(0deg)scale(1);transform:rotate(0deg)scale(1)}25%{opacity:.8;-webkit-transform:rotate(90deg)scale(1.1);transform:rotate(90deg)scale(1.1)}50%{opacity:1;-webkit-transform:rotate(180deg)scale(1.2);transform:rotate(180deg)scale(1.2)}75%{opacity:.8;-webkit-transform:rotate(270deg)scale(1.1);transform:rotate(270deg)scale(1.1)}}@-moz-keyframes sparkle-rotate{0%,100%{opacity:.4;-moz-transform:rotate(0deg)scale(1);transform:rotate(0deg)scale(1)}25%{opacity:.8;-moz-transform:rotate(90deg)scale(1.1);transform:rotate(90deg)scale(1.1)}50%{opacity:1;-moz-transform:rotate(180deg)scale(1.2);transform:rotate(180deg)scale(1.2)}75%{opacity:.8;-moz-transform:rotate(270deg)scale(1.1);transform:rotate(270deg)scale(1.1)}}@-o-keyframes sparkle-rotate{0%,100%{opacity:.4;-o-transform:rotate(0deg)scale(1);transform:rotate(0deg)scale(1)}25%{opacity:.8;-o-transform:rotate(90deg)scale(1.1);transform:rotate(90deg)scale(1.1)}50%{opacity:1;-o-transform:rotate(180deg)scale(1.2);transform:rotate(180deg)scale(1.2)}75%{opacity:.8;-o-transform:rotate(270deg)scale(1.1);transform:rotate(270deg)scale(1.1)}}@keyframes sparkle-rotate{0%,100%{opacity:.4;-webkit-transform:rotate(0deg)scale(1);-moz-transform:rotate(0deg)scale(1);-o-transform:rotate(0deg)scale(1);transform:rotate(0deg)scale(1)}25%{opacity:.8;-webkit-transform:rotate(90deg)scale(1.1);-moz-transform:rotate(90deg)scale(1.1);-o-transform:rotate(90deg)scale(1.1);transform:rotate(90deg)scale(1.1)}50%{opacity:1;-webkit-transform:rotate(180deg)scale(1.2);-moz-transform:rotate(180deg)scale(1.2);-o-transform:rotate(180deg)scale(1.2);transform:rotate(180deg)scale(1.2)}75%{opacity:.8;-webkit-transform:rotate(270deg)scale(1.1);-moz-transform:rotate(270deg)scale(1.1);-o-transform:rotate(270deg)scale(1.1);transform:rotate(270deg)scale(1.1)}}@-webkit-keyframes glow-pulse{0%,100%{-webkit-filter:drop-shadow(0 0 5px rgba(59,130,246,.5));filter:drop-shadow(0 0 5px rgba(59,130,246,.5))}50%{-webkit-filter:drop-shadow(0 0 20px rgba(59,130,246,.8));filter:drop-shadow(0 0 20px rgba(59,130,246,.8))}}@-moz-keyframes glow-pulse{0%,100%{filter:drop-shadow(0 0 5px rgba(59,130,246,.5))}50%{filter:drop-shadow(0 0 20px rgba(59,130,246,.8))}}@-o-keyframes glow-pulse{0%,100%{filter:drop-shadow(0 0 5px rgba(59,130,246,.5))}50%{filter:drop-shadow(0 0 20px rgba(59,130,246,.8))}}@keyframes glow-pulse{0%,100%{-webkit-filter:drop-shadow(0 0 5px rgba(59,130,246,.5));filter:drop-shadow(0 0 5px rgba(59,130,246,.5))}50%{-webkit-filter:drop-shadow(0 0 20px rgba(59,130,246,.8));filter:drop-shadow(0 0 20px rgba(59,130,246,.8))}}"})]})}},61135:()=>{},64616:(e,t,a)=>{"use strict";a.d(t,{Toaster:()=>l});var s=a(60687),r=a(10218),o=a(52581);let l=({...e})=>{let{theme:t="system"}=(0,r.D)();return(0,s.jsx)(o.l$,{theme:t,className:"toaster group",style:{"--normal-bg":"var(--popover)","--normal-text":"var(--popover-foreground)","--normal-border":"var(--border)"},...e})}},70440:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>r});var s=a(31658);let r=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},89713:(e,t,a)=>{Promise.resolve().then(a.bind(a,64616))},94626:(e,t,a)=>{Promise.resolve().then(a.bind(a,57347))},99441:(e,t,a)=>{Promise.resolve().then(a.bind(a,48482))}};