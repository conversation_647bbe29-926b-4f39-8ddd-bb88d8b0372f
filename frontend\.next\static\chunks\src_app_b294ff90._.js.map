{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/fwu_incubation%28react%2Blaravel%29/incubation/frontend/src/app/components/shared/SectionTitle.tsx"], "sourcesContent": ["// components/shared/SectionTitle.tsx\r\ninterface SectionTitleProps {\r\n  title: string;\r\n  subtitle?: string;\r\n  align?: 'left' | 'center' | 'right';\r\n  accentColor?: string;\r\n}\r\n\r\nconst SectionTitle: React.FC<SectionTitleProps> = ({\r\n  title,\r\n  subtitle,\r\n  align = 'center',\r\n  accentColor = 'indigo'\r\n}) => {\r\n  const alignmentClass = {\r\n    left: 'text-left',\r\n    center: 'text-center',\r\n    right: 'text-right',\r\n  };\r\n\r\n  const accentColorClass = {\r\n    indigo: 'bg-indigo-600',\r\n    blue: 'bg-blue-600',\r\n    teal: 'bg-teal-600',\r\n    purple: 'bg-purple-600',\r\n    green: 'bg-green-600',\r\n  };\r\n\r\n  const textColorClass = {\r\n    indigo: 'text-indigo-600',\r\n    blue: 'text-blue-600',\r\n    teal: 'text-teal-600',\r\n    purple: 'text-purple-600',\r\n    green: 'text-green-600',\r\n  };\r\n\r\n  return (\r\n    <div className={`mb-12 md:mb-16 ${alignmentClass[align]}`}>\r\n      {subtitle && (\r\n        <p className={`${textColorClass[accentColor as keyof typeof textColorClass]} font-semibold text-sm md:text-base uppercase tracking-wider mb-2`}>\r\n          {subtitle}\r\n        </p>\r\n      )}\r\n      <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900\">\r\n        {title}\r\n      </h2>\r\n      <div className={`mt-4 h-1.5 w-24 ${align === 'center' ? 'mx-auto' : (align === 'right' ? 'ml-auto' : '')} ${accentColorClass[accentColor as keyof typeof accentColorClass]} rounded-full`}></div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default SectionTitle;"], "names": [], "mappings": "AAAA,qCAAqC;;;;;;AAQrC,MAAM,eAA4C,CAAC,EACjD,KAAK,EACL,QAAQ,EACR,QAAQ,QAAQ,EAChB,cAAc,QAAQ,EACvB;IACC,MAAM,iBAAiB;QACrB,MAAM;QACN,QAAQ;QACR,OAAO;IACT;IAEA,MAAM,mBAAmB;QACvB,QAAQ;QACR,MAAM;QACN,MAAM;QACN,QAAQ;QACR,OAAO;IACT;IAEA,MAAM,iBAAiB;QACrB,QAAQ;QACR,MAAM;QACN,MAAM;QACN,QAAQ;QACR,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAC,eAAe,EAAE,cAAc,CAAC,MAAM,EAAE;;YACtD,0BACC,6LAAC;gBAAE,WAAW,GAAG,cAAc,CAAC,YAA2C,CAAC,iEAAiE,CAAC;0BAC3I;;;;;;0BAGL,6LAAC;gBAAG,WAAU;0BACX;;;;;;0BAEH,6LAAC;gBAAI,WAAW,CAAC,gBAAgB,EAAE,UAAU,WAAW,YAAa,UAAU,UAAU,YAAY,GAAI,CAAC,EAAE,gBAAgB,CAAC,YAA6C,CAAC,aAAa,CAAC;;;;;;;;;;;;AAG/L;KAzCM;uCA2CS", "debugId": null}}, {"offset": {"line": 79, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/fwu_incubation%28react%2Blaravel%29/incubation/frontend/src/app/components/about/TeamMemberCard.tsx"], "sourcesContent": ["// components/about/TeamMemberCard.tsx\r\nimport Image from 'next/image';\r\nimport { Linkedin, Twitter, Mail, Quote } from 'lucide-react';\r\n\r\nexport interface Member {\r\n  id: string;\r\n  name: string;\r\n  role: string;\r\n  imageUrl: string;\r\n  bio?: string; // Optional short bio\r\n  linkedin?: string;\r\n  twitter?: string;\r\n  email?: string;\r\n}\r\n\r\ninterface TeamMemberCardProps {\r\n  member: Member;\r\n}\r\n\r\nconst TeamMemberCard: React.FC<TeamMemberCardProps> = ({ member }) => {\r\n  return (\r\n    <div className=\"bg-white rounded-xl shadow-lg p-6 text-center transform hover:scale-105 hover:shadow-2xl transition-all duration-500 flex flex-col items-center h-full border border-gray-100 hover:border-blue-200 relative group overflow-hidden\">\r\n      {/* Background pattern */}\r\n      <div className=\"absolute inset-0 opacity-0 group-hover:opacity-5 transition-opacity duration-500\" style={{\r\n        backgroundImage: 'radial-gradient(circle, #4a90e2 1px, transparent 1px)',\r\n        backgroundSize: '20px 20px'\r\n      }}></div>\r\n\r\n      {/* Decorative corner accent */}\r\n      <div className=\"absolute top-0 right-0 w-16 h-16 overflow-hidden\">\r\n        <div className=\"absolute transform rotate-45 bg-blue-600 w-16 h-3 -top-2 -right-8 opacity-70\"></div>\r\n      </div>\r\n\r\n      {/* Profile Image with Glow Effect */}\r\n      <div className=\"relative w-32 h-32 md:w-40 md:h-40 mb-6 rounded-full overflow-hidden border-4 border-white shadow-lg group-hover:shadow-blue-200 transition-all duration-500\">\r\n        <div className=\"absolute inset-0 bg-gradient-to-br from-blue-50 to-white\"></div>\r\n        <Image\r\n          src={member.imageUrl}\r\n          alt={member.name}\r\n          width={160}\r\n          height={160}\r\n          className=\"relative z-10 object-cover\"\r\n        />\r\n        <div className=\"absolute inset-0 bg-blue-400/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500\"></div>\r\n      </div>\r\n\r\n      {/* Name and Role */}\r\n      <h3 className=\"text-xl md:text-2xl font-bold text-blue-900 mb-1 group-hover:text-blue-700 transition-colors duration-300\">{member.name}</h3>\r\n      <p className=\"text-blue-600 font-medium mb-4 px-2\">{member.role}</p>\r\n\r\n      {/* Bio with Quote Icon */}\r\n      {member.bio && (\r\n        <div className=\"relative text-sm text-gray-600 mb-6 px-2 flex-grow\">\r\n          <Quote className=\"text-blue-100 absolute top-0 left-0 transform -translate-x-1/2 -translate-y-1/2 opacity-50\" />\r\n          <p className=\"relative z-10\">{member.bio}</p>\r\n        </div>\r\n      )}\r\n\r\n      {/* Social Links with Enhanced Styling */}\r\n      {(member.linkedin || member.twitter || member.email) && (\r\n        <div className=\"flex space-x-4 mt-auto pt-4 border-t border-gray-200 w-full justify-center\">\r\n          {member.linkedin && (\r\n            <a\r\n              href={member.linkedin}\r\n              target=\"_blank\"\r\n              rel=\"noopener noreferrer\"\r\n              className=\"text-gray-400 hover:text-blue-600 transition-colors duration-300 transform hover:scale-110\"\r\n              aria-label={`LinkedIn profile of ${member.name}`}\r\n            >\r\n              <Linkedin size={22} />\r\n            </a>\r\n          )}\r\n          {member.twitter && (\r\n            <a\r\n              href={member.twitter}\r\n              target=\"_blank\"\r\n              rel=\"noopener noreferrer\"\r\n              className=\"text-gray-400 hover:text-blue-400 transition-colors duration-300 transform hover:scale-110\"\r\n              aria-label={`Twitter profile of ${member.name}`}\r\n            >\r\n              <Twitter size={22} />\r\n            </a>\r\n          )}\r\n          {member.email && (\r\n            <a\r\n              href={`mailto:${member.email}`}\r\n              className=\"text-gray-400 hover:text-teal-500 transition-colors duration-300 transform hover:scale-110\"\r\n              aria-label={`Email ${member.name}`}\r\n            >\r\n              <Mail size={22} />\r\n            </a>\r\n          )}\r\n        </div>\r\n      )}\r\n\r\n      {/* Hover effect overlay */}\r\n      <div className=\"absolute inset-0 bg-gradient-to-t from-blue-50 to-transparent opacity-0 group-hover:opacity-10 transition-opacity duration-500\"></div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default TeamMemberCard;"], "names": [], "mappings": "AAAA,sCAAsC;;;;;AACtC;AACA;AAAA;AAAA;AAAA;;;;AAiBA,MAAM,iBAAgD,CAAC,EAAE,MAAM,EAAE;IAC/D,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;gBAAmF,OAAO;oBACvG,iBAAiB;oBACjB,gBAAgB;gBAClB;;;;;;0BAGA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;;;;;;;;;;0BAIjB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC,gIAAA,CAAA,UAAK;wBACJ,KAAK,OAAO,QAAQ;wBACpB,KAAK,OAAO,IAAI;wBAChB,OAAO;wBACP,QAAQ;wBACR,WAAU;;;;;;kCAEZ,6LAAC;wBAAI,WAAU;;;;;;;;;;;;0BAIjB,6LAAC;gBAAG,WAAU;0BAA6G,OAAO,IAAI;;;;;;0BACtI,6LAAC;gBAAE,WAAU;0BAAuC,OAAO,IAAI;;;;;;YAG9D,OAAO,GAAG,kBACT,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,uMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;kCACjB,6LAAC;wBAAE,WAAU;kCAAiB,OAAO,GAAG;;;;;;;;;;;;YAK3C,CAAC,OAAO,QAAQ,IAAI,OAAO,OAAO,IAAI,OAAO,KAAK,mBACjD,6LAAC;gBAAI,WAAU;;oBACZ,OAAO,QAAQ,kBACd,6LAAC;wBACC,MAAM,OAAO,QAAQ;wBACrB,QAAO;wBACP,KAAI;wBACJ,WAAU;wBACV,cAAY,CAAC,oBAAoB,EAAE,OAAO,IAAI,EAAE;kCAEhD,cAAA,6LAAC,6MAAA,CAAA,WAAQ;4BAAC,MAAM;;;;;;;;;;;oBAGnB,OAAO,OAAO,kBACb,6LAAC;wBACC,MAAM,OAAO,OAAO;wBACpB,QAAO;wBACP,KAAI;wBACJ,WAAU;wBACV,cAAY,CAAC,mBAAmB,EAAE,OAAO,IAAI,EAAE;kCAE/C,cAAA,6LAAC,2MAAA,CAAA,UAAO;4BAAC,MAAM;;;;;;;;;;;oBAGlB,OAAO,KAAK,kBACX,6LAAC;wBACC,MAAM,CAAC,OAAO,EAAE,OAAO,KAAK,EAAE;wBAC9B,WAAU;wBACV,cAAY,CAAC,MAAM,EAAE,OAAO,IAAI,EAAE;kCAElC,cAAA,6LAAC,qMAAA,CAAA,OAAI;4BAAC,MAAM;;;;;;;;;;;;;;;;;0BAOpB,6LAAC;gBAAI,WAAU;;;;;;;;;;;;AAGrB;KAhFM;uCAkFS", "debugId": null}}, {"offset": {"line": 283, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/fwu_incubation%28react%2Blaravel%29/incubation/frontend/src/app/components/about/AdvisoryBoardSection.tsx"], "sourcesContent": ["\"use client\"\r\nimport SectionTitle from '../shared/SectionTitle';\r\nimport TeamMemberCard, { Member } from './TeamMemberCard'; // Reusing the card\r\nimport { useEffect, useState, useRef } from 'react';\r\n\r\n// Advisory board members data\r\nconst advisoryBoardData: Member[] = [\r\n  {\r\n    id: 'ab1',\r\n    name: 'Dr. <PERSON><PERSON><PERSON>',\r\n    role: 'Chairman, University Grants Commission',\r\n    imageUrl: '/images/advisors/ugc-chairman.jpg',\r\n    bio: 'Providing strategic guidance on higher education policy and development.',\r\n    linkedin: 'https://linkedin.com/in/bishnuupreti'\r\n  },\r\n  {\r\n    id: 'ab2',\r\n    name: 'Prof. Dr. <PERSON><PERSON>',\r\n    role: 'Former Dean, Tribhuvan University',\r\n    imageUrl: '/images/advisors/tu-dean.jpg',\r\n    bio: 'Offering expertise in academic program development and quality assurance.',\r\n    linkedin: 'https://linkedin.com/in/bhimsubedi'\r\n  },\r\n  {\r\n    id: 'ab3',\r\n    name: 'Dr. <PERSON><PERSON>',\r\n    role: 'International Education Consultant',\r\n    imageUrl: '/images/advisors/education-consultant.jpg',\r\n    bio: 'Advising on international partnerships and global education standards.',\r\n    twitter: 'https://twitter.com/meenasharma'\r\n  },\r\n  {\r\n    id: 'ab4',\r\n    name: 'Mr. <PERSON>endra <PERSON>',\r\n    role: 'Industry Representative',\r\n    imageUrl: '/images/advisors/industry-rep.jpg',\r\n    bio: 'Bridging academia and industry to enhance employment opportunities for graduates.',\r\n    email: '<EMAIL>'\r\n  },\r\n  {\r\n    id: 'ab5',\r\n    name: 'Dr. Sushila Thapa',\r\n    role: 'Research & Innovation Expert',\r\n    imageUrl: '/images/advisors/research-expert.jpg',\r\n    bio: 'Guiding research initiatives and promoting innovation across disciplines.',\r\n    linkedin: 'https://linkedin.com/in/sushilathapa'\r\n  },\r\n  {\r\n    id: 'ab6',\r\n    name: 'Mr. Dipak Bhatta',\r\n    role: 'Community Representative',\r\n    imageUrl: '/images/advisors/community-rep.jpg',\r\n    bio: 'Representing community interests and facilitating university-community engagement.',\r\n    email: '<EMAIL>'\r\n  },\r\n];\r\n\r\nconst AdvisoryBoardSection = () => {\r\n  const [isVisible, setIsVisible] = useState(false);\r\n  const sectionRef = useRef(null);\r\n\r\n  useEffect(() => {\r\n    const observer = new IntersectionObserver(\r\n      ([entry]) => {\r\n        if (entry.isIntersecting) {\r\n          setIsVisible(true);\r\n          observer.disconnect();\r\n        }\r\n      },\r\n      { threshold: 0.1 }\r\n    );\r\n\r\n    // Store current ref value in a variable to use in cleanup\r\n    const currentRef = sectionRef.current;\r\n\r\n    if (currentRef) {\r\n      observer.observe(currentRef);\r\n    }\r\n\r\n    return () => {\r\n      // Use the stored ref value in cleanup\r\n      if (currentRef) {\r\n        observer.unobserve(currentRef);\r\n      }\r\n    };\r\n  }, []);\r\n\r\n  return (\r\n    <section ref={sectionRef} className=\"py-16 md:py-24 bg-gradient-to-b from-white to-blue-50 relative overflow-hidden\">\r\n      {/* Decorative elements */}\r\n      <div className=\"absolute top-0 left-0 w-full h-full opacity-5\" style={{\r\n        backgroundImage: 'url(\"data:image/svg+xml,%3Csvg width=\\'100\\' height=\\'100\\' viewBox=\\'0 0 100 100\\' xmlns=\\'http://www.w3.org/2000/svg\\'%3E%3Cpath d=\\'M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z\\' fill=\\'%234a90e2\\' fill-opacity=\\'0.1\\' fill-rule=\\'evenodd\\'/%3E%3C/svg%3E\")',\r\n      }}></div>\r\n\r\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 relative z-10\">\r\n        <div className={`transition-all duration-700 transform ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>\r\n          <SectionTitle title=\"Our Esteemed Advisory Board\" subtitle=\"Guidance & Expertise\" />\r\n          <p className=\"text-center text-gray-700 max-w-3xl mx-auto mb-12\">\r\n            Our advisory board brings together distinguished professionals from academia, industry, and the community\r\n            to provide strategic guidance and ensure the university&#39;s continued growth and relevance.\r\n          </p>\r\n        </div>\r\n\r\n        <div className=\"grid sm:grid-cols-2 lg:grid-cols-3 gap-8\">\r\n          {advisoryBoardData.map((member, index) => (\r\n            <div\r\n              key={member.id}\r\n              className={`transform transition-all duration-700 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-20'}`}\r\n              style={{ transitionDelay: `${index * 150}ms` }}\r\n            >\r\n              <TeamMemberCard member={member} />\r\n            </div>\r\n          ))}\r\n        </div>\r\n\r\n        {/* Quote section */}\r\n        <div className={`mt-16 text-center transition-all duration-700 delay-1000 transform ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>\r\n          <blockquote className=\"bg-white p-8 rounded-xl shadow-lg border border-blue-100 max-w-3xl mx-auto relative\">\r\n            {/* Large quote marks */}\r\n            <div className=\"absolute top-4 left-4 text-blue-200 opacity-50 text-6xl font-serif\">&ldquo;</div>\r\n            <div className=\"absolute bottom-4 right-4 text-blue-200 opacity-50 text-6xl font-serif\">&rdquo;</div>\r\n\r\n            <p className=\"text-xl text-gray-700 italic relative z-10 mb-4\">\r\n              The advisory board is committed to supporting Far Western University in its mission to provide quality education\r\n              and enhance research and innovation to meet the needs of society and contribute to national development.\r\n            </p>\r\n            <footer className=\"text-gray-600\">\r\n              <strong>Advisory Board Statement</strong>\r\n            </footer>\r\n          </blockquote>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\nexport default AdvisoryBoardSection;"], "names": [], "mappings": ";;;;AACA;AACA,0QAA2D,mBAAmB;AAC9E;;;AAHA;;;;AAKA,8BAA8B;AAC9B,MAAM,oBAA8B;IAClC;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,UAAU;QACV,KAAK;QACL,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,UAAU;QACV,KAAK;QACL,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,UAAU;QACV,KAAK;QACL,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,UAAU;QACV,KAAK;QACL,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,UAAU;QACV,KAAK;QACL,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,UAAU;QACV,KAAK;QACL,OAAO;IACT;CACD;AAED,MAAM,uBAAuB;;IAC3B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAE1B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0CAAE;YACR,MAAM,WAAW,IAAI;kDACnB,CAAC,CAAC,MAAM;oBACN,IAAI,MAAM,cAAc,EAAE;wBACxB,aAAa;wBACb,SAAS,UAAU;oBACrB;gBACF;iDACA;gBAAE,WAAW;YAAI;YAGnB,0DAA0D;YAC1D,MAAM,aAAa,WAAW,OAAO;YAErC,IAAI,YAAY;gBACd,SAAS,OAAO,CAAC;YACnB;YAEA;kDAAO;oBACL,sCAAsC;oBACtC,IAAI,YAAY;wBACd,SAAS,SAAS,CAAC;oBACrB;gBACF;;QACF;yCAAG,EAAE;IAEL,qBACE,6LAAC;QAAQ,KAAK;QAAY,WAAU;;0BAElC,6LAAC;gBAAI,WAAU;gBAAgD,OAAO;oBACpE,iBAAiB;gBACnB;;;;;;0BAEA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAW,CAAC,sCAAsC,EAAE,YAAY,8BAA8B,4BAA4B;;0CAC7H,6LAAC,sJAAA,CAAA,UAAY;gCAAC,OAAM;gCAA8B,UAAS;;;;;;0CAC3D,6LAAC;gCAAE,WAAU;0CAAoD;;;;;;;;;;;;kCAMnE,6LAAC;wBAAI,WAAU;kCACZ,kBAAkB,GAAG,CAAC,CAAC,QAAQ,sBAC9B,6LAAC;gCAEC,WAAW,CAAC,sCAAsC,EAAE,YAAY,8BAA8B,4BAA4B;gCAC1H,OAAO;oCAAE,iBAAiB,GAAG,QAAQ,IAAI,EAAE,CAAC;gCAAC;0CAE7C,cAAA,6LAAC,uJAAA,CAAA,UAAc;oCAAC,QAAQ;;;;;;+BAJnB,OAAO,EAAE;;;;;;;;;;kCAUpB,6LAAC;wBAAI,WAAW,CAAC,mEAAmE,EAAE,YAAY,8BAA8B,4BAA4B;kCAC1J,cAAA,6LAAC;4BAAW,WAAU;;8CAEpB,6LAAC;oCAAI,WAAU;8CAAqE;;;;;;8CACpF,6LAAC;oCAAI,WAAU;8CAAyE;;;;;;8CAExF,6LAAC;oCAAE,WAAU;8CAAkD;;;;;;8CAI/D,6LAAC;oCAAO,WAAU;8CAChB,cAAA,6LAAC;kDAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOtB;GA7EM;KAAA;uCA+ES", "debugId": null}}, {"offset": {"line": 525, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/fwu_incubation%28react%2Blaravel%29/incubation/frontend/src/app/components/about/TeamSection.tsx"], "sourcesContent": ["\"use client\"\r\nimport SectionTitle from '../shared/SectionTitle';\r\nimport TeamMemberCard, { Member } from './TeamMemberCard';\r\nimport { useEffect, useState, useRef } from 'react';\r\n\r\n// University leadership team data\r\nconst teamMembersData: Member[] = [\r\n  {\r\n    id: 'tm1',\r\n    name: 'Prof. Dr. <PERSON><PERSON>',\r\n    role: 'Vice Chancellor',\r\n    imageUrl: '/images/team/vice-chancellor.jpg',\r\n    bio: 'Leading the university with a vision for academic excellence and innovation.',\r\n    linkedin: 'https://linkedin.com/in/hemrajpant',\r\n    email: '<EMAIL>'\r\n  },\r\n  {\r\n    id: 'tm2',\r\n    name: 'Prof. Dr. <PERSON><PERSON><PERSON>',\r\n    role: 'Registrar',\r\n    imageUrl: '/images/team/registrar.jpg',\r\n    bio: 'Overseeing administrative functions and institutional operations.',\r\n    linkedin: 'https://linkedin.com/in/narendradhami',\r\n    email: '<EMAIL>'\r\n  },\r\n  {\r\n    id: 'tm3',\r\n    name: 'Prof. Dr. <PERSON>',\r\n    role: 'Dean of Academic Affairs',\r\n    imageUrl: '/images/team/dean-academic.jpg',\r\n    bio: 'Coordinating academic programs and ensuring educational quality.',\r\n    twitter: 'https://twitter.com/santoshthapa',\r\n    email: '<EMAIL>'\r\n  },\r\n  {\r\n    id: 'tm4',\r\n    name: 'Dr. <PERSON>esh Joshi',\r\n    role: 'Director of Research',\r\n    imageUrl: '/images/team/research-director.jpg',\r\n    bio: 'Leading research initiatives and fostering innovation across disciplines.',\r\n    email: '<EMAIL>'\r\n  },\r\n];\r\n\r\nconst TeamSection = () => {\r\n  const [isVisible, setIsVisible] = useState(false);\r\n  const sectionRef = useRef(null);\r\n\r\n  useEffect(() => {\r\n    const observer = new IntersectionObserver(\r\n      ([entry]) => {\r\n        if (entry.isIntersecting) {\r\n          setIsVisible(true);\r\n          observer.disconnect();\r\n        }\r\n      },\r\n      { threshold: 0.1 }\r\n    );\r\n\r\n    // Store current ref value in a variable to use in cleanup\r\n    const currentRef = sectionRef.current;\r\n\r\n    if (currentRef) {\r\n      observer.observe(currentRef);\r\n    }\r\n\r\n    return () => {\r\n      // Use the stored ref value in cleanup\r\n      if (currentRef) {\r\n        observer.unobserve(currentRef);\r\n      }\r\n    };\r\n  }, []);\r\n\r\n  return (\r\n    <section ref={sectionRef} className=\"py-16 md:py-24 bg-gradient-to-b from-blue-50 to-white relative overflow-hidden\">\r\n      {/* Background pattern */}\r\n      <div className=\"absolute inset-0 opacity-5\">\r\n        <div className=\"absolute inset-0\" style={{\r\n          backgroundImage: 'radial-gradient(circle, #4a90e2 1px, transparent 1px)',\r\n          backgroundSize: '30px 30px'\r\n        }}></div>\r\n      </div>\r\n\r\n      {/* Decorative elements */}\r\n      <div className=\"absolute -top-20 -left-20 w-96 h-96 bg-blue-200 rounded-full opacity-20 blur-3xl\"></div>\r\n      <div className=\"absolute -bottom-20 -right-20 w-96 h-96 bg-teal-200 rounded-full opacity-20 blur-3xl\"></div>\r\n\r\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 relative z-10\">\r\n        <div className={`transition-all duration-700 transform ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>\r\n          <SectionTitle title=\"University Leadership\" subtitle=\"Guiding Excellence\" />\r\n          <p className=\"text-center text-gray-700 max-w-3xl mx-auto mb-12\">\r\n            Our dedicated leadership team brings together decades of academic and administrative experience\r\n            to guide Far Western University toward its vision of excellence in education and research.\r\n          </p>\r\n        </div>\r\n\r\n        <div className=\"grid sm:grid-cols-2 lg:grid-cols-4 gap-8\">\r\n          {teamMembersData.map((member, index) => (\r\n            <div\r\n              key={member.id}\r\n              className={`transform transition-all duration-700 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-20'}`}\r\n              style={{ transitionDelay: `${index * 200}ms` }}\r\n            >\r\n              <TeamMemberCard member={member} />\r\n            </div>\r\n          ))}\r\n        </div>\r\n\r\n        {/* Call to action */}\r\n        <div className={`mt-16 text-center transition-all duration-700 delay-1000 transform ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>\r\n          <div className=\"inline-block bg-white p-6 rounded-xl shadow-lg border border-blue-100 hover:border-blue-300 transition-all duration-300\">\r\n            <h3 className=\"text-xl font-bold text-blue-900 mb-2\">Join Our Academic Community</h3>\r\n            <p className=\"text-gray-700 mb-4\">\r\n              Far Western University welcomes talented faculty and staff to join our mission of educational excellence.\r\n            </p>\r\n            <a\r\n              href=\"/careers\"\r\n              className=\"inline-flex items-center bg-gradient-to-r from-blue-600 to-blue-700 text-white py-2 px-6 rounded-md hover:shadow-lg transition-all duration-300\"\r\n            >\r\n              Explore Opportunities\r\n              <svg className=\"w-4 h-4 ml-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M14 5l7 7m0 0l-7 7m7-7H3\"></path>\r\n              </svg>\r\n            </a>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\nexport default TeamSection;"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;AAHA;;;;AAKA,kCAAkC;AAClC,MAAM,kBAA4B;IAChC;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,UAAU;QACV,KAAK;QACL,UAAU;QACV,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,UAAU;QACV,KAAK;QACL,UAAU;QACV,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,UAAU;QACV,KAAK;QACL,SAAS;QACT,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,UAAU;QACV,KAAK;QACL,OAAO;IACT;CACD;AAED,MAAM,cAAc;;IAClB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAE1B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM,WAAW,IAAI;yCACnB,CAAC,CAAC,MAAM;oBACN,IAAI,MAAM,cAAc,EAAE;wBACxB,aAAa;wBACb,SAAS,UAAU;oBACrB;gBACF;wCACA;gBAAE,WAAW;YAAI;YAGnB,0DAA0D;YAC1D,MAAM,aAAa,WAAW,OAAO;YAErC,IAAI,YAAY;gBACd,SAAS,OAAO,CAAC;YACnB;YAEA;yCAAO;oBACL,sCAAsC;oBACtC,IAAI,YAAY;wBACd,SAAS,SAAS,CAAC;oBACrB;gBACF;;QACF;gCAAG,EAAE;IAEL,qBACE,6LAAC;QAAQ,KAAK;QAAY,WAAU;;0BAElC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;oBAAmB,OAAO;wBACvC,iBAAiB;wBACjB,gBAAgB;oBAClB;;;;;;;;;;;0BAIF,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAI,WAAU;;;;;;0BAEf,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAW,CAAC,sCAAsC,EAAE,YAAY,8BAA8B,4BAA4B;;0CAC7H,6LAAC,sJAAA,CAAA,UAAY;gCAAC,OAAM;gCAAwB,UAAS;;;;;;0CACrD,6LAAC;gCAAE,WAAU;0CAAoD;;;;;;;;;;;;kCAMnE,6LAAC;wBAAI,WAAU;kCACZ,gBAAgB,GAAG,CAAC,CAAC,QAAQ,sBAC5B,6LAAC;gCAEC,WAAW,CAAC,sCAAsC,EAAE,YAAY,8BAA8B,4BAA4B;gCAC1H,OAAO;oCAAE,iBAAiB,GAAG,QAAQ,IAAI,EAAE,CAAC;gCAAC;0CAE7C,cAAA,6LAAC,uJAAA,CAAA,UAAc;oCAAC,QAAQ;;;;;;+BAJnB,OAAO,EAAE;;;;;;;;;;kCAUpB,6LAAC;wBAAI,WAAW,CAAC,mEAAmE,EAAE,YAAY,8BAA8B,4BAA4B;kCAC1J,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAuC;;;;;;8CACrD,6LAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAGlC,6LAAC;oCACC,MAAK;oCACL,WAAU;;wCACX;sDAEC,6LAAC;4CAAI,WAAU;4CAAe,MAAK;4CAAO,QAAO;4CAAe,SAAQ;4CAAY,OAAM;sDACxF,cAAA,6LAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAY;gDAAI,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQrF;GAtFM;KAAA;uCAwFS", "debugId": null}}, {"offset": {"line": 786, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/fwu_incubation%28react%2Blaravel%29/incubation/frontend/src/app/components/about/HistoryTimeline.tsx"], "sourcesContent": ["\"use client\"\r\nimport SectionTitle from '../shared/SectionTitle';\r\nimport { useEffect, useState, useRef } from 'react';\r\nimport { Building2, GraduationCap, Handshake, Globe } from 'lucide-react';\r\n\r\nconst timelineEvents = [\r\n  {\r\n    year: '2010',\r\n    title: 'Establishment',\r\n    description: 'Far Western University was established by the Act of Parliament as a government-funded national university.',\r\n    icon: <Building2 />,\r\n    color: 'bg-blue-600'\r\n  },\r\n  {\r\n    year: '2012',\r\n    title: 'Academic Expansion',\r\n    description: 'Expanded academic programs with the introduction of multiple faculties and departments.',\r\n    icon: <GraduationCap />,\r\n    color: 'bg-teal-500'\r\n  },\r\n  {\r\n    year: '2015',\r\n    title: 'Research Center',\r\n    description: 'Established the Research, Innovation & Development Center to promote scholarly activities.',\r\n    icon: <Globe />,\r\n    color: 'bg-yellow-500'\r\n  },\r\n  {\r\n    year: '2020',\r\n    title: 'International Partnerships',\r\n    description: 'Formed strategic partnerships with international universities for academic collaboration and exchange programs.',\r\n    icon: <Handshake />,\r\n    color: 'bg-indigo-600'\r\n  },\r\n  {\r\n    year: '2023',\r\n    title: 'Digital Transformation',\r\n    description: 'Implemented comprehensive digital infrastructure to enhance teaching, learning, and administrative processes.',\r\n    icon: <Globe />,\r\n    color: 'bg-purple-600'\r\n  }\r\n];\r\n\r\nconst HistoryTimeline = () => {\r\n  const [isVisible, setIsVisible] = useState(false);\r\n  const sectionRef = useRef(null);\r\n\r\n  useEffect(() => {\r\n    const observer = new IntersectionObserver(\r\n      ([entry]) => {\r\n        if (entry.isIntersecting) {\r\n          setIsVisible(true);\r\n          observer.disconnect();\r\n        }\r\n      },\r\n      { threshold: 0.1 }\r\n    );\r\n\r\n    // Store current ref value in a variable to use in cleanup\r\n    const currentRef = sectionRef.current;\r\n\r\n    if (currentRef) {\r\n      observer.observe(currentRef);\r\n    }\r\n\r\n    return () => {\r\n      // Use the stored ref value in cleanup\r\n      if (currentRef) {\r\n        observer.unobserve(currentRef);\r\n      }\r\n    };\r\n  }, []);\r\n\r\n  return (\r\n    <section ref={sectionRef} className=\"py-16 md:py-24 bg-blue-50 relative overflow-hidden\">\r\n      {/* Background decorative elements */}\r\n      <div className=\"absolute top-0 left-0 w-full h-full\" style={{\r\n        backgroundImage: 'url(\"data:image/svg+xml,%3Csvg width=\\'60\\' height=\\'60\\' viewBox=\\'0 0 60 60\\' xmlns=\\'http://www.w3.org/2000/svg\\'%3E%3Cg fill=\\'none\\' fill-rule=\\'evenodd\\'%3E%3Cg fill=\\'%234a90e2\\' fill-opacity=\\'0.05\\'%3E%3Cpath d=\\'M36 34h4v1h-4v-1zm0-2h1v4h-1v-4zm2-2h1v1h-1v-1zm-2 2h1v1h-1v-1zm-2-2h1v1h-1v-1zm2-2h1v1h-1v-1zm-2 2h1v1h-1v-1zm-2-2h1v1h-1v-1z\\'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\")',\r\n      }}></div>\r\n\r\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 relative z-10\">\r\n        <div className={`transition-all duration-700 transform ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>\r\n          <SectionTitle title=\"Our Journey\" subtitle=\"History & Milestones\" />\r\n        </div>\r\n\r\n        <div className=\"mt-16 relative\">\r\n          {/* Vertical line */}\r\n          <div className=\"absolute left-1/2 transform -translate-x-1/2 h-full w-1 bg-blue-200 rounded-full\"></div>\r\n\r\n          {/* Timeline events */}\r\n          {timelineEvents.map((event, index) => (\r\n            <div\r\n              key={index}\r\n              className={`flex items-center mb-16 last:mb-0 transition-all duration-700 transform ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}\r\n              style={{ transitionDelay: `${index * 200}ms` }}\r\n            >\r\n              {/* Left side (odd indices) */}\r\n              {index % 2 === 0 ? (\r\n                <>\r\n                  <div className=\"w-1/2 pr-8 md:pr-16 text-right\">\r\n                    <div className=\"mb-2 text-3xl font-bold text-blue-700\">{event.year}</div>\r\n                    <h3 className=\"text-xl font-semibold text-blue-900 mb-2\">{event.title}</h3>\r\n                    <p className=\"text-gray-600\">{event.description}</p>\r\n                  </div>\r\n\r\n                  {/* Center icon */}\r\n                  <div className={`absolute left-1/2 transform -translate-x-1/2 w-12 h-12 rounded-full ${event.color} text-white flex items-center justify-center z-10 shadow-lg border-4 border-white`}>\r\n                    {event.icon}\r\n                  </div>\r\n\r\n                  {/* Right side (empty for odd indices) */}\r\n                  <div className=\"w-1/2 pl-8 md:pl-16\"></div>\r\n                </>\r\n              ) : (\r\n                <>\r\n                  {/* Left side (empty for even indices) */}\r\n                  <div className=\"w-1/2 pr-8 md:pr-16\"></div>\r\n\r\n                  {/* Center icon */}\r\n                  <div className={`absolute left-1/2 transform -translate-x-1/2 w-12 h-12 rounded-full ${event.color} text-white flex items-center justify-center z-10 shadow-lg border-4 border-white`}>\r\n                    {event.icon}\r\n                  </div>\r\n\r\n                  {/* Right side (even indices) */}\r\n                  <div className=\"w-1/2 pl-8 md:pl-16\">\r\n                    <div className=\"mb-2 text-3xl font-bold text-blue-700\">{event.year}</div>\r\n                    <h3 className=\"text-xl font-semibold text-blue-900 mb-2\">{event.title}</h3>\r\n                    <p className=\"text-gray-600\">{event.description}</p>\r\n                  </div>\r\n                </>\r\n              )}\r\n            </div>\r\n          ))}\r\n        </div>\r\n\r\n        {/* Future vision */}\r\n        <div className={`mt-16 text-center transition-all duration-700 delay-1000 transform ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>\r\n          <div className=\"inline-block bg-white p-6 rounded-xl shadow-lg border border-blue-100\">\r\n            <h3 className=\"text-2xl font-bold text-blue-900 mb-2\">Looking to the Future</h3>\r\n            <p className=\"text-gray-700\">\r\n              Far Western University continues to grow and evolve, committed to providing quality education\r\n              and enhancing research and innovation to meet the needs of society.\r\n            </p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\nexport default HistoryTimeline;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAAA;AAAA;AAAA;;;AAHA;;;;AAKA,MAAM,iBAAiB;IACrB;QACE,MAAM;QACN,OAAO;QACP,aAAa;QACb,oBAAM,6LAAC,mNAAA,CAAA,YAAS;;;;;QAChB,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;QACb,oBAAM,6LAAC,2NAAA,CAAA,gBAAa;;;;;QACpB,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;QACb,oBAAM,6LAAC,uMAAA,CAAA,QAAK;;;;;QACZ,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;QACb,oBAAM,6LAAC,+MAAA,CAAA,YAAS;;;;;QAChB,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;QACb,oBAAM,6LAAC,uMAAA,CAAA,QAAK;;;;;QACZ,OAAO;IACT;CACD;AAED,MAAM,kBAAkB;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAE1B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,MAAM,WAAW,IAAI;6CACnB,CAAC,CAAC,MAAM;oBACN,IAAI,MAAM,cAAc,EAAE;wBACxB,aAAa;wBACb,SAAS,UAAU;oBACrB;gBACF;4CACA;gBAAE,WAAW;YAAI;YAGnB,0DAA0D;YAC1D,MAAM,aAAa,WAAW,OAAO;YAErC,IAAI,YAAY;gBACd,SAAS,OAAO,CAAC;YACnB;YAEA;6CAAO;oBACL,sCAAsC;oBACtC,IAAI,YAAY;wBACd,SAAS,SAAS,CAAC;oBACrB;gBACF;;QACF;oCAAG,EAAE;IAEL,qBACE,6LAAC;QAAQ,KAAK;QAAY,WAAU;;0BAElC,6LAAC;gBAAI,WAAU;gBAAsC,OAAO;oBAC1D,iBAAiB;gBACnB;;;;;;0BAEA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAW,CAAC,sCAAsC,EAAE,YAAY,8BAA8B,4BAA4B;kCAC7H,cAAA,6LAAC,sJAAA,CAAA,UAAY;4BAAC,OAAM;4BAAc,UAAS;;;;;;;;;;;kCAG7C,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;;;;;4BAGd,eAAe,GAAG,CAAC,CAAC,OAAO,sBAC1B,6LAAC;oCAEC,WAAW,CAAC,wEAAwE,EAAE,YAAY,8BAA8B,4BAA4B;oCAC5J,OAAO;wCAAE,iBAAiB,GAAG,QAAQ,IAAI,EAAE,CAAC;oCAAC;8CAG5C,QAAQ,MAAM,kBACb;;0DACE,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAyC,MAAM,IAAI;;;;;;kEAClE,6LAAC;wDAAG,WAAU;kEAA4C,MAAM,KAAK;;;;;;kEACrE,6LAAC;wDAAE,WAAU;kEAAiB,MAAM,WAAW;;;;;;;;;;;;0DAIjD,6LAAC;gDAAI,WAAW,CAAC,oEAAoE,EAAE,MAAM,KAAK,CAAC,iFAAiF,CAAC;0DAClL,MAAM,IAAI;;;;;;0DAIb,6LAAC;gDAAI,WAAU;;;;;;;qEAGjB;;0DAEE,6LAAC;gDAAI,WAAU;;;;;;0DAGf,6LAAC;gDAAI,WAAW,CAAC,oEAAoE,EAAE,MAAM,KAAK,CAAC,iFAAiF,CAAC;0DAClL,MAAM,IAAI;;;;;;0DAIb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAyC,MAAM,IAAI;;;;;;kEAClE,6LAAC;wDAAG,WAAU;kEAA4C,MAAM,KAAK;;;;;;kEACrE,6LAAC;wDAAE,WAAU;kEAAiB,MAAM,WAAW;;;;;;;;;;;;;;mCAnChD;;;;;;;;;;;kCA4CX,6LAAC;wBAAI,WAAW,CAAC,mEAAmE,EAAE,YAAY,8BAA8B,4BAA4B;kCAC1J,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,6LAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASzC;GAzGM;KAAA;uCA2GS", "debugId": null}}, {"offset": {"line": 1110, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/fwu_incubation%28react%2Blaravel%29/incubation/frontend/src/app/components/home/<USER>"], "sourcesContent": ["// components/home/<USER>\n\"use client\"\r\nimport Image from 'next/image';\r\nimport { useEffect, useState } from 'react';\r\nimport { ChevronDown } from 'lucide-react';\r\n\r\nconst HeroBanner = () => {\r\n  const [isVisible, setIsVisible] = useState(false);\r\n\r\n  useEffect(() => {\r\n    setIsVisible(true);\r\n\r\n    // Add keyframes for animations\r\n    const styleElement = document.createElement('style');\r\n    styleElement.innerHTML = `\r\n      @keyframes float {\r\n        0%, 100% { transform: translateY(0); }\r\n        50% { transform: translateY(-20px); }\r\n      }\r\n      @keyframes pulse {\r\n        0%, 100% { opacity: 0.8; transform: scale(1); }\r\n        50% { opacity: 0.4; transform: scale(0.95); }\r\n      }\r\n      @keyframes scale {\r\n        0% { transform: scale(1.1); }\r\n        100% { transform: scale(1.2); }\r\n      }\r\n      @keyframes moveUpRight {\r\n        0% { transform: translate(0, 0); opacity: 0; }\r\n        10% { opacity: 1; }\r\n        90% { opacity: 1; }\r\n        100% { transform: translate(100px, -300px); opacity: 0; }\r\n      }\r\n      @keyframes moveLeftRight {\r\n        0%, 100% { transform: translateX(0); }\r\n        50% { transform: translateX(-2%); }\r\n      }\r\n      @keyframes spin-slow {\r\n        from { transform: rotate(0deg); }\r\n        to { transform: rotate(360deg); }\r\n      }\r\n    `;\r\n    document.head.appendChild(styleElement);\r\n\r\n    return () => {\r\n      document.head.removeChild(styleElement);\r\n    };\r\n  }, []);\r\n\r\n  return (\r\n    <div className=\"relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-indigo-900 via-blue-900 to-indigo-800\">\r\n      {/* Animated background elements */}\r\n      <div className=\"absolute inset-0 z-0\">\r\n        {/* Gradient overlay */}\r\n        <div className=\"absolute inset-0 bg-gradient-to-b from-transparent via-blue-900/50 to-indigo-900/80\"></div>\r\n\r\n        {/* Animated mesh grid */}\r\n        <div className=\"absolute inset-0 opacity-10\"\r\n          style={{\r\n            backgroundImage: 'linear-gradient(#4f46e5 1px, transparent 1px), linear-gradient(to right, #4f46e5 1px, transparent 1px)',\r\n            backgroundSize: '40px 40px'\r\n          }}>\r\n        </div>\r\n\r\n        {/* Animated circles */}\r\n        <div className=\"absolute top-1/4 left-10 w-64 h-64 bg-blue-500/20 rounded-full blur-3xl\"\r\n          style={{animation: 'pulse 8s ease-in-out infinite'}}></div>\r\n        <div className=\"absolute bottom-1/4 right-10 w-80 h-80 bg-indigo-500/20 rounded-full blur-3xl\"\r\n          style={{animation: 'pulse 10s ease-in-out infinite 1s'}}></div>\r\n        <div className=\"absolute top-1/3 right-1/4 w-40 h-40 bg-purple-500/20 rounded-full blur-2xl\"\r\n          style={{animation: 'pulse 7s ease-in-out infinite 0.5s'}}></div>\r\n\r\n        {/* Rotating gradient circle */}\r\n        <div className=\"absolute -top-40 -left-40 w-96 h-96 opacity-30\"\r\n          style={{\r\n            background: 'conic-gradient(from 0deg, #4f46e5, #3b82f6, #0ea5e9, #4f46e5)',\r\n            borderRadius: '50%',\r\n            animation: 'spin-slow 20s linear infinite'\r\n          }}>\r\n        </div>\r\n\r\n        {/* Animated particles */}\r\n        <div className=\"absolute inset-0 z-0\">\r\n          {[...Array(30)].map((_, i) => (\r\n            <div\r\n              key={i}\r\n              className=\"absolute rounded-full\"\r\n              style={{\r\n                width: `${Math.random() * 4 + 1}px`,\r\n                height: `${Math.random() * 4 + 1}px`,\r\n                backgroundColor: i % 3 === 0 ? '#93c5fd' : i % 3 === 1 ? '#c4b5fd' : '#bae6fd',\r\n                top: `${Math.random() * 100}%`,\r\n                left: `${Math.random() * 100}%`,\r\n                animation: `moveUpRight ${Math.random() * 15 + 15}s linear infinite`,\r\n                animationDelay: `${Math.random() * 5}s`,\r\n                opacity: Math.random() * 0.5 + 0.3\r\n              }}\r\n            ></div>\r\n          ))}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Main content */}\r\n      <div className=\"relative z-10 max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-20\">\r\n        <div className=\"flex flex-col lg:flex-row items-center gap-12\">\r\n          {/* Left content */}\r\n          <div className=\"lg:w-1/2 text-center lg:text-left\">\r\n            <div className={`transition-all duration-1000 transform ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>\r\n              {/* Badge */}\r\n              <div className=\"inline-block mb-6 px-3 py-1 bg-white/10 backdrop-blur-md rounded-full\">\r\n                <span className=\"text-blue-200 font-medium text-sm\">FWU Incubation Center</span>\r\n              </div>\r\n\r\n              {/* Heading */}\r\n              <h1 className=\"text-4xl sm:text-5xl md:text-6xl font-extrabold leading-tight mb-6\">\r\n                <span className=\"block mb-2 text-white\">Far Western University</span>\r\n                <span className=\"block text-transparent bg-clip-text bg-gradient-to-r from-blue-300 via-indigo-300 to-purple-300\">\r\n                  Empowering Innovation\r\n                </span>\r\n              </h1>\r\n\r\n              {/* Description */}\r\n              <p className=\"text-lg sm:text-xl text-blue-100 mb-8 max-w-xl mx-auto lg:mx-0 leading-relaxed\">\r\n                Join our incubation center and transform your innovative ideas into successful ventures with expert mentorship, resources, and a collaborative ecosystem.\r\n              </p>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Right content - 3D floating illustration */}\r\n          <div className={`lg:w-1/2 transition-all duration-1000 delay-500 transform ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>\r\n            <div className=\"relative h-[400px] md:h-[500px] w-full\">\r\n              {/* Main image with floating animation */}\r\n              <div className=\"absolute inset-0 flex items-center justify-center\" style={{animation: 'float 6s ease-in-out infinite'}}>\r\n                <div className=\"relative w-full h-full max-w-md mx-auto\">\r\n                  <Image\r\n                    src=\"https://images.unsplash.com/photo-1521737604893-d14cc237f11d?q=80&w=1000&auto=format&fit=crop\"\r\n                    alt=\"FWU Incubation Center\"\r\n                    fill\r\n                    className=\"object-cover rounded-2xl shadow-2xl\"\r\n                    priority\r\n                  />\r\n                  {/* Gradient overlay */}\r\n                  <div className=\"absolute inset-0 bg-gradient-to-tr from-indigo-900/40 to-transparent rounded-2xl\"></div>\r\n\r\n                  {/* Floating elements */}\r\n                  <div className=\"absolute -top-6 -right-6 bg-white rounded-2xl shadow-xl p-4 animate-float-slow\">\r\n                    <div className=\"flex items-center gap-3\">\r\n                      <div className=\"w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center\">\r\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6 text-blue-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\r\n                        </svg>\r\n                      </div>\r\n                      <div>\r\n                        <p className=\"text-sm font-bold text-gray-900\">Innovation Hub</p>\r\n                        <p className=\"text-xs text-gray-500\">Cutting-edge facilities</p>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n\r\n                  <div className=\"absolute -bottom-6 -left-6 bg-white rounded-2xl shadow-xl p-4\" style={{animation: 'float 5s ease-in-out infinite 1s'}}>\r\n                    <div className=\"flex items-center gap-3\">\r\n                      <div className=\"w-10 h-10 bg-indigo-100 rounded-full flex items-center justify-center\">\r\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6 text-indigo-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\" />\r\n                        </svg>\r\n                      </div>\r\n                      <div>\r\n                        <p className=\"text-sm font-bold text-gray-900\">Mentorship</p>\r\n                        <p className=\"text-xs text-gray-500\">Expert guidance</p>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Background glow effect */}\r\n              <div className=\"absolute inset-0 -z-10 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-full blur-3xl opacity-20 transform scale-75\"></div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Scroll Down Indicator */}\r\n      <div className={`absolute bottom-8 left-1/2 transform -translate-x-1/2 transition-all duration-1000 delay-700 ${isVisible ? 'opacity-100' : 'opacity-0'}`}>\r\n        <div className=\"flex flex-col items-center\">\r\n          <span className=\"text-blue-200 text-sm mb-2\">Scroll Down</span>\r\n          <div className=\"w-10 h-10 rounded-full border-2 border-blue-200 flex items-center justify-center animate-bounce\">\r\n            <ChevronDown className=\"text-blue-200\" />\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Bottom Wave */}\r\n      <div className=\"absolute bottom-0 left-0 right-0\">\r\n        <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 1440 120\" className=\"w-full h-auto\">\r\n          <path\r\n            fill=\"#ffffff\"\r\n            fillOpacity=\"1\"\r\n            d=\"M0,64L48,80C96,96,192,128,288,128C384,128,480,96,576,80C672,64,768,64,864,74.7C960,85,1056,107,1152,112C1248,117,1344,107,1392,101.3L1440,96L1440,120L1392,120C1344,120,1248,120,1152,120C1056,120,960,120,864,120C768,120,672,120,576,120C480,120,384,120,288,120C192,120,96,120,48,120L0,120Z\"\r\n          ></path>\r\n        </svg>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\n// Add CSS keyframes for animations\r\nconst styles = `\r\n@keyframes float {\r\n  0%, 100% { transform: translateY(0); }\r\n  50% { transform: translateY(-10px); }\r\n}\r\n@keyframes pulse {\r\n  0%, 100% { opacity: 1; transform: scale(1); }\r\n  50% { opacity: 0.8; transform: scale(0.95); }\r\n}\r\n@keyframes scale {\r\n  0% { transform: scale(1.1); }\r\n  100% { transform: scale(1.2); }\r\n}\r\n@keyframes moveUpRight {\r\n  0% { transform: translate(0, 0); opacity: 0; }\r\n  10% { opacity: 1; }\r\n  90% { opacity: 1; }\r\n  100% { transform: translate(100px, -300px); opacity: 0; }\r\n}\r\n@keyframes moveLeftRight {\r\n  0%, 100% { transform: translateX(0); }\r\n  50% { transform: translateX(-2%); }\r\n}\r\n`;\r\n\r\n// Add the styles to the document\r\nif (typeof document !== 'undefined') {\r\n  const styleElement = document.createElement('style');\r\n  styleElement.innerHTML = styles;\r\n  document.head.appendChild(styleElement);\r\n}\r\n\r\nexport default HeroBanner;"], "names": [], "mappings": "AAAA,iCAAiC;;;;;AAEjC;AACA;AACA;;;AAHA;;;;AAKA,MAAM,aAAa;;IACjB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,aAAa;YAEb,+BAA+B;YAC/B,MAAM,eAAe,SAAS,aAAa,CAAC;YAC5C,aAAa,SAAS,GAAG,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;IA2B1B,CAAC;YACD,SAAS,IAAI,CAAC,WAAW,CAAC;YAE1B;wCAAO;oBACL,SAAS,IAAI,CAAC,WAAW,CAAC;gBAC5B;;QACF;+BAAG,EAAE;IAEL,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;;;;;kCAGf,6LAAC;wBAAI,WAAU;wBACb,OAAO;4BACL,iBAAiB;4BACjB,gBAAgB;wBAClB;;;;;;kCAIF,6LAAC;wBAAI,WAAU;wBACb,OAAO;4BAAC,WAAW;wBAA+B;;;;;;kCACpD,6LAAC;wBAAI,WAAU;wBACb,OAAO;4BAAC,WAAW;wBAAmC;;;;;;kCACxD,6LAAC;wBAAI,WAAU;wBACb,OAAO;4BAAC,WAAW;wBAAoC;;;;;;kCAGzD,6LAAC;wBAAI,WAAU;wBACb,OAAO;4BACL,YAAY;4BACZ,cAAc;4BACd,WAAW;wBACb;;;;;;kCAIF,6LAAC;wBAAI,WAAU;kCACZ;+BAAI,MAAM;yBAAI,CAAC,GAAG,CAAC,CAAC,GAAG,kBACtB,6LAAC;gCAEC,WAAU;gCACV,OAAO;oCACL,OAAO,GAAG,KAAK,MAAM,KAAK,IAAI,EAAE,EAAE,CAAC;oCACnC,QAAQ,GAAG,KAAK,MAAM,KAAK,IAAI,EAAE,EAAE,CAAC;oCACpC,iBAAiB,IAAI,MAAM,IAAI,YAAY,IAAI,MAAM,IAAI,YAAY;oCACrE,KAAK,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;oCAC9B,MAAM,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;oCAC/B,WAAW,CAAC,YAAY,EAAE,KAAK,MAAM,KAAK,KAAK,GAAG,iBAAiB,CAAC;oCACpE,gBAAgB,GAAG,KAAK,MAAM,KAAK,EAAE,CAAC,CAAC;oCACvC,SAAS,KAAK,MAAM,KAAK,MAAM;gCACjC;+BAXK;;;;;;;;;;;;;;;;0BAkBb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAW,CAAC,uCAAuC,EAAE,YAAY,8BAA8B,4BAA4B;;kDAE9H,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAK,WAAU;sDAAoC;;;;;;;;;;;kDAItD,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;gDAAK,WAAU;0DAAwB;;;;;;0DACxC,6LAAC;gDAAK,WAAU;0DAAkG;;;;;;;;;;;;kDAMpH,6LAAC;wCAAE,WAAU;kDAAiF;;;;;;;;;;;;;;;;;sCAOlG,6LAAC;4BAAI,WAAW,CAAC,0DAA0D,EAAE,YAAY,8BAA8B,4BAA4B;sCACjJ,cAAA,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;wCAAoD,OAAO;4CAAC,WAAW;wCAA+B;kDACnH,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,gIAAA,CAAA,UAAK;oDACJ,KAAI;oDACJ,KAAI;oDACJ,IAAI;oDACJ,WAAU;oDACV,QAAQ;;;;;;8DAGV,6LAAC;oDAAI,WAAU;;;;;;8DAGf,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAI,OAAM;oEAA6B,WAAU;oEAAwB,MAAK;oEAAO,SAAQ;oEAAY,QAAO;8EAC/G,cAAA,6LAAC;wEAAK,eAAc;wEAAQ,gBAAe;wEAAQ,aAAa;wEAAG,GAAE;;;;;;;;;;;;;;;;0EAGzE,6LAAC;;kFACC,6LAAC;wEAAE,WAAU;kFAAkC;;;;;;kFAC/C,6LAAC;wEAAE,WAAU;kFAAwB;;;;;;;;;;;;;;;;;;;;;;;8DAK3C,6LAAC;oDAAI,WAAU;oDAAgE,OAAO;wDAAC,WAAW;oDAAkC;8DAClI,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAI,OAAM;oEAA6B,WAAU;oEAA0B,MAAK;oEAAO,SAAQ;oEAAY,QAAO;8EACjH,cAAA,6LAAC;wEAAK,eAAc;wEAAQ,gBAAe;wEAAQ,aAAa;wEAAG,GAAE;;;;;;;;;;;;;;;;0EAGzE,6LAAC;;kFACC,6LAAC;wEAAE,WAAU;kFAAkC;;;;;;kFAC/C,6LAAC;wEAAE,WAAU;kFAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAQ/C,6LAAC;wCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOvB,6LAAC;gBAAI,WAAW,CAAC,6FAA6F,EAAE,YAAY,gBAAgB,aAAa;0BACvJ,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAK,WAAU;sCAA6B;;;;;;sCAC7C,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,uNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;0BAM7B,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,OAAM;oBAA6B,SAAQ;oBAAe,WAAU;8BACvE,cAAA,6LAAC;wBACC,MAAK;wBACL,aAAY;wBACZ,GAAE;;;;;;;;;;;;;;;;;;;;;;AAMd;GAtMM;KAAA;AAwMN,mCAAmC;AACnC,MAAM,SAAS,CAAC;;;;;;;;;;;;;;;;;;;;;;;AAuBhB,CAAC;AAED,iCAAiC;AACjC,IAAI,OAAO,aAAa,aAAa;IACnC,MAAM,eAAe,SAAS,aAAa,CAAC;IAC5C,aAAa,SAAS,GAAG;IACzB,SAAS,IAAI,CAAC,WAAW,CAAC;AAC5B;uCAEe", "debugId": null}}, {"offset": {"line": 1660, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/fwu_incubation%28react%2Blaravel%29/incubation/frontend/src/app/components/home/<USER>"], "sourcesContent": ["// components/home/<USER>\n\"use client\"\r\nimport Image from 'next/image';\r\nimport { Check, ArrowRight } from 'lucide-react';\r\nimport { useEffect, useState, useRef } from 'react';\r\nimport Link from 'next/link';\r\n\r\nconst IntroSection = () => {\r\n  const [isVisible, setIsVisible] = useState(false);\r\n  const sectionRef = useRef(null);\r\n\r\n  useEffect(() => {\r\n    const observer = new IntersectionObserver(\r\n      ([entry]) => {\r\n        if (entry.isIntersecting) {\r\n          setIsVisible(true);\r\n          observer.disconnect();\r\n        }\r\n      },\r\n      { threshold: 0.1 }\r\n    );\r\n\r\n    const currentRef = sectionRef.current;\r\n    if (currentRef) {\r\n      observer.observe(currentRef);\r\n    }\r\n\r\n    return () => {\r\n      if (currentRef) {\r\n        observer.unobserve(currentRef);\r\n      }\r\n    };\r\n  }, []);\r\n\r\n  return (\r\n    <section ref={sectionRef} className=\"py-24 md:py-32 bg-white relative overflow-hidden\">\r\n      {/* Background decorative elements */}\r\n      <div className=\"absolute top-0 right-0 w-[600px] h-[600px] bg-blue-50 rounded-full opacity-70 blur-3xl -translate-x-1/3 -translate-y-1/3\"></div>\r\n      <div className=\"absolute bottom-0 left-0 w-[500px] h-[500px] bg-indigo-50 rounded-full opacity-70 blur-3xl translate-x-1/4 translate-y-1/4\"></div>\r\n\r\n      {/* Subtle pattern overlay */}\r\n      <div className=\"absolute inset-0 opacity-5\"\r\n        style={{\r\n          backgroundImage: 'radial-gradient(#4338ca 1px, transparent 1px)',\r\n          backgroundSize: '40px 40px'\r\n        }}>\r\n      </div>\r\n\r\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 relative z-10\">\r\n        {/* Section header */}\r\n        <div className={`max-w-3xl mx-auto text-center mb-16 transition-all duration-700 transform ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>\r\n          <div className=\"inline-block mb-4\">\r\n            <div className=\"flex items-center justify-center w-16 h-16 mx-auto bg-indigo-100 rounded-full mb-4\">\r\n              <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-8 w-8 text-indigo-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4\" />\r\n              </svg>\r\n            </div>\r\n          </div>\r\n          <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">FWU Incubation Center</h2>\r\n          <div className=\"w-24 h-1 bg-indigo-600 mx-auto mb-6 rounded-full\"></div>\r\n          <p className=\"text-xl text-gray-600 leading-relaxed\">\r\n            Transforming innovative ideas into successful ventures through mentorship, resources, and a collaborative ecosystem.\r\n          </p>\r\n        </div>\r\n\r\n        <div className=\"grid md:grid-cols-2 gap-16 items-center\">\r\n          {/* Left content */}\r\n          <div className={`space-y-8 transition-all duration-700 delay-100 transform ${isVisible ? 'opacity-100 translate-x-0' : 'opacity-0 -translate-x-10'}`}>\r\n            <div className=\"bg-white rounded-2xl shadow-xl p-8 border border-gray-100 hover:border-indigo-100 transition-all duration-300 hover:shadow-indigo-100/20\">\r\n              <h3 className=\"text-2xl font-bold text-gray-900 mb-4 flex items-center\">\r\n                <span className=\"flex items-center justify-center w-10 h-10 bg-indigo-100 rounded-full text-indigo-600 mr-4\">\r\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\r\n                  </svg>\r\n                </span>\r\n                Our Mission\r\n              </h3>\r\n              <p className=\"text-gray-600 leading-relaxed mb-6\">\r\n                The FWU Incubation Center aims to foster innovation and entrepreneurship by providing a supportive environment for startups to grow and succeed. We bridge the gap between academic research and commercial applications.\r\n              </p>\r\n              <p className=\"text-gray-600 leading-relaxed\">\r\n                Our strategic location at Far Western University gives access to local talent and provides an opportunity for the overall development of the region through innovation and technology transfer.\r\n              </p>\r\n            </div>\r\n\r\n            <div className=\"bg-gradient-to-br from-indigo-50 to-blue-50 rounded-2xl p-8\">\r\n              <h3 className=\"text-xl font-bold text-indigo-900 mb-6\">What We Offer</h3>\r\n              <div className=\"grid sm:grid-cols-2 gap-4\">\r\n                {[\r\n                  'Mentorship & Guidance',\r\n                  'Workspace & Resources',\r\n                  'Funding Opportunities',\r\n                  'Networking Events',\r\n                  'Technical Support',\r\n                  'Business Development'\r\n                ].map((item, index) => (\r\n                  <div\r\n                    key={index}\r\n                    className=\"flex items-center p-3 bg-white rounded-xl shadow-sm transition-all duration-300 hover:shadow-md hover:-translate-y-1 transform\"\r\n                    style={{\r\n                      transitionDelay: `${index * 100}ms`,\r\n                      animation: isVisible ? `fadeSlideIn 0.5s ease-out forwards ${300 + index * 100}ms` : 'none',\r\n                      opacity: 0,\r\n                      transform: 'translateY(10px)'\r\n                    }}\r\n                  >\r\n                    <div className=\"flex-shrink-0 w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center mr-3 text-indigo-600\">\r\n                      <Check className=\"w-4 h-4\" />\r\n                    </div>\r\n                    <span className=\"text-gray-700 font-medium\">{item}</span>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n\r\n              <div className=\"mt-8 text-center\">\r\n                <Link href=\"/programs\" className=\"inline-flex items-center text-indigo-600 font-semibold hover:text-indigo-800 transition-colors group\">\r\n                  Explore Our Programs\r\n                  <ArrowRight className=\"ml-2 group-hover:translate-x-1 transition-transform\" />\r\n                </Link>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Right content - Image with overlays */}\r\n          <div className={`transition-all duration-700 delay-300 transform ${isVisible ? 'opacity-100 translate-x-0' : 'opacity-0 translate-x-10'}`}>\r\n            <div className=\"relative rounded-2xl overflow-hidden shadow-2xl group\">\r\n              {/* Main image */}\r\n              <div className=\"relative h-[600px] w-full\">\r\n                <Image\r\n                  src=\"https://images.unsplash.com/photo-1522202176988-66273c2fd55f?q=80&w=1000&auto=format&fit=crop\"\r\n                  alt=\"FWU Incubation Center\"\r\n                  fill\r\n                  sizes=\"(max-width: 768px) 100vw, 50vw\"\r\n                  className=\"object-cover object-center transform group-hover:scale-105 transition-transform duration-700 ease-in-out\"\r\n                />\r\n                <div className=\"absolute inset-0 bg-gradient-to-t from-indigo-900/70 via-indigo-900/30 to-transparent\"></div>\r\n              </div>\r\n\r\n              {/* Floating info cards */}\r\n              <div className=\"absolute top-6 right-6 bg-white rounded-xl shadow-lg p-4 max-w-[200px] transform transition-transform duration-500 group-hover:-translate-y-2\">\r\n                <div className=\"flex items-center mb-2\">\r\n                  <div className=\"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3\">\r\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-4 w-4 text-blue-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\" />\r\n                    </svg>\r\n                  </div>\r\n                  <h4 className=\"font-bold text-gray-900\">20+ Startups</h4>\r\n                </div>\r\n                <p className=\"text-sm text-gray-600\">Currently incubating innovative startups across various sectors</p>\r\n              </div>\r\n\r\n              <div className=\"absolute bottom-6 left-6 bg-white rounded-xl shadow-lg p-4 max-w-[240px] transform transition-transform duration-500 group-hover:translate-y-2\">\r\n                <h3 className=\"text-lg font-bold text-gray-900 mb-2\">Join Our Community</h3>\r\n                <p className=\"text-sm text-gray-600 mb-3\">\r\n                  Be part of a thriving ecosystem of innovators, mentors, and industry experts\r\n                </p>\r\n                <Link href=\"/apply\" className=\"inline-flex items-center text-sm font-medium text-indigo-600 hover:text-indigo-800\">\r\n                  Apply Now <ArrowRight className=\"ml-1 w-3 h-3\" />\r\n                </Link>\r\n              </div>\r\n\r\n              {/* Floating dots decoration */}\r\n              <div className=\"absolute inset-0 pointer-events-none\">\r\n                {[...Array(8)].map((_, i) => (\r\n                  <div\r\n                    key={i}\r\n                    className=\"absolute w-2 h-2 bg-white rounded-full\"\r\n                    style={{\r\n                      top: `${20 + Math.random() * 60}%`,\r\n                      left: `${20 + Math.random() * 60}%`,\r\n                      animation: `float ${3 + Math.random() * 4}s ease-in-out infinite ${Math.random() * 2}s`\r\n                    }}\r\n                  ></div>\r\n                ))}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* CSS Animations */}\r\n      <style jsx>{`\r\n        @keyframes fadeSlideIn {\r\n          from {\r\n            opacity: 0;\r\n            transform: translateY(10px);\r\n          }\r\n          to {\r\n            opacity: 1;\r\n            transform: translateY(0);\r\n          }\r\n        }\r\n        @keyframes float {\r\n          0%, 100% { transform: translateY(0) scale(1); }\r\n          50% { transform: translateY(-10px) scale(1.2); }\r\n        }\r\n      `}</style>\r\n    </section>\r\n  );\r\n};\r\nexport default IntroSection;"], "names": [], "mappings": "AAAA,mCAAmC;;;;;;AAEnC;AACA;AAAA;AACA;AACA;;;AAJA;;;;;;AAMA,MAAM,eAAe;;IACnB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAE1B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM,WAAW,IAAI;0CACnB,CAAC,CAAC,MAAM;oBACN,IAAI,MAAM,cAAc,EAAE;wBACxB,aAAa;wBACb,SAAS,UAAU;oBACrB;gBACF;yCACA;gBAAE,WAAW;YAAI;YAGnB,MAAM,aAAa,WAAW,OAAO;YACrC,IAAI,YAAY;gBACd,SAAS,OAAO,CAAC;YACnB;YAEA;0CAAO;oBACL,IAAI,YAAY;wBACd,SAAS,SAAS,CAAC;oBACrB;gBACF;;QACF;iCAAG,EAAE;IAEL,qBACE,6LAAC;QAAQ,KAAK;kDAAsB;;0BAElC,6LAAC;0DAAc;;;;;;0BACf,6LAAC;0DAAc;;;;;;0BAGf,6LAAC;gBACC,OAAO;oBACL,iBAAiB;oBACjB,gBAAgB;gBAClB;0DAJa;;;;;;0BAOf,6LAAC;0DAAc;;kCAEb,6LAAC;kEAAe,CAAC,0EAA0E,EAAE,YAAY,8BAA8B,4BAA4B;;0CACjK,6LAAC;0EAAc;0CACb,cAAA,6LAAC;8EAAc;8CACb,cAAA,6LAAC;wCAAI,OAAM;wCAAiE,MAAK;wCAAO,SAAQ;wCAAY,QAAO;kFAAjE;kDAChD,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;0CAI3E,6LAAC;0EAAa;0CAAoD;;;;;;0CAClE,6LAAC;0EAAc;;;;;;0CACf,6LAAC;0EAAY;0CAAwC;;;;;;;;;;;;kCAKvD,6LAAC;kEAAc;;0CAEb,6LAAC;0EAAe,CAAC,0DAA0D,EAAE,YAAY,8BAA8B,6BAA6B;;kDAClJ,6LAAC;kFAAc;;0DACb,6LAAC;0FAAa;;kEACZ,6LAAC;kGAAe;kEACd,cAAA,6LAAC;4DAAI,OAAM;4DAAiD,MAAK;4DAAO,SAAQ;4DAAY,QAAO;sGAAjD;sEAChD,cAAA,6LAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;;;;;;;oDAElE;;;;;;;0DAGT,6LAAC;0FAAY;0DAAqC;;;;;;0DAGlD,6LAAC;0FAAY;0DAAgC;;;;;;;;;;;;kDAK/C,6LAAC;kFAAc;;0DACb,6LAAC;0FAAa;0DAAyC;;;;;;0DACvD,6LAAC;0FAAc;0DACZ;oDACC;oDACA;oDACA;oDACA;oDACA;oDACA;iDACD,CAAC,GAAG,CAAC,CAAC,MAAM,sBACX,6LAAC;wDAGC,OAAO;4DACL,iBAAiB,GAAG,QAAQ,IAAI,EAAE,CAAC;4DACnC,WAAW,YAAY,CAAC,mCAAmC,EAAE,MAAM,QAAQ,IAAI,EAAE,CAAC,GAAG;4DACrF,SAAS;4DACT,WAAW;wDACb;kGANU;;0EAQV,6LAAC;0GAAc;0EACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;;;;;;0EAEnB,6LAAC;0GAAe;0EAA6B;;;;;;;uDAZxC;;;;;;;;;;0DAiBX,6LAAC;0FAAc;0DACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAY,WAAU;;wDAAuG;sEAEtI,6LAAC,qNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAO9B,6LAAC;0EAAe,CAAC,gDAAgD,EAAE,YAAY,8BAA8B,4BAA4B;0CACvI,cAAA,6LAAC;8EAAc;;sDAEb,6LAAC;sFAAc;;8DACb,6LAAC,gIAAA,CAAA,UAAK;oDACJ,KAAI;oDACJ,KAAI;oDACJ,IAAI;oDACJ,OAAM;oDACN,WAAU;;;;;;8DAEZ,6LAAC;8FAAc;;;;;;;;;;;;sDAIjB,6LAAC;sFAAc;;8DACb,6LAAC;8FAAc;;sEACb,6LAAC;sGAAc;sEACb,cAAA,6LAAC;gEAAI,OAAM;gEAA+D,MAAK;gEAAO,SAAQ;gEAAY,QAAO;0GAA/D;0EAChD,cAAA,6LAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAa;oEAAG,GAAE;;;;;;;;;;;;;;;;;sEAGzE,6LAAC;sGAAa;sEAA0B;;;;;;;;;;;;8DAE1C,6LAAC;8FAAY;8DAAwB;;;;;;;;;;;;sDAGvC,6LAAC;sFAAc;;8DACb,6LAAC;8FAAa;8DAAuC;;;;;;8DACrD,6LAAC;8FAAY;8DAA6B;;;;;;8DAG1C,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAS,WAAU;;wDAAqF;sEACvG,6LAAC,qNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;;;;;;;;;;;;;sDAKpC,6LAAC;sFAAc;sDACZ;mDAAI,MAAM;6CAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC;oDAGC,OAAO;wDACL,KAAK,GAAG,KAAK,KAAK,MAAM,KAAK,GAAG,CAAC,CAAC;wDAClC,MAAM,GAAG,KAAK,KAAK,MAAM,KAAK,GAAG,CAAC,CAAC;wDACnC,WAAW,CAAC,MAAM,EAAE,IAAI,KAAK,MAAM,KAAK,EAAE,uBAAuB,EAAE,KAAK,MAAM,KAAK,EAAE,CAAC,CAAC;oDACzF;8FALU;mDADL;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkCzB;GAhMM;KAAA;uCAiMS", "debugId": null}}, {"offset": {"line": 2184, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/fwu_incubation%28react%2Blaravel%29/incubation/frontend/src/app/about/page.tsx"], "sourcesContent": ["\"use client\"\r\nimport React from 'react';\r\nimport AdvisoryBoardSection from '../components/about/AdvisoryBoardSection';\r\nimport TeamSection from '../components/about/TeamSection';\r\nimport HistoryTimeline from '../components/about/HistoryTimeline';\r\nimport HeroBanner from '../components/home/<USER>';\r\nimport IntroSection from '../components/home/<USER>';\r\n\r\nexport default function AboutPage() {\r\n    // Store current ref values in variables to use in cleanup\r\n\r\n  return (\r\n    <>\r\n      <main className=\"overflow-hidden\">\r\n        {/* Hero Banner with Parallax and Animation Effects */}\r\n       \r\n         <HeroBanner/>\r\n         <IntroSection/>\r\n        <HistoryTimeline />\r\n      \r\n\r\n        <TeamSection />\r\n        <AdvisoryBoardSection />\r\n        \r\n        {/* CSS Animations */}\r\n        <style jsx>{`\r\n          @keyframes float {\r\n            0%, 100% { transform: translateY(0) scale(1); }\r\n            50% { transform: translateY(-20px) scale(1.2); }\r\n          }\r\n          @keyframes wave {\r\n            0%, 100% { transform: translateX(0); }\r\n            50% { transform: translateX(-2%); }\r\n          }\r\n        `}</style>\r\n      </main>\r\n    </>\r\n  );\r\n}"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;;AAQe,SAAS;IACpB,0DAA0D;IAE5D,qBACE;kBACE,cAAA,6LAAC;sDAAe;;8BAGb,6LAAC,kJAAA,CAAA,UAAU;;;;;8BACX,6LAAC,oJAAA,CAAA,UAAY;;;;;8BACd,6LAAC,wJAAA,CAAA,UAAe;;;;;8BAGhB,6LAAC,oJAAA,CAAA,UAAW;;;;;8BACZ,6LAAC,6JAAA,CAAA,UAAoB;;;;;;;;;;;;;;;;AAgB7B;KA9BwB", "debugId": null}}]}