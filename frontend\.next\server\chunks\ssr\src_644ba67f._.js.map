{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/fwu_incubation%28react%2Blaravel%29/incubation/frontend/src/app/components/shared/SectionTitle.tsx"], "sourcesContent": ["// components/shared/SectionTitle.tsx\r\ninterface SectionTitleProps {\r\n  title: string;\r\n  subtitle?: string;\r\n  align?: 'left' | 'center' | 'right';\r\n  accentColor?: string;\r\n}\r\n\r\nconst SectionTitle: React.FC<SectionTitleProps> = ({\r\n  title,\r\n  subtitle,\r\n  align = 'center',\r\n  accentColor = 'indigo'\r\n}) => {\r\n  const alignmentClass = {\r\n    left: 'text-left',\r\n    center: 'text-center',\r\n    right: 'text-right',\r\n  };\r\n\r\n  const accentColorClass = {\r\n    indigo: 'bg-indigo-600',\r\n    blue: 'bg-blue-600',\r\n    teal: 'bg-teal-600',\r\n    purple: 'bg-purple-600',\r\n    green: 'bg-green-600',\r\n  };\r\n\r\n  const textColorClass = {\r\n    indigo: 'text-indigo-600',\r\n    blue: 'text-blue-600',\r\n    teal: 'text-teal-600',\r\n    purple: 'text-purple-600',\r\n    green: 'text-green-600',\r\n  };\r\n\r\n  return (\r\n    <div className={`mb-12 md:mb-16 ${alignmentClass[align]}`}>\r\n      {subtitle && (\r\n        <p className={`${textColorClass[accentColor as keyof typeof textColorClass]} font-semibold text-sm md:text-base uppercase tracking-wider mb-2`}>\r\n          {subtitle}\r\n        </p>\r\n      )}\r\n      <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900\">\r\n        {title}\r\n      </h2>\r\n      <div className={`mt-4 h-1.5 w-24 ${align === 'center' ? 'mx-auto' : (align === 'right' ? 'ml-auto' : '')} ${accentColorClass[accentColor as keyof typeof accentColorClass]} rounded-full`}></div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default SectionTitle;"], "names": [], "mappings": "AAAA,qCAAqC;;;;;;AAQrC,MAAM,eAA4C,CAAC,EACjD,KAAK,EACL,QAAQ,EACR,QAAQ,QAAQ,EAChB,cAAc,QAAQ,EACvB;IACC,MAAM,iBAAiB;QACrB,MAAM;QACN,QAAQ;QACR,OAAO;IACT;IAEA,MAAM,mBAAmB;QACvB,QAAQ;QACR,MAAM;QACN,MAAM;QACN,QAAQ;QACR,OAAO;IACT;IAEA,MAAM,iBAAiB;QACrB,QAAQ;QACR,MAAM;QACN,MAAM;QACN,QAAQ;QACR,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,eAAe,EAAE,cAAc,CAAC,MAAM,EAAE;;YACtD,0BACC,8OAAC;gBAAE,WAAW,GAAG,cAAc,CAAC,YAA2C,CAAC,iEAAiE,CAAC;0BAC3I;;;;;;0BAGL,8OAAC;gBAAG,WAAU;0BACX;;;;;;0BAEH,8OAAC;gBAAI,WAAW,CAAC,gBAAgB,EAAE,UAAU,WAAW,YAAa,UAAU,UAAU,YAAY,GAAI,CAAC,EAAE,gBAAgB,CAAC,YAA6C,CAAC,aAAa,CAAC;;;;;;;;;;;;AAG/L;uCAEe", "debugId": null}}, {"offset": {"line": 73, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/fwu_incubation%28react%2Blaravel%29/incubation/frontend/src/app/components/programs/GalleryImageCard.tsx"], "sourcesContent": ["\"use client\"\r\nimport Image from 'next/image';\r\nimport Link from 'next/link';\r\n\r\nexport interface GalleryImageCardProps {\r\n  imageUrl: string;\r\n  altText: string;\r\n  caption?: string;\r\n  eventDate?: string;\r\n  isLoaded?: boolean;\r\n}\r\n\r\nconst GalleryImageCard: React.FC<GalleryImageCardProps> = ({\r\n  imageUrl,\r\n  altText,\r\n  caption,\r\n  eventDate,\r\n  isLoaded = true\r\n}) => {\r\n  return (\r\n    <Link href=\"/gallery\" className=\"block\">\r\n      <div className=\"relative group rounded-lg overflow-hidden shadow-lg h-64 transform hover:shadow-2xl transition-all duration-300 hover:-translate-y-1 bg-gray-200\">\r\n        <div className=\"w-full h-full relative\">\r\n          {isLoaded && (\r\n            <Image\r\n              src={imageUrl}\r\n              alt={altText}\r\n              fill\r\n              className=\"object-cover transform group-hover:scale-110 transition-transform duration-500 ease-in-out\"\r\n              onError={(e) => {\r\n                // Fallback to a placeholder on error\r\n                const target = e.target as HTMLImageElement;\r\n                target.src = \"https://via.placeholder.com/800x600/e2e8f0/475569?text=FWU+Incubation\";\r\n              }}\r\n            />\r\n          )}\r\n        </div>\r\n        {(caption || eventDate) && (\r\n          <div className=\"absolute inset-0 bg-gradient-to-t from-black/70 via-black/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex flex-col justify-end p-4\">\r\n            {caption && <h4 className=\"text-white text-lg font-semibold\">{caption}</h4>}\r\n            {eventDate && <p className=\"text-gray-300 text-sm\">{eventDate}</p>}\r\n          </div>\r\n        )}\r\n      </div>\r\n    </Link>\r\n  );\r\n};\r\n\r\nexport default GalleryImageCard;"], "names": [], "mappings": ";;;;AACA;AACA;AAFA;;;;AAYA,MAAM,mBAAoD,CAAC,EACzD,QAAQ,EACR,OAAO,EACP,OAAO,EACP,SAAS,EACT,WAAW,IAAI,EAChB;IACC,qBACE,8OAAC,4JAAA,CAAA,UAAI;QAAC,MAAK;QAAW,WAAU;kBAC9B,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACZ,0BACC,8OAAC,6HAAA,CAAA,UAAK;wBACJ,KAAK;wBACL,KAAK;wBACL,IAAI;wBACJ,WAAU;wBACV,SAAS,CAAC;4BACR,qCAAqC;4BACrC,MAAM,SAAS,EAAE,MAAM;4BACvB,OAAO,GAAG,GAAG;wBACf;;;;;;;;;;;gBAIL,CAAC,WAAW,SAAS,mBACpB,8OAAC;oBAAI,WAAU;;wBACZ,yBAAW,8OAAC;4BAAG,WAAU;sCAAoC;;;;;;wBAC7D,2BAAa,8OAAC;4BAAE,WAAU;sCAAyB;;;;;;;;;;;;;;;;;;;;;;;AAMhE;uCAEe", "debugId": null}}, {"offset": {"line": 156, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/fwu_incubation%28react%2Blaravel%29/incubation/frontend/src/app/components/programs/PastEventsGallerySection%20.tsx"], "sourcesContent": ["\"use client\"\r\nimport SectionTitle from '../shared/SectionTitle';\r\nimport GalleryImageCard from './GalleryImageCard';\r\nimport Link from 'next/link';\r\nimport { useEffect, useState } from 'react';\r\n\r\n// Gallery images data with Unsplash images\r\nconst pastEventsData = [\r\n  {\r\n    id: 'pe1',\r\n    imageUrl: 'https://images.unsplash.com/photo-1517245386807-bb43f82c33c4?q=80&w=800&auto=format&fit=crop',\r\n    altText: 'Incubation Center Planning Meeting',\r\n    caption: 'Incubation Center Planning Meeting',\r\n    eventDate: 'March 15, 2025',\r\n  },\r\n  {\r\n    id: 'pe2',\r\n    imageUrl: 'https://images.unsplash.com/photo-1519389950473-47ba0277781c?q=80&w=800&auto=format&fit=crop',\r\n    altText: 'Startup Mentorship Session',\r\n    caption: 'Startup Mentorship Session',\r\n    eventDate: 'March 20, 2025',\r\n  },\r\n  {\r\n    id: 'pe3',\r\n    imageUrl: 'https://images.unsplash.com/photo-1531482615713-2afd69097998?q=80&w=800&auto=format&fit=crop',\r\n    altText: 'Entrepreneurship Skills Development',\r\n    caption: 'Entrepreneurship Skills Development',\r\n    eventDate: 'January 25, 2025',\r\n  },\r\n  {\r\n    id: 'pe4',\r\n    imageUrl: 'https://images.unsplash.com/photo-1540575467063-178a50c2df87?q=80&w=800&auto=format&fit=crop',\r\n    altText: 'International Conference on Innovation',\r\n    caption: 'International Conference on Innovation',\r\n    eventDate: 'February 15, 2025',\r\n  },\r\n  {\r\n    id: 'pe5',\r\n    imageUrl: 'https://images.unsplash.com/photo-1504384308090-c894fdcc538d?q=80&w=800&auto=format&fit=crop',\r\n    altText: 'Innovation Hackathon',\r\n    caption: 'Innovation Hackathon',\r\n    eventDate: 'April 12-13, 2025',\r\n  },\r\n  {\r\n    id: 'pe6',\r\n    imageUrl: 'https://images.unsplash.com/photo-1560439514-4e9645039924?q=80&w=800&auto=format&fit=crop',\r\n    altText: 'MOU Signing with Industry Partners',\r\n    caption: 'MOU Signing with Industry Partners',\r\n    eventDate: 'December 10, 2024',\r\n  },\r\n];\r\n\r\nconst PastEventsGallerySection = () => {\r\n  const [isLoaded, setIsLoaded] = useState(false);\r\n\r\n  // Simulate image loading\r\n  useEffect(() => {\r\n    setIsLoaded(true);\r\n  }, []);\r\n\r\n  return (\r\n    <section className=\"py-16 md:py-24 bg-gradient-to-b from-brand-light to-white\">\r\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\r\n        <SectionTitle title=\"FWU Incubation Center Gallery\" subtitle=\"Moments & Memories\" />\r\n\r\n        <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8 mt-12\">\r\n          {pastEventsData.map((event, index) => (\r\n            <div\r\n              key={event.id}\r\n              className=\"opacity-0 animate-fadeIn\"\r\n              style={{\r\n                animationDelay: `${index * 150}ms`,\r\n                animationFillMode: 'forwards'\r\n              }}\r\n            >\r\n              <GalleryImageCard\r\n                imageUrl={event.imageUrl}\r\n                altText={event.altText}\r\n                caption={event.caption}\r\n                eventDate={event.eventDate}\r\n                isLoaded={isLoaded}\r\n              />\r\n            </div>\r\n          ))}\r\n        </div>\r\n\r\n        <div className=\"text-center mt-12 opacity-0 animate-fadeIn animation-delay-1000\" style={{ animationFillMode: 'forwards' }}>\r\n          <Link\r\n            href=\"/gallery\"\r\n            className=\"inline-block bg-brand-primary hover:bg-brand-primary-dark border border-blue-400 font-semibold py-3 px-8 rounded-lg shadow-md transition-all duration-300 hover:scale-105 transform\"\r\n          >\r\n            View Full Gallery\r\n          </Link>\r\n          <p className=\"text-gray-600 mt-2\">\r\n            Explore more photos from our events, workshops, and partnerships\r\n          </p>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\nexport default PastEventsGallerySection;"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AAJA;;;;;;AAMA,2CAA2C;AAC3C,MAAM,iBAAiB;IACrB;QACE,IAAI;QACJ,UAAU;QACV,SAAS;QACT,SAAS;QACT,WAAW;IACb;IACA;QACE,IAAI;QACJ,UAAU;QACV,SAAS;QACT,SAAS;QACT,WAAW;IACb;IACA;QACE,IAAI;QACJ,UAAU;QACV,SAAS;QACT,SAAS;QACT,WAAW;IACb;IACA;QACE,IAAI;QACJ,UAAU;QACV,SAAS;QACT,SAAS;QACT,WAAW;IACb;IACA;QACE,IAAI;QACJ,UAAU;QACV,SAAS;QACT,SAAS;QACT,WAAW;IACb;IACA;QACE,IAAI;QACJ,UAAU;QACV,SAAS;QACT,SAAS;QACT,WAAW;IACb;CACD;AAED,MAAM,2BAA2B;IAC/B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,yBAAyB;IACzB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,YAAY;IACd,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,mJAAA,CAAA,UAAY;oBAAC,OAAM;oBAAgC,UAAS;;;;;;8BAE7D,8OAAC;oBAAI,WAAU;8BACZ,eAAe,GAAG,CAAC,CAAC,OAAO,sBAC1B,8OAAC;4BAEC,WAAU;4BACV,OAAO;gCACL,gBAAgB,GAAG,QAAQ,IAAI,EAAE,CAAC;gCAClC,mBAAmB;4BACrB;sCAEA,cAAA,8OAAC,yJAAA,CAAA,UAAgB;gCACf,UAAU,MAAM,QAAQ;gCACxB,SAAS,MAAM,OAAO;gCACtB,SAAS,MAAM,OAAO;gCACtB,WAAW,MAAM,SAAS;gCAC1B,UAAU;;;;;;2BAZP,MAAM,EAAE;;;;;;;;;;8BAkBnB,8OAAC;oBAAI,WAAU;oBAAkE,OAAO;wBAAE,mBAAmB;oBAAW;;sCACtH,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;sCACX;;;;;;sCAGD,8OAAC;4BAAE,WAAU;sCAAqB;;;;;;;;;;;;;;;;;;;;;;;AAO5C;uCAEe", "debugId": null}}, {"offset": {"line": 311, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/fwu_incubation%28react%2Blaravel%29/incubation/frontend/src/app/components/programs/UpcomingEventCard.tsx"], "sourcesContent": ["\"use client\"\r\nimport { Calendar, Clock, MapPin, ChevronRight, Users, Star, ExternalLink } from 'lucide-react';\r\nimport { useState } from 'react';\r\n\r\nexport interface UpcomingEvent {\r\n  id: string;\r\n  date: string; // e.g., \"Oct 25\"\r\n  fullDate: string; // e.g., \"October 25, 2024\"\r\n  time?: string; // e.g., \"10:00 AM - 04:00 PM\"\r\n  title: string;\r\n  type: string; // e.g., \"Workshop\", \"Deadline\", \"Networking\"\r\n  location?: string; // e.g., \"Online\" or \"FWU Auditorium\"\r\n  description: string;\r\n  color?: string; // Tailwind color class e.g. 'bg-blue-500'\r\n  detailedDescription?: string; // More details for expanded view\r\n  registrationLink?: string; // Link to register for the event\r\n  capacity?: number; // Number of available spots\r\n  status?: 'upcoming' | 'ongoing' | 'completed' | 'cancelled'; // Event status\r\n}\r\n\r\ninterface UpcomingEventCardProps {\r\n  event: UpcomingEvent;\r\n}\r\n\r\nconst UpcomingEventCard: React.FC<UpcomingEventCardProps> = ({ event }) => {\r\n  const [isExpanded, setIsExpanded] = useState(false);\r\n  const typeColor = event.color || 'bg-blue-600';\r\n\r\n  const toggleExpand = () => {\r\n    setIsExpanded(!isExpanded);\r\n  };\r\n\r\n  const getStatusColor = (status?: string) => {\r\n    const colors = {\r\n      'upcoming': 'bg-green-100 text-green-800',\r\n      'ongoing': 'bg-blue-100 text-blue-800',\r\n      'completed': 'bg-gray-100 text-gray-800',\r\n      'cancelled': 'bg-red-100 text-red-800'\r\n    };\r\n    return colors[status as keyof typeof colors] || 'bg-green-100 text-green-800';\r\n  };\r\n\r\n  return (\r\n    <div className=\"group relative bg-white rounded-3xl shadow-xl border border-gray-100 overflow-hidden hover:shadow-2xl transition-all duration-500 hover:-translate-y-2\">\r\n      {/* Gradient Accent */}\r\n      <div className={`absolute top-0 left-0 right-0 h-1 ${typeColor} transform origin-left scale-x-0 group-hover:scale-x-100 transition-transform duration-700`}></div>\r\n\r\n      {/* Status Badge */}\r\n      {event.status && (\r\n        <div className=\"absolute top-4 right-4 z-10\">\r\n          <span className={`px-3 py-1 rounded-full text-xs font-semibold ${getStatusColor(event.status)}`}>\r\n            {event.status.toUpperCase()}\r\n          </span>\r\n        </div>\r\n      )}\r\n\r\n      <div className=\"flex flex-col lg:flex-row\">\r\n        {/* Enhanced Date Column */}\r\n        <div className={`p-8 lg:w-1/4 flex flex-col items-center justify-center text-white ${typeColor} relative overflow-hidden group-hover:scale-105 transition-transform duration-300`}>\r\n          {/* Background Elements */}\r\n          <div className=\"absolute inset-0 overflow-hidden\">\r\n            <div className=\"absolute top-4 right-4 w-16 h-16 bg-white/10 rounded-full blur-xl\"></div>\r\n            <div className=\"absolute bottom-4 left-4 w-12 h-12 bg-white/10 rounded-full blur-lg\"></div>\r\n\r\n            {/* Pattern */}\r\n            <div className=\"absolute inset-0 opacity-20\"\r\n                 style={{\r\n                   backgroundImage: 'radial-gradient(#ffffff 1px, transparent 1px)',\r\n                   backgroundSize: '15px 15px'\r\n                 }}>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"relative z-10 text-center\">\r\n            <div className=\"text-4xl lg:text-5xl font-black mb-2 drop-shadow-lg\">\r\n              {event.date.split(' ')[1] || event.date.split(' ')[0]}\r\n            </div>\r\n            <div className=\"text-lg uppercase font-bold tracking-wider opacity-90\">\r\n              {event.date.split(' ')[0]}\r\n            </div>\r\n\r\n            {/* Event Type Badge */}\r\n            <div className=\"mt-4 px-3 py-1 bg-white/20 backdrop-blur-sm rounded-full border border-white/30\">\r\n              <span className=\"text-xs font-semibold uppercase tracking-wide\">{event.type}</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Enhanced Content Column */}\r\n        <div className=\"p-8 lg:p-10 flex-grow\">\r\n          <div className=\"flex justify-between items-start mb-6\">\r\n            {/* Priority/Featured Badge */}\r\n            <div className=\"flex items-center space-x-2\">\r\n              <Star className=\"text-yellow-500\" size={16} />\r\n              <span className=\"text-sm font-semibold text-gray-700\">Featured Event</span>\r\n            </div>\r\n\r\n            {/* Expand Button */}\r\n            <button\r\n              onClick={toggleExpand}\r\n              className=\"w-10 h-10 flex items-center justify-center bg-gradient-to-r from-blue-50 to-purple-50 hover:from-blue-100 hover:to-purple-100 text-blue-600 rounded-2xl focus:outline-none transition-all duration-300 hover:scale-110 active:scale-95 shadow-md\"\r\n              aria-label={isExpanded ? \"Collapse details\" : \"Expand details\"}\r\n            >\r\n              <div className={`transform transition-transform duration-300 ${isExpanded ? 'rotate-90' : 'rotate-0'}`}>\r\n                <ChevronRight size={20} />\r\n              </div>\r\n            </button>\r\n          </div>\r\n\r\n          <h3 className=\"text-2xl lg:text-3xl font-bold text-gray-900 mb-4 group-hover:text-transparent group-hover:bg-clip-text group-hover:bg-gradient-to-r group-hover:from-blue-600 group-hover:to-purple-600 transition-all duration-300 leading-tight\">\r\n            {event.title}\r\n          </h3>\r\n\r\n          <p className=\"text-gray-600 mb-6 leading-relaxed text-lg\">{event.description}</p>\r\n\r\n          {/* Enhanced Event Details */}\r\n          <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 mb-6\">\r\n            <div className=\"flex items-center space-x-3 p-3 bg-gradient-to-r from-blue-50 to-blue-100 rounded-xl border border-blue-200\">\r\n              <Calendar className=\"text-blue-600\" size={18} />\r\n              <div>\r\n                <div className=\"text-xs font-semibold text-blue-700 uppercase tracking-wide\">Date</div>\r\n                <div className=\"text-sm font-medium text-blue-900\">{event.fullDate}</div>\r\n              </div>\r\n            </div>\r\n\r\n            {event.time && (\r\n              <div className=\"flex items-center space-x-3 p-3 bg-gradient-to-r from-purple-50 to-purple-100 rounded-xl border border-purple-200\">\r\n                <Clock className=\"text-purple-600\" size={18} />\r\n                <div>\r\n                  <div className=\"text-xs font-semibold text-purple-700 uppercase tracking-wide\">Time</div>\r\n                  <div className=\"text-sm font-medium text-purple-900\">{event.time}</div>\r\n                </div>\r\n              </div>\r\n            )}\r\n\r\n            {event.location && (\r\n              <div className=\"flex items-center space-x-3 p-3 bg-gradient-to-r from-indigo-50 to-indigo-100 rounded-xl border border-indigo-200\">\r\n                <MapPin className=\"text-indigo-600\" size={18} />\r\n                <div>\r\n                  <div className=\"text-xs font-semibold text-indigo-700 uppercase tracking-wide\">Location</div>\r\n                  <div className=\"text-sm font-medium text-indigo-900\">{event.location}</div>\r\n                </div>\r\n              </div>\r\n            )}\r\n\r\n            {event.capacity && (\r\n              <div className=\"flex items-center space-x-3 p-3 bg-gradient-to-r from-green-50 to-green-100 rounded-xl border border-green-200\">\r\n                <Users className=\"text-green-600\" size={18} />\r\n                <div>\r\n                  <div className=\"text-xs font-semibold text-green-700 uppercase tracking-wide\">Capacity</div>\r\n                  <div className=\"text-sm font-medium text-green-900\">{event.capacity} spots</div>\r\n                </div>\r\n              </div>\r\n            )}\r\n          </div>\r\n\r\n          {/* Quick Action Button */}\r\n          {event.registrationLink && (\r\n            <div className=\"flex justify-end\">\r\n              <a\r\n                href={event.registrationLink}\r\n                className=\"group/btn inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-2xl transition-all duration-300 hover:shadow-lg hover:shadow-blue-500/25 hover:-translate-y-1\"\r\n              >\r\n                <span className=\"mr-2\">Learn More</span>\r\n                <ExternalLink className=\"group-hover/btn:translate-x-1 transition-transform duration-300\" size={16} />\r\n              </a>\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Enhanced Expandable Details Section */}\r\n      <div\r\n        className={`px-8 lg:px-10 pb-8 pt-0 border-t border-gray-200 overflow-hidden transition-all duration-700 ease-in-out ${\r\n          isExpanded ? 'max-h-[500px] opacity-100' : 'max-h-0 opacity-0'\r\n        }`}\r\n      >\r\n        <div className=\"pt-8\">\r\n          {/* Detailed Description */}\r\n          <div className=\"mb-8\">\r\n            <h4 className=\"text-lg font-bold text-gray-900 mb-4 flex items-center\">\r\n              <div className=\"w-6 h-6 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center mr-3\">\r\n                <Star className=\"text-white\" size={12} />\r\n              </div>\r\n              Event Details\r\n            </h4>\r\n            <div className=\"bg-gradient-to-r from-gray-50 to-blue-50 rounded-2xl p-6 border border-gray-200\">\r\n              <p className=\"text-gray-700 leading-relaxed\">\r\n                {event.detailedDescription || \"More details about this event will be announced soon. Stay tuned for updates and prepare for an exciting learning experience!\"}\r\n              </p>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Action Buttons */}\r\n          <div className=\"flex flex-col sm:flex-row gap-4\">\r\n            {event.registrationLink && (\r\n              <a\r\n                href={event.registrationLink}\r\n                className=\"group relative flex-1 inline-flex items-center justify-center bg-gradient-to-r from-blue-600 to-purple-600 text-white font-bold py-4 px-6 rounded-2xl transition-all duration-300 hover:shadow-xl hover:shadow-blue-500/25 hover:-translate-y-1 overflow-hidden\"\r\n              >\r\n                <div className=\"absolute inset-0 bg-gradient-to-r from-blue-700 to-purple-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\r\n                <ExternalLink className=\"relative z-10 mr-2\" size={20} />\r\n                <span className=\"relative z-10\">Register Now</span>\r\n              </a>\r\n            )}\r\n\r\n            <button\r\n              onClick={() => {\r\n                // Add to calendar functionality\r\n                alert('Add to calendar functionality would be implemented here');\r\n              }}\r\n              className=\"group relative flex-1 inline-flex items-center justify-center bg-white border-2 border-gray-300 text-gray-700 font-bold py-4 px-6 rounded-2xl transition-all duration-300 hover:border-blue-400 hover:text-blue-600 hover:shadow-lg hover:-translate-y-1\"\r\n            >\r\n              <Calendar className=\"mr-2\" size={20} />\r\n              <span>Add to Calendar</span>\r\n            </button>\r\n          </div>\r\n\r\n          {/* Additional Info */}\r\n          <div className=\"mt-6 p-4 bg-gradient-to-r from-yellow-50 to-orange-50 rounded-xl border border-yellow-200\">\r\n            <div className=\"flex items-start space-x-3\">\r\n              <div className=\"w-6 h-6 bg-yellow-400 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5\">\r\n                <Star className=\"text-white\" size={12} />\r\n              </div>\r\n              <div>\r\n                <h5 className=\"font-semibold text-yellow-800 mb-1\">Pro Tip</h5>\r\n                <p className=\"text-yellow-700 text-sm leading-relaxed\">\r\n                  Register early to secure your spot and receive exclusive pre-event materials and networking opportunities!\r\n                </p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default UpcomingEventCard;"], "names": [], "mappings": ";;;;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAFA;;;;AAwBA,MAAM,oBAAsD,CAAC,EAAE,KAAK,EAAE;IACpE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,YAAY,MAAM,KAAK,IAAI;IAEjC,MAAM,eAAe;QACnB,cAAc,CAAC;IACjB;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,SAAS;YACb,YAAY;YACZ,WAAW;YACX,aAAa;YACb,aAAa;QACf;QACA,OAAO,MAAM,CAAC,OAA8B,IAAI;IAClD;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAW,CAAC,kCAAkC,EAAE,UAAU,0FAA0F,CAAC;;;;;;YAGzJ,MAAM,MAAM,kBACX,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAK,WAAW,CAAC,6CAA6C,EAAE,eAAe,MAAM,MAAM,GAAG;8BAC5F,MAAM,MAAM,CAAC,WAAW;;;;;;;;;;;0BAK/B,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAW,CAAC,kEAAkE,EAAE,UAAU,iFAAiF,CAAC;;0CAE/K,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;;;;;kDAGf,8OAAC;wCAAI,WAAU;wCACV,OAAO;4CACL,iBAAiB;4CACjB,gBAAgB;wCAClB;;;;;;;;;;;;0CAIP,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACZ,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;;;;;;kDAEvD,8OAAC;wCAAI,WAAU;kDACZ,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;;;;;;kDAI3B,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDAAiD,MAAM,IAAI;;;;;;;;;;;;;;;;;;;;;;;kCAMjF,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;gDAAkB,MAAM;;;;;;0DACxC,8OAAC;gDAAK,WAAU;0DAAsC;;;;;;;;;;;;kDAIxD,8OAAC;wCACC,SAAS;wCACT,WAAU;wCACV,cAAY,aAAa,qBAAqB;kDAE9C,cAAA,8OAAC;4CAAI,WAAW,CAAC,4CAA4C,EAAE,aAAa,cAAc,YAAY;sDACpG,cAAA,8OAAC,sNAAA,CAAA,eAAY;gDAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;0CAK1B,8OAAC;gCAAG,WAAU;0CACX,MAAM,KAAK;;;;;;0CAGd,8OAAC;gCAAE,WAAU;0CAA8C,MAAM,WAAW;;;;;;0CAG5E,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;gDAAgB,MAAM;;;;;;0DAC1C,8OAAC;;kEACC,8OAAC;wDAAI,WAAU;kEAA8D;;;;;;kEAC7E,8OAAC;wDAAI,WAAU;kEAAqC,MAAM,QAAQ;;;;;;;;;;;;;;;;;;oCAIrE,MAAM,IAAI,kBACT,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;gDAAkB,MAAM;;;;;;0DACzC,8OAAC;;kEACC,8OAAC;wDAAI,WAAU;kEAAgE;;;;;;kEAC/E,8OAAC;wDAAI,WAAU;kEAAuC,MAAM,IAAI;;;;;;;;;;;;;;;;;;oCAKrE,MAAM,QAAQ,kBACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,0MAAA,CAAA,SAAM;gDAAC,WAAU;gDAAkB,MAAM;;;;;;0DAC1C,8OAAC;;kEACC,8OAAC;wDAAI,WAAU;kEAAgE;;;;;;kEAC/E,8OAAC;wDAAI,WAAU;kEAAuC,MAAM,QAAQ;;;;;;;;;;;;;;;;;;oCAKzE,MAAM,QAAQ,kBACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;gDAAiB,MAAM;;;;;;0DACxC,8OAAC;;kEACC,8OAAC;wDAAI,WAAU;kEAA+D;;;;;;kEAC9E,8OAAC;wDAAI,WAAU;;4DAAsC,MAAM,QAAQ;4DAAC;;;;;;;;;;;;;;;;;;;;;;;;;4BAO3E,MAAM,gBAAgB,kBACrB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,MAAM,MAAM,gBAAgB;oCAC5B,WAAU;;sDAEV,8OAAC;4CAAK,WAAU;sDAAO;;;;;;sDACvB,8OAAC,sNAAA,CAAA,eAAY;4CAAC,WAAU;4CAAkE,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ1G,8OAAC;gBACC,WAAW,CAAC,yGAAyG,EACnH,aAAa,8BAA8B,qBAC3C;0BAEF,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;gDAAa,MAAM;;;;;;;;;;;wCAC/B;;;;;;;8CAGR,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAE,WAAU;kDACV,MAAM,mBAAmB,IAAI;;;;;;;;;;;;;;;;;sCAMpC,8OAAC;4BAAI,WAAU;;gCACZ,MAAM,gBAAgB,kBACrB,8OAAC;oCACC,MAAM,MAAM,gBAAgB;oCAC5B,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC,sNAAA,CAAA,eAAY;4CAAC,WAAU;4CAAqB,MAAM;;;;;;sDACnD,8OAAC;4CAAK,WAAU;sDAAgB;;;;;;;;;;;;8CAIpC,8OAAC;oCACC,SAAS;wCACP,gCAAgC;wCAChC,MAAM;oCACR;oCACA,WAAU;;sDAEV,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;4CAAO,MAAM;;;;;;sDACjC,8OAAC;sDAAK;;;;;;;;;;;;;;;;;;sCAKV,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;4CAAa,MAAM;;;;;;;;;;;kDAErC,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAqC;;;;;;0DACnD,8OAAC;gDAAE,WAAU;0DAA0C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUvE;uCAEe", "debugId": null}}, {"offset": {"line": 954, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/fwu_incubation%28react%2Blaravel%29/incubation/frontend/src/data/programsData.tsx"], "sourcesContent": ["import { Laptop, Users, Rocket, GraduationCap, Code, Lightbulb, Target, Award } from 'lucide-react';\r\nimport { Program } from '../types/program.types';\r\n\r\nexport const programsData: Program[] = [\r\n  {\r\n    id: 'bootcamp',\r\n    slug: 'intensive-bootcamps',\r\n    icon: <Laptop />,\r\n    title: 'Intensive Bootcamps',\r\n    shortDescription: 'Deep-dive, skill-based training programs designed to rapidly accelerate your knowledge in specific tech and business domains.',\r\n    longDescription: 'Our intensive bootcamps are designed to transform beginners into skilled practitioners in a short period of time. Through hands-on projects, expert mentorship, and a carefully structured curriculum, participants gain practical experience and build a portfolio of work that demonstrates their capabilities.',\r\n    bgColorClass: 'bg-gradient-to-br from-indigo-50 via-white to-purple-50',\r\n    accentColor: 'indigo',\r\n    category: 'bootcamp' as const,\r\n    level: 'all-levels' as const,\r\n    format: 'hybrid' as const,\r\n    duration: '4-12 weeks, depending on program',\r\n    schedule: 'Full-time (Mon-Fri, 9am-5pm) or Part-time options available',\r\n    capacity: 25,\r\n    location: {\r\n      venue: 'FWU Campus',\r\n      address: 'Far Western University Campus',\r\n      city: 'Mahendranagar',\r\n      country: 'Nepal',\r\n      isOnline: true,\r\n      isHybrid: true,\r\n    },\r\n    benefits: [\r\n      'Accelerated learning in a condensed timeframe',\r\n      'Hands-on project-based curriculum',\r\n      'Direct mentorship from industry professionals',\r\n      'Networking opportunities with peers and potential employers',\r\n      'Certificate of completion recognized by industry partners'\r\n    ],\r\n    learningOutcomes: [\r\n      'Master practical skills in chosen technology stack',\r\n      'Build a professional portfolio of projects',\r\n      'Develop problem-solving and critical thinking abilities'\r\n    ],\r\n    eligibilityRequirements: [\r\n      'Basic computer literacy',\r\n      'Commitment to full-time or part-time schedule',\r\n      'Passion for learning and technology'\r\n    ],\r\n    applicationProcess: [\r\n      'Submit application form with background information',\r\n      'Complete technical assessment (if applicable)',\r\n      'Interview with program coordinators',\r\n      'Receive acceptance decision within 2 weeks',\r\n      'Pay program fee to secure your spot'\r\n    ],\r\n    targetAudience: [\r\n      'Career changers seeking new skills',\r\n      'Recent graduates',\r\n      'Working professionals looking to upskill'\r\n    ],\r\n    imageUrl: '/programs/bootcamp-hero.jpg',\r\n    heroImageUrl: 'https://images.unsplash.com/photo-1522202176988-66273c2fd55f?q=80&w=1200&auto=format&fit=crop',\r\n    upcomingDates: [\r\n      {\r\n        date: 'January 15, 2025',\r\n        title: 'Web Development Bootcamp',\r\n        description: 'Learn modern web development with React and Node.js',\r\n        capacity: 25,\r\n        status: 'upcoming' as const\r\n      },\r\n      {\r\n        date: 'March 1, 2025',\r\n        title: 'Data Science Fundamentals',\r\n        description: 'Master data analysis and machine learning basics',\r\n        capacity: 20,\r\n        status: 'upcoming' as const\r\n      },\r\n      {\r\n        date: 'May 10, 2025',\r\n        title: 'Mobile App Development',\r\n        description: 'Build cross-platform mobile applications',\r\n        capacity: 20,\r\n        status: 'upcoming' as const\r\n      }\r\n    ],\r\n    tags: ['technology', 'skills', 'career', 'intensive'],\r\n    featured: true,\r\n    status: 'active' as const,\r\n    createdAt: '2024-01-01T00:00:00Z',\r\n    updatedAt: '2024-11-01T00:00:00Z'\r\n  },\r\n  {\r\n    id: 'hackathon',\r\n    slug: 'innovation-hackathons',\r\n    icon: <Users />,\r\n    title: 'Innovation Hackathons',\r\n    shortDescription: 'High-energy, collaborative events where participants team up to solve real-world challenges and build innovative prototypes within a limited timeframe.',\r\n    longDescription: 'Our hackathons bring together diverse talents to tackle real-world challenges in an intense, collaborative environment. Participants form cross-functional teams and work against the clock to develop innovative solutions, which are then presented to a panel of judges from industry and academia.',\r\n    bgColorClass: 'bg-gradient-to-br from-teal-50 via-white to-cyan-50',\r\n    accentColor: 'teal',\r\n    category: 'hackathon' as const,\r\n    level: 'all-levels' as const,\r\n    format: 'in-person' as const,\r\n    duration: '24-48 hours (weekend events)',\r\n    schedule: 'Quarterly events throughout the year',\r\n    capacity: 100,\r\n    location: {\r\n      venue: 'FWU Innovation Hub',\r\n      address: 'Far Western University Innovation Hub',\r\n      city: 'Mahendranagar',\r\n      country: 'Nepal',\r\n      isOnline: false,\r\n      isHybrid: false,\r\n    },\r\n    benefits: [\r\n      'Develop rapid problem-solving and prototyping skills',\r\n      'Build your network with like-minded innovators',\r\n      'Gain exposure to industry challenges and opportunities',\r\n      'Win prizes and potential funding for your ideas',\r\n      'Receive feedback from industry experts and potential users'\r\n    ],\r\n    learningOutcomes: [\r\n      'Master rapid prototyping techniques',\r\n      'Develop teamwork and collaboration skills',\r\n      'Learn to work under pressure and tight deadlines'\r\n    ],\r\n    eligibilityRequirements: [\r\n      'Open to all skill levels',\r\n      'Team formation encouraged but not required',\r\n      'Commitment to full event participation'\r\n    ],\r\n    applicationProcess: [\r\n      'Register individually or as a team (2-5 members)',\r\n      'Submit your background and areas of expertise',\r\n      'Receive confirmation and pre-event materials',\r\n      'Attend optional pre-hackathon workshops'\r\n    ],\r\n    targetAudience: [\r\n      'Developers and designers',\r\n      'Entrepreneurs and innovators',\r\n      'Students and professionals',\r\n      'Anyone passionate about problem-solving'\r\n    ],\r\n    imageUrl: '/programs/hackathon-hero.jpg',\r\n    heroImageUrl: 'https://images.unsplash.com/photo-1591115765373-5207764f72e4?q=80&w=1200&auto=format&fit=crop',\r\n    upcomingDates: [\r\n      {\r\n        date: 'December 12-14, 2024',\r\n        title: 'FinTech Hackathon Challenge',\r\n        description: 'Build innovative financial technology solutions',\r\n        capacity: 100,\r\n        status: 'upcoming' as const\r\n      },\r\n      {\r\n        date: 'February 20-22, 2025',\r\n        title: 'HealthTech Innovation Weekend',\r\n        description: 'Create healthcare technology solutions',\r\n        capacity: 80,\r\n        status: 'upcoming' as const\r\n      },\r\n      {\r\n        date: 'April 15-17, 2025',\r\n        title: 'Sustainability Solutions Hackathon',\r\n        description: 'Develop sustainable technology solutions',\r\n        capacity: 100,\r\n        status: 'upcoming' as const\r\n      }\r\n    ],\r\n    tags: ['innovation', 'collaboration', 'competition', 'prototyping'],\r\n    featured: true,\r\n    status: 'active' as const,\r\n    createdAt: '2024-01-01T00:00:00Z',\r\n    updatedAt: '2024-11-01T00:00:00Z'\r\n  },\r\n  {\r\n    id: 'demoday',\r\n    slug: 'startup-demo-days',\r\n    icon: <Rocket />,\r\n    title: 'Startup Demo Days',\r\n    shortDescription: 'An exclusive platform for our incubated startups to pitch their ventures to investors, industry leaders, and potential partners.',\r\n    longDescription: 'Demo Days are the culmination of our incubation programs, where startups showcase their progress and pitch to a curated audience of investors, industry partners, and media. These high-visibility events provide startups with the opportunity to secure funding, partnerships, and media coverage.',\r\n    bgColorClass: 'bg-gradient-to-br from-amber-50 via-white to-orange-50',\r\n    accentColor: 'amber',\r\n    category: 'demo-day' as const,\r\n    level: 'advanced' as const,\r\n    format: 'hybrid' as const,\r\n    duration: '1 full day event',\r\n    schedule: 'Bi-annual (Spring and Fall)',\r\n    capacity: 15,\r\n    location: {\r\n      venue: 'FWU Auditorium',\r\n      address: 'Far Western University Main Campus',\r\n      city: 'Mahendranagar',\r\n      country: 'Nepal',\r\n      isOnline: true,\r\n      isHybrid: true,\r\n    },\r\n    benefits: [\r\n      'Pitch to a curated audience of investors and partners',\r\n      'Receive professional pitch coaching and preparation',\r\n      'Network with potential investors and strategic partners',\r\n      'Media exposure and PR opportunities',\r\n      'Post-event introductions to interested stakeholders'\r\n    ],\r\n    learningOutcomes: [\r\n      'Master the art of startup pitching',\r\n      'Develop investor presentation skills',\r\n      'Learn to handle Q&A sessions effectively'\r\n    ],\r\n    eligibilityRequirements: [\r\n      'Must be enrolled in FWU Incubation programs',\r\n      'Startup must meet readiness criteria',\r\n      'Completed pitch preparation workshops'\r\n    ],\r\n    applicationProcess: [\r\n      'Only open to startups in FWU Incubation programs',\r\n      'Selection based on readiness and progress metrics',\r\n      'Mandatory pitch preparation workshops',\r\n      'Final selection by incubation program directors'\r\n    ],\r\n    targetAudience: [\r\n      'Incubated startup founders',\r\n      'Startup teams ready for investment',\r\n      'Entrepreneurs seeking funding'\r\n    ],\r\n    imageUrl: '/programs/demoday-hero.jpg',\r\n    heroImageUrl: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?q=80&w=1200&auto=format&fit=crop',\r\n    upcomingDates: [\r\n      {\r\n        date: 'November 30, 2024',\r\n        title: 'Fall 2024 Demo Day',\r\n        description: 'Showcase your startup to investors and partners',\r\n        capacity: 15,\r\n        status: 'upcoming' as const\r\n      },\r\n      {\r\n        date: 'May 25, 2025',\r\n        title: 'Spring 2025 Demo Day',\r\n        description: 'Present your venture to the investment community',\r\n        capacity: 15,\r\n        status: 'upcoming' as const\r\n      }\r\n    ],\r\n    tags: ['pitching', 'investment', 'networking', 'showcase'],\r\n    featured: true,\r\n    status: 'active' as const,\r\n    createdAt: '2024-01-01T00:00:00Z',\r\n    updatedAt: '2024-11-01T00:00:00Z'\r\n  },\r\n  {\r\n    id: 'workshops',\r\n    slug: 'expert-workshops',\r\n    icon: <GraduationCap />,\r\n    title: 'Expert Workshops',\r\n    shortDescription: 'Focused sessions led by industry experts on crucial topics like marketing, finance, legal aspects, and technology trends.',\r\n    longDescription: 'Our expert workshops provide targeted knowledge and skills development in specific areas critical to startup success. Led by industry practitioners and subject matter experts, these sessions combine theoretical frameworks with practical applications.',\r\n    bgColorClass: 'bg-gradient-to-br from-pink-50 via-white to-rose-50',\r\n    accentColor: 'pink',\r\n    category: 'workshop' as const,\r\n    level: 'all-levels' as const,\r\n    format: 'hybrid' as const,\r\n    duration: '2-4 hours per workshop',\r\n    schedule: 'Monthly workshops on rotating topics',\r\n    capacity: 40,\r\n    location: {\r\n      venue: 'FWU Incubation Center',\r\n      address: 'Far Western University Incubation Center',\r\n      city: 'Mahendranagar',\r\n      country: 'Nepal',\r\n      isOnline: true,\r\n      isHybrid: true,\r\n    },\r\n    benefits: [\r\n      'Learn practical skills directly applicable to your business',\r\n      'Access to industry experts and their networks',\r\n      'Receive personalized feedback on your specific challenges',\r\n      'Connect with peers facing similar challenges',\r\n      'Take home actionable templates and resources'\r\n    ],\r\n    learningOutcomes: [\r\n      'Gain practical business skills',\r\n      'Develop industry-specific knowledge',\r\n      'Build professional networks'\r\n    ],\r\n    eligibilityRequirements: [\r\n      'Open to all entrepreneurs',\r\n      'Some workshops may have prerequisites',\r\n      'Registration required'\r\n    ],\r\n    applicationProcess: [\r\n      'Open to all entrepreneurs and startup team members',\r\n      'Registration required, with priority for FWU incubated startups',\r\n      'Some advanced workshops may have prerequisites'\r\n    ],\r\n    targetAudience: [\r\n      'Entrepreneurs and startup founders',\r\n      'Business professionals',\r\n      'Students interested in entrepreneurship'\r\n    ],\r\n    imageUrl: '/programs/workshop-hero.jpg',\r\n    heroImageUrl: 'https://images.unsplash.com/photo-1515187029135-18ee286d815b?q=80&w=1200&auto=format&fit=crop',\r\n    upcomingDates: [\r\n      {\r\n        date: 'November 15, 2024',\r\n        title: 'Design Thinking Workshop for Innovators',\r\n        description: 'Learn human-centered design principles',\r\n        capacity: 40,\r\n        status: 'upcoming' as const\r\n      },\r\n      {\r\n        date: 'January 10, 2025',\r\n        title: 'Funding Strategies for Early-Stage Startups',\r\n        description: 'Master the art of raising capital',\r\n        capacity: 30,\r\n        status: 'upcoming' as const\r\n      },\r\n      {\r\n        date: 'February 5, 2025',\r\n        title: 'Digital Marketing Essentials',\r\n        description: 'Build effective digital marketing strategies',\r\n        capacity: 40,\r\n        status: 'upcoming' as const\r\n      },\r\n      {\r\n        date: 'March 12, 2025',\r\n        title: 'Legal Fundamentals for Startups',\r\n        description: 'Navigate legal requirements for startups',\r\n        capacity: 35,\r\n        status: 'upcoming' as const\r\n      }\r\n    ],\r\n    tags: ['education', 'skills', 'networking', 'business'],\r\n    featured: false,\r\n    status: 'active' as const,\r\n    createdAt: '2024-01-01T00:00:00Z',\r\n    updatedAt: '2024-11-01T00:00:00Z'\r\n  },\r\n];\r\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAAA;AAAA;;;AAGO,MAAM,eAA0B;IACrC;QACE,IAAI;QACJ,MAAM;QACN,oBAAM,8OAAC,sMAAA,CAAA,SAAM;;;;;QACb,OAAO;QACP,kBAAkB;QAClB,iBAAiB;QACjB,cAAc;QACd,aAAa;QACb,UAAU;QACV,OAAO;QACP,QAAQ;QACR,UAAU;QACV,UAAU;QACV,UAAU;QACV,UAAU;YACR,OAAO;YACP,SAAS;YACT,MAAM;YACN,SAAS;YACT,UAAU;YACV,UAAU;QACZ;QACA,UAAU;YACR;YACA;YACA;YACA;YACA;SACD;QACD,kBAAkB;YAChB;YACA;YACA;SACD;QACD,yBAAyB;YACvB;YACA;YACA;SACD;QACD,oBAAoB;YAClB;YACA;YACA;YACA;YACA;SACD;QACD,gBAAgB;YACd;YACA;YACA;SACD;QACD,UAAU;QACV,cAAc;QACd,eAAe;YACb;gBACE,MAAM;gBACN,OAAO;gBACP,aAAa;gBACb,UAAU;gBACV,QAAQ;YACV;YACA;gBACE,MAAM;gBACN,OAAO;gBACP,aAAa;gBACb,UAAU;gBACV,QAAQ;YACV;YACA;gBACE,MAAM;gBACN,OAAO;gBACP,aAAa;gBACb,UAAU;gBACV,QAAQ;YACV;SACD;QACD,MAAM;YAAC;YAAc;YAAU;YAAU;SAAY;QACrD,UAAU;QACV,QAAQ;QACR,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,oBAAM,8OAAC,oMAAA,CAAA,QAAK;;;;;QACZ,OAAO;QACP,kBAAkB;QAClB,iBAAiB;QACjB,cAAc;QACd,aAAa;QACb,UAAU;QACV,OAAO;QACP,QAAQ;QACR,UAAU;QACV,UAAU;QACV,UAAU;QACV,UAAU;YACR,OAAO;YACP,SAAS;YACT,MAAM;YACN,SAAS;YACT,UAAU;YACV,UAAU;QACZ;QACA,UAAU;YACR;YACA;YACA;YACA;YACA;SACD;QACD,kBAAkB;YAChB;YACA;YACA;SACD;QACD,yBAAyB;YACvB;YACA;YACA;SACD;QACD,oBAAoB;YAClB;YACA;YACA;YACA;SACD;QACD,gBAAgB;YACd;YACA;YACA;YACA;SACD;QACD,UAAU;QACV,cAAc;QACd,eAAe;YACb;gBACE,MAAM;gBACN,OAAO;gBACP,aAAa;gBACb,UAAU;gBACV,QAAQ;YACV;YACA;gBACE,MAAM;gBACN,OAAO;gBACP,aAAa;gBACb,UAAU;gBACV,QAAQ;YACV;YACA;gBACE,MAAM;gBACN,OAAO;gBACP,aAAa;gBACb,UAAU;gBACV,QAAQ;YACV;SACD;QACD,MAAM;YAAC;YAAc;YAAiB;YAAe;SAAc;QACnE,UAAU;QACV,QAAQ;QACR,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,oBAAM,8OAAC,sMAAA,CAAA,SAAM;;;;;QACb,OAAO;QACP,kBAAkB;QAClB,iBAAiB;QACjB,cAAc;QACd,aAAa;QACb,UAAU;QACV,OAAO;QACP,QAAQ;QACR,UAAU;QACV,UAAU;QACV,UAAU;QACV,UAAU;YACR,OAAO;YACP,SAAS;YACT,MAAM;YACN,SAAS;YACT,UAAU;YACV,UAAU;QACZ;QACA,UAAU;YACR;YACA;YACA;YACA;YACA;SACD;QACD,kBAAkB;YAChB;YACA;YACA;SACD;QACD,yBAAyB;YACvB;YACA;YACA;SACD;QACD,oBAAoB;YAClB;YACA;YACA;YACA;SACD;QACD,gBAAgB;YACd;YACA;YACA;SACD;QACD,UAAU;QACV,cAAc;QACd,eAAe;YACb;gBACE,MAAM;gBACN,OAAO;gBACP,aAAa;gBACb,UAAU;gBACV,QAAQ;YACV;YACA;gBACE,MAAM;gBACN,OAAO;gBACP,aAAa;gBACb,UAAU;gBACV,QAAQ;YACV;SACD;QACD,MAAM;YAAC;YAAY;YAAc;YAAc;SAAW;QAC1D,UAAU;QACV,QAAQ;QACR,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,oBAAM,8OAAC,wNAAA,CAAA,gBAAa;;;;;QACpB,OAAO;QACP,kBAAkB;QAClB,iBAAiB;QACjB,cAAc;QACd,aAAa;QACb,UAAU;QACV,OAAO;QACP,QAAQ;QACR,UAAU;QACV,UAAU;QACV,UAAU;QACV,UAAU;YACR,OAAO;YACP,SAAS;YACT,MAAM;YACN,SAAS;YACT,UAAU;YACV,UAAU;QACZ;QACA,UAAU;YACR;YACA;YACA;YACA;YACA;SACD;QACD,kBAAkB;YAChB;YACA;YACA;SACD;QACD,yBAAyB;YACvB;YACA;YACA;SACD;QACD,oBAAoB;YAClB;YACA;YACA;SACD;QACD,gBAAgB;YACd;YACA;YACA;SACD;QACD,UAAU;QACV,cAAc;QACd,eAAe;YACb;gBACE,MAAM;gBACN,OAAO;gBACP,aAAa;gBACb,UAAU;gBACV,QAAQ;YACV;YACA;gBACE,MAAM;gBACN,OAAO;gBACP,aAAa;gBACb,UAAU;gBACV,QAAQ;YACV;YACA;gBACE,MAAM;gBACN,OAAO;gBACP,aAAa;gBACb,UAAU;gBACV,QAAQ;YACV;YACA;gBACE,MAAM;gBACN,OAAO;gBACP,aAAa;gBACb,UAAU;gBACV,QAAQ;YACV;SACD;QACD,MAAM;YAAC;YAAa;YAAU;YAAc;SAAW;QACvD,UAAU;QACV,QAAQ;QACR,WAAW;QACX,WAAW;IACb;CACD", "debugId": null}}, {"offset": {"line": 1337, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/fwu_incubation%28react%2Blaravel%29/incubation/frontend/src/app/components/programs/ProgramCalendarSection%20.tsx"], "sourcesContent": ["\"use client\"\r\nimport UpcomingEventCard, { UpcomingEvent } from './UpcomingEventCard';\r\nimport { Calendar, Filter, Search, Sparkles, Star, Clock, MapPin } from 'lucide-react';\r\nimport { useState, useEffect } from 'react';\r\nimport { programsData } from '../../../data/programsData';\r\n\r\n// Convert program data to events\r\nconst generateEventsFromPrograms = (): UpcomingEvent[] => {\r\n  const events: UpcomingEvent[] = [];\r\n\r\n  programsData.forEach(program => {\r\n    program.upcomingDates.forEach(date => {\r\n      events.push({\r\n        id: `${program.id}-${date.date}`,\r\n        date: new Date(date.date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' }).toUpperCase(),\r\n        fullDate: date.date,\r\n        time: program.schedule,\r\n        title: date.title,\r\n        type: program.category.charAt(0).toUpperCase() + program.category.slice(1),\r\n        location: program.location.isOnline ? 'Online' : program.location.venue,\r\n        description: date.description || program.shortDescription,\r\n        color: getColorByCategory(program.category),\r\n        detailedDescription: program.longDescription,\r\n        registrationLink: `/programs/${program.slug}`,\r\n        capacity: date.capacity,\r\n        status: date.status\r\n      });\r\n    });\r\n  });\r\n\r\n  return events.sort((a, b) => new Date(a.fullDate).getTime() - new Date(b.fullDate).getTime());\r\n};\r\n\r\nconst getColorByCategory = (category: string): string => {\r\n  const colors = {\r\n    'bootcamp': 'bg-blue-600',\r\n    'hackathon': 'bg-teal-600',\r\n    'workshop': 'bg-purple-600',\r\n    'demo-day': 'bg-amber-600',\r\n    'mentorship': 'bg-green-600',\r\n    'accelerator': 'bg-red-600'\r\n  };\r\n  return colors[category as keyof typeof colors] || 'bg-gray-600';\r\n};\r\n\r\n// Enhanced Dummy Data with more details (keeping some for variety)\r\nconst additionalEventsData: UpcomingEvent[] = [\r\n  {\r\n    id: 'ue1',\r\n    date: 'NOV 15',\r\n    fullDate: 'November 15, 2024',\r\n    time: '09:00 AM - 05:00 PM',\r\n    title: 'Design Thinking Workshop for Innovators',\r\n    type: 'Workshop',\r\n    location: 'FWU Incubation Hall A',\r\n    description: 'Learn human-centered design principles to create impactful solutions.',\r\n    color: 'bg-purple-500',\r\n    detailedDescription: 'This full-day workshop will introduce participants to the core principles of design thinking and how to apply them to solve complex problems. Led by industry experts, you will learn techniques for empathizing with users, defining problems, ideating solutions, prototyping, and testing. By the end of the workshop, you will have a practical toolkit for approaching innovation challenges with a human-centered mindset.',\r\n    registrationLink: '/register/design-thinking-workshop'\r\n  },\r\n  {\r\n    id: 'ue2',\r\n    date: 'NOV 28',\r\n    fullDate: 'November 28, 2024',\r\n    title: 'Application Deadline: Winter Cohort 2025',\r\n    type: 'Deadline',\r\n    description: 'Submit your startup applications for the upcoming winter incubation program.',\r\n    color: 'bg-red-500',\r\n    detailedDescription: 'The Winter Cohort 2025 is our flagship 12-week incubation program designed for early-stage startups ready to accelerate their growth. Selected startups will receive mentorship, workspace, seed funding opportunities, and access to our network of investors and industry partners. Applications must include your business plan, team information, current traction, and growth strategy.',\r\n    registrationLink: '/apply/winter-cohort-2025'\r\n  },\r\n  {\r\n    id: 'ue3',\r\n    date: 'DEC 05',\r\n    fullDate: 'December 05, 2024',\r\n    time: '02:00 PM - 04:00 PM',\r\n    title: 'Investor Connect: Meet & Greet',\r\n    type: 'Networking',\r\n    location: 'Online (Zoom)',\r\n    description: 'An opportunity for selected startups to interact with potential investors.',\r\n    color: 'bg-teal-500',\r\n    detailedDescription: 'This exclusive virtual networking event brings together promising startups and potential investors in a structured yet casual format. Each startup will have the opportunity to introduce their venture in a brief pitch, followed by breakout rooms for more in-depth conversations with interested investors. This is not a formal pitching event but rather a chance to build relationships that could lead to future investment opportunities.',\r\n    registrationLink: '/register/investor-connect'\r\n  },\r\n  {\r\n    id: 'ue4',\r\n    date: 'DEC 12',\r\n    fullDate: 'December 12-14, 2024',\r\n    title: 'FinTech Hackathon Challenge',\r\n    type: 'Hackathon',\r\n    location: 'FWU Main Auditorium',\r\n    description: 'Develop innovative solutions for the financial technology sector and win prizes.',\r\n    color: 'bg-blue-600',\r\n    detailedDescription: 'Join us for an intensive 48-hour hackathon focused on developing innovative solutions for the financial technology sector. Participants will form teams to tackle real-world challenges provided by our industry partners. Cash prizes totaling $10,000 will be awarded to the top three teams, with the first-place team also receiving incubation support to develop their solution further. All skill levels are welcome, and mentors will be available throughout the event.',\r\n    registrationLink: '/register/fintech-hackathon'\r\n  },\r\n  {\r\n    id: 'ue5',\r\n    date: 'JAN 10',\r\n    fullDate: 'January 10, 2025',\r\n    time: '10:00 AM - 12:00 PM',\r\n    title: 'Funding Strategies for Early-Stage Startups',\r\n    type: 'Workshop',\r\n    location: 'FWU Incubation Center',\r\n    description: 'Learn about different funding options and how to approach investors effectively.',\r\n    color: 'bg-purple-500',\r\n    detailedDescription: 'This workshop will cover various funding strategies available to early-stage startups, including bootstrapping, angel investment, venture capital, grants, and crowdfunding. Our expert speakers will share insights on when to pursue each option, how to prepare your startup for investment, and tactics for successful fundraising. The session will include case studies of successful funding journeys and common pitfalls to avoid.',\r\n    registrationLink: '/register/funding-strategies-workshop'\r\n  },\r\n];\r\n\r\n// Helper to sort events by fullDate (simplistic, assumes \"Month Day, Year\" format)\r\nconst sortEvents = (events: UpcomingEvent[]): UpcomingEvent[] => {\r\n  return events.sort((a, b) => new Date(a.fullDate).getTime() - new Date(b.fullDate).getTime());\r\n};\r\n\r\n// Get unique event types for filtering\r\nconst getUniqueEventTypes = (events: UpcomingEvent[]): string[] => {\r\n  return Array.from(new Set(events.map(event => event.type))).sort();\r\n};\r\n\r\nconst ProgramCalendarSection = () => {\r\n  const [searchTerm, setSearchTerm] = useState('');\r\n  const [selectedType, setSelectedType] = useState('');\r\n  const [isVisible, setIsVisible] = useState(false);\r\n  const [allEvents, setAllEvents] = useState<UpcomingEvent[]>([]);\r\n\r\n  useEffect(() => {\r\n    setIsVisible(true);\r\n    // Combine program events with additional events\r\n    const programEvents = generateEventsFromPrograms();\r\n    const combinedEvents = [...programEvents, ...additionalEventsData];\r\n    setAllEvents(combinedEvents);\r\n  }, []);\r\n\r\n  const eventTypes = getUniqueEventTypes(allEvents);\r\n\r\n  // Filter events based on search term and selected type\r\n  const filteredEvents = allEvents\r\n    .filter((event: UpcomingEvent) =>\r\n      event.title.toLowerCase().includes(searchTerm.toLowerCase()) ||\r\n      event.description.toLowerCase().includes(searchTerm.toLowerCase())\r\n    )\r\n    .filter((event: UpcomingEvent) =>\r\n      selectedType ? event.type === selectedType : true\r\n    );\r\n\r\n  const sortedEvents = sortEvents(filteredEvents);\r\n\r\n  return (\r\n    <section className=\"py-20 md:py-28 bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-100 relative overflow-hidden\">\r\n      {/* Background Elements */}\r\n      <div className=\"absolute inset-0 overflow-hidden\">\r\n        <div className=\"absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-400/10 to-purple-400/10 rounded-full blur-3xl\"></div>\r\n        <div className=\"absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-indigo-400/10 to-pink-400/10 rounded-full blur-3xl\"></div>\r\n\r\n        {/* Floating Elements */}\r\n        {[...Array(15)].map((_, i) => (\r\n          <div\r\n            key={i}\r\n            className={`absolute rounded-full ${\r\n              i % 4 === 0 ? 'bg-blue-400/20' :\r\n              i % 4 === 1 ? 'bg-purple-400/20' :\r\n              i % 4 === 2 ? 'bg-indigo-400/20' : 'bg-pink-400/20'\r\n            }`}\r\n            style={{\r\n              width: `${Math.random() * 4 + 2}px`,\r\n              height: `${Math.random() * 4 + 2}px`,\r\n              top: `${Math.random() * 100}%`,\r\n              left: `${Math.random() * 100}%`,\r\n              animation: `float-gentle ${\r\n                8 + Math.random() * 12\r\n              }s infinite ease-in-out`,\r\n              animationDelay: `${Math.random() * 8}s`,\r\n            }}\r\n          ></div>\r\n        ))}\r\n      </div>\r\n\r\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 relative z-10\">\r\n        {/* Enhanced Section Header */}\r\n        <div className={`text-center mb-16 transition-all duration-1000 transform ${\r\n          isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'\r\n        }`}>\r\n          <div className=\"inline-block mb-6\">\r\n            <div className=\"flex items-center justify-center space-x-3 bg-white/80 backdrop-blur-md px-6 py-3 rounded-2xl border border-white/50 shadow-lg\">\r\n              <Calendar className=\"text-blue-600\" size={20} />\r\n              <span className=\"text-blue-600 font-semibold tracking-wide\">STAY INFORMED</span>\r\n              <Sparkles className=\"text-purple-600\" size={16} />\r\n            </div>\r\n          </div>\r\n\r\n          <h2 className=\"text-4xl md:text-5xl lg:text-6xl font-black mb-6 leading-tight\">\r\n            <span className=\"text-gray-900\">Upcoming </span>\r\n            <span className=\"text-transparent bg-clip-text bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600\">\r\n              Events\r\n            </span>\r\n            <br />\r\n            <span className=\"text-gray-700 text-2xl md:text-3xl font-normal\">\r\n              at FWU Incubation Center\r\n            </span>\r\n          </h2>\r\n\r\n          <p className=\"text-xl text-gray-700 max-w-3xl mx-auto leading-relaxed\">\r\n            Stay updated with our latest programs, workshops, and networking events.\r\n            <span className=\"font-semibold text-blue-600\"> Never miss an opportunity</span> to grow your startup.\r\n          </p>\r\n        </div>\r\n\r\n        {/* Enhanced Search and Filter Controls */}\r\n        <div className={`mb-16 transition-all duration-1000 delay-300 transform ${\r\n          isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'\r\n        }`}>\r\n          <div className=\"bg-white/80 backdrop-blur-md rounded-3xl p-8 border border-white/50 shadow-xl\">\r\n            <div className=\"flex flex-col lg:flex-row gap-6\">\r\n              {/* Search Bar */}\r\n              <div className=\"relative flex-grow\">\r\n                <div className=\"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none\">\r\n                  <Search className=\"text-blue-500\" size={20} />\r\n                </div>\r\n                <input\r\n                  type=\"text\"\r\n                  placeholder=\"Search events, workshops, or topics...\"\r\n                  value={searchTerm}\r\n                  onChange={(e) => setSearchTerm(e.target.value)}\r\n                  className=\"block w-full pl-12 pr-4 py-4 border-2 border-gray-200 rounded-2xl focus:ring-2 focus:ring-blue-400 focus:border-blue-400 transition-all duration-300 bg-white/90 backdrop-blur-sm text-gray-900 placeholder-gray-500\"\r\n                />\r\n              </div>\r\n\r\n              {/* Event Type Filter */}\r\n              <div className=\"relative\">\r\n                <div className=\"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none\">\r\n                  <Filter className=\"text-purple-500\" size={20} />\r\n                </div>\r\n                <select\r\n                  value={selectedType}\r\n                  onChange={(e) => setSelectedType(e.target.value)}\r\n                  className=\"block w-full lg:w-64 pl-12 pr-10 py-4 border-2 border-gray-200 rounded-2xl focus:ring-2 focus:ring-purple-400 focus:border-purple-400 transition-all duration-300 appearance-none bg-white/90 backdrop-blur-sm text-gray-900\"\r\n                >\r\n                  <option value=\"\">All Event Types</option>\r\n                  {eventTypes.map(type => (\r\n                    <option key={type} value={type}>{type}</option>\r\n                  ))}\r\n                </select>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Active Filters Display */}\r\n            {(selectedType || searchTerm) && (\r\n              <div className=\"mt-6 flex flex-wrap gap-3\">\r\n                {selectedType && (\r\n                  <span className=\"inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-purple-100 text-purple-800 border border-purple-200\">\r\n                    Type: {selectedType}\r\n                    <button\r\n                      onClick={() => setSelectedType('')}\r\n                      className=\"ml-2 text-purple-600 hover:text-purple-800 transition-colors duration-200\"\r\n                    >\r\n                      ×\r\n                    </button>\r\n                  </span>\r\n                )}\r\n                {searchTerm && (\r\n                  <span className=\"inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-blue-100 text-blue-800 border border-blue-200\">\r\n                    Search: &quot;{searchTerm}&quot;\r\n                    <button\r\n                      onClick={() => setSearchTerm('')}\r\n                      className=\"ml-2 text-blue-600 hover:text-blue-800 transition-colors duration-200\"\r\n                    >\r\n                      ×\r\n                    </button>\r\n                  </span>\r\n                )}\r\n              </div>\r\n            )}\r\n\r\n            {/* Quick Stats */}\r\n            <div className=\"mt-6 pt-6 border-t border-gray-200\">\r\n              <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\r\n                <div className=\"text-center\">\r\n                  <div className=\"text-2xl font-bold text-blue-600\">{allEvents.length}</div>\r\n                  <div className=\"text-sm text-gray-600\">Total Events</div>\r\n                </div>\r\n                <div className=\"text-center\">\r\n                  <div className=\"text-2xl font-bold text-purple-600\">{eventTypes.length}</div>\r\n                  <div className=\"text-sm text-gray-600\">Event Types</div>\r\n                </div>\r\n                <div className=\"text-center\">\r\n                  <div className=\"text-2xl font-bold text-indigo-600\">{sortedEvents.length}</div>\r\n                  <div className=\"text-sm text-gray-600\">Filtered Results</div>\r\n                </div>\r\n                <div className=\"text-center\">\r\n                  <div className=\"text-2xl font-bold text-pink-600\">\r\n                    {allEvents.filter(e => e.status === 'upcoming').length}\r\n                  </div>\r\n                  <div className=\"text-sm text-gray-600\">Upcoming</div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Enhanced Events List */}\r\n        {sortedEvents.length > 0 ? (\r\n          <div className=\"space-y-8\">\r\n            {sortedEvents.map((event, index) => (\r\n              <div\r\n                key={event.id}\r\n                className={`transition-all duration-700 transform ${\r\n                  isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'\r\n                }`}\r\n                style={{\r\n                  transitionDelay: `${600 + index * 150}ms`\r\n                }}\r\n              >\r\n                <UpcomingEventCard event={event} />\r\n              </div>\r\n            ))}\r\n          </div>\r\n        ) : (\r\n          <div className={`text-center py-20 transition-all duration-1000 transform ${\r\n            isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'\r\n          }`}>\r\n            <div className=\"bg-white/80 backdrop-blur-md rounded-3xl p-12 border border-white/50 shadow-xl\">\r\n              <div className=\"w-24 h-24 mx-auto bg-gradient-to-r from-blue-100 to-purple-100 rounded-full flex items-center justify-center mb-6\">\r\n                <Calendar className=\"text-blue-500\" size={32} />\r\n              </div>\r\n              <h3 className=\"text-3xl font-bold text-gray-900 mb-4\">No Events Found</h3>\r\n              <p className=\"text-gray-600 max-w-md mx-auto mb-6 leading-relaxed\">\r\n                {searchTerm || selectedType\r\n                  ? \"No events match your current search criteria. Try adjusting your filters or search terms.\"\r\n                  : \"No upcoming events scheduled at the moment. Please check back soon for exciting new programs!\"}\r\n              </p>\r\n              {(searchTerm || selectedType) && (\r\n                <button\r\n                  onClick={() => {\r\n                    setSearchTerm('');\r\n                    setSelectedType('');\r\n                  }}\r\n                  className=\"px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-xl hover:shadow-lg transition-all duration-300 font-semibold\"\r\n                >\r\n                  Clear All Filters\r\n                </button>\r\n              )}\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {/* Enhanced Calendar Subscription */}\r\n        <div className={`mt-24 transition-all duration-1000 delay-700 transform ${\r\n          isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'\r\n        }`}>\r\n          <div className=\"relative bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900 rounded-3xl shadow-2xl overflow-hidden\">\r\n            {/* Background Elements */}\r\n            <div className=\"absolute inset-0\">\r\n              <div className=\"absolute top-20 right-20 w-64 h-64 bg-blue-500/10 rounded-full blur-3xl\"></div>\r\n              <div className=\"absolute bottom-20 left-20 w-64 h-64 bg-purple-500/10 rounded-full blur-3xl\"></div>\r\n            </div>\r\n\r\n            <div className=\"relative z-10 p-12 lg:p-16 text-center\">\r\n              <div className=\"inline-block mb-6\">\r\n                <div className=\"flex items-center justify-center space-x-3 bg-white/10 backdrop-blur-md px-6 py-3 rounded-2xl border border-white/20\">\r\n                  <Calendar className=\"text-blue-400\" size={20} />\r\n                  <span className=\"text-white font-semibold\">STAY CONNECTED</span>\r\n                  <Star className=\"text-yellow-400\" size={16} />\r\n                </div>\r\n              </div>\r\n\r\n              <h3 className=\"text-3xl lg:text-4xl font-bold text-white mb-6 leading-tight\">\r\n                Never Miss an\r\n                <span className=\"text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-400\"> Opportunity</span>\r\n              </h3>\r\n\r\n              <p className=\"text-blue-100 text-lg mb-8 max-w-2xl mx-auto leading-relaxed\">\r\n                Subscribe to our calendar to receive automatic updates about upcoming events, workshops,\r\n                and programs at the FWU Incubation Center. Stay ahead of the curve!\r\n              </p>\r\n\r\n              <div className=\"flex flex-col sm:flex-row justify-center gap-4 mb-8\">\r\n                <a\r\n                  href=\"/subscribe-calendar\"\r\n                  className=\"group relative px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-bold rounded-2xl transition-all duration-300 hover:shadow-2xl hover:shadow-blue-500/25 hover:-translate-y-1 flex items-center justify-center overflow-hidden\"\r\n                >\r\n                  <div className=\"absolute inset-0 bg-gradient-to-r from-blue-700 to-purple-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\r\n                  <Calendar className=\"relative z-10 mr-2\" size={20} />\r\n                  <span className=\"relative z-10\">Subscribe to Calendar</span>\r\n                </a>\r\n\r\n                <a\r\n                  href=\"/events\"\r\n                  className=\"group relative px-8 py-4 bg-white/10 hover:bg-white/20 backdrop-blur-md text-white font-bold rounded-2xl transition-all duration-300 border-2 border-white/30 hover:border-white/50 hover:shadow-2xl hover:shadow-white/10 hover:-translate-y-1 flex items-center justify-center overflow-hidden\"\r\n                >\r\n                  <div className=\"absolute inset-0 bg-white/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\r\n                  <span className=\"relative z-10\">View All Events</span>\r\n                </a>\r\n              </div>\r\n\r\n              <p className=\"text-blue-200 text-sm\">\r\n                Compatible with Google Calendar, Outlook, Apple Calendar, and more\r\n              </p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Custom CSS for animations */}\r\n        <style jsx global>{`\r\n          @keyframes float-gentle {\r\n            0%, 100% { transform: translate(0, 0) rotate(0deg); }\r\n            25% { transform: translate(5px, -5px) rotate(45deg); }\r\n            50% { transform: translate(-3px, -8px) rotate(90deg); }\r\n            75% { transform: translate(-5px, 3px) rotate(135deg); }\r\n          }\r\n        `}</style>\r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\nexport default ProgramCalendarSection;"], "names": [], "mappings": ";;;;;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAJA;;;;;;;AAMA,iCAAiC;AACjC,MAAM,6BAA6B;IACjC,MAAM,SAA0B,EAAE;IAElC,4HAAA,CAAA,eAAY,CAAC,OAAO,CAAC,CAAA;QACnB,QAAQ,aAAa,CAAC,OAAO,CAAC,CAAA;YAC5B,OAAO,IAAI,CAAC;gBACV,IAAI,GAAG,QAAQ,EAAE,CAAC,CAAC,EAAE,KAAK,IAAI,EAAE;gBAChC,MAAM,IAAI,KAAK,KAAK,IAAI,EAAE,kBAAkB,CAAC,SAAS;oBAAE,OAAO;oBAAS,KAAK;gBAAU,GAAG,WAAW;gBACrG,UAAU,KAAK,IAAI;gBACnB,MAAM,QAAQ,QAAQ;gBACtB,OAAO,KAAK,KAAK;gBACjB,MAAM,QAAQ,QAAQ,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK,QAAQ,QAAQ,CAAC,KAAK,CAAC;gBACxE,UAAU,QAAQ,QAAQ,CAAC,QAAQ,GAAG,WAAW,QAAQ,QAAQ,CAAC,KAAK;gBACvE,aAAa,KAAK,WAAW,IAAI,QAAQ,gBAAgB;gBACzD,OAAO,mBAAmB,QAAQ,QAAQ;gBAC1C,qBAAqB,QAAQ,eAAe;gBAC5C,kBAAkB,CAAC,UAAU,EAAE,QAAQ,IAAI,EAAE;gBAC7C,UAAU,KAAK,QAAQ;gBACvB,QAAQ,KAAK,MAAM;YACrB;QACF;IACF;IAEA,OAAO,OAAO,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,QAAQ,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,QAAQ,EAAE,OAAO;AAC5F;AAEA,MAAM,qBAAqB,CAAC;IAC1B,MAAM,SAAS;QACb,YAAY;QACZ,aAAa;QACb,YAAY;QACZ,YAAY;QACZ,cAAc;QACd,eAAe;IACjB;IACA,OAAO,MAAM,CAAC,SAAgC,IAAI;AACpD;AAEA,mEAAmE;AACnE,MAAM,uBAAwC;IAC5C;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,MAAM;QACN,OAAO;QACP,MAAM;QACN,UAAU;QACV,aAAa;QACb,OAAO;QACP,qBAAqB;QACrB,kBAAkB;IACpB;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,OAAO;QACP,MAAM;QACN,aAAa;QACb,OAAO;QACP,qBAAqB;QACrB,kBAAkB;IACpB;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,MAAM;QACN,OAAO;QACP,MAAM;QACN,UAAU;QACV,aAAa;QACb,OAAO;QACP,qBAAqB;QACrB,kBAAkB;IACpB;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,OAAO;QACP,MAAM;QACN,UAAU;QACV,aAAa;QACb,OAAO;QACP,qBAAqB;QACrB,kBAAkB;IACpB;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,MAAM;QACN,OAAO;QACP,MAAM;QACN,UAAU;QACV,aAAa;QACb,OAAO;QACP,qBAAqB;QACrB,kBAAkB;IACpB;CACD;AAED,mFAAmF;AACnF,MAAM,aAAa,CAAC;IAClB,OAAO,OAAO,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,QAAQ,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,QAAQ,EAAE,OAAO;AAC5F;AAEA,uCAAuC;AACvC,MAAM,sBAAsB,CAAC;IAC3B,OAAO,MAAM,IAAI,CAAC,IAAI,IAAI,OAAO,GAAG,CAAC,CAAA,QAAS,MAAM,IAAI,IAAI,IAAI;AAClE;AAEA,MAAM,yBAAyB;IAC7B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IAE9D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,aAAa;QACb,gDAAgD;QAChD,MAAM,gBAAgB;QACtB,MAAM,iBAAiB;eAAI;eAAkB;SAAqB;QAClE,aAAa;IACf,GAAG,EAAE;IAEL,MAAM,aAAa,oBAAoB;IAEvC,uDAAuD;IACvD,MAAM,iBAAiB,UACpB,MAAM,CAAC,CAAC,QACP,MAAM,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACzD,MAAM,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,KAEhE,MAAM,CAAC,CAAC,QACP,eAAe,MAAM,IAAI,KAAK,eAAe;IAGjD,MAAM,eAAe,WAAW;IAEhC,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;oBAGd;2BAAI,MAAM;qBAAI,CAAC,GAAG,CAAC,CAAC,GAAG,kBACtB,8OAAC;4BAEC,WAAW,CAAC,sBAAsB,EAChC,IAAI,MAAM,IAAI,mBACd,IAAI,MAAM,IAAI,qBACd,IAAI,MAAM,IAAI,qBAAqB,kBACnC;4BACF,OAAO;gCACL,OAAO,GAAG,KAAK,MAAM,KAAK,IAAI,EAAE,EAAE,CAAC;gCACnC,QAAQ,GAAG,KAAK,MAAM,KAAK,IAAI,EAAE,EAAE,CAAC;gCACpC,KAAK,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;gCAC9B,MAAM,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;gCAC/B,WAAW,CAAC,aAAa,EACvB,IAAI,KAAK,MAAM,KAAK,GACrB,sBAAsB,CAAC;gCACxB,gBAAgB,GAAG,KAAK,MAAM,KAAK,EAAE,CAAC,CAAC;4BACzC;2BAfK;;;;;;;;;;;0BAoBX,8OAAC;0DAAc;;kCAEb,8OAAC;kEAAe,CAAC,yDAAyD,EACxE,YAAY,8BAA8B,4BAC1C;;0CACA,8OAAC;0EAAc;0CACb,cAAA,8OAAC;8EAAc;;sDACb,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;4CAAgB,MAAM;;;;;;sDAC1C,8OAAC;sFAAe;sDAA4C;;;;;;sDAC5D,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;4CAAkB,MAAM;;;;;;;;;;;;;;;;;0CAIhD,8OAAC;0EAAa;;kDACZ,8OAAC;kFAAe;kDAAgB;;;;;;kDAChC,8OAAC;kFAAe;kDAA4F;;;;;;kDAG5G,8OAAC;;;;;;;kDACD,8OAAC;kFAAe;kDAAiD;;;;;;;;;;;;0CAKnE,8OAAC;0EAAY;;oCAA0D;kDAErE,8OAAC;kFAAe;kDAA8B;;;;;;oCAAiC;;;;;;;;;;;;;kCAKnF,8OAAC;kEAAe,CAAC,uDAAuD,EACtE,YAAY,8BAA8B,4BAC1C;kCACA,cAAA,8OAAC;sEAAc;;8CACb,8OAAC;8EAAc;;sDAEb,8OAAC;sFAAc;;8DACb,8OAAC;8FAAc;8DACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;wDAAC,WAAU;wDAAgB,MAAM;;;;;;;;;;;8DAE1C,8OAAC;oDACC,MAAK;oDACL,aAAY;oDACZ,OAAO;oDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;8FACnC;;;;;;;;;;;;sDAKd,8OAAC;sFAAc;;8DACb,8OAAC;8FAAc;8DACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;wDAAC,WAAU;wDAAkB,MAAM;;;;;;;;;;;8DAE5C,8OAAC;oDACC,OAAO;oDACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;8FACrC;;sEAEV,8OAAC;4DAAO,OAAM;;sEAAG;;;;;;wDAChB,WAAW,GAAG,CAAC,CAAA,qBACd,8OAAC;gEAAkB,OAAO;;0EAAO;+DAApB;;;;;;;;;;;;;;;;;;;;;;;gCAOpB,CAAC,gBAAgB,UAAU,mBAC1B,8OAAC;8EAAc;;wCACZ,8BACC,8OAAC;sFAAe;;gDAA6H;gDACpI;8DACP,8OAAC;oDACC,SAAS,IAAM,gBAAgB;8FACrB;8DACX;;;;;;;;;;;;wCAKJ,4BACC,8OAAC;sFAAe;;gDAAuH;gDACtH;gDAAW;8DAC1B,8OAAC;oDACC,SAAS,IAAM,cAAc;8FACnB;8DACX;;;;;;;;;;;;;;;;;;8CAST,8OAAC;8EAAc;8CACb,cAAA,8OAAC;kFAAc;;0DACb,8OAAC;0FAAc;;kEACb,8OAAC;kGAAc;kEAAoC,UAAU,MAAM;;;;;;kEACnE,8OAAC;kGAAc;kEAAwB;;;;;;;;;;;;0DAEzC,8OAAC;0FAAc;;kEACb,8OAAC;kGAAc;kEAAsC,WAAW,MAAM;;;;;;kEACtE,8OAAC;kGAAc;kEAAwB;;;;;;;;;;;;0DAEzC,8OAAC;0FAAc;;kEACb,8OAAC;kGAAc;kEAAsC,aAAa,MAAM;;;;;;kEACxE,8OAAC;kGAAc;kEAAwB;;;;;;;;;;;;0DAEzC,8OAAC;0FAAc;;kEACb,8OAAC;kGAAc;kEACZ,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,YAAY,MAAM;;;;;;kEAExD,8OAAC;kGAAc;kEAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAQhD,aAAa,MAAM,GAAG,kBACrB,8OAAC;kEAAc;kCACZ,aAAa,GAAG,CAAC,CAAC,OAAO,sBACxB,8OAAC;gCAKC,OAAO;oCACL,iBAAiB,GAAG,MAAM,QAAQ,IAAI,EAAE,CAAC;gCAC3C;0EALW,CAAC,sCAAsC,EAChD,YAAY,8BAA8B,4BAC1C;0CAKF,cAAA,8OAAC,0JAAA,CAAA,UAAiB;oCAAC,OAAO;;;;;;+BARrB,MAAM,EAAE;;;;;;;;;6CAanB,8OAAC;kEAAe,CAAC,yDAAyD,EACxE,YAAY,8BAA8B,4BAC1C;kCACA,cAAA,8OAAC;sEAAc;;8CACb,8OAAC;8EAAc;8CACb,cAAA,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;wCAAgB,MAAM;;;;;;;;;;;8CAE5C,8OAAC;8EAAa;8CAAwC;;;;;;8CACtD,8OAAC;8EAAY;8CACV,cAAc,eACX,8FACA;;;;;;gCAEL,CAAC,cAAc,YAAY,mBAC1B,8OAAC;oCACC,SAAS;wCACP,cAAc;wCACd,gBAAgB;oCAClB;8EACU;8CACX;;;;;;;;;;;;;;;;;kCAST,8OAAC;kEAAe,CAAC,uDAAuD,EACtE,YAAY,8BAA8B,4BAC1C;kCACA,cAAA,8OAAC;sEAAc;;8CAEb,8OAAC;8EAAc;;sDACb,8OAAC;sFAAc;;;;;;sDACf,8OAAC;sFAAc;;;;;;;;;;;;8CAGjB,8OAAC;8EAAc;;sDACb,8OAAC;sFAAc;sDACb,cAAA,8OAAC;0FAAc;;kEACb,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;wDAAgB,MAAM;;;;;;kEAC1C,8OAAC;kGAAe;kEAA2B;;;;;;kEAC3C,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;wDAAkB,MAAM;;;;;;;;;;;;;;;;;sDAI5C,8OAAC;sFAAa;;gDAA+D;8DAE3E,8OAAC;8FAAe;8DAA6E;;;;;;;;;;;;sDAG/F,8OAAC;sFAAY;sDAA+D;;;;;;sDAK5E,8OAAC;sFAAc;;8DACb,8OAAC;oDACC,MAAK;8FACK;;sEAEV,8OAAC;sGAAc;;;;;;sEACf,8OAAC,0MAAA,CAAA,WAAQ;4DAAC,WAAU;4DAAqB,MAAM;;;;;;sEAC/C,8OAAC;sGAAe;sEAAgB;;;;;;;;;;;;8DAGlC,8OAAC;oDACC,MAAK;8FACK;;sEAEV,8OAAC;sGAAc;;;;;;sEACf,8OAAC;sGAAe;sEAAgB;;;;;;;;;;;;;;;;;;sDAIpC,8OAAC;sFAAY;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmBnD;uCAEe", "debugId": null}}, {"offset": {"line": 2215, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/fwu_incubation%28react%2Blaravel%29/incubation/frontend/src/app/components/programs/ProgramTypeCard.tsx"], "sourcesContent": ["\r\n\"use client\"\r\nimport { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, MapPin, Star, Calendar, Award } from 'lucide-react';\r\nimport Link from 'next/link';\r\nimport Image from 'next/image';\r\nimport { Program } from '../../../types/program.types';\r\n\r\nexport interface ProgramTypeCardProps {\r\n  program: Program;\r\n  variant?: 'default' | 'featured' | 'compact';\r\n  showImage?: boolean;\r\n  showStats?: boolean;\r\n  className?: string;\r\n}\r\n\r\nconst ProgramTypeCard: React.FC<ProgramTypeCardProps> = ({\r\n  program,\r\n  variant = 'default',\r\n  showImage = true,\r\n  showStats = true,\r\n  className = ''\r\n}) => {\r\n  const getAccentColor = (category: string) => {\r\n    const colors = {\r\n      'bootcamp': 'from-blue-500 to-indigo-600',\r\n      'hackathon': 'from-teal-500 to-cyan-600',\r\n      'workshop': 'from-purple-500 to-pink-600',\r\n      'demo-day': 'from-amber-500 to-orange-600',\r\n      'mentorship': 'from-green-500 to-emerald-600',\r\n      'accelerator': 'from-red-500 to-rose-600'\r\n    };\r\n    return colors[category as keyof typeof colors] || 'from-gray-500 to-gray-600';\r\n  };\r\n\r\n  const getStatusColor = (status: string) => {\r\n    const colors = {\r\n      'active': 'bg-green-100 text-green-800',\r\n      'coming-soon': 'bg-yellow-100 text-yellow-800',\r\n      'inactive': 'bg-gray-100 text-gray-800',\r\n      'archived': 'bg-red-100 text-red-800'\r\n    };\r\n    return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800';\r\n  };\r\n\r\n  return (\r\n    <div className={`group relative bg-white rounded-3xl border border-gray-100 shadow-xl hover:shadow-2xl transition-all duration-500 overflow-hidden hover:-translate-y-2 ${className}`}>\r\n      {/* Gradient Accent */}\r\n      <div className={`absolute top-0 left-0 right-0 h-1 bg-gradient-to-r ${getAccentColor(program.category)} transform origin-left scale-x-0 group-hover:scale-x-100 transition-transform duration-700`}></div>\r\n\r\n      {/* Featured Badge */}\r\n      {program.featured && (\r\n        <div className=\"absolute top-4 right-4 z-10\">\r\n          <div className=\"bg-gradient-to-r from-yellow-400 to-orange-400 text-white px-3 py-1 rounded-full text-xs font-bold flex items-center shadow-lg\">\r\n            <Star className=\"mr-1\" size={12} />\r\n            Featured\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Status Badge */}\r\n      <div className=\"absolute top-4 left-4 z-10\">\r\n        <span className={`px-3 py-1 rounded-full text-xs font-semibold ${getStatusColor(program.status)}`}>\r\n          {program.status.replace('-', ' ').toUpperCase()}\r\n        </span>\r\n      </div>\r\n\r\n      {/* Hero Image */}\r\n      {showImage && program.heroImageUrl && (\r\n        <div className=\"relative h-48 overflow-hidden\">\r\n          <Image\r\n            src={program.heroImageUrl}\r\n            alt={program.title}\r\n            fill\r\n            className=\"object-cover group-hover:scale-110 transition-transform duration-700\"\r\n          />\r\n          <div className=\"absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent\"></div>\r\n\r\n          {/* Category Badge on Image */}\r\n          <div className=\"absolute bottom-4 left-4\">\r\n            <span className={`px-3 py-1 rounded-full text-xs font-bold text-white bg-gradient-to-r ${getAccentColor(program.category)} shadow-lg`}>\r\n              {program.category.charAt(0).toUpperCase() + program.category.slice(1).replace('-', ' ')}\r\n            </span>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Content */}\r\n      <div className=\"p-8\">\r\n        {/* Icon and Title */}\r\n        <div className=\"flex items-start justify-between mb-6\">\r\n          <div className=\"flex items-center space-x-4\">\r\n            {program.icon && (\r\n              <div className={`w-16 h-16 rounded-2xl bg-gradient-to-r ${getAccentColor(program.category)} flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300`}>\r\n                <div className=\"text-white text-2xl\">\r\n                  {program.icon}\r\n                </div>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n\r\n        <h3 className=\"text-2xl font-bold text-gray-900 mb-4 group-hover:text-transparent group-hover:bg-clip-text group-hover:bg-gradient-to-r group-hover:from-blue-600 group-hover:to-purple-600 transition-all duration-300\">\r\n          {program.title}\r\n        </h3>\r\n\r\n        <p className=\"text-gray-600 mb-6 leading-relaxed line-clamp-3\">\r\n          {program.shortDescription}\r\n        </p>\r\n\r\n        {/* Program Details */}\r\n        <div className=\"grid grid-cols-2 gap-4 mb-6\">\r\n          <div className=\"flex items-center space-x-2 text-sm text-gray-500\">\r\n            <Clock size={16} />\r\n            <span>{program.duration}</span>\r\n          </div>\r\n          <div className=\"flex items-center space-x-2 text-sm text-gray-500\">\r\n            <Users size={16} />\r\n            <span>{program.capacity} spots</span>\r\n          </div>\r\n          <div className=\"flex items-center space-x-2 text-sm text-gray-500\">\r\n            <MapPin size={16} />\r\n            <span>{program.format}</span>\r\n          </div>\r\n          <div className=\"flex items-center space-x-2 text-sm text-gray-500\">\r\n            <Calendar size={16} />\r\n            <span>{program.upcomingDates.length} dates</span>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Benefits Preview */}\r\n        <div className=\"mb-6\">\r\n          <h4 className=\"text-sm font-semibold text-gray-700 mb-3\">Key Benefits:</h4>\r\n          <div className=\"space-y-2\">\r\n            {program.benefits.slice(0, 2).map((benefit, index) => (\r\n              <div key={index} className=\"flex items-start space-x-2\">\r\n                <div className=\"w-4 h-4 bg-gradient-to-r from-green-400 to-blue-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5\">\r\n                  <Star className=\"text-white\" size={8} />\r\n                </div>\r\n                <span className=\"text-sm text-gray-600 leading-relaxed\">{benefit}</span>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Stats */}\r\n        {showStats && program.stats && (\r\n          <div className=\"grid grid-cols-2 gap-4 mb-6 p-4 bg-gray-50 rounded-xl\">\r\n            {program.stats.successRate && (\r\n              <div className=\"text-center\">\r\n                <div className=\"text-lg font-bold text-green-600\">{program.stats.successRate}%</div>\r\n                <div className=\"text-xs text-gray-500\">Success Rate</div>\r\n              </div>\r\n            )}\r\n            {program.stats.totalParticipants && (\r\n              <div className=\"text-center\">\r\n                <div className=\"text-lg font-bold text-blue-600\">{program.stats.totalParticipants}+</div>\r\n                <div className=\"text-xs text-gray-500\">Participants</div>\r\n              </div>\r\n            )}\r\n          </div>\r\n        )}\r\n\r\n        {/* Tags */}\r\n        <div className=\"flex flex-wrap gap-2 mb-6\">\r\n          {program.tags.slice(0, 3).map((tag, index) => (\r\n            <span\r\n              key={index}\r\n              className=\"px-2 py-1 bg-blue-50 text-blue-600 text-xs font-medium rounded-lg\"\r\n            >\r\n              {tag}\r\n            </span>\r\n          ))}\r\n          {program.tags.length > 3 && (\r\n            <span className=\"px-2 py-1 bg-gray-50 text-gray-500 text-xs font-medium rounded-lg\">\r\n              +{program.tags.length - 3} more\r\n            </span>\r\n          )}\r\n        </div>\r\n\r\n        {/* Action Button */}\r\n        <Link\r\n          href={`/programs/${program.slug}`}\r\n          className={`group/btn relative w-full flex items-center justify-center px-6 py-4 bg-gradient-to-r ${getAccentColor(program.category)} text-white font-bold rounded-2xl transition-all duration-300 hover:shadow-lg hover:shadow-blue-500/25 overflow-hidden`}\r\n        >\r\n          <div className=\"absolute inset-0 bg-white/20 opacity-0 group-hover/btn:opacity-100 transition-opacity duration-300\"></div>\r\n          <span className=\"relative z-10 mr-2\">Learn More</span>\r\n          <ArrowRight className=\"relative z-10 group-hover/btn:translate-x-1 transition-transform duration-300\" size={20} />\r\n        </Link>\r\n\r\n        {/* Next Date */}\r\n        {program.upcomingDates.length > 0 && (\r\n          <div className=\"mt-4 p-3 bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl border border-blue-100\">\r\n            <div className=\"flex items-center justify-between\">\r\n              <div>\r\n                <div className=\"text-sm font-semibold text-gray-700\">Next Session</div>\r\n                <div className=\"text-xs text-gray-500\">{program.upcomingDates[0].date}</div>\r\n              </div>\r\n              <div className=\"flex items-center space-x-1\">\r\n                <Award className=\"text-blue-500\" size={16} />\r\n                <span className=\"text-xs text-blue-600 font-medium\">\r\n                  {program.upcomingDates[0].capacity} spots\r\n                </span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ProgramTypeCard;"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAHA;;;;;AAcA,MAAM,kBAAkD,CAAC,EACvD,OAAO,EACP,UAAU,SAAS,EACnB,YAAY,IAAI,EAChB,YAAY,IAAI,EAChB,YAAY,EAAE,EACf;IACC,MAAM,iBAAiB,CAAC;QACtB,MAAM,SAAS;YACb,YAAY;YACZ,aAAa;YACb,YAAY;YACZ,YAAY;YACZ,cAAc;YACd,eAAe;QACjB;QACA,OAAO,MAAM,CAAC,SAAgC,IAAI;IACpD;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,SAAS;YACb,UAAU;YACV,eAAe;YACf,YAAY;YACZ,YAAY;QACd;QACA,OAAO,MAAM,CAAC,OAA8B,IAAI;IAClD;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,uJAAuJ,EAAE,WAAW;;0BAEnL,8OAAC;gBAAI,WAAW,CAAC,mDAAmD,EAAE,eAAe,QAAQ,QAAQ,EAAE,0FAA0F,CAAC;;;;;;YAGjM,QAAQ,QAAQ,kBACf,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,kMAAA,CAAA,OAAI;4BAAC,WAAU;4BAAO,MAAM;;;;;;wBAAM;;;;;;;;;;;;0BAOzC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAK,WAAW,CAAC,6CAA6C,EAAE,eAAe,QAAQ,MAAM,GAAG;8BAC9F,QAAQ,MAAM,CAAC,OAAO,CAAC,KAAK,KAAK,WAAW;;;;;;;;;;;YAKhD,aAAa,QAAQ,YAAY,kBAChC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,6HAAA,CAAA,UAAK;wBACJ,KAAK,QAAQ,YAAY;wBACzB,KAAK,QAAQ,KAAK;wBAClB,IAAI;wBACJ,WAAU;;;;;;kCAEZ,8OAAC;wBAAI,WAAU;;;;;;kCAGf,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAK,WAAW,CAAC,qEAAqE,EAAE,eAAe,QAAQ,QAAQ,EAAE,UAAU,CAAC;sCAClI,QAAQ,QAAQ,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK,QAAQ,QAAQ,CAAC,KAAK,CAAC,GAAG,OAAO,CAAC,KAAK;;;;;;;;;;;;;;;;;0BAO3F,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACZ,QAAQ,IAAI,kBACX,8OAAC;gCAAI,WAAW,CAAC,uCAAuC,EAAE,eAAe,QAAQ,QAAQ,EAAE,mGAAmG,CAAC;0CAC7L,cAAA,8OAAC;oCAAI,WAAU;8CACZ,QAAQ,IAAI;;;;;;;;;;;;;;;;;;;;;kCAOvB,8OAAC;wBAAG,WAAU;kCACX,QAAQ,KAAK;;;;;;kCAGhB,8OAAC;wBAAE,WAAU;kCACV,QAAQ,gBAAgB;;;;;;kCAI3B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oMAAA,CAAA,QAAK;wCAAC,MAAM;;;;;;kDACb,8OAAC;kDAAM,QAAQ,QAAQ;;;;;;;;;;;;0CAEzB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oMAAA,CAAA,QAAK;wCAAC,MAAM;;;;;;kDACb,8OAAC;;4CAAM,QAAQ,QAAQ;4CAAC;;;;;;;;;;;;;0CAE1B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0MAAA,CAAA,SAAM;wCAAC,MAAM;;;;;;kDACd,8OAAC;kDAAM,QAAQ,MAAM;;;;;;;;;;;;0CAEvB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,MAAM;;;;;;kDAChB,8OAAC;;4CAAM,QAAQ,aAAa,CAAC,MAAM;4CAAC;;;;;;;;;;;;;;;;;;;kCAKxC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA2C;;;;;;0CACzD,8OAAC;gCAAI,WAAU;0CACZ,QAAQ,QAAQ,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,SAAS,sBAC1C,8OAAC;wCAAgB,WAAU;;0DACzB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;oDAAa,MAAM;;;;;;;;;;;0DAErC,8OAAC;gDAAK,WAAU;0DAAyC;;;;;;;uCAJjD;;;;;;;;;;;;;;;;oBAWf,aAAa,QAAQ,KAAK,kBACzB,8OAAC;wBAAI,WAAU;;4BACZ,QAAQ,KAAK,CAAC,WAAW,kBACxB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;4CAAoC,QAAQ,KAAK,CAAC,WAAW;4CAAC;;;;;;;kDAC7E,8OAAC;wCAAI,WAAU;kDAAwB;;;;;;;;;;;;4BAG1C,QAAQ,KAAK,CAAC,iBAAiB,kBAC9B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;4CAAmC,QAAQ,KAAK,CAAC,iBAAiB;4CAAC;;;;;;;kDAClF,8OAAC;wCAAI,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;kCAO/C,8OAAC;wBAAI,WAAU;;4BACZ,QAAQ,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,KAAK,sBAClC,8OAAC;oCAEC,WAAU;8CAET;mCAHI;;;;;4BAMR,QAAQ,IAAI,CAAC,MAAM,GAAG,mBACrB,8OAAC;gCAAK,WAAU;;oCAAoE;oCAChF,QAAQ,IAAI,CAAC,MAAM,GAAG;oCAAE;;;;;;;;;;;;;kCAMhC,8OAAC,4JAAA,CAAA,UAAI;wBACH,MAAM,CAAC,UAAU,EAAE,QAAQ,IAAI,EAAE;wBACjC,WAAW,CAAC,sFAAsF,EAAE,eAAe,QAAQ,QAAQ,EAAE,sHAAsH,CAAC;;0CAE5P,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAK,WAAU;0CAAqB;;;;;;0CACrC,8OAAC,kNAAA,CAAA,aAAU;gCAAC,WAAU;gCAAgF,MAAM;;;;;;;;;;;;oBAI7G,QAAQ,aAAa,CAAC,MAAM,GAAG,mBAC9B,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAI,WAAU;sDAAsC;;;;;;sDACrD,8OAAC;4CAAI,WAAU;sDAAyB,QAAQ,aAAa,CAAC,EAAE,CAAC,IAAI;;;;;;;;;;;;8CAEvE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;4CAAgB,MAAM;;;;;;sDACvC,8OAAC;4CAAK,WAAU;;gDACb,QAAQ,aAAa,CAAC,EAAE,CAAC,QAAQ;gDAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASrD;uCAEe", "debugId": null}}, {"offset": {"line": 2771, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/fwu_incubation%28react%2Blaravel%29/incubation/frontend/src/app/components/programs/ProgramTypesSection.tsx"], "sourcesContent": ["\r\n\"use client\"\r\nimport { useState, useEffect } from 'react';\r\nimport ProgramTypeCard from './ProgramTypeCard';\r\nimport { programsData } from '../../../data/programsData';\r\nimport { Sparkles, Target, Users, Award, TrendingUp, Filter, Search, ArrowRight } from 'lucide-react';\r\n\r\nconst ProgramTypesSection = () => {\r\n  const [isVisible, setIsVisible] = useState(false);\r\n  const [filteredPrograms, setFilteredPrograms] = useState(programsData);\r\n  const [selectedCategory, setSelectedCategory] = useState('all');\r\n  const [searchTerm, setSearchTerm] = useState('');\r\n\r\n  useEffect(() => {\r\n    setIsVisible(true);\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    let filtered = programsData;\r\n\r\n    // Filter by category\r\n    if (selectedCategory !== 'all') {\r\n      filtered = filtered.filter(program => program.category === selectedCategory);\r\n    }\r\n\r\n    // Filter by search term\r\n    if (searchTerm) {\r\n      filtered = filtered.filter(program =>\r\n        program.title.toLowerCase().includes(searchTerm.toLowerCase()) ||\r\n        program.shortDescription.toLowerCase().includes(searchTerm.toLowerCase()) ||\r\n        program.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))\r\n      );\r\n    }\r\n\r\n    setFilteredPrograms(filtered);\r\n  }, [selectedCategory, searchTerm]);\r\n\r\n  const categories = ['all', ...Array.from(new Set(programsData.map(p => p.category)))];\r\n\r\n  return (\r\n    <section className=\"py-20 md:py-28 bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 relative overflow-hidden\">\r\n      {/* Background Elements */}\r\n      <div className=\"absolute inset-0 overflow-hidden\">\r\n        <div className=\"absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-400/10 to-purple-400/10 rounded-full blur-3xl\"></div>\r\n        <div className=\"absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-indigo-400/10 to-pink-400/10 rounded-full blur-3xl\"></div>\r\n\r\n        {/* Floating Elements */}\r\n        {[...Array(20)].map((_, i) => (\r\n          <div\r\n            key={i}\r\n            className={`absolute rounded-full ${\r\n              i % 4 === 0 ? 'bg-blue-400/20' :\r\n              i % 4 === 1 ? 'bg-purple-400/20' :\r\n              i % 4 === 2 ? 'bg-indigo-400/20' : 'bg-pink-400/20'\r\n            }`}\r\n            style={{\r\n              width: `${Math.random() * 4 + 2}px`,\r\n              height: `${Math.random() * 4 + 2}px`,\r\n              top: `${Math.random() * 100}%`,\r\n              left: `${Math.random() * 100}%`,\r\n              animation: `float-gentle ${\r\n                8 + Math.random() * 12\r\n              }s infinite ease-in-out`,\r\n              animationDelay: `${Math.random() * 8}s`,\r\n            }}\r\n          ></div>\r\n        ))}\r\n      </div>\r\n\r\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 relative z-10\">\r\n        {/* Enhanced Section Header */}\r\n        <div className={`text-center mb-16 transition-all duration-1000 transform ${\r\n          isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'\r\n        }`}>\r\n          <div className=\"inline-block mb-6\">\r\n            <div className=\"flex items-center justify-center space-x-3 bg-white/80 backdrop-blur-md px-6 py-3 rounded-2xl border border-white/50 shadow-lg\">\r\n              <Sparkles className=\"text-blue-600\" size={20} />\r\n              <span className=\"text-blue-600 font-semibold tracking-wide\">INNOVATE & GROW</span>\r\n              <Target className=\"text-purple-600\" size={16} />\r\n            </div>\r\n          </div>\r\n\r\n          <h2 className=\"text-4xl md:text-5xl lg:text-6xl font-black mb-6 leading-tight\">\r\n            <span className=\"text-gray-900\">FWU </span>\r\n            <span className=\"text-transparent bg-clip-text bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600\">\r\n              Incubation\r\n            </span>\r\n            <br />\r\n            <span className=\"text-transparent bg-clip-text bg-gradient-to-r from-purple-600 via-indigo-600 to-blue-600\">\r\n              Programs\r\n            </span>\r\n          </h2>\r\n\r\n          <p className=\"text-xl text-gray-700 max-w-3xl mx-auto leading-relaxed mb-8\">\r\n            Transform your innovative ideas into successful ventures with our comprehensive\r\n            <span className=\"font-semibold text-blue-600\"> incubation programs</span>.\r\n            Join a vibrant community of entrepreneurs and change-makers.\r\n          </p>\r\n\r\n          {/* Program Stats */}\r\n          <div className=\"grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto\">\r\n            {[\r\n              { icon: Users, number: \"500+\", label: \"Entrepreneurs\", color: \"text-blue-600\" },\r\n              { icon: Award, number: \"50+\", label: \"Success Stories\", color: \"text-purple-600\" },\r\n              { icon: TrendingUp, number: \"95%\", label: \"Success Rate\", color: \"text-indigo-600\" },\r\n              { icon: Target, number: \"20+\", label: \"Active Programs\", color: \"text-pink-600\" }\r\n            ].map((stat, index) => (\r\n              <div\r\n                key={index}\r\n                className={`bg-white/80 backdrop-blur-md p-4 rounded-2xl border border-white/50 shadow-lg hover:shadow-xl transition-all duration-300 group ${\r\n                  isVisible ? 'animate-fade-in-up' : 'opacity-0'\r\n                }`}\r\n                style={{ animationDelay: `${600 + index * 150}ms` }}\r\n              >\r\n                <stat.icon className={`${stat.color} mx-auto mb-2 group-hover:scale-110 transition-transform duration-300`} size={24} />\r\n                <div className={`text-2xl font-bold ${stat.color} mb-1`}>{stat.number}</div>\r\n                <div className=\"text-gray-600 text-sm font-medium\">{stat.label}</div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Enhanced Search and Filter Section */}\r\n        <div className={`mb-16 transition-all duration-1000 delay-300 transform ${\r\n          isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'\r\n        }`}>\r\n          <div className=\"bg-white/80 backdrop-blur-md rounded-3xl p-8 border border-white/50 shadow-xl\">\r\n            <div className=\"flex flex-col lg:flex-row gap-6\">\r\n              {/* Search Bar */}\r\n              <div className=\"relative flex-grow\">\r\n                <div className=\"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none\">\r\n                  <Search className=\"text-blue-500\" size={20} />\r\n                </div>\r\n                <input\r\n                  type=\"text\"\r\n                  placeholder=\"Search programs, skills, or topics...\"\r\n                  value={searchTerm}\r\n                  onChange={(e) => setSearchTerm(e.target.value)}\r\n                  className=\"block w-full pl-12 pr-4 py-4 border-2 border-gray-200 rounded-2xl focus:ring-2 focus:ring-blue-400 focus:border-blue-400 transition-all duration-300 bg-white/90 backdrop-blur-sm text-gray-900 placeholder-gray-500\"\r\n                />\r\n              </div>\r\n\r\n              {/* Category Filter */}\r\n              <div className=\"relative\">\r\n                <div className=\"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none\">\r\n                  <Filter className=\"text-purple-500\" size={20} />\r\n                </div>\r\n                <select\r\n                  value={selectedCategory}\r\n                  onChange={(e) => setSelectedCategory(e.target.value)}\r\n                  className=\"block w-full lg:w-64 pl-12 pr-10 py-4 border-2 border-gray-200 rounded-2xl focus:ring-2 focus:ring-purple-400 focus:border-purple-400 transition-all duration-300 appearance-none bg-white/90 backdrop-blur-sm text-gray-900\"\r\n                >\r\n                  <option value=\"all\">All Categories</option>\r\n                  {categories.filter(cat => cat !== 'all').map(category => (\r\n                    <option key={category} value={category}>\r\n                      {category.charAt(0).toUpperCase() + category.slice(1).replace('-', ' ')}\r\n                    </option>\r\n                  ))}\r\n                </select>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Active Filters Display */}\r\n            {(selectedCategory !== 'all' || searchTerm) && (\r\n              <div className=\"mt-4 flex flex-wrap gap-2\">\r\n                {selectedCategory !== 'all' && (\r\n                  <span className=\"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-purple-100 text-purple-800\">\r\n                    Category: {selectedCategory.charAt(0).toUpperCase() + selectedCategory.slice(1)}\r\n                    <button\r\n                      onClick={() => setSelectedCategory('all')}\r\n                      className=\"ml-2 text-purple-600 hover:text-purple-800\"\r\n                    >\r\n                      ×\r\n                    </button>\r\n                  </span>\r\n                )}\r\n                {searchTerm && (\r\n                  <span className=\"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800\">\r\n                    Search: \"{searchTerm}\"\r\n                    <button\r\n                      onClick={() => setSearchTerm('')}\r\n                      className=\"ml-2 text-blue-600 hover:text-blue-800\"\r\n                    >\r\n                      ×\r\n                    </button>\r\n                  </span>\r\n                )}\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Enhanced Program Cards */}\r\n        <div className=\"grid md:grid-cols-2 lg:grid-cols-2 gap-8\">\r\n          {filteredPrograms.length > 0 ? (\r\n            filteredPrograms.map((program, index) => (\r\n              <div\r\n                key={program.id}\r\n                className={`transition-all duration-700 transform ${\r\n                  isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'\r\n                }`}\r\n                style={{\r\n                  animationDelay: `${800 + index * 200}ms`,\r\n                  transitionDelay: `${800 + index * 200}ms`\r\n                }}\r\n              >\r\n                <ProgramTypeCard\r\n                  program={program}\r\n                />\r\n              </div>\r\n            ))\r\n          ) : (\r\n            <div className=\"col-span-full text-center py-20\">\r\n              <div className=\"w-24 h-24 mx-auto bg-gray-100 rounded-full flex items-center justify-center mb-6\">\r\n                <Search className=\"text-gray-400\" size={32} />\r\n              </div>\r\n              <h3 className=\"text-2xl font-bold text-gray-900 mb-3\">No Programs Found</h3>\r\n              <p className=\"text-gray-600 max-w-md mx-auto mb-6\">\r\n                No programs match your current search criteria. Try adjusting your filters or search terms.\r\n              </p>\r\n              <button\r\n                onClick={() => {\r\n                  setSearchTerm('');\r\n                  setSelectedCategory('all');\r\n                }}\r\n                className=\"px-6 py-3 bg-blue-600 text-white rounded-xl hover:bg-blue-700 transition-colors duration-300\"\r\n              >\r\n                Clear Filters\r\n              </button>\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        {/* Enhanced CTA Section */}\r\n        <div className={`mt-24 transition-all duration-1000 delay-700 transform ${\r\n          isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'\r\n        }`}>\r\n          <div className=\"relative bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900 rounded-3xl shadow-2xl overflow-hidden\">\r\n            {/* Background Elements */}\r\n            <div className=\"absolute inset-0\">\r\n              <div className=\"absolute top-20 right-20 w-64 h-64 bg-blue-500/10 rounded-full blur-3xl\"></div>\r\n              <div className=\"absolute bottom-20 left-20 w-64 h-64 bg-purple-500/10 rounded-full blur-3xl\"></div>\r\n\r\n              {/* Floating particles */}\r\n              {[...Array(15)].map((_, i) => (\r\n                <div\r\n                  key={i}\r\n                  className=\"absolute w-1 h-1 bg-white/30 rounded-full\"\r\n                  style={{\r\n                    top: `${Math.random() * 100}%`,\r\n                    left: `${Math.random() * 100}%`,\r\n                    animation: `float-gentle ${\r\n                      6 + Math.random() * 8\r\n                    }s infinite ease-in-out`,\r\n                    animationDelay: `${Math.random() * 6}s`,\r\n                  }}\r\n                ></div>\r\n              ))}\r\n            </div>\r\n\r\n            <div className=\"relative z-10 p-12 lg:p-16\">\r\n              <div className=\"grid lg:grid-cols-2 gap-12 items-center\">\r\n                {/* Left Content */}\r\n                <div>\r\n                  <div className=\"inline-block mb-6\">\r\n                    <div className=\"flex items-center space-x-3 bg-white/10 backdrop-blur-md px-6 py-3 rounded-2xl border border-white/20\">\r\n                      <Sparkles className=\"text-yellow-400\" size={20} />\r\n                      <span className=\"text-white font-semibold\">Join Our Community</span>\r\n                      <Target className=\"text-yellow-400\" size={16} />\r\n                    </div>\r\n                  </div>\r\n\r\n                  <h3 className=\"text-3xl lg:text-4xl font-bold text-white mb-6 leading-tight\">\r\n                    Ready to Transform Your\r\n                    <span className=\"text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-400\"> Ideas</span> into\r\n                    <span className=\"text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-pink-400\"> Reality</span>?\r\n                  </h3>\r\n\r\n                  <p className=\"text-blue-100 text-lg mb-8 leading-relaxed\">\r\n                    Join the Far Western University Incubation Center and become part of a vibrant ecosystem of\r\n                    innovators, entrepreneurs, and change-makers. We welcome students, faculty, alumni, and\r\n                    external entrepreneurs with groundbreaking ideas.\r\n                  </p>\r\n\r\n                  {/* Features */}\r\n                  <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-4 mb-8\">\r\n                    {[\r\n                      { icon: Users, text: \"Expert Mentorship\", color: \"text-blue-400\" },\r\n                      { icon: Award, text: \"Funding Opportunities\", color: \"text-purple-400\" },\r\n                      { icon: Target, text: \"Workspace Access\", color: \"text-indigo-400\" },\r\n                      { icon: TrendingUp, text: \"Network Building\", color: \"text-pink-400\" }\r\n                    ].map((feature, index) => (\r\n                      <div key={index} className=\"flex items-center space-x-3 p-3 bg-white/5 backdrop-blur-sm rounded-xl border border-white/10\">\r\n                        <feature.icon className={feature.color} size={20} />\r\n                        <span className=\"text-white font-medium\">{feature.text}</span>\r\n                      </div>\r\n                    ))}\r\n                  </div>\r\n\r\n                  {/* Action Buttons */}\r\n                  <div className=\"flex flex-col sm:flex-row gap-4\">\r\n                    <a\r\n                      href=\"/apply\"\r\n                      className=\"group relative px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-bold rounded-2xl transition-all duration-300 hover:shadow-2xl hover:shadow-blue-500/25 hover:-translate-y-1 flex items-center justify-center overflow-hidden\"\r\n                    >\r\n                      <div className=\"absolute inset-0 bg-gradient-to-r from-blue-700 to-purple-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\r\n                      <span className=\"relative z-10\">Apply Now</span>\r\n                      <ArrowRight className=\"relative z-10 ml-2 group-hover:translate-x-1 transition-transform duration-300\" />\r\n                    </a>\r\n\r\n                    <a\r\n                      href=\"/contact\"\r\n                      className=\"group relative px-8 py-4 bg-white/10 hover:bg-white/20 backdrop-blur-md text-white font-bold rounded-2xl transition-all duration-300 border-2 border-white/30 hover:border-white/50 hover:shadow-2xl hover:shadow-white/10 hover:-translate-y-1 flex items-center justify-center overflow-hidden\"\r\n                    >\r\n                      <div className=\"absolute inset-0 bg-white/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\r\n                      <span className=\"relative z-10\">Contact Us</span>\r\n                    </a>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Right Content - Stats Card */}\r\n                <div className=\"relative\">\r\n                  <div className=\"bg-white/10 backdrop-blur-md rounded-3xl p-8 border border-white/20 shadow-2xl\">\r\n                    <h4 className=\"text-2xl font-bold text-white mb-6 text-center\">Our Impact</h4>\r\n\r\n                    <div className=\"grid grid-cols-2 gap-6\">\r\n                      <div className=\"text-center\">\r\n                        <div className=\"text-3xl font-bold text-blue-400 mb-2\">500+</div>\r\n                        <div className=\"text-blue-200 text-sm\">Entrepreneurs Supported</div>\r\n                      </div>\r\n                      <div className=\"text-center\">\r\n                        <div className=\"text-3xl font-bold text-purple-400 mb-2\">50+</div>\r\n                        <div className=\"text-purple-200 text-sm\">Successful Startups</div>\r\n                      </div>\r\n                      <div className=\"text-center\">\r\n                        <div className=\"text-3xl font-bold text-indigo-400 mb-2\">95%</div>\r\n                        <div className=\"text-indigo-200 text-sm\">Success Rate</div>\r\n                      </div>\r\n                      <div className=\"text-center\">\r\n                        <div className=\"text-3xl font-bold text-pink-400 mb-2\">$2M+</div>\r\n                        <div className=\"text-pink-200 text-sm\">Funding Raised</div>\r\n                      </div>\r\n                    </div>\r\n\r\n                    <div className=\"mt-8 p-4 bg-white/5 rounded-2xl border border-white/10\">\r\n                      <p className=\"text-white text-center text-sm leading-relaxed\">\r\n                        &quot;The FWU Incubation Center transformed my idea into a thriving business.\r\n                        The mentorship and resources were invaluable.&quot;\r\n                      </p>\r\n                      <div className=\"text-center mt-3\">\r\n                        <div className=\"text-blue-300 font-semibold text-sm\">- Sarah Chen</div>\r\n                        <div className=\"text-blue-200 text-xs\">TechStart Founder</div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n\r\n                  {/* Floating Elements */}\r\n                  <div className=\"absolute -top-4 -right-4 w-16 h-16 bg-gradient-to-r from-yellow-400 to-orange-400 rounded-2xl flex items-center justify-center shadow-lg\">\r\n                    <Award className=\"text-white\" size={24} />\r\n                  </div>\r\n\r\n                  <div className=\"absolute -bottom-4 -left-4 w-12 h-12 bg-gradient-to-r from-green-400 to-blue-400 rounded-xl flex items-center justify-center shadow-lg\">\r\n                    <Target className=\"text-white\" size={16} />\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Custom CSS for animations */}\r\n        <style jsx global>{`\r\n          @keyframes float-gentle {\r\n            0%, 100% { transform: translate(0, 0) rotate(0deg); }\r\n            25% { transform: translate(5px, -5px) rotate(45deg); }\r\n            50% { transform: translate(-3px, -8px) rotate(90deg); }\r\n            75% { transform: translate(-5px, 3px) rotate(135deg); }\r\n          }\r\n\r\n          @keyframes fade-in-up {\r\n            from {\r\n              opacity: 0;\r\n              transform: translateY(30px);\r\n            }\r\n            to {\r\n              opacity: 1;\r\n              transform: translateY(0);\r\n            }\r\n          }\r\n\r\n          .animate-fade-in-up {\r\n            animation: fade-in-up 0.6s ease-out forwards;\r\n          }\r\n\r\n          .line-clamp-3 {\r\n            display: -webkit-box;\r\n            -webkit-line-clamp: 3;\r\n            -webkit-box-orient: vertical;\r\n            overflow: hidden;\r\n          }\r\n        `}</style>\r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\nexport default ProgramTypesSection;"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;;;AAMA,MAAM,sBAAsB;IAC1B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,4HAAA,CAAA,eAAY;IACrE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,aAAa;IACf,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW,4HAAA,CAAA,eAAY;QAE3B,qBAAqB;QACrB,IAAI,qBAAqB,OAAO;YAC9B,WAAW,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,QAAQ,KAAK;QAC7D;QAEA,wBAAwB;QACxB,IAAI,YAAY;YACd,WAAW,SAAS,MAAM,CAAC,CAAA,UACzB,QAAQ,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC3D,QAAQ,gBAAgB,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACtE,QAAQ,IAAI,CAAC,IAAI,CAAC,CAAA,MAAO,IAAI,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QAE9E;QAEA,oBAAoB;IACtB,GAAG;QAAC;QAAkB;KAAW;IAEjC,MAAM,aAAa;QAAC;WAAU,MAAM,IAAI,CAAC,IAAI,IAAI,4HAAA,CAAA,eAAY,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,QAAQ;KAAI;IAErF,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;oBAGd;2BAAI,MAAM;qBAAI,CAAC,GAAG,CAAC,CAAC,GAAG,kBACtB,8OAAC;4BAEC,WAAW,CAAC,sBAAsB,EAChC,IAAI,MAAM,IAAI,mBACd,IAAI,MAAM,IAAI,qBACd,IAAI,MAAM,IAAI,qBAAqB,kBACnC;4BACF,OAAO;gCACL,OAAO,GAAG,KAAK,MAAM,KAAK,IAAI,EAAE,EAAE,CAAC;gCACnC,QAAQ,GAAG,KAAK,MAAM,KAAK,IAAI,EAAE,EAAE,CAAC;gCACpC,KAAK,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;gCAC9B,MAAM,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;gCAC/B,WAAW,CAAC,aAAa,EACvB,IAAI,KAAK,MAAM,KAAK,GACrB,sBAAsB,CAAC;gCACxB,gBAAgB,GAAG,KAAK,MAAM,KAAK,EAAE,CAAC,CAAC;4BACzC;2BAfK;;;;;;;;;;;0BAoBX,8OAAC;0DAAc;;kCAEb,8OAAC;kEAAe,CAAC,yDAAyD,EACxE,YAAY,8BAA8B,4BAC1C;;0CACA,8OAAC;0EAAc;0CACb,cAAA,8OAAC;8EAAc;;sDACb,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;4CAAgB,MAAM;;;;;;sDAC1C,8OAAC;sFAAe;sDAA4C;;;;;;sDAC5D,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;4CAAkB,MAAM;;;;;;;;;;;;;;;;;0CAI9C,8OAAC;0EAAa;;kDACZ,8OAAC;kFAAe;kDAAgB;;;;;;kDAChC,8OAAC;kFAAe;kDAA4F;;;;;;kDAG5G,8OAAC;;;;;;;kDACD,8OAAC;kFAAe;kDAA4F;;;;;;;;;;;;0CAK9G,8OAAC;0EAAY;;oCAA+D;kDAE1E,8OAAC;kFAAe;kDAA8B;;;;;;oCAA2B;;;;;;;0CAK3E,8OAAC;0EAAc;0CACZ;oCACC;wCAAE,MAAM,oMAAA,CAAA,QAAK;wCAAE,QAAQ;wCAAQ,OAAO;wCAAiB,OAAO;oCAAgB;oCAC9E;wCAAE,MAAM,oMAAA,CAAA,QAAK;wCAAE,QAAQ;wCAAO,OAAO;wCAAmB,OAAO;oCAAkB;oCACjF;wCAAE,MAAM,kNAAA,CAAA,aAAU;wCAAE,QAAQ;wCAAO,OAAO;wCAAgB,OAAO;oCAAkB;oCACnF;wCAAE,MAAM,sMAAA,CAAA,SAAM;wCAAE,QAAQ;wCAAO,OAAO;wCAAmB,OAAO;oCAAgB;iCACjF,CAAC,GAAG,CAAC,CAAC,MAAM,sBACX,8OAAC;wCAKC,OAAO;4CAAE,gBAAgB,GAAG,MAAM,QAAQ,IAAI,EAAE,CAAC;wCAAC;kFAHvC,CAAC,gIAAgI,EAC1I,YAAY,uBAAuB,aACnC;;0DAGF,8OAAC,KAAK,IAAI;gDAAC,WAAW,GAAG,KAAK,KAAK,CAAC,qEAAqE,CAAC;gDAAE,MAAM;;;;;;0DAClH,8OAAC;0FAAe,CAAC,mBAAmB,EAAE,KAAK,KAAK,CAAC,KAAK,CAAC;0DAAG,KAAK,MAAM;;;;;;0DACrE,8OAAC;0FAAc;0DAAqC,KAAK,KAAK;;;;;;;uCARzD;;;;;;;;;;;;;;;;kCAeb,8OAAC;kEAAe,CAAC,uDAAuD,EACtE,YAAY,8BAA8B,4BAC1C;kCACA,cAAA,8OAAC;sEAAc;;8CACb,8OAAC;8EAAc;;sDAEb,8OAAC;sFAAc;;8DACb,8OAAC;8FAAc;8DACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;wDAAC,WAAU;wDAAgB,MAAM;;;;;;;;;;;8DAE1C,8OAAC;oDACC,MAAK;oDACL,aAAY;oDACZ,OAAO;oDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;8FACnC;;;;;;;;;;;;sDAKd,8OAAC;sFAAc;;8DACb,8OAAC;8FAAc;8DACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;wDAAC,WAAU;wDAAkB,MAAM;;;;;;;;;;;8DAE5C,8OAAC;oDACC,OAAO;oDACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;8FACzC;;sEAEV,8OAAC;4DAAO,OAAM;;sEAAM;;;;;;wDACnB,WAAW,MAAM,CAAC,CAAA,MAAO,QAAQ,OAAO,GAAG,CAAC,CAAA,yBAC3C,8OAAC;gEAAsB,OAAO;;0EAC3B,SAAS,MAAM,CAAC,GAAG,WAAW,KAAK,SAAS,KAAK,CAAC,GAAG,OAAO,CAAC,KAAK;+DADxD;;;;;;;;;;;;;;;;;;;;;;;gCASpB,CAAC,qBAAqB,SAAS,UAAU,mBACxC,8OAAC;8EAAc;;wCACZ,qBAAqB,uBACpB,8OAAC;sFAAe;;gDAAoG;gDACvG,iBAAiB,MAAM,CAAC,GAAG,WAAW,KAAK,iBAAiB,KAAK,CAAC;8DAC7E,8OAAC;oDACC,SAAS,IAAM,oBAAoB;8FACzB;8DACX;;;;;;;;;;;;wCAKJ,4BACC,8OAAC;sFAAe;;gDAAgG;gDACpG;gDAAW;8DACrB,8OAAC;oDACC,SAAS,IAAM,cAAc;8FACnB;8DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAWb,8OAAC;kEAAc;kCACZ,iBAAiB,MAAM,GAAG,IACzB,iBAAiB,GAAG,CAAC,CAAC,SAAS,sBAC7B,8OAAC;gCAKC,OAAO;oCACL,gBAAgB,GAAG,MAAM,QAAQ,IAAI,EAAE,CAAC;oCACxC,iBAAiB,GAAG,MAAM,QAAQ,IAAI,EAAE,CAAC;gCAC3C;0EANW,CAAC,sCAAsC,EAChD,YAAY,8BAA8B,4BAC1C;0CAMF,cAAA,8OAAC,wJAAA,CAAA,UAAe;oCACd,SAAS;;;;;;+BAVN,QAAQ,EAAE;;;;sDAenB,8OAAC;sEAAc;;8CACb,8OAAC;8EAAc;8CACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;wCAAgB,MAAM;;;;;;;;;;;8CAE1C,8OAAC;8EAAa;8CAAwC;;;;;;8CACtD,8OAAC;8EAAY;8CAAsC;;;;;;8CAGnD,8OAAC;oCACC,SAAS;wCACP,cAAc;wCACd,oBAAoB;oCACtB;8EACU;8CACX;;;;;;;;;;;;;;;;;kCAQP,8OAAC;kEAAe,CAAC,uDAAuD,EACtE,YAAY,8BAA8B,4BAC1C;kCACA,cAAA,8OAAC;sEAAc;;8CAEb,8OAAC;8EAAc;;sDACb,8OAAC;sFAAc;;;;;;sDACf,8OAAC;sFAAc;;;;;;wCAGd;+CAAI,MAAM;yCAAI,CAAC,GAAG,CAAC,CAAC,GAAG,kBACtB,8OAAC;gDAGC,OAAO;oDACL,KAAK,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;oDAC9B,MAAM,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;oDAC/B,WAAW,CAAC,aAAa,EACvB,IAAI,KAAK,MAAM,KAAK,EACrB,sBAAsB,CAAC;oDACxB,gBAAgB,GAAG,KAAK,MAAM,KAAK,EAAE,CAAC,CAAC;gDACzC;0FARU;+CADL;;;;;;;;;;;8CAcX,8OAAC;8EAAc;8CACb,cAAA,8OAAC;kFAAc;;0DAEb,8OAAC;;;kEACC,8OAAC;kGAAc;kEACb,cAAA,8OAAC;sGAAc;;8EACb,8OAAC,0MAAA,CAAA,WAAQ;oEAAC,WAAU;oEAAkB,MAAM;;;;;;8EAC5C,8OAAC;8GAAe;8EAA2B;;;;;;8EAC3C,8OAAC,sMAAA,CAAA,SAAM;oEAAC,WAAU;oEAAkB,MAAM;;;;;;;;;;;;;;;;;kEAI9C,8OAAC;kGAAa;;4DAA+D;0EAE3E,8OAAC;0GAAe;0EAA6E;;;;;;4DAAa;0EAC1G,8OAAC;0GAAe;0EAA6E;;;;;;4DAAe;;;;;;;kEAG9G,8OAAC;kGAAY;kEAA6C;;;;;;kEAO1D,8OAAC;kGAAc;kEACZ;4DACC;gEAAE,MAAM,oMAAA,CAAA,QAAK;gEAAE,MAAM;gEAAqB,OAAO;4DAAgB;4DACjE;gEAAE,MAAM,oMAAA,CAAA,QAAK;gEAAE,MAAM;gEAAyB,OAAO;4DAAkB;4DACvE;gEAAE,MAAM,sMAAA,CAAA,SAAM;gEAAE,MAAM;gEAAoB,OAAO;4DAAkB;4DACnE;gEAAE,MAAM,kNAAA,CAAA,aAAU;gEAAE,MAAM;gEAAoB,OAAO;4DAAgB;yDACtE,CAAC,GAAG,CAAC,CAAC,SAAS,sBACd,8OAAC;0GAA0B;;kFACzB,8OAAC,QAAQ,IAAI;wEAAC,WAAW,QAAQ,KAAK;wEAAE,MAAM;;;;;;kFAC9C,8OAAC;kHAAe;kFAA0B,QAAQ,IAAI;;;;;;;+DAF9C;;;;;;;;;;kEAQd,8OAAC;kGAAc;;0EACb,8OAAC;gEACC,MAAK;0GACK;;kFAEV,8OAAC;kHAAc;;;;;;kFACf,8OAAC;kHAAe;kFAAgB;;;;;;kFAChC,8OAAC,kNAAA,CAAA,aAAU;wEAAC,WAAU;;;;;;;;;;;;0EAGxB,8OAAC;gEACC,MAAK;0GACK;;kFAEV,8OAAC;kHAAc;;;;;;kFACf,8OAAC;kHAAe;kFAAgB;;;;;;;;;;;;;;;;;;;;;;;;0DAMtC,8OAAC;0FAAc;;kEACb,8OAAC;kGAAc;;0EACb,8OAAC;0GAAa;0EAAiD;;;;;;0EAE/D,8OAAC;0GAAc;;kFACb,8OAAC;kHAAc;;0FACb,8OAAC;0HAAc;0FAAwC;;;;;;0FACvD,8OAAC;0HAAc;0FAAwB;;;;;;;;;;;;kFAEzC,8OAAC;kHAAc;;0FACb,8OAAC;0HAAc;0FAA0C;;;;;;0FACzD,8OAAC;0HAAc;0FAA0B;;;;;;;;;;;;kFAE3C,8OAAC;kHAAc;;0FACb,8OAAC;0HAAc;0FAA0C;;;;;;0FACzD,8OAAC;0HAAc;0FAA0B;;;;;;;;;;;;kFAE3C,8OAAC;kHAAc;;0FACb,8OAAC;0HAAc;0FAAwC;;;;;;0FACvD,8OAAC;0HAAc;0FAAwB;;;;;;;;;;;;;;;;;;0EAI3C,8OAAC;0GAAc;;kFACb,8OAAC;kHAAY;kFAAiD;;;;;;kFAI9D,8OAAC;kHAAc;;0FACb,8OAAC;0HAAc;0FAAsC;;;;;;0FACrD,8OAAC;0HAAc;0FAAwB;;;;;;;;;;;;;;;;;;;;;;;;kEAM7C,8OAAC;kGAAc;kEACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;4DAAa,MAAM;;;;;;;;;;;kEAGtC,8OAAC;kGAAc;kEACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;4DAAC,WAAU;4DAAa,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0CzD;uCAEe", "debugId": null}}, {"offset": {"line": 3787, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/fwu_incubation%28react%2Blaravel%29/incubation/frontend/src/app/programs/page.tsx"], "sourcesContent": ["\"use client\"\r\nimport { useState, useEffect, useRef } from 'react';\r\nimport PastEventsGallerySection from \"../components/programs/PastEventsGallerySection \";\r\nimport ProgramCalendarSection from \"../components/programs/ProgramCalendarSection \";\r\nimport ProgramTypesSection from \"../components/programs/ProgramTypesSection\";\r\nimport {\r\n  ArrowDown,\r\n  Sparkles,\r\n  Target,\r\n  Users,\r\n  Award,\r\n\r\n  Rocket,\r\n  Star,\r\n  ChevronRight,\r\n  Play\r\n} from 'lucide-react';\r\nimport Image from 'next/image';\r\nimport Link from 'next/link';\r\n\r\nexport default function ProgramsPage() {\r\n  const [isVisible, setIsVisible] = useState(false);\r\n  const [activeFeature, setActiveFeature] = useState(0);\r\n  const heroRef = useRef(null);\r\n\r\n  useEffect(() => {\r\n    setIsVisible(true);\r\n\r\n    // Auto-rotate features\r\n    const interval = setInterval(() => {\r\n      setActiveFeature((prev) => (prev + 1) % 4);\r\n    }, 3000);\r\n\r\n    return () => clearInterval(interval);\r\n  }, []);\r\n\r\n  // Function to scroll to the next section smoothly\r\n  const scrollToNextSection = () => {\r\n    const programsSection = document.getElementById('program-types');\r\n    if (programsSection) {\r\n      programsSection.scrollIntoView({ behavior: 'smooth' });\r\n    }\r\n  };\r\n\r\n \r\n  return (\r\n    <>\r\n      {/* Enhanced Hero Section with Modern Design */}\r\n      <section\r\n        ref={heroRef}\r\n        className=\"relative min-h-[100vh] flex items-center overflow-hidden bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900\"\r\n      >\r\n        {/* Dynamic Background Elements */}\r\n        <div className=\"absolute inset-0\">\r\n          {/* Hero Background Image */}\r\n          <Image\r\n            src=\"https://images.unsplash.com/photo-1522202176988-66273c2fd55f?q=80&w=2000&auto=format&fit=crop\"\r\n            alt=\"FWU Incubation Programs\"\r\n            fill\r\n            priority\r\n            className=\"object-cover opacity-20\"\r\n          />\r\n\r\n          {/* Gradient Overlays */}\r\n          <div className=\"absolute inset-0 bg-gradient-to-br from-blue-900/95 via-indigo-900/90 to-purple-900/95\"></div>\r\n          <div className=\"absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent\"></div>\r\n\r\n          {/* Animated Mesh Pattern */}\r\n          <div\r\n            className=\"absolute inset-0 opacity-10\"\r\n            style={{\r\n              backgroundImage: `\r\n                linear-gradient(rgba(59, 130, 246, 0.3) 1px, transparent 1px),\r\n                linear-gradient(90deg, rgba(59, 130, 246, 0.3) 1px, transparent 1px)\r\n              `,\r\n              backgroundSize: '60px 60px',\r\n              animation: 'mesh-drift 25s linear infinite'\r\n            }}\r\n          ></div>\r\n        </div>\r\n\r\n        {/* Floating Elements */}\r\n        <div className=\"absolute inset-0 overflow-hidden\">\r\n          {/* Animated Particles */}\r\n          {[...Array(40)].map((_, i) => (\r\n            <div\r\n              key={i}\r\n              className={`absolute rounded-full ${\r\n                i % 5 === 0 ? 'bg-blue-400/30' :\r\n                i % 5 === 1 ? 'bg-purple-400/30' :\r\n                i % 5 === 2 ? 'bg-indigo-400/30' :\r\n                i % 5 === 3 ? 'bg-pink-400/30' : 'bg-cyan-400/30'\r\n              }`}\r\n              style={{\r\n                width: `${Math.random() * 8 + 3}px`,\r\n                height: `${Math.random() * 8 + 3}px`,\r\n                top: `${Math.random() * 100}%`,\r\n                left: `${Math.random() * 100}%`,\r\n                animation: `float-drift ${\r\n                  10 + Math.random() * 20\r\n                }s infinite ease-in-out`,\r\n                animationDelay: `${Math.random() * 10}s`,\r\n              }}\r\n            ></div>\r\n          ))}\r\n\r\n          {/* Glowing Orbs */}\r\n          {[...Array(6)].map((_, i) => (\r\n            <div\r\n              key={`orb-${i}`}\r\n              className={`absolute rounded-full blur-2xl ${\r\n                i % 3 === 0 ? 'bg-blue-500/15' :\r\n                i % 3 === 1 ? 'bg-purple-500/15' : 'bg-indigo-500/15'\r\n              }`}\r\n              style={{\r\n                width: `${Math.random() * 300 + 150}px`,\r\n                height: `${Math.random() * 300 + 150}px`,\r\n                top: `${Math.random() * 100}%`,\r\n                left: `${Math.random() * 100}%`,\r\n                animation: `pulse-glow ${\r\n                  15 + Math.random() * 10\r\n                }s infinite ease-in-out`,\r\n                animationDelay: `${Math.random() * 8}s`,\r\n              }}\r\n            ></div>\r\n          ))}\r\n        </div>\r\n\r\n        {/* Geometric Decorations */}\r\n        <div className=\"absolute inset-0\">\r\n          <div className=\"absolute top-32 right-32 w-40 h-40 border-2 border-white/10 rounded-full animate-spin-slow\"></div>\r\n          <div className=\"absolute bottom-40 left-32 w-32 h-32 border border-purple-400/20 rotate-45 animate-pulse\"></div>\r\n          <div className=\"absolute top-1/2 right-1/4 w-20 h-20 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-xl rotate-12 animate-float\"></div>\r\n        </div>\r\n\r\n        {/* Main Content */}\r\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 relative z-20\">\r\n          <div className=\"grid lg:grid-cols-2 gap-12 items-center\">\r\n            {/* Left Content */}\r\n            <div className={`text-center lg:text-left transition-all duration-1000 transform ${\r\n              isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'\r\n            }`}>\r\n              {/* Enhanced Badge */}\r\n              <div className=\"inline-block mb-8\">\r\n                <div className=\"flex items-center justify-center lg:justify-start space-x-3 bg-gradient-to-r from-white/20 to-white/10 backdrop-blur-md px-6 py-3 rounded-2xl border border-white/30 shadow-2xl\">\r\n                  <Sparkles className=\"text-yellow-300\" size={20} />\r\n                  <span className=\"text-white font-semibold text-sm tracking-wider uppercase\">\r\n                    Innovation Hub\r\n                  </span>\r\n                  <Star className=\"text-yellow-300\" size={16} />\r\n                </div>\r\n              </div>\r\n\r\n              {/* Main Heading */}\r\n              <h1 className=\"text-5xl sm:text-6xl md:text-7xl lg:text-8xl font-black mb-8 leading-tight\">\r\n                <span className=\"block text-white mb-2\">FWU</span>\r\n                <span className=\"block text-transparent bg-clip-text bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 animate-gradient-x\">\r\n                  Incubation\r\n                </span>\r\n                <span className=\"block text-transparent bg-clip-text bg-gradient-to-r from-purple-400 via-pink-400 to-blue-400 animate-gradient-x-reverse\">\r\n                  Programs\r\n                </span>\r\n              </h1>\r\n\r\n              {/* Description */}\r\n              <p className=\"text-xl sm:text-2xl text-blue-100 max-w-2xl mx-auto lg:mx-0 mb-6 leading-relaxed\">\r\n                Empowering <span className=\"font-semibold text-white\">innovation</span> and\r\n                <span className=\"text-purple-300 font-semibold\"> entrepreneurship</span> at Far Western University\r\n              </p>\r\n\r\n              <p className=\"text-lg text-blue-200 max-w-2xl mx-auto lg:mx-0 mb-10 leading-relaxed\">\r\n                Discover our range of specialized programs designed to support entrepreneurs at every stage of their journey,\r\n                from ideation to market launch. Join our vibrant community of innovators and change-makers.\r\n              </p>\r\n\r\n              {/* Action Buttons */}\r\n              <div className=\"flex flex-col sm:flex-row items-center justify-center lg:justify-start gap-6 mb-12\">\r\n                <button\r\n                  onClick={scrollToNextSection}\r\n                  className=\"group relative px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-bold rounded-2xl transition-all duration-300 hover:shadow-2xl hover:shadow-blue-500/25 hover:-translate-y-1 flex items-center overflow-hidden\"\r\n                >\r\n                  <div className=\"absolute inset-0 bg-gradient-to-r from-blue-700 to-purple-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\r\n                  <span className=\"relative z-10\">Explore Programs</span>\r\n                  <ArrowDown className=\"relative z-10 ml-2 group-hover:translate-y-1 transition-transform duration-300\" />\r\n                </button>\r\n\r\n                <Link\r\n                  href=\"/apply\"\r\n                  className=\"group relative px-8 py-4 bg-white/10 hover:bg-white/20 backdrop-blur-md text-white font-bold rounded-2xl transition-all duration-300 border-2 border-white/30 hover:border-white/50 hover:shadow-2xl hover:shadow-white/10 hover:-translate-y-1 flex items-center overflow-hidden\"\r\n                >\r\n                  <div className=\"absolute inset-0 bg-white/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\r\n                  <span className=\"relative z-10\">Apply Now</span>\r\n                  <ChevronRight className=\"relative z-10 ml-2 group-hover:translate-x-1 transition-transform duration-300\" />\r\n                </Link>\r\n              </div>\r\n\r\n              {/* Feature Highlights */}\r\n              <div className=\"grid grid-cols-2 gap-4 max-w-md mx-auto lg:mx-0\">\r\n                {[\r\n                  { icon: Target, text: \"Expert Mentorship\", color: \"text-blue-400\" },\r\n                  { icon: Users, text: \"Vibrant Community\", color: \"text-purple-400\" },\r\n                  { icon: Rocket, text: \"Launch Support\", color: \"text-pink-400\" },\r\n                  { icon: Award, text: \"Industry Recognition\", color: \"text-yellow-400\" }\r\n                ].map((item, index) => (\r\n                  <div\r\n                    key={index}\r\n                    className={`flex items-center space-x-3 p-3 bg-white/5 backdrop-blur-sm rounded-xl border border-white/10 hover:bg-white/10 transition-all duration-300 group ${\r\n                      activeFeature === index ? 'bg-white/15 border-white/30' : ''\r\n                    }`}\r\n                  >\r\n                    <item.icon className={`${item.color} group-hover:scale-110 transition-transform duration-300`} size={20} />\r\n                    <span className=\"text-white font-medium text-sm\">{item.text}</span>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            </div>\r\n\r\n            {/* Right Content - Video/Image */}\r\n            <div className={`relative transition-all duration-1000 delay-300 transform ${\r\n              isVisible ? 'opacity-100 translate-x-0' : 'opacity-0 translate-x-10'\r\n            }`}>\r\n              <div className=\"relative rounded-3xl overflow-hidden shadow-2xl group\">\r\n                {/* Main Image */}\r\n                <div className=\"relative h-[500px] lg:h-[600px] w-full\">\r\n                  <Image\r\n                    src=\"https://images.unsplash.com/photo-1521737604893-d14cc237f11d?q=80&w=1000&auto=format&fit=crop\"\r\n                    alt=\"FWU Incubation Programs\"\r\n                    fill\r\n                    className=\"object-cover transform group-hover:scale-105 transition-transform duration-700\"\r\n                  />\r\n                  <div className=\"absolute inset-0 bg-gradient-to-t from-indigo-900/70 via-transparent to-transparent\"></div>\r\n\r\n                  {/* Play Button Overlay */}\r\n                  <div className=\"absolute inset-0 flex items-center justify-center\">\r\n                    <button className=\"w-20 h-20 bg-white/20 backdrop-blur-md rounded-full flex items-center justify-center border-2 border-white/30 hover:bg-white/30 transition-all duration-300 group-hover:scale-110\">\r\n                      <Play className=\"text-white ml-1\" size={32} />\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Floating Stats Cards */}\r\n                <div className=\"absolute -bottom-6 -left-6 bg-white/90 backdrop-blur-md p-4 rounded-2xl shadow-xl border border-white/50\">\r\n                  <div className=\"text-2xl font-bold text-indigo-600 mb-1\">20+</div>\r\n                  <div className=\"text-sm text-gray-600\">Active Programs</div>\r\n                </div>\r\n\r\n                <div className=\"absolute -top-6 -right-6 bg-white/90 backdrop-blur-md p-4 rounded-2xl shadow-xl border border-white/50\">\r\n                  <div className=\"text-2xl font-bold text-purple-600 mb-1\">500+</div>\r\n                  <div className=\"text-sm text-gray-600\">Participants</div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Enhanced Stats Section */}\r\n        <div className=\"absolute bottom-20 left-1/2 transform -translate-x-1/2 w-full max-w-4xl px-4\">\r\n          <div className={`grid grid-cols-2 md:grid-cols-4 gap-4 transition-all duration-1000 delay-500 transform ${\r\n            isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'\r\n          }`}>\r\n            {[\r\n              { number: \"20+\", label: \"Active Programs\", color: \"from-blue-500 to-blue-600\" },\r\n              { number: \"500+\", label: \"Participants\", color: \"from-purple-500 to-purple-600\" },\r\n              { number: \"50+\", label: \"Expert Mentors\", color: \"from-indigo-500 to-indigo-600\" },\r\n              { number: \"95%\", label: \"Success Rate\", color: \"from-pink-500 to-pink-600\" }\r\n            ].map((stat, index) => (\r\n              <div\r\n                key={index}\r\n                className=\"bg-white/10 backdrop-blur-md p-4 rounded-2xl border border-white/20 hover:bg-white/20 transition-all duration-300 group\"\r\n              >\r\n                <div className={`text-2xl md:text-3xl font-bold bg-gradient-to-r ${stat.color} bg-clip-text text-transparent mb-1 group-hover:scale-110 transition-transform duration-300`}>\r\n                  {stat.number}\r\n                </div>\r\n                <div className=\"text-white/80 text-sm font-medium\">{stat.label}</div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Scroll Indicator */}\r\n        <div className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2\">\r\n          <button\r\n            onClick={scrollToNextSection}\r\n            className=\"w-12 h-12 bg-white/10 backdrop-blur-md rounded-full flex items-center justify-center border border-white/20 hover:bg-white/20 transition-all duration-300 group animate-bounce\"\r\n            aria-label=\"Scroll down to programs\"\r\n          >\r\n            <ArrowDown className=\"text-white group-hover:translate-y-1 transition-transform duration-300\" size={24} />\r\n          </button>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Program Types Section with ID for scroll targeting */}\r\n      {/* Program Types Section */}\r\n      <div id=\"program-types\">\r\n        <ProgramTypesSection />\r\n      </div>\r\n\r\n      {/* Calendar Section */}\r\n      <ProgramCalendarSection />\r\n\r\n      {/* Past Events Gallery */}\r\n      <PastEventsGallerySection />\r\n\r\n      {/* Custom CSS for animations */}\r\n      <style jsx global>{`\r\n        @keyframes mesh-drift {\r\n          0% { transform: translate(0, 0); }\r\n          100% { transform: translate(-60px, -60px); }\r\n        }\r\n\r\n        @keyframes float-drift {\r\n          0%, 100% { transform: translate(0, 0) rotate(0deg); }\r\n          25% { transform: translate(10px, -10px) rotate(90deg); }\r\n          50% { transform: translate(-5px, -15px) rotate(180deg); }\r\n          75% { transform: translate(-10px, 5px) rotate(270deg); }\r\n        }\r\n\r\n        @keyframes pulse-glow {\r\n          0%, 100% { opacity: 0.3; transform: scale(1); }\r\n          50% { opacity: 0.6; transform: scale(1.1); }\r\n        }\r\n\r\n        @keyframes gradient-x {\r\n          0%, 100% { background-position: 0% 50%; }\r\n          50% { background-position: 100% 50%; }\r\n        }\r\n\r\n        @keyframes gradient-x-reverse {\r\n          0%, 100% { background-position: 100% 50%; }\r\n          50% { background-position: 0% 50%; }\r\n        }\r\n\r\n        .animate-gradient-x {\r\n          background-size: 200% 200%;\r\n          animation: gradient-x 3s ease infinite;\r\n        }\r\n\r\n        .animate-gradient-x-reverse {\r\n          background-size: 200% 200%;\r\n          animation: gradient-x-reverse 3s ease infinite;\r\n        }\r\n\r\n        .animate-float {\r\n          animation: float 6s ease-in-out infinite;\r\n        }\r\n\r\n        .animate-spin-slow {\r\n          animation: spin 20s linear infinite;\r\n        }\r\n\r\n        @keyframes float {\r\n          0%, 100% { transform: translateY(0px) rotate(0deg); }\r\n          50% { transform: translateY(-20px) rotate(180deg); }\r\n        }\r\n      `}</style>\r\n    </>\r\n  );\r\n}"], "names": [], "mappings": ";;;;;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AACA;AAlBA;;;;;;;;;;AAoBe,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,aAAa;QAEb,uBAAuB;QACvB,MAAM,WAAW,YAAY;YAC3B,iBAAiB,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI;QAC1C,GAAG;QAEH,OAAO,IAAM,cAAc;IAC7B,GAAG,EAAE;IAEL,kDAAkD;IAClD,MAAM,sBAAsB;QAC1B,MAAM,kBAAkB,SAAS,cAAc,CAAC;QAChD,IAAI,iBAAiB;YACnB,gBAAgB,cAAc,CAAC;gBAAE,UAAU;YAAS;QACtD;IACF;IAGA,qBACE;;0BAEE,8OAAC;gBACC,KAAK;0DACK;;kCAGV,8OAAC;kEAAc;;0CAEb,8OAAC,6HAAA,CAAA,UAAK;gCACJ,KAAI;gCACJ,KAAI;gCACJ,IAAI;gCACJ,QAAQ;gCACR,WAAU;;;;;;0CAIZ,8OAAC;0EAAc;;;;;;0CACf,8OAAC;0EAAc;;;;;;0CAGf,8OAAC;gCAEC,OAAO;oCACL,iBAAiB,CAAC;;;cAGlB,CAAC;oCACD,gBAAgB;oCAChB,WAAW;gCACb;0EARU;;;;;;;;;;;;kCAad,8OAAC;kEAAc;;4BAEZ;mCAAI,MAAM;6BAAI,CAAC,GAAG,CAAC,CAAC,GAAG,kBACtB,8OAAC;oCAQC,OAAO;wCACL,OAAO,GAAG,KAAK,MAAM,KAAK,IAAI,EAAE,EAAE,CAAC;wCACnC,QAAQ,GAAG,KAAK,MAAM,KAAK,IAAI,EAAE,EAAE,CAAC;wCACpC,KAAK,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;wCAC9B,MAAM,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;wCAC/B,WAAW,CAAC,YAAY,EACtB,KAAK,KAAK,MAAM,KAAK,GACtB,sBAAsB,CAAC;wCACxB,gBAAgB,GAAG,KAAK,MAAM,KAAK,GAAG,CAAC,CAAC;oCAC1C;8EAfW,CAAC,sBAAsB,EAChC,IAAI,MAAM,IAAI,mBACd,IAAI,MAAM,IAAI,qBACd,IAAI,MAAM,IAAI,qBACd,IAAI,MAAM,IAAI,mBAAmB,kBACjC;mCANG;;;;;4BAqBR;mCAAI,MAAM;6BAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC;oCAMC,OAAO;wCACL,OAAO,GAAG,KAAK,MAAM,KAAK,MAAM,IAAI,EAAE,CAAC;wCACvC,QAAQ,GAAG,KAAK,MAAM,KAAK,MAAM,IAAI,EAAE,CAAC;wCACxC,KAAK,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;wCAC9B,MAAM,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;wCAC/B,WAAW,CAAC,WAAW,EACrB,KAAK,KAAK,MAAM,KAAK,GACtB,sBAAsB,CAAC;wCACxB,gBAAgB,GAAG,KAAK,MAAM,KAAK,EAAE,CAAC,CAAC;oCACzC;8EAbW,CAAC,+BAA+B,EACzC,IAAI,MAAM,IAAI,mBACd,IAAI,MAAM,IAAI,qBAAqB,oBACnC;mCAJG,CAAC,IAAI,EAAE,GAAG;;;;;;;;;;;kCAoBrB,8OAAC;kEAAc;;0CACb,8OAAC;0EAAc;;;;;;0CACf,8OAAC;0EAAc;;;;;;0CACf,8OAAC;0EAAc;;;;;;;;;;;;kCAIjB,8OAAC;kEAAc;kCACb,cAAA,8OAAC;sEAAc;;8CAEb,8OAAC;8EAAe,CAAC,gEAAgE,EAC/E,YAAY,8BAA8B,4BAC1C;;sDAEA,8OAAC;sFAAc;sDACb,cAAA,8OAAC;0FAAc;;kEACb,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;wDAAkB,MAAM;;;;;;kEAC5C,8OAAC;kGAAe;kEAA4D;;;;;;kEAG5E,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;wDAAkB,MAAM;;;;;;;;;;;;;;;;;sDAK5C,8OAAC;sFAAa;;8DACZ,8OAAC;8FAAe;8DAAwB;;;;;;8DACxC,8OAAC;8FAAe;8DAAmH;;;;;;8DAGnI,8OAAC;8FAAe;8DAA2H;;;;;;;;;;;;sDAM7I,8OAAC;sFAAY;;gDAAmF;8DACnF,8OAAC;8FAAe;8DAA2B;;;;;;gDAAiB;8DACvE,8OAAC;8FAAe;8DAAgC;;;;;;gDAAwB;;;;;;;sDAG1E,8OAAC;sFAAY;sDAAwE;;;;;;sDAMrF,8OAAC;sFAAc;;8DACb,8OAAC;oDACC,SAAS;8FACC;;sEAEV,8OAAC;sGAAc;;;;;;sEACf,8OAAC;sGAAe;sEAAgB;;;;;;sEAChC,8OAAC,gNAAA,CAAA,YAAS;4DAAC,WAAU;;;;;;;;;;;;8DAGvB,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;;sEAEV,8OAAC;sGAAc;;;;;;sEACf,8OAAC;sGAAe;sEAAgB;;;;;;sEAChC,8OAAC,sNAAA,CAAA,eAAY;4DAAC,WAAU;;;;;;;;;;;;;;;;;;sDAK5B,8OAAC;sFAAc;sDACZ;gDACC;oDAAE,MAAM,sMAAA,CAAA,SAAM;oDAAE,MAAM;oDAAqB,OAAO;gDAAgB;gDAClE;oDAAE,MAAM,oMAAA,CAAA,QAAK;oDAAE,MAAM;oDAAqB,OAAO;gDAAkB;gDACnE;oDAAE,MAAM,sMAAA,CAAA,SAAM;oDAAE,MAAM;oDAAkB,OAAO;gDAAgB;gDAC/D;oDAAE,MAAM,oMAAA,CAAA,QAAK;oDAAE,MAAM;oDAAwB,OAAO;gDAAkB;6CACvE,CAAC,GAAG,CAAC,CAAC,MAAM,sBACX,8OAAC;8FAEY,CAAC,kJAAkJ,EAC5J,kBAAkB,QAAQ,gCAAgC,IAC1D;;sEAEF,8OAAC,KAAK,IAAI;4DAAC,WAAW,GAAG,KAAK,KAAK,CAAC,wDAAwD,CAAC;4DAAE,MAAM;;;;;;sEACrG,8OAAC;sGAAe;sEAAkC,KAAK,IAAI;;;;;;;mDANtD;;;;;;;;;;;;;;;;8CAab,8OAAC;8EAAe,CAAC,0DAA0D,EACzE,YAAY,8BAA8B,4BAC1C;8CACA,cAAA,8OAAC;kFAAc;;0DAEb,8OAAC;0FAAc;;kEACb,8OAAC,6HAAA,CAAA,UAAK;wDACJ,KAAI;wDACJ,KAAI;wDACJ,IAAI;wDACJ,WAAU;;;;;;kEAEZ,8OAAC;kGAAc;;;;;;kEAGf,8OAAC;kGAAc;kEACb,cAAA,8OAAC;sGAAiB;sEAChB,cAAA,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;gEAAkB,MAAM;;;;;;;;;;;;;;;;;;;;;;0DAM9C,8OAAC;0FAAc;;kEACb,8OAAC;kGAAc;kEAA0C;;;;;;kEACzD,8OAAC;kGAAc;kEAAwB;;;;;;;;;;;;0DAGzC,8OAAC;0FAAc;;kEACb,8OAAC;kGAAc;kEAA0C;;;;;;kEACzD,8OAAC;kGAAc;kEAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQjD,8OAAC;kEAAc;kCACb,cAAA,8OAAC;sEAAe,CAAC,uFAAuF,EACtG,YAAY,8BAA8B,4BAC1C;sCACC;gCACC;oCAAE,QAAQ;oCAAO,OAAO;oCAAmB,OAAO;gCAA4B;gCAC9E;oCAAE,QAAQ;oCAAQ,OAAO;oCAAgB,OAAO;gCAAgC;gCAChF;oCAAE,QAAQ;oCAAO,OAAO;oCAAkB,OAAO;gCAAgC;gCACjF;oCAAE,QAAQ;oCAAO,OAAO;oCAAgB,OAAO;gCAA4B;6BAC5E,CAAC,GAAG,CAAC,CAAC,MAAM,sBACX,8OAAC;8EAEW;;sDAEV,8OAAC;sFAAe,CAAC,gDAAgD,EAAE,KAAK,KAAK,CAAC,2FAA2F,CAAC;sDACvK,KAAK,MAAM;;;;;;sDAEd,8OAAC;sFAAc;sDAAqC,KAAK,KAAK;;;;;;;mCANzD;;;;;;;;;;;;;;;kCAab,8OAAC;kEAAc;kCACb,cAAA,8OAAC;4BACC,SAAS;4BAET,cAAW;sEADD;sCAGV,cAAA,8OAAC,gNAAA,CAAA,YAAS;gCAAC,WAAU;gCAAyE,MAAM;;;;;;;;;;;;;;;;;;;;;;0BAO1G,8OAAC;gBAAI,IAAG;;0BACN,cAAA,8OAAC,4JAAA,CAAA,UAAmB;;;;;;;;;;0BAItB,8OAAC,iKAAA,CAAA,UAAsB;;;;;0BAGvB,8OAAC,mKAAA,CAAA,UAAwB;;;;;;;;;;;AAwD/B", "debugId": null}}]}