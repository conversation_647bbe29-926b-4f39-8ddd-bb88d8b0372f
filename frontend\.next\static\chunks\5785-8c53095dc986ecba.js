"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5785],{432:(e,t,n)=>{n.d(t,{E:()=>g});var r=n(52020),o=n(39853),i=n(7165),a=n(25910),l=class extends a.Q{constructor(e={}){super(),this.config=e,this.#e=new Map}#e;build(e,t,n){let i=t.queryKey,a=t.queryHash??(0,r.F$)(i,t),l=this.get(a);return l||(l=new o.X({client:e,queryKey:i,queryHash:a,options:e.defaultQueryOptions(t),state:n,defaultOptions:e.getQueryDefaults(i)}),this.add(l)),l}add(e){this.#e.has(e.queryHash)||(this.#e.set(e.queryHash,e),this.notify({type:"added",query:e}))}remove(e){let t=this.#e.get(e.queryHash);t&&(e.destroy(),t===e&&this.#e.delete(e.queryHash),this.notify({type:"removed",query:e}))}clear(){i.jG.batch(()=>{this.getAll().forEach(e=>{this.remove(e)})})}get(e){return this.#e.get(e)}getAll(){return[...this.#e.values()]}find(e){let t={exact:!0,...e};return this.getAll().find(e=>(0,r.MK)(t,e))}findAll(e={}){let t=this.getAll();return Object.keys(e).length>0?t.filter(t=>(0,r.MK)(e,t)):t}notify(e){i.jG.batch(()=>{this.listeners.forEach(t=>{t(e)})})}onFocus(){i.jG.batch(()=>{this.getAll().forEach(e=>{e.onFocus()})})}onOnline(){i.jG.batch(()=>{this.getAll().forEach(e=>{e.onOnline()})})}},s=n(57948),u=n(6784),c=class extends s.k{#t;#n;#r;constructor(e){super(),this.mutationId=e.mutationId,this.#n=e.mutationCache,this.#t=[],this.state=e.state||{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0},this.setOptions(e.options),this.scheduleGc()}setOptions(e){this.options=e,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(e){this.#t.includes(e)||(this.#t.push(e),this.clearGcTimeout(),this.#n.notify({type:"observerAdded",mutation:this,observer:e}))}removeObserver(e){this.#t=this.#t.filter(t=>t!==e),this.scheduleGc(),this.#n.notify({type:"observerRemoved",mutation:this,observer:e})}optionalRemove(){this.#t.length||("pending"===this.state.status?this.scheduleGc():this.#n.remove(this))}continue(){return this.#r?.continue()??this.execute(this.state.variables)}async execute(e){let t=()=>{this.#o({type:"continue"})};this.#r=(0,u.II)({fn:()=>this.options.mutationFn?this.options.mutationFn(e):Promise.reject(Error("No mutationFn found")),onFail:(e,t)=>{this.#o({type:"failed",failureCount:e,error:t})},onPause:()=>{this.#o({type:"pause"})},onContinue:t,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>this.#n.canRun(this)});let n="pending"===this.state.status,r=!this.#r.canStart();try{if(n)t();else{this.#o({type:"pending",variables:e,isPaused:r}),await this.#n.config.onMutate?.(e,this);let t=await this.options.onMutate?.(e);t!==this.state.context&&this.#o({type:"pending",context:t,variables:e,isPaused:r})}let o=await this.#r.start();return await this.#n.config.onSuccess?.(o,e,this.state.context,this),await this.options.onSuccess?.(o,e,this.state.context),await this.#n.config.onSettled?.(o,null,this.state.variables,this.state.context,this),await this.options.onSettled?.(o,null,e,this.state.context),this.#o({type:"success",data:o}),o}catch(t){try{throw await this.#n.config.onError?.(t,e,this.state.context,this),await this.options.onError?.(t,e,this.state.context),await this.#n.config.onSettled?.(void 0,t,this.state.variables,this.state.context,this),await this.options.onSettled?.(void 0,t,e,this.state.context),t}finally{this.#o({type:"error",error:t})}}finally{this.#n.runNext(this)}}#o(e){this.state=(t=>{switch(e.type){case"failed":return{...t,failureCount:e.failureCount,failureReason:e.error};case"pause":return{...t,isPaused:!0};case"continue":return{...t,isPaused:!1};case"pending":return{...t,context:e.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:e.isPaused,status:"pending",variables:e.variables,submittedAt:Date.now()};case"success":return{...t,data:e.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...t,data:void 0,error:e.error,failureCount:t.failureCount+1,failureReason:e.error,isPaused:!1,status:"error"}}})(this.state),i.jG.batch(()=>{this.#t.forEach(t=>{t.onMutationUpdate(e)}),this.#n.notify({mutation:this,type:"updated",action:e})})}},d=class extends a.Q{constructor(e={}){super(),this.config=e,this.#i=new Set,this.#a=new Map,this.#l=0}#i;#a;#l;build(e,t,n){let r=new c({mutationCache:this,mutationId:++this.#l,options:e.defaultMutationOptions(t),state:n});return this.add(r),r}add(e){this.#i.add(e);let t=f(e);if("string"==typeof t){let n=this.#a.get(t);n?n.push(e):this.#a.set(t,[e])}this.notify({type:"added",mutation:e})}remove(e){if(this.#i.delete(e)){let t=f(e);if("string"==typeof t){let n=this.#a.get(t);if(n)if(n.length>1){let t=n.indexOf(e);-1!==t&&n.splice(t,1)}else n[0]===e&&this.#a.delete(t)}}this.notify({type:"removed",mutation:e})}canRun(e){let t=f(e);if("string"!=typeof t)return!0;{let n=this.#a.get(t),r=n?.find(e=>"pending"===e.state.status);return!r||r===e}}runNext(e){let t=f(e);if("string"!=typeof t)return Promise.resolve();{let n=this.#a.get(t)?.find(t=>t!==e&&t.state.isPaused);return n?.continue()??Promise.resolve()}}clear(){i.jG.batch(()=>{this.#i.forEach(e=>{this.notify({type:"removed",mutation:e})}),this.#i.clear(),this.#a.clear()})}getAll(){return Array.from(this.#i)}find(e){let t={exact:!0,...e};return this.getAll().find(e=>(0,r.nJ)(t,e))}findAll(e={}){return this.getAll().filter(t=>(0,r.nJ)(e,t))}notify(e){i.jG.batch(()=>{this.listeners.forEach(t=>{t(e)})})}resumePausedMutations(){let e=this.getAll().filter(e=>e.state.isPaused);return i.jG.batch(()=>Promise.all(e.map(e=>e.continue().catch(r.lQ))))}};function f(e){return e.options.scope?.id}var h=n(50920),p=n(21239);function m(e){return{onFetch:(t,n)=>{let o=t.options,i=t.fetchOptions?.meta?.fetchMore?.direction,a=t.state.data?.pages||[],l=t.state.data?.pageParams||[],s={pages:[],pageParams:[]},u=0,c=async()=>{let n=!1,c=e=>{Object.defineProperty(e,"signal",{enumerable:!0,get:()=>(t.signal.aborted?n=!0:t.signal.addEventListener("abort",()=>{n=!0}),t.signal)})},d=(0,r.ZM)(t.options,t.fetchOptions),f=async(e,o,i)=>{if(n)return Promise.reject();if(null==o&&e.pages.length)return Promise.resolve(e);let a=(()=>{let e={client:t.client,queryKey:t.queryKey,pageParam:o,direction:i?"backward":"forward",meta:t.options.meta};return c(e),e})(),l=await d(a),{maxPages:s}=t.options,u=i?r.ZZ:r.y9;return{pages:u(e.pages,l,s),pageParams:u(e.pageParams,o,s)}};if(i&&a.length){let e="backward"===i,t={pages:a,pageParams:l},n=(e?function(e,{pages:t,pageParams:n}){return t.length>0?e.getPreviousPageParam?.(t[0],t,n[0],n):void 0}:y)(o,t);s=await f(t,n,e)}else{let t=e??a.length;do{let e=0===u?l[0]??o.initialPageParam:y(o,s);if(u>0&&null==e)break;s=await f(s,e),u++}while(u<t)}return s};t.options.persister?t.fetchFn=()=>t.options.persister?.(c,{client:t.client,queryKey:t.queryKey,meta:t.options.meta,signal:t.signal},n):t.fetchFn=c}}}function y(e,{pages:t,pageParams:n}){let r=t.length-1;return t.length>0?e.getNextPageParam(t[r],t,n[r],n):void 0}var g=class{#s;#n;#u;#c;#d;#f;#h;#p;constructor(e={}){this.#s=e.queryCache||new l,this.#n=e.mutationCache||new d,this.#u=e.defaultOptions||{},this.#c=new Map,this.#d=new Map,this.#f=0}mount(){this.#f++,1===this.#f&&(this.#h=h.m.subscribe(async e=>{e&&(await this.resumePausedMutations(),this.#s.onFocus())}),this.#p=p.t.subscribe(async e=>{e&&(await this.resumePausedMutations(),this.#s.onOnline())}))}unmount(){this.#f--,0===this.#f&&(this.#h?.(),this.#h=void 0,this.#p?.(),this.#p=void 0)}isFetching(e){return this.#s.findAll({...e,fetchStatus:"fetching"}).length}isMutating(e){return this.#n.findAll({...e,status:"pending"}).length}getQueryData(e){let t=this.defaultQueryOptions({queryKey:e});return this.#s.get(t.queryHash)?.state.data}ensureQueryData(e){let t=this.defaultQueryOptions(e),n=this.#s.build(this,t),o=n.state.data;return void 0===o?this.fetchQuery(e):(e.revalidateIfStale&&n.isStaleByTime((0,r.d2)(t.staleTime,n))&&this.prefetchQuery(t),Promise.resolve(o))}getQueriesData(e){return this.#s.findAll(e).map(({queryKey:e,state:t})=>[e,t.data])}setQueryData(e,t,n){let o=this.defaultQueryOptions({queryKey:e}),i=this.#s.get(o.queryHash),a=i?.state.data,l=(0,r.Zw)(t,a);if(void 0!==l)return this.#s.build(this,o).setData(l,{...n,manual:!0})}setQueriesData(e,t,n){return i.jG.batch(()=>this.#s.findAll(e).map(({queryKey:e})=>[e,this.setQueryData(e,t,n)]))}getQueryState(e){let t=this.defaultQueryOptions({queryKey:e});return this.#s.get(t.queryHash)?.state}removeQueries(e){let t=this.#s;i.jG.batch(()=>{t.findAll(e).forEach(e=>{t.remove(e)})})}resetQueries(e,t){let n=this.#s;return i.jG.batch(()=>(n.findAll(e).forEach(e=>{e.reset()}),this.refetchQueries({type:"active",...e},t)))}cancelQueries(e,t={}){let n={revert:!0,...t};return Promise.all(i.jG.batch(()=>this.#s.findAll(e).map(e=>e.cancel(n)))).then(r.lQ).catch(r.lQ)}invalidateQueries(e,t={}){return i.jG.batch(()=>(this.#s.findAll(e).forEach(e=>{e.invalidate()}),e?.refetchType==="none")?Promise.resolve():this.refetchQueries({...e,type:e?.refetchType??e?.type??"active"},t))}refetchQueries(e,t={}){let n={...t,cancelRefetch:t.cancelRefetch??!0};return Promise.all(i.jG.batch(()=>this.#s.findAll(e).filter(e=>!e.isDisabled()&&!e.isStatic()).map(e=>{let t=e.fetch(void 0,n);return n.throwOnError||(t=t.catch(r.lQ)),"paused"===e.state.fetchStatus?Promise.resolve():t}))).then(r.lQ)}fetchQuery(e){let t=this.defaultQueryOptions(e);void 0===t.retry&&(t.retry=!1);let n=this.#s.build(this,t);return n.isStaleByTime((0,r.d2)(t.staleTime,n))?n.fetch(t):Promise.resolve(n.state.data)}prefetchQuery(e){return this.fetchQuery(e).then(r.lQ).catch(r.lQ)}fetchInfiniteQuery(e){return e.behavior=m(e.pages),this.fetchQuery(e)}prefetchInfiniteQuery(e){return this.fetchInfiniteQuery(e).then(r.lQ).catch(r.lQ)}ensureInfiniteQueryData(e){return e.behavior=m(e.pages),this.ensureQueryData(e)}resumePausedMutations(){return p.t.isOnline()?this.#n.resumePausedMutations():Promise.resolve()}getQueryCache(){return this.#s}getMutationCache(){return this.#n}getDefaultOptions(){return this.#u}setDefaultOptions(e){this.#u=e}setQueryDefaults(e,t){this.#c.set((0,r.EN)(e),{queryKey:e,defaultOptions:t})}getQueryDefaults(e){let t=[...this.#c.values()],n={};return t.forEach(t=>{(0,r.Cp)(e,t.queryKey)&&Object.assign(n,t.defaultOptions)}),n}setMutationDefaults(e,t){this.#d.set((0,r.EN)(e),{mutationKey:e,defaultOptions:t})}getMutationDefaults(e){let t=[...this.#d.values()],n={};return t.forEach(t=>{(0,r.Cp)(e,t.mutationKey)&&Object.assign(n,t.defaultOptions)}),n}defaultQueryOptions(e){if(e._defaulted)return e;let t={...this.#u.queries,...this.getQueryDefaults(e.queryKey),...e,_defaulted:!0};return t.queryHash||(t.queryHash=(0,r.F$)(t.queryKey,t)),void 0===t.refetchOnReconnect&&(t.refetchOnReconnect="always"!==t.networkMode),void 0===t.throwOnError&&(t.throwOnError=!!t.suspense),!t.networkMode&&t.persister&&(t.networkMode="offlineFirst"),t.queryFn===r.hT&&(t.enabled=!1),t}defaultMutationOptions(e){return e?._defaulted?e:{...this.#u.mutations,...e?.mutationKey&&this.getMutationDefaults(e.mutationKey),...e,_defaulted:!0}}clear(){this.#s.clear(),this.#n.clear()}}},17580:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},22432:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("panel-left",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}]])},23861:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("bell",[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]])},35695:(e,t,n)=>{var r=n(18999);n.o(r,"usePathname")&&n.d(t,{usePathname:function(){return r.usePathname}}),n.o(r,"useRouter")&&n.d(t,{useRouter:function(){return r.useRouter}})},38795:(e,t,n)=>{n.d(t,{Mz:()=>eX,i3:()=>eZ,UC:()=>eY,bL:()=>eU,Bk:()=>eO});var r=n(12115);let o=["top","right","bottom","left"],i=Math.min,a=Math.max,l=Math.round,s=Math.floor,u=e=>({x:e,y:e}),c={left:"right",right:"left",bottom:"top",top:"bottom"},d={start:"end",end:"start"};function f(e,t){return"function"==typeof e?e(t):e}function h(e){return e.split("-")[0]}function p(e){return e.split("-")[1]}function m(e){return"x"===e?"y":"x"}function y(e){return"y"===e?"height":"width"}function g(e){return["top","bottom"].includes(h(e))?"y":"x"}function v(e){return e.replace(/start|end/g,e=>d[e])}function w(e){return e.replace(/left|right|bottom|top/g,e=>c[e])}function x(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function b(e){let{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function C(e,t,n){let r,{reference:o,floating:i}=e,a=g(t),l=m(g(t)),s=y(l),u=h(t),c="y"===a,d=o.x+o.width/2-i.width/2,f=o.y+o.height/2-i.height/2,v=o[s]/2-i[s]/2;switch(u){case"top":r={x:d,y:o.y-i.height};break;case"bottom":r={x:d,y:o.y+o.height};break;case"right":r={x:o.x+o.width,y:f};break;case"left":r={x:o.x-i.width,y:f};break;default:r={x:o.x,y:o.y}}switch(p(t)){case"start":r[l]-=v*(n&&c?-1:1);break;case"end":r[l]+=v*(n&&c?-1:1)}return r}let R=async(e,t,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:a}=n,l=i.filter(Boolean),s=await (null==a.isRTL?void 0:a.isRTL(t)),u=await a.getElementRects({reference:e,floating:t,strategy:o}),{x:c,y:d}=C(u,r,s),f=r,h={},p=0;for(let n=0;n<l.length;n++){let{name:i,fn:m}=l[n],{x:y,y:g,data:v,reset:w}=await m({x:c,y:d,initialPlacement:r,placement:f,strategy:o,middlewareData:h,rects:u,platform:a,elements:{reference:e,floating:t}});c=null!=y?y:c,d=null!=g?g:d,h={...h,[i]:{...h[i],...v}},w&&p<=50&&(p++,"object"==typeof w&&(w.placement&&(f=w.placement),w.rects&&(u=!0===w.rects?await a.getElementRects({reference:e,floating:t,strategy:o}):w.rects),{x:c,y:d}=C(u,f,s)),n=-1)}return{x:c,y:d,placement:f,strategy:o,middlewareData:h}};async function M(e,t){var n;void 0===t&&(t={});let{x:r,y:o,platform:i,rects:a,elements:l,strategy:s}=e,{boundary:u="clippingAncestors",rootBoundary:c="viewport",elementContext:d="floating",altBoundary:h=!1,padding:p=0}=f(t,e),m=x(p),y=l[h?"floating"===d?"reference":"floating":d],g=b(await i.getClippingRect({element:null==(n=await (null==i.isElement?void 0:i.isElement(y)))||n?y:y.contextElement||await (null==i.getDocumentElement?void 0:i.getDocumentElement(l.floating)),boundary:u,rootBoundary:c,strategy:s})),v="floating"===d?{x:r,y:o,width:a.floating.width,height:a.floating.height}:a.reference,w=await (null==i.getOffsetParent?void 0:i.getOffsetParent(l.floating)),C=await (null==i.isElement?void 0:i.isElement(w))&&await (null==i.getScale?void 0:i.getScale(w))||{x:1,y:1},R=b(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:l,rect:v,offsetParent:w,strategy:s}):v);return{top:(g.top-R.top+m.top)/C.y,bottom:(R.bottom-g.bottom+m.bottom)/C.y,left:(g.left-R.left+m.left)/C.x,right:(R.right-g.right+m.right)/C.x}}function A(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function k(e){return o.some(t=>e[t]>=0)}async function P(e,t){let{placement:n,platform:r,elements:o}=e,i=await (null==r.isRTL?void 0:r.isRTL(o.floating)),a=h(n),l=p(n),s="y"===g(n),u=["left","top"].includes(a)?-1:1,c=i&&s?-1:1,d=f(t,e),{mainAxis:m,crossAxis:y,alignmentAxis:v}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return l&&"number"==typeof v&&(y="end"===l?-1*v:v),s?{x:y*c,y:m*u}:{x:m*u,y:y*c}}function j(){return"undefined"!=typeof window}function D(e){return O(e)?(e.nodeName||"").toLowerCase():"#document"}function E(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function T(e){var t;return null==(t=(O(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function O(e){return!!j()&&(e instanceof Node||e instanceof E(e).Node)}function S(e){return!!j()&&(e instanceof Element||e instanceof E(e).Element)}function L(e){return!!j()&&(e instanceof HTMLElement||e instanceof E(e).HTMLElement)}function I(e){return!!j()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof E(e).ShadowRoot)}function F(e){let{overflow:t,overflowX:n,overflowY:r,display:o}=G(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function _(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function N(e){let t=q(),n=S(e)?G(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(n.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(n.contain||"").includes(e))}function q(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function Q(e){return["html","body","#document"].includes(D(e))}function G(e){return E(e).getComputedStyle(e)}function H(e){return S(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function K(e){if("html"===D(e))return e;let t=e.assignedSlot||e.parentNode||I(e)&&e.host||T(e);return I(t)?t.host:t}function B(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let o=function e(t){let n=K(t);return Q(n)?t.ownerDocument?t.ownerDocument.body:t.body:L(n)&&F(n)?n:e(n)}(e),i=o===(null==(r=e.ownerDocument)?void 0:r.body),a=E(o);if(i){let e=V(a);return t.concat(a,a.visualViewport||[],F(o)?o:[],e&&n?B(e):[])}return t.concat(o,B(o,[],n))}function V(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function W(e){let t=G(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,o=L(e),i=o?e.offsetWidth:n,a=o?e.offsetHeight:r,s=l(n)!==i||l(r)!==a;return s&&(n=i,r=a),{width:n,height:r,$:s}}function z(e){return S(e)?e:e.contextElement}function U(e){let t=z(e);if(!L(t))return u(1);let n=t.getBoundingClientRect(),{width:r,height:o,$:i}=W(t),a=(i?l(n.width):n.width)/r,s=(i?l(n.height):n.height)/o;return a&&Number.isFinite(a)||(a=1),s&&Number.isFinite(s)||(s=1),{x:a,y:s}}let X=u(0);function Y(e){let t=E(e);return q()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:X}function Z(e,t,n,r){var o;void 0===t&&(t=!1),void 0===n&&(n=!1);let i=e.getBoundingClientRect(),a=z(e),l=u(1);t&&(r?S(r)&&(l=U(r)):l=U(e));let s=(void 0===(o=n)&&(o=!1),r&&(!o||r===E(a))&&o)?Y(a):u(0),c=(i.left+s.x)/l.x,d=(i.top+s.y)/l.y,f=i.width/l.x,h=i.height/l.y;if(a){let e=E(a),t=r&&S(r)?E(r):r,n=e,o=V(n);for(;o&&r&&t!==n;){let e=U(o),t=o.getBoundingClientRect(),r=G(o),i=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,a=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;c*=e.x,d*=e.y,f*=e.x,h*=e.y,c+=i,d+=a,o=V(n=E(o))}}return b({width:f,height:h,x:c,y:d})}function $(e,t){let n=H(e).scrollLeft;return t?t.left+n:Z(T(e)).left+n}function J(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:$(e,r)),y:r.top+t.scrollTop}}function ee(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=E(e),r=T(e),o=n.visualViewport,i=r.clientWidth,a=r.clientHeight,l=0,s=0;if(o){i=o.width,a=o.height;let e=q();(!e||e&&"fixed"===t)&&(l=o.offsetLeft,s=o.offsetTop)}return{width:i,height:a,x:l,y:s}}(e,n);else if("document"===t)r=function(e){let t=T(e),n=H(e),r=e.ownerDocument.body,o=a(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=a(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),l=-n.scrollLeft+$(e),s=-n.scrollTop;return"rtl"===G(r).direction&&(l+=a(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:l,y:s}}(T(e));else if(S(t))r=function(e,t){let n=Z(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=L(e)?U(e):u(1),a=e.clientWidth*i.x,l=e.clientHeight*i.y;return{width:a,height:l,x:o*i.x,y:r*i.y}}(t,n);else{let n=Y(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return b(r)}function et(e){return"static"===G(e).position}function en(e,t){if(!L(e)||"fixed"===G(e).position)return null;if(t)return t(e);let n=e.offsetParent;return T(e)===n&&(n=n.ownerDocument.body),n}function er(e,t){let n=E(e);if(_(e))return n;if(!L(e)){let t=K(e);for(;t&&!Q(t);){if(S(t)&&!et(t))return t;t=K(t)}return n}let r=en(e,t);for(;r&&["table","td","th"].includes(D(r))&&et(r);)r=en(r,t);return r&&Q(r)&&et(r)&&!N(r)?n:r||function(e){let t=K(e);for(;L(t)&&!Q(t);){if(N(t))return t;if(_(t))break;t=K(t)}return null}(e)||n}let eo=async function(e){let t=this.getOffsetParent||er,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=L(t),o=T(t),i="fixed"===n,a=Z(e,!0,i,t),l={scrollLeft:0,scrollTop:0},s=u(0);if(r||!r&&!i)if(("body"!==D(t)||F(o))&&(l=H(t)),r){let e=Z(t,!0,i,t);s.x=e.x+t.clientLeft,s.y=e.y+t.clientTop}else o&&(s.x=$(o));i&&!r&&o&&(s.x=$(o));let c=!o||r||i?u(0):J(o,l);return{x:a.left+l.scrollLeft-s.x-c.x,y:a.top+l.scrollTop-s.y-c.y,width:a.width,height:a.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},ei={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e,i="fixed"===o,a=T(r),l=!!t&&_(t.floating);if(r===a||l&&i)return n;let s={scrollLeft:0,scrollTop:0},c=u(1),d=u(0),f=L(r);if((f||!f&&!i)&&(("body"!==D(r)||F(a))&&(s=H(r)),L(r))){let e=Z(r);c=U(r),d.x=e.x+r.clientLeft,d.y=e.y+r.clientTop}let h=!a||f||i?u(0):J(a,s,!0);return{width:n.width*c.x,height:n.height*c.y,x:n.x*c.x-s.scrollLeft*c.x+d.x+h.x,y:n.y*c.y-s.scrollTop*c.y+d.y+h.y}},getDocumentElement:T,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e,l=[..."clippingAncestors"===n?_(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=B(e,[],!1).filter(e=>S(e)&&"body"!==D(e)),o=null,i="fixed"===G(e).position,a=i?K(e):e;for(;S(a)&&!Q(a);){let t=G(a),n=N(a);n||"fixed"!==t.position||(o=null),(i?!n&&!o:!n&&"static"===t.position&&!!o&&["absolute","fixed"].includes(o.position)||F(a)&&!n&&function e(t,n){let r=K(t);return!(r===n||!S(r)||Q(r))&&("fixed"===G(r).position||e(r,n))}(e,a))?r=r.filter(e=>e!==a):o=t,a=K(a)}return t.set(e,r),r}(t,this._c):[].concat(n),r],s=l[0],u=l.reduce((e,n)=>{let r=ee(t,n,o);return e.top=a(r.top,e.top),e.right=i(r.right,e.right),e.bottom=i(r.bottom,e.bottom),e.left=a(r.left,e.left),e},ee(t,s,o));return{width:u.right-u.left,height:u.bottom-u.top,x:u.left,y:u.top}},getOffsetParent:er,getElementRects:eo,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=W(e);return{width:t,height:n}},getScale:U,isElement:S,isRTL:function(e){return"rtl"===G(e).direction}};function ea(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let el=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:o,rects:l,platform:s,elements:u,middlewareData:c}=t,{element:d,padding:h=0}=f(e,t)||{};if(null==d)return{};let v=x(h),w={x:n,y:r},b=m(g(o)),C=y(b),R=await s.getDimensions(d),M="y"===b,A=M?"clientHeight":"clientWidth",k=l.reference[C]+l.reference[b]-w[b]-l.floating[C],P=w[b]-l.reference[b],j=await (null==s.getOffsetParent?void 0:s.getOffsetParent(d)),D=j?j[A]:0;D&&await (null==s.isElement?void 0:s.isElement(j))||(D=u.floating[A]||l.floating[C]);let E=D/2-R[C]/2-1,T=i(v[M?"top":"left"],E),O=i(v[M?"bottom":"right"],E),S=D-R[C]-O,L=D/2-R[C]/2+(k/2-P/2),I=a(T,i(L,S)),F=!c.arrow&&null!=p(o)&&L!==I&&l.reference[C]/2-(L<T?T:O)-R[C]/2<0,_=F?L<T?L-T:L-S:0;return{[b]:w[b]+_,data:{[b]:I,centerOffset:L-I-_,...F&&{alignmentOffset:_}},reset:F}}}),es=(e,t,n)=>{let r=new Map,o={platform:ei,...n},i={...o.platform,_c:r};return R(e,t,{...o,platform:i})};var eu=n(47650),ec="undefined"!=typeof document?r.useLayoutEffect:function(){};function ed(e,t){let n,r,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!ed(e[r],t[r]))return!1;return!0}if((n=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){let n=o[r];if(("_owner"!==n||!e.$$typeof)&&!ed(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function ef(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function eh(e,t){let n=ef(e);return Math.round(t*n)/n}function ep(e){let t=r.useRef(e);return ec(()=>{t.current=e}),t}let em=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?el({element:n.current,padding:r}).fn(t):{}:n?el({element:n,padding:r}).fn(t):{}}}),ey=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:o,y:i,placement:a,middlewareData:l}=t,s=await P(t,e);return a===(null==(n=l.offset)?void 0:n.placement)&&null!=(r=l.arrow)&&r.alignmentOffset?{}:{x:o+s.x,y:i+s.y,data:{...s,placement:a}}}}}(e),options:[e,t]}),eg=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:o}=t,{mainAxis:l=!0,crossAxis:s=!1,limiter:u={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...c}=f(e,t),d={x:n,y:r},p=await M(t,c),y=g(h(o)),v=m(y),w=d[v],x=d[y];if(l){let e="y"===v?"top":"left",t="y"===v?"bottom":"right",n=w+p[e],r=w-p[t];w=a(n,i(w,r))}if(s){let e="y"===y?"top":"left",t="y"===y?"bottom":"right",n=x+p[e],r=x-p[t];x=a(n,i(x,r))}let b=u.fn({...t,[v]:w,[y]:x});return{...b,data:{x:b.x-n,y:b.y-r,enabled:{[v]:l,[y]:s}}}}}}(e),options:[e,t]}),ev=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:o,rects:i,middlewareData:a}=t,{offset:l=0,mainAxis:s=!0,crossAxis:u=!0}=f(e,t),c={x:n,y:r},d=g(o),p=m(d),y=c[p],v=c[d],w=f(l,t),x="number"==typeof w?{mainAxis:w,crossAxis:0}:{mainAxis:0,crossAxis:0,...w};if(s){let e="y"===p?"height":"width",t=i.reference[p]-i.floating[e]+x.mainAxis,n=i.reference[p]+i.reference[e]-x.mainAxis;y<t?y=t:y>n&&(y=n)}if(u){var b,C;let e="y"===p?"width":"height",t=["top","left"].includes(h(o)),n=i.reference[d]-i.floating[e]+(t&&(null==(b=a.offset)?void 0:b[d])||0)+(t?0:x.crossAxis),r=i.reference[d]+i.reference[e]+(t?0:(null==(C=a.offset)?void 0:C[d])||0)-(t?x.crossAxis:0);v<n?v=n:v>r&&(v=r)}return{[p]:y,[d]:v}}}}(e),options:[e,t]}),ew=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,o,i,a;let{placement:l,middlewareData:s,rects:u,initialPlacement:c,platform:d,elements:x}=t,{mainAxis:b=!0,crossAxis:C=!0,fallbackPlacements:R,fallbackStrategy:A="bestFit",fallbackAxisSideDirection:k="none",flipAlignment:P=!0,...j}=f(e,t);if(null!=(n=s.arrow)&&n.alignmentOffset)return{};let D=h(l),E=g(c),T=h(c)===c,O=await (null==d.isRTL?void 0:d.isRTL(x.floating)),S=R||(T||!P?[w(c)]:function(e){let t=w(e);return[v(e),t,v(t)]}(c)),L="none"!==k;!R&&L&&S.push(...function(e,t,n,r){let o=p(e),i=function(e,t,n){let r=["left","right"],o=["right","left"];switch(e){case"top":case"bottom":if(n)return t?o:r;return t?r:o;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(h(e),"start"===n,r);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(v)))),i}(c,P,k,O));let I=[c,...S],F=await M(t,j),_=[],N=(null==(r=s.flip)?void 0:r.overflows)||[];if(b&&_.push(F[D]),C){let e=function(e,t,n){void 0===n&&(n=!1);let r=p(e),o=m(g(e)),i=y(o),a="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[i]>t.floating[i]&&(a=w(a)),[a,w(a)]}(l,u,O);_.push(F[e[0]],F[e[1]])}if(N=[...N,{placement:l,overflows:_}],!_.every(e=>e<=0)){let e=((null==(o=s.flip)?void 0:o.index)||0)+1,t=I[e];if(t&&("alignment"!==C||E===g(t)||N.every(e=>e.overflows[0]>0&&g(e.placement)===E)))return{data:{index:e,overflows:N},reset:{placement:t}};let n=null==(i=N.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!n)switch(A){case"bestFit":{let e=null==(a=N.filter(e=>{if(L){let t=g(e.placement);return t===E||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:a[0];e&&(n=e);break}case"initialPlacement":n=c}if(l!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}),ex=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let o,l,{placement:s,rects:u,platform:c,elements:d}=t,{apply:m=()=>{},...y}=f(e,t),v=await M(t,y),w=h(s),x=p(s),b="y"===g(s),{width:C,height:R}=u.floating;"top"===w||"bottom"===w?(o=w,l=x===(await (null==c.isRTL?void 0:c.isRTL(d.floating))?"start":"end")?"left":"right"):(l=w,o="end"===x?"top":"bottom");let A=R-v.top-v.bottom,k=C-v.left-v.right,P=i(R-v[o],A),j=i(C-v[l],k),D=!t.middlewareData.shift,E=P,T=j;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(T=k),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(E=A),D&&!x){let e=a(v.left,0),t=a(v.right,0),n=a(v.top,0),r=a(v.bottom,0);b?T=C-2*(0!==e||0!==t?e+t:a(v.left,v.right)):E=R-2*(0!==n||0!==r?n+r:a(v.top,v.bottom))}await m({...t,availableWidth:T,availableHeight:E});let O=await c.getDimensions(d.floating);return C!==O.width||R!==O.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),eb=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...o}=f(e,t);switch(r){case"referenceHidden":{let e=A(await M(t,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:k(e)}}}case"escaped":{let e=A(await M(t,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:k(e)}}}default:return{}}}}}(e),options:[e,t]}),eC=(e,t)=>({...em(e),options:[e,t]});var eR=n(63655),eM=n(95155),eA=r.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...i}=e;return(0,eM.jsx)(eR.sG.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,eM.jsx)("polygon",{points:"0,0 30,0 15,10"})})});eA.displayName="Arrow";var ek=n(6101),eP=n(46081),ej=n(39033),eD=n(52712),eE="Popper",[eT,eO]=(0,eP.A)(eE),[eS,eL]=eT(eE),eI=e=>{let{__scopePopper:t,children:n}=e,[o,i]=r.useState(null);return(0,eM.jsx)(eS,{scope:t,anchor:o,onAnchorChange:i,children:n})};eI.displayName=eE;var eF="PopperAnchor",e_=r.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:o,...i}=e,a=eL(eF,n),l=r.useRef(null),s=(0,ek.s)(t,l);return r.useEffect(()=>{a.onAnchorChange((null==o?void 0:o.current)||l.current)}),o?null:(0,eM.jsx)(eR.sG.div,{...i,ref:s})});e_.displayName=eF;var eN="PopperContent",[eq,eQ]=eT(eN),eG=r.forwardRef((e,t)=>{var n,o,l,u,c,d,f,h;let{__scopePopper:p,side:m="bottom",sideOffset:y=0,align:g="center",alignOffset:v=0,arrowPadding:w=0,avoidCollisions:x=!0,collisionBoundary:b=[],collisionPadding:C=0,sticky:R="partial",hideWhenDetached:M=!1,updatePositionStrategy:A="optimized",onPlaced:k,...P}=e,j=eL(eN,p),[D,E]=r.useState(null),O=(0,ek.s)(t,e=>E(e)),[S,L]=r.useState(null),I=function(e){let[t,n]=r.useState(void 0);return(0,eD.N)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}(S),F=null!=(f=null==I?void 0:I.width)?f:0,_=null!=(h=null==I?void 0:I.height)?h:0,N="number"==typeof C?C:{top:0,right:0,bottom:0,left:0,...C},q=Array.isArray(b)?b:[b],Q=q.length>0,G={padding:N,boundary:q.filter(eV),altBoundary:Q},{refs:H,floatingStyles:K,placement:V,isPositioned:W,middlewareData:U}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:o=[],platform:i,elements:{reference:a,floating:l}={},transform:s=!0,whileElementsMounted:u,open:c}=e,[d,f]=r.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[h,p]=r.useState(o);ed(h,o)||p(o);let[m,y]=r.useState(null),[g,v]=r.useState(null),w=r.useCallback(e=>{e!==R.current&&(R.current=e,y(e))},[]),x=r.useCallback(e=>{e!==M.current&&(M.current=e,v(e))},[]),b=a||m,C=l||g,R=r.useRef(null),M=r.useRef(null),A=r.useRef(d),k=null!=u,P=ep(u),j=ep(i),D=ep(c),E=r.useCallback(()=>{if(!R.current||!M.current)return;let e={placement:t,strategy:n,middleware:h};j.current&&(e.platform=j.current),es(R.current,M.current,e).then(e=>{let t={...e,isPositioned:!1!==D.current};T.current&&!ed(A.current,t)&&(A.current=t,eu.flushSync(()=>{f(t)}))})},[h,t,n,j,D]);ec(()=>{!1===c&&A.current.isPositioned&&(A.current.isPositioned=!1,f(e=>({...e,isPositioned:!1})))},[c]);let T=r.useRef(!1);ec(()=>(T.current=!0,()=>{T.current=!1}),[]),ec(()=>{if(b&&(R.current=b),C&&(M.current=C),b&&C){if(P.current)return P.current(b,C,E);E()}},[b,C,E,P,k]);let O=r.useMemo(()=>({reference:R,floating:M,setReference:w,setFloating:x}),[w,x]),S=r.useMemo(()=>({reference:b,floating:C}),[b,C]),L=r.useMemo(()=>{let e={position:n,left:0,top:0};if(!S.floating)return e;let t=eh(S.floating,d.x),r=eh(S.floating,d.y);return s?{...e,transform:"translate("+t+"px, "+r+"px)",...ef(S.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,s,S.floating,d.x,d.y]);return r.useMemo(()=>({...d,update:E,refs:O,elements:S,floatingStyles:L}),[d,E,O,S,L])}({strategy:"fixed",placement:m+("center"!==g?"-"+g:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e,t,n,r){let o;void 0===r&&(r={});let{ancestorScroll:l=!0,ancestorResize:u=!0,elementResize:c="function"==typeof ResizeObserver,layoutShift:d="function"==typeof IntersectionObserver,animationFrame:f=!1}=r,h=z(e),p=l||u?[...h?B(h):[],...B(t)]:[];p.forEach(e=>{l&&e.addEventListener("scroll",n,{passive:!0}),u&&e.addEventListener("resize",n)});let m=h&&d?function(e,t){let n,r=null,o=T(e);function l(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function u(c,d){void 0===c&&(c=!1),void 0===d&&(d=1),l();let f=e.getBoundingClientRect(),{left:h,top:p,width:m,height:y}=f;if(c||t(),!m||!y)return;let g=s(p),v=s(o.clientWidth-(h+m)),w={rootMargin:-g+"px "+-v+"px "+-s(o.clientHeight-(p+y))+"px "+-s(h)+"px",threshold:a(0,i(1,d))||1},x=!0;function b(t){let r=t[0].intersectionRatio;if(r!==d){if(!x)return u();r?u(!1,r):n=setTimeout(()=>{u(!1,1e-7)},1e3)}1!==r||ea(f,e.getBoundingClientRect())||u(),x=!1}try{r=new IntersectionObserver(b,{...w,root:o.ownerDocument})}catch(e){r=new IntersectionObserver(b,w)}r.observe(e)}(!0),l}(h,n):null,y=-1,g=null;c&&(g=new ResizeObserver(e=>{let[r]=e;r&&r.target===h&&g&&(g.unobserve(t),cancelAnimationFrame(y),y=requestAnimationFrame(()=>{var e;null==(e=g)||e.observe(t)})),n()}),h&&!f&&g.observe(h),g.observe(t));let v=f?Z(e):null;return f&&function t(){let r=Z(e);v&&!ea(v,r)&&n(),v=r,o=requestAnimationFrame(t)}(),n(),()=>{var e;p.forEach(e=>{l&&e.removeEventListener("scroll",n),u&&e.removeEventListener("resize",n)}),null==m||m(),null==(e=g)||e.disconnect(),g=null,f&&cancelAnimationFrame(o)}}(...t,{animationFrame:"always"===A})},elements:{reference:j.anchor},middleware:[ey({mainAxis:y+_,alignmentAxis:v}),x&&eg({mainAxis:!0,crossAxis:!1,limiter:"partial"===R?ev():void 0,...G}),x&&ew({...G}),ex({...G,apply:e=>{let{elements:t,rects:n,availableWidth:r,availableHeight:o}=e,{width:i,height:a}=n.reference,l=t.floating.style;l.setProperty("--radix-popper-available-width","".concat(r,"px")),l.setProperty("--radix-popper-available-height","".concat(o,"px")),l.setProperty("--radix-popper-anchor-width","".concat(i,"px")),l.setProperty("--radix-popper-anchor-height","".concat(a,"px"))}}),S&&eC({element:S,padding:w}),eW({arrowWidth:F,arrowHeight:_}),M&&eb({strategy:"referenceHidden",...G})]}),[X,Y]=ez(V),$=(0,ej.c)(k);(0,eD.N)(()=>{W&&(null==$||$())},[W,$]);let J=null==(n=U.arrow)?void 0:n.x,ee=null==(o=U.arrow)?void 0:o.y,et=(null==(l=U.arrow)?void 0:l.centerOffset)!==0,[en,er]=r.useState();return(0,eD.N)(()=>{D&&er(window.getComputedStyle(D).zIndex)},[D]),(0,eM.jsx)("div",{ref:H.setFloating,"data-radix-popper-content-wrapper":"",style:{...K,transform:W?K.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:en,"--radix-popper-transform-origin":[null==(u=U.transformOrigin)?void 0:u.x,null==(c=U.transformOrigin)?void 0:c.y].join(" "),...(null==(d=U.hide)?void 0:d.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,eM.jsx)(eq,{scope:p,placedSide:X,onArrowChange:L,arrowX:J,arrowY:ee,shouldHideArrow:et,children:(0,eM.jsx)(eR.sG.div,{"data-side":X,"data-align":Y,...P,ref:O,style:{...P.style,animation:W?void 0:"none"}})})})});eG.displayName=eN;var eH="PopperArrow",eK={top:"bottom",right:"left",bottom:"top",left:"right"},eB=r.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=eQ(eH,n),i=eK[o.placedSide];return(0,eM.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,eM.jsx)(eA,{...r,ref:t,style:{...r.style,display:"block"}})})});function eV(e){return null!==e}eB.displayName=eH;var eW=e=>({name:"transformOrigin",options:e,fn(t){var n,r,o,i,a;let{placement:l,rects:s,middlewareData:u}=t,c=(null==(n=u.arrow)?void 0:n.centerOffset)!==0,d=c?0:e.arrowWidth,f=c?0:e.arrowHeight,[h,p]=ez(l),m={start:"0%",center:"50%",end:"100%"}[p],y=(null!=(i=null==(r=u.arrow)?void 0:r.x)?i:0)+d/2,g=(null!=(a=null==(o=u.arrow)?void 0:o.y)?a:0)+f/2,v="",w="";return"bottom"===h?(v=c?m:"".concat(y,"px"),w="".concat(-f,"px")):"top"===h?(v=c?m:"".concat(y,"px"),w="".concat(s.floating.height+f,"px")):"right"===h?(v="".concat(-f,"px"),w=c?m:"".concat(g,"px")):"left"===h&&(v="".concat(s.floating.width+f,"px"),w=c?m:"".concat(g,"px")),{data:{x:v,y:w}}}});function ez(e){let[t,n="center"]=e.split("-");return[t,n]}var eU=eI,eX=e_,eY=eG,eZ=eB},42076:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("image-plus",[["path",{d:"M16 5h6",key:"1vod17"}],["path",{d:"M19 2v6",key:"4bpg5p"}],["path",{d:"M21 11.5V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h7.5",key:"1ue2ih"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}]])},45347:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("newspaper",[["path",{d:"M15 18h-5",key:"95g1m2"}],["path",{d:"M18 14h-8",key:"sponae"}],["path",{d:"M4 22h16a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2H8a2 2 0 0 0-2 2v16a2 2 0 0 1-4 0v-9a2 2 0 0 1 2-2h2",key:"39pd36"}],["rect",{width:"8",height:"4",x:"10",y:"6",rx:"1",key:"aywv1n"}]])},55670:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("user-check",[["path",{d:"m16 11 2 2 4-4",key:"9rsbq5"}],["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},57340:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]])},74466:(e,t,n)=>{n.d(t,{F:()=>a});var r=n(52596);let o=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,i=r.$,a=(e,t)=>n=>{var r;if((null==t?void 0:t.variants)==null)return i(e,null==n?void 0:n.class,null==n?void 0:n.className);let{variants:a,defaultVariants:l}=t,s=Object.keys(a).map(e=>{let t=null==n?void 0:n[e],r=null==l?void 0:l[e];if(null===t)return null;let i=o(t)||o(r);return a[e][i]}),u=n&&Object.entries(n).reduce((e,t)=>{let[n,r]=t;return void 0===r||(e[n]=r),e},{});return i(e,s,null==t||null==(r=t.compoundVariants)?void 0:r.reduce((e,t)=>{let{class:n,className:r,...o}=t;return Object.entries(o).every(e=>{let[t,n]=e;return Array.isArray(n)?n.includes({...l,...u}[t]):({...l,...u})[t]===n})?[...e,n,r]:e},[]),null==n?void 0:n.class,null==n?void 0:n.className)}},78578:(e,t,n)=>{n.d(t,{UC:()=>tl,q7:()=>tu,JU:()=>ts,ZL:()=>ta,bL:()=>to,wv:()=>tc,l9:()=>ti});var r,o=n(12115),i=n(85185),a=n(6101),l=n(46081),s=n(5845),u=n(63655);function c(e,t,n){if(!t.has(e))throw TypeError("attempted to "+n+" private field on non-instance");return t.get(e)}function d(e,t){var n=c(e,t,"get");return n.get?n.get.call(e):n.value}function f(e,t,n){var r=c(e,t,"set");if(r.set)r.set.call(e,n);else{if(!r.writable)throw TypeError("attempted to set read only private field");r.value=n}return n}var h=n(99708),p=n(95155);function m(e){let t=e+"CollectionProvider",[n,r]=(0,l.A)(t),[i,s]=n(t,{collectionRef:{current:null},itemMap:new Map}),u=e=>{let{scope:t,children:n}=e,r=o.useRef(null),a=o.useRef(new Map).current;return(0,p.jsx)(i,{scope:t,itemMap:a,collectionRef:r,children:n})};u.displayName=t;let c=e+"CollectionSlot",d=(0,h.TL)(c),f=o.forwardRef((e,t)=>{let{scope:n,children:r}=e,o=s(c,n),i=(0,a.s)(t,o.collectionRef);return(0,p.jsx)(d,{ref:i,children:r})});f.displayName=c;let m=e+"CollectionItemSlot",y="data-radix-collection-item",g=(0,h.TL)(m),v=o.forwardRef((e,t)=>{let{scope:n,children:r,...i}=e,l=o.useRef(null),u=(0,a.s)(t,l),c=s(m,n);return o.useEffect(()=>(c.itemMap.set(l,{ref:l,...i}),()=>void c.itemMap.delete(l))),(0,p.jsx)(g,{...{[y]:""},ref:u,children:r})});return v.displayName=m,[{Provider:u,Slot:f,ItemSlot:v},function(t){let n=s(e+"CollectionConsumer",t);return o.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(y,"]")));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},r]}var y=new WeakMap;function g(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let n=function(e,t){let n=e.length,r=v(t),o=r>=0?r:n+r;return o<0||o>=n?-1:o}(e,t);return -1===n?void 0:e[n]}function v(e){return e!=e||0===e?0:Math.trunc(e)}r=new WeakMap;var w=o.createContext(void 0);function x(e){let t=o.useContext(w);return e||t||"ltr"}var b=n(19178),C=n(92293),R=n(25519),M=n(61285),A=n(38795),k=n(34378),P=n(28905),j=n(39033),D="rovingFocusGroup.onEntryFocus",E={bubbles:!1,cancelable:!0},T="RovingFocusGroup",[O,S,L]=m(T),[I,F]=(0,l.A)(T,[L]),[_,N]=I(T),q=o.forwardRef((e,t)=>(0,p.jsx)(O.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(O.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(Q,{...e,ref:t})})}));q.displayName=T;var Q=o.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,orientation:r,loop:l=!1,dir:c,currentTabStopId:d,defaultCurrentTabStopId:f,onCurrentTabStopIdChange:h,onEntryFocus:m,preventScrollOnEntryFocus:y=!1,...g}=e,v=o.useRef(null),w=(0,a.s)(t,v),b=x(c),[C,R]=(0,s.i)({prop:d,defaultProp:null!=f?f:null,onChange:h,caller:T}),[M,A]=o.useState(!1),k=(0,j.c)(m),P=S(n),O=o.useRef(!1),[L,I]=o.useState(0);return o.useEffect(()=>{let e=v.current;if(e)return e.addEventListener(D,k),()=>e.removeEventListener(D,k)},[k]),(0,p.jsx)(_,{scope:n,orientation:r,dir:b,loop:l,currentTabStopId:C,onItemFocus:o.useCallback(e=>R(e),[R]),onItemShiftTab:o.useCallback(()=>A(!0),[]),onFocusableItemAdd:o.useCallback(()=>I(e=>e+1),[]),onFocusableItemRemove:o.useCallback(()=>I(e=>e-1),[]),children:(0,p.jsx)(u.sG.div,{tabIndex:M||0===L?-1:0,"data-orientation":r,...g,ref:w,style:{outline:"none",...e.style},onMouseDown:(0,i.m)(e.onMouseDown,()=>{O.current=!0}),onFocus:(0,i.m)(e.onFocus,e=>{let t=!O.current;if(e.target===e.currentTarget&&t&&!M){let t=new CustomEvent(D,E);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=P().filter(e=>e.focusable);B([e.find(e=>e.active),e.find(e=>e.id===C),...e].filter(Boolean).map(e=>e.ref.current),y)}}O.current=!1}),onBlur:(0,i.m)(e.onBlur,()=>A(!1))})})}),G="RovingFocusGroupItem",H=o.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,focusable:r=!0,active:a=!1,tabStopId:l,children:s,...c}=e,d=(0,M.B)(),f=l||d,h=N(G,n),m=h.currentTabStopId===f,y=S(n),{onFocusableItemAdd:g,onFocusableItemRemove:v,currentTabStopId:w}=h;return o.useEffect(()=>{if(r)return g(),()=>v()},[r,g,v]),(0,p.jsx)(O.ItemSlot,{scope:n,id:f,focusable:r,active:a,children:(0,p.jsx)(u.sG.span,{tabIndex:m?0:-1,"data-orientation":h.orientation,...c,ref:t,onMouseDown:(0,i.m)(e.onMouseDown,e=>{r?h.onItemFocus(f):e.preventDefault()}),onFocus:(0,i.m)(e.onFocus,()=>h.onItemFocus(f)),onKeyDown:(0,i.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void h.onItemShiftTab();if(e.target!==e.currentTarget)return;let t=function(e,t,n){var r;let o=(r=e.key,"rtl"!==n?r:"ArrowLeft"===r?"ArrowRight":"ArrowRight"===r?"ArrowLeft":r);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return K[o]}(e,h.orientation,h.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let n=y().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)n.reverse();else if("prev"===t||"next"===t){"prev"===t&&n.reverse();let r=n.indexOf(e.currentTarget);n=h.loop?function(e,t){return e.map((n,r)=>e[(t+r)%e.length])}(n,r+1):n.slice(r+1)}setTimeout(()=>B(n))}}),children:"function"==typeof s?s({isCurrentTabStop:m,hasTabStop:null!=w}):s})})});H.displayName=G;var K={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function B(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=document.activeElement;for(let r of e)if(r===n||(r.focus({preventScroll:t}),document.activeElement!==n))return}var V=n(38168),W=n(93795),z=["Enter"," "],U=["ArrowUp","PageDown","End"],X=["ArrowDown","PageUp","Home",...U],Y={ltr:[...z,"ArrowRight"],rtl:[...z,"ArrowLeft"]},Z={ltr:["ArrowLeft"],rtl:["ArrowRight"]},$="Menu",[J,ee,et]=m($),[en,er]=(0,l.A)($,[et,A.Bk,F]),eo=(0,A.Bk)(),ei=F(),[ea,el]=en($),[es,eu]=en($),ec=e=>{let{__scopeMenu:t,open:n=!1,children:r,dir:i,onOpenChange:a,modal:l=!0}=e,s=eo(t),[u,c]=o.useState(null),d=o.useRef(!1),f=(0,j.c)(a),h=x(i);return o.useEffect(()=>{let e=()=>{d.current=!0,document.addEventListener("pointerdown",t,{capture:!0,once:!0}),document.addEventListener("pointermove",t,{capture:!0,once:!0})},t=()=>d.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",t,{capture:!0}),document.removeEventListener("pointermove",t,{capture:!0})}},[]),(0,p.jsx)(A.bL,{...s,children:(0,p.jsx)(ea,{scope:t,open:n,onOpenChange:f,content:u,onContentChange:c,children:(0,p.jsx)(es,{scope:t,onClose:o.useCallback(()=>f(!1),[f]),isUsingKeyboardRef:d,dir:h,modal:l,children:r})})})};ec.displayName=$;var ed=o.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e,o=eo(n);return(0,p.jsx)(A.Mz,{...o,...r,ref:t})});ed.displayName="MenuAnchor";var ef="MenuPortal",[eh,ep]=en(ef,{forceMount:void 0}),em=e=>{let{__scopeMenu:t,forceMount:n,children:r,container:o}=e,i=el(ef,t);return(0,p.jsx)(eh,{scope:t,forceMount:n,children:(0,p.jsx)(P.C,{present:n||i.open,children:(0,p.jsx)(k.Z,{asChild:!0,container:o,children:r})})})};em.displayName=ef;var ey="MenuContent",[eg,ev]=en(ey),ew=o.forwardRef((e,t)=>{let n=ep(ey,e.__scopeMenu),{forceMount:r=n.forceMount,...o}=e,i=el(ey,e.__scopeMenu),a=eu(ey,e.__scopeMenu);return(0,p.jsx)(J.Provider,{scope:e.__scopeMenu,children:(0,p.jsx)(P.C,{present:r||i.open,children:(0,p.jsx)(J.Slot,{scope:e.__scopeMenu,children:a.modal?(0,p.jsx)(ex,{...o,ref:t}):(0,p.jsx)(eb,{...o,ref:t})})})})}),ex=o.forwardRef((e,t)=>{let n=el(ey,e.__scopeMenu),r=o.useRef(null),l=(0,a.s)(t,r);return o.useEffect(()=>{let e=r.current;if(e)return(0,V.Eq)(e)},[]),(0,p.jsx)(eR,{...e,ref:l,trapFocus:n.open,disableOutsidePointerEvents:n.open,disableOutsideScroll:!0,onFocusOutside:(0,i.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>n.onOpenChange(!1)})}),eb=o.forwardRef((e,t)=>{let n=el(ey,e.__scopeMenu);return(0,p.jsx)(eR,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>n.onOpenChange(!1)})}),eC=(0,h.TL)("MenuContent.ScrollLock"),eR=o.forwardRef((e,t)=>{let{__scopeMenu:n,loop:r=!1,trapFocus:l,onOpenAutoFocus:s,onCloseAutoFocus:u,disableOutsidePointerEvents:c,onEntryFocus:d,onEscapeKeyDown:f,onPointerDownOutside:h,onFocusOutside:m,onInteractOutside:y,onDismiss:g,disableOutsideScroll:v,...w}=e,x=el(ey,n),M=eu(ey,n),k=eo(n),P=ei(n),j=ee(n),[D,E]=o.useState(null),T=o.useRef(null),O=(0,a.s)(t,T,x.onContentChange),S=o.useRef(0),L=o.useRef(""),I=o.useRef(0),F=o.useRef(null),_=o.useRef("right"),N=o.useRef(0),Q=v?W.A:o.Fragment,G=e=>{var t,n;let r=L.current+e,o=j().filter(e=>!e.disabled),i=document.activeElement,a=null==(t=o.find(e=>e.ref.current===i))?void 0:t.textValue,l=function(e,t,n){var r;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,i=n?e.indexOf(n):-1,a=(r=Math.max(i,0),e.map((t,n)=>e[(r+n)%e.length]));1===o.length&&(a=a.filter(e=>e!==n));let l=a.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return l!==n?l:void 0}(o.map(e=>e.textValue),r,a),s=null==(n=o.find(e=>e.textValue===l))?void 0:n.ref.current;!function e(t){L.current=t,window.clearTimeout(S.current),""!==t&&(S.current=window.setTimeout(()=>e(""),1e3))}(r),s&&setTimeout(()=>s.focus())};o.useEffect(()=>()=>window.clearTimeout(S.current),[]),(0,C.Oh)();let H=o.useCallback(e=>{var t,n;return _.current===(null==(t=F.current)?void 0:t.side)&&function(e,t){return!!t&&function(e,t){let{x:n,y:r}=e,o=!1;for(let e=0,i=t.length-1;e<t.length;i=e++){let a=t[e],l=t[i],s=a.x,u=a.y,c=l.x,d=l.y;u>r!=d>r&&n<(c-s)*(r-u)/(d-u)+s&&(o=!o)}return o}({x:e.clientX,y:e.clientY},t)}(e,null==(n=F.current)?void 0:n.area)},[]);return(0,p.jsx)(eg,{scope:n,searchRef:L,onItemEnter:o.useCallback(e=>{H(e)&&e.preventDefault()},[H]),onItemLeave:o.useCallback(e=>{var t;H(e)||(null==(t=T.current)||t.focus(),E(null))},[H]),onTriggerLeave:o.useCallback(e=>{H(e)&&e.preventDefault()},[H]),pointerGraceTimerRef:I,onPointerGraceIntentChange:o.useCallback(e=>{F.current=e},[]),children:(0,p.jsx)(Q,{...v?{as:eC,allowPinchZoom:!0}:void 0,children:(0,p.jsx)(R.n,{asChild:!0,trapped:l,onMountAutoFocus:(0,i.m)(s,e=>{var t;e.preventDefault(),null==(t=T.current)||t.focus({preventScroll:!0})}),onUnmountAutoFocus:u,children:(0,p.jsx)(b.qW,{asChild:!0,disableOutsidePointerEvents:c,onEscapeKeyDown:f,onPointerDownOutside:h,onFocusOutside:m,onInteractOutside:y,onDismiss:g,children:(0,p.jsx)(q,{asChild:!0,...P,dir:M.dir,orientation:"vertical",loop:r,currentTabStopId:D,onCurrentTabStopIdChange:E,onEntryFocus:(0,i.m)(d,e=>{M.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,p.jsx)(A.UC,{role:"menu","aria-orientation":"vertical","data-state":eX(x.open),"data-radix-menu-content":"",dir:M.dir,...k,...w,ref:O,style:{outline:"none",...w.style},onKeyDown:(0,i.m)(w.onKeyDown,e=>{let t=e.target.closest("[data-radix-menu-content]")===e.currentTarget,n=e.ctrlKey||e.altKey||e.metaKey,r=1===e.key.length;t&&("Tab"===e.key&&e.preventDefault(),!n&&r&&G(e.key));let o=T.current;if(e.target!==o||!X.includes(e.key))return;e.preventDefault();let i=j().filter(e=>!e.disabled).map(e=>e.ref.current);U.includes(e.key)&&i.reverse(),function(e){let t=document.activeElement;for(let n of e)if(n===t||(n.focus(),document.activeElement!==t))return}(i)}),onBlur:(0,i.m)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(S.current),L.current="")}),onPointerMove:(0,i.m)(e.onPointerMove,e$(e=>{let t=e.target,n=N.current!==e.clientX;e.currentTarget.contains(t)&&n&&(_.current=e.clientX>N.current?"right":"left",N.current=e.clientX)}))})})})})})})});ew.displayName=ey;var eM=o.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,p.jsx)(u.sG.div,{role:"group",...r,ref:t})});eM.displayName="MenuGroup";var eA=o.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,p.jsx)(u.sG.div,{...r,ref:t})});eA.displayName="MenuLabel";var ek="MenuItem",eP="menu.itemSelect",ej=o.forwardRef((e,t)=>{let{disabled:n=!1,onSelect:r,...l}=e,s=o.useRef(null),c=eu(ek,e.__scopeMenu),d=ev(ek,e.__scopeMenu),f=(0,a.s)(t,s),h=o.useRef(!1);return(0,p.jsx)(eD,{...l,ref:f,disabled:n,onClick:(0,i.m)(e.onClick,()=>{let e=s.current;if(!n&&e){let t=new CustomEvent(eP,{bubbles:!0,cancelable:!0});e.addEventListener(eP,e=>null==r?void 0:r(e),{once:!0}),(0,u.hO)(e,t),t.defaultPrevented?h.current=!1:c.onClose()}}),onPointerDown:t=>{var n;null==(n=e.onPointerDown)||n.call(e,t),h.current=!0},onPointerUp:(0,i.m)(e.onPointerUp,e=>{var t;h.current||null==(t=e.currentTarget)||t.click()}),onKeyDown:(0,i.m)(e.onKeyDown,e=>{let t=""!==d.searchRef.current;n||t&&" "===e.key||z.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});ej.displayName=ek;var eD=o.forwardRef((e,t)=>{let{__scopeMenu:n,disabled:r=!1,textValue:l,...s}=e,c=ev(ek,n),d=ei(n),f=o.useRef(null),h=(0,a.s)(t,f),[m,y]=o.useState(!1),[g,v]=o.useState("");return o.useEffect(()=>{let e=f.current;if(e){var t;v((null!=(t=e.textContent)?t:"").trim())}},[s.children]),(0,p.jsx)(J.ItemSlot,{scope:n,disabled:r,textValue:null!=l?l:g,children:(0,p.jsx)(H,{asChild:!0,...d,focusable:!r,children:(0,p.jsx)(u.sG.div,{role:"menuitem","data-highlighted":m?"":void 0,"aria-disabled":r||void 0,"data-disabled":r?"":void 0,...s,ref:h,onPointerMove:(0,i.m)(e.onPointerMove,e$(e=>{r?c.onItemLeave(e):(c.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,i.m)(e.onPointerLeave,e$(e=>c.onItemLeave(e))),onFocus:(0,i.m)(e.onFocus,()=>y(!0)),onBlur:(0,i.m)(e.onBlur,()=>y(!1))})})})}),eE=o.forwardRef((e,t)=>{let{checked:n=!1,onCheckedChange:r,...o}=e;return(0,p.jsx)(eN,{scope:e.__scopeMenu,checked:n,children:(0,p.jsx)(ej,{role:"menuitemcheckbox","aria-checked":eY(n)?"mixed":n,...o,ref:t,"data-state":eZ(n),onSelect:(0,i.m)(o.onSelect,()=>null==r?void 0:r(!!eY(n)||!n),{checkForDefaultPrevented:!1})})})});eE.displayName="MenuCheckboxItem";var eT="MenuRadioGroup",[eO,eS]=en(eT,{value:void 0,onValueChange:()=>{}}),eL=o.forwardRef((e,t)=>{let{value:n,onValueChange:r,...o}=e,i=(0,j.c)(r);return(0,p.jsx)(eO,{scope:e.__scopeMenu,value:n,onValueChange:i,children:(0,p.jsx)(eM,{...o,ref:t})})});eL.displayName=eT;var eI="MenuRadioItem",eF=o.forwardRef((e,t)=>{let{value:n,...r}=e,o=eS(eI,e.__scopeMenu),a=n===o.value;return(0,p.jsx)(eN,{scope:e.__scopeMenu,checked:a,children:(0,p.jsx)(ej,{role:"menuitemradio","aria-checked":a,...r,ref:t,"data-state":eZ(a),onSelect:(0,i.m)(r.onSelect,()=>{var e;return null==(e=o.onValueChange)?void 0:e.call(o,n)},{checkForDefaultPrevented:!1})})})});eF.displayName=eI;var e_="MenuItemIndicator",[eN,eq]=en(e_,{checked:!1}),eQ=o.forwardRef((e,t)=>{let{__scopeMenu:n,forceMount:r,...o}=e,i=eq(e_,n);return(0,p.jsx)(P.C,{present:r||eY(i.checked)||!0===i.checked,children:(0,p.jsx)(u.sG.span,{...o,ref:t,"data-state":eZ(i.checked)})})});eQ.displayName=e_;var eG=o.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,p.jsx)(u.sG.div,{role:"separator","aria-orientation":"horizontal",...r,ref:t})});eG.displayName="MenuSeparator";var eH=o.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e,o=eo(n);return(0,p.jsx)(A.i3,{...o,...r,ref:t})});eH.displayName="MenuArrow";var[eK,eB]=en("MenuSub"),eV="MenuSubTrigger",eW=o.forwardRef((e,t)=>{let n=el(eV,e.__scopeMenu),r=eu(eV,e.__scopeMenu),l=eB(eV,e.__scopeMenu),s=ev(eV,e.__scopeMenu),u=o.useRef(null),{pointerGraceTimerRef:c,onPointerGraceIntentChange:d}=s,f={__scopeMenu:e.__scopeMenu},h=o.useCallback(()=>{u.current&&window.clearTimeout(u.current),u.current=null},[]);return o.useEffect(()=>h,[h]),o.useEffect(()=>{let e=c.current;return()=>{window.clearTimeout(e),d(null)}},[c,d]),(0,p.jsx)(ed,{asChild:!0,...f,children:(0,p.jsx)(eD,{id:l.triggerId,"aria-haspopup":"menu","aria-expanded":n.open,"aria-controls":l.contentId,"data-state":eX(n.open),...e,ref:(0,a.t)(t,l.onTriggerChange),onClick:t=>{var r;null==(r=e.onClick)||r.call(e,t),e.disabled||t.defaultPrevented||(t.currentTarget.focus(),n.open||n.onOpenChange(!0))},onPointerMove:(0,i.m)(e.onPointerMove,e$(t=>{s.onItemEnter(t),!t.defaultPrevented&&(e.disabled||n.open||u.current||(s.onPointerGraceIntentChange(null),u.current=window.setTimeout(()=>{n.onOpenChange(!0),h()},100)))})),onPointerLeave:(0,i.m)(e.onPointerLeave,e$(e=>{var t,r;h();let o=null==(t=n.content)?void 0:t.getBoundingClientRect();if(o){let t=null==(r=n.content)?void 0:r.dataset.side,i="right"===t,a=o[i?"left":"right"],l=o[i?"right":"left"];s.onPointerGraceIntentChange({area:[{x:e.clientX+(i?-5:5),y:e.clientY},{x:a,y:o.top},{x:l,y:o.top},{x:l,y:o.bottom},{x:a,y:o.bottom}],side:t}),window.clearTimeout(c.current),c.current=window.setTimeout(()=>s.onPointerGraceIntentChange(null),300)}else{if(s.onTriggerLeave(e),e.defaultPrevented)return;s.onPointerGraceIntentChange(null)}})),onKeyDown:(0,i.m)(e.onKeyDown,t=>{let o=""!==s.searchRef.current;if(!e.disabled&&(!o||" "!==t.key)&&Y[r.dir].includes(t.key)){var i;n.onOpenChange(!0),null==(i=n.content)||i.focus(),t.preventDefault()}})})})});eW.displayName=eV;var ez="MenuSubContent",eU=o.forwardRef((e,t)=>{let n=ep(ey,e.__scopeMenu),{forceMount:r=n.forceMount,...l}=e,s=el(ey,e.__scopeMenu),u=eu(ey,e.__scopeMenu),c=eB(ez,e.__scopeMenu),d=o.useRef(null),f=(0,a.s)(t,d);return(0,p.jsx)(J.Provider,{scope:e.__scopeMenu,children:(0,p.jsx)(P.C,{present:r||s.open,children:(0,p.jsx)(J.Slot,{scope:e.__scopeMenu,children:(0,p.jsx)(eR,{id:c.contentId,"aria-labelledby":c.triggerId,...l,ref:f,align:"start",side:"rtl"===u.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{var t;u.isUsingKeyboardRef.current&&(null==(t=d.current)||t.focus()),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,i.m)(e.onFocusOutside,e=>{e.target!==c.trigger&&s.onOpenChange(!1)}),onEscapeKeyDown:(0,i.m)(e.onEscapeKeyDown,e=>{u.onClose(),e.preventDefault()}),onKeyDown:(0,i.m)(e.onKeyDown,e=>{let t=e.currentTarget.contains(e.target),n=Z[u.dir].includes(e.key);if(t&&n){var r;s.onOpenChange(!1),null==(r=c.trigger)||r.focus(),e.preventDefault()}})})})})})});function eX(e){return e?"open":"closed"}function eY(e){return"indeterminate"===e}function eZ(e){return eY(e)?"indeterminate":e?"checked":"unchecked"}function e$(e){return t=>"mouse"===t.pointerType?e(t):void 0}eU.displayName=ez;var eJ="DropdownMenu",[e0,e1]=(0,l.A)(eJ,[er]),e2=er(),[e5,e9]=e0(eJ),e4=e=>{let{__scopeDropdownMenu:t,children:n,dir:r,open:i,defaultOpen:a,onOpenChange:l,modal:u=!0}=e,c=e2(t),d=o.useRef(null),[f,h]=(0,s.i)({prop:i,defaultProp:null!=a&&a,onChange:l,caller:eJ});return(0,p.jsx)(e5,{scope:t,triggerId:(0,M.B)(),triggerRef:d,contentId:(0,M.B)(),open:f,onOpenChange:h,onOpenToggle:o.useCallback(()=>h(e=>!e),[h]),modal:u,children:(0,p.jsx)(ec,{...c,open:f,onOpenChange:h,dir:r,modal:u,children:n})})};e4.displayName=eJ;var e6="DropdownMenuTrigger",e8=o.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,disabled:r=!1,...o}=e,l=e9(e6,n),s=e2(n);return(0,p.jsx)(ed,{asChild:!0,...s,children:(0,p.jsx)(u.sG.button,{type:"button",id:l.triggerId,"aria-haspopup":"menu","aria-expanded":l.open,"aria-controls":l.open?l.contentId:void 0,"data-state":l.open?"open":"closed","data-disabled":r?"":void 0,disabled:r,...o,ref:(0,a.t)(t,l.triggerRef),onPointerDown:(0,i.m)(e.onPointerDown,e=>{!r&&0===e.button&&!1===e.ctrlKey&&(l.onOpenToggle(),l.open||e.preventDefault())}),onKeyDown:(0,i.m)(e.onKeyDown,e=>{!r&&(["Enter"," "].includes(e.key)&&l.onOpenToggle(),"ArrowDown"===e.key&&l.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});e8.displayName=e6;var e3=e=>{let{__scopeDropdownMenu:t,...n}=e,r=e2(t);return(0,p.jsx)(em,{...r,...n})};e3.displayName="DropdownMenuPortal";var e7="DropdownMenuContent",te=o.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,a=e9(e7,n),l=e2(n),s=o.useRef(!1);return(0,p.jsx)(ew,{id:a.contentId,"aria-labelledby":a.triggerId,...l,...r,ref:t,onCloseAutoFocus:(0,i.m)(e.onCloseAutoFocus,e=>{var t;s.current||null==(t=a.triggerRef.current)||t.focus(),s.current=!1,e.preventDefault()}),onInteractOutside:(0,i.m)(e.onInteractOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey,r=2===t.button||n;(!a.modal||r)&&(s.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});te.displayName=e7,o.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=e2(n);return(0,p.jsx)(eM,{...o,...r,ref:t})}).displayName="DropdownMenuGroup";var tt=o.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=e2(n);return(0,p.jsx)(eA,{...o,...r,ref:t})});tt.displayName="DropdownMenuLabel";var tn=o.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=e2(n);return(0,p.jsx)(ej,{...o,...r,ref:t})});tn.displayName="DropdownMenuItem",o.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=e2(n);return(0,p.jsx)(eE,{...o,...r,ref:t})}).displayName="DropdownMenuCheckboxItem",o.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=e2(n);return(0,p.jsx)(eL,{...o,...r,ref:t})}).displayName="DropdownMenuRadioGroup",o.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=e2(n);return(0,p.jsx)(eF,{...o,...r,ref:t})}).displayName="DropdownMenuRadioItem",o.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=e2(n);return(0,p.jsx)(eQ,{...o,...r,ref:t})}).displayName="DropdownMenuItemIndicator";var tr=o.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=e2(n);return(0,p.jsx)(eG,{...o,...r,ref:t})});tr.displayName="DropdownMenuSeparator",o.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=e2(n);return(0,p.jsx)(eH,{...o,...r,ref:t})}).displayName="DropdownMenuArrow",o.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=e2(n);return(0,p.jsx)(eW,{...o,...r,ref:t})}).displayName="DropdownMenuSubTrigger",o.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=e2(n);return(0,p.jsx)(eU,{...o,...r,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})}).displayName="DropdownMenuSubContent";var to=e4,ti=e8,ta=e3,tl=te,ts=tt,tu=tn,tc=tr},83921:(e,t,n)=>{n.d(t,{i3:()=>Y,UC:()=>X,ZL:()=>U,Kq:()=>V,bL:()=>W,l9:()=>z});var r=n(12115),o=n(85185),i=n(6101),a=n(46081),l=n(19178),s=n(61285),u=n(38795),c=n(34378),d=n(28905),f=n(63655),h=n(99708),p=n(5845),m=n(95155),y=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),g=r.forwardRef((e,t)=>(0,m.jsx)(f.sG.span,{...e,ref:t,style:{...y,...e.style}}));g.displayName="VisuallyHidden";var[v,w]=(0,a.A)("Tooltip",[u.Bk]),x=(0,u.Bk)(),b="TooltipProvider",C="tooltip.open",[R,M]=v(b),A=e=>{let{__scopeTooltip:t,delayDuration:n=700,skipDelayDuration:o=300,disableHoverableContent:i=!1,children:a}=e,l=r.useRef(!0),s=r.useRef(!1),u=r.useRef(0);return r.useEffect(()=>{let e=u.current;return()=>window.clearTimeout(e)},[]),(0,m.jsx)(R,{scope:t,isOpenDelayedRef:l,delayDuration:n,onOpen:r.useCallback(()=>{window.clearTimeout(u.current),l.current=!1},[]),onClose:r.useCallback(()=>{window.clearTimeout(u.current),u.current=window.setTimeout(()=>l.current=!0,o)},[o]),isPointerInTransitRef:s,onPointerInTransitChange:r.useCallback(e=>{s.current=e},[]),disableHoverableContent:i,children:a})};A.displayName=b;var k="Tooltip",[P,j]=v(k),D=e=>{let{__scopeTooltip:t,children:n,open:o,defaultOpen:i,onOpenChange:a,disableHoverableContent:l,delayDuration:c}=e,d=M(k,e.__scopeTooltip),f=x(t),[h,y]=r.useState(null),g=(0,s.B)(),v=r.useRef(0),w=null!=l?l:d.disableHoverableContent,b=null!=c?c:d.delayDuration,R=r.useRef(!1),[A,j]=(0,p.i)({prop:o,defaultProp:null!=i&&i,onChange:e=>{e?(d.onOpen(),document.dispatchEvent(new CustomEvent(C))):d.onClose(),null==a||a(e)},caller:k}),D=r.useMemo(()=>A?R.current?"delayed-open":"instant-open":"closed",[A]),E=r.useCallback(()=>{window.clearTimeout(v.current),v.current=0,R.current=!1,j(!0)},[j]),T=r.useCallback(()=>{window.clearTimeout(v.current),v.current=0,j(!1)},[j]),O=r.useCallback(()=>{window.clearTimeout(v.current),v.current=window.setTimeout(()=>{R.current=!0,j(!0),v.current=0},b)},[b,j]);return r.useEffect(()=>()=>{v.current&&(window.clearTimeout(v.current),v.current=0)},[]),(0,m.jsx)(u.bL,{...f,children:(0,m.jsx)(P,{scope:t,contentId:g,open:A,stateAttribute:D,trigger:h,onTriggerChange:y,onTriggerEnter:r.useCallback(()=>{d.isOpenDelayedRef.current?O():E()},[d.isOpenDelayedRef,O,E]),onTriggerLeave:r.useCallback(()=>{w?T():(window.clearTimeout(v.current),v.current=0)},[T,w]),onOpen:E,onClose:T,disableHoverableContent:w,children:n})})};D.displayName=k;var E="TooltipTrigger",T=r.forwardRef((e,t)=>{let{__scopeTooltip:n,...a}=e,l=j(E,n),s=M(E,n),c=x(n),d=r.useRef(null),h=(0,i.s)(t,d,l.onTriggerChange),p=r.useRef(!1),y=r.useRef(!1),g=r.useCallback(()=>p.current=!1,[]);return r.useEffect(()=>()=>document.removeEventListener("pointerup",g),[g]),(0,m.jsx)(u.Mz,{asChild:!0,...c,children:(0,m.jsx)(f.sG.button,{"aria-describedby":l.open?l.contentId:void 0,"data-state":l.stateAttribute,...a,ref:h,onPointerMove:(0,o.m)(e.onPointerMove,e=>{"touch"!==e.pointerType&&(y.current||s.isPointerInTransitRef.current||(l.onTriggerEnter(),y.current=!0))}),onPointerLeave:(0,o.m)(e.onPointerLeave,()=>{l.onTriggerLeave(),y.current=!1}),onPointerDown:(0,o.m)(e.onPointerDown,()=>{l.open&&l.onClose(),p.current=!0,document.addEventListener("pointerup",g,{once:!0})}),onFocus:(0,o.m)(e.onFocus,()=>{p.current||l.onOpen()}),onBlur:(0,o.m)(e.onBlur,l.onClose),onClick:(0,o.m)(e.onClick,l.onClose)})})});T.displayName=E;var O="TooltipPortal",[S,L]=v(O,{forceMount:void 0}),I=e=>{let{__scopeTooltip:t,forceMount:n,children:r,container:o}=e,i=j(O,t);return(0,m.jsx)(S,{scope:t,forceMount:n,children:(0,m.jsx)(d.C,{present:n||i.open,children:(0,m.jsx)(c.Z,{asChild:!0,container:o,children:r})})})};I.displayName=O;var F="TooltipContent",_=r.forwardRef((e,t)=>{let n=L(F,e.__scopeTooltip),{forceMount:r=n.forceMount,side:o="top",...i}=e,a=j(F,e.__scopeTooltip);return(0,m.jsx)(d.C,{present:r||a.open,children:a.disableHoverableContent?(0,m.jsx)(H,{side:o,...i,ref:t}):(0,m.jsx)(N,{side:o,...i,ref:t})})}),N=r.forwardRef((e,t)=>{let n=j(F,e.__scopeTooltip),o=M(F,e.__scopeTooltip),a=r.useRef(null),l=(0,i.s)(t,a),[s,u]=r.useState(null),{trigger:c,onClose:d}=n,f=a.current,{onPointerInTransitChange:h}=o,p=r.useCallback(()=>{u(null),h(!1)},[h]),y=r.useCallback((e,t)=>{let n=e.currentTarget,r={x:e.clientX,y:e.clientY},o=function(e,t){let n=Math.abs(t.top-e.y),r=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),i=Math.abs(t.left-e.x);switch(Math.min(n,r,o,i)){case i:return"left";case o:return"right";case n:return"top";case r:return"bottom";default:throw Error("unreachable")}}(r,n.getBoundingClientRect());u(function(e){let t=e.slice();return t.sort((e,t)=>e.x<t.x?-1:e.x>t.x?1:e.y<t.y?-1:1*!!(e.y>t.y)),function(e){if(e.length<=1)return e.slice();let t=[];for(let n=0;n<e.length;n++){let r=e[n];for(;t.length>=2;){let e=t[t.length-1],n=t[t.length-2];if((e.x-n.x)*(r.y-n.y)>=(e.y-n.y)*(r.x-n.x))t.pop();else break}t.push(r)}t.pop();let n=[];for(let t=e.length-1;t>=0;t--){let r=e[t];for(;n.length>=2;){let e=n[n.length-1],t=n[n.length-2];if((e.x-t.x)*(r.y-t.y)>=(e.y-t.y)*(r.x-t.x))n.pop();else break}n.push(r)}return(n.pop(),1===t.length&&1===n.length&&t[0].x===n[0].x&&t[0].y===n[0].y)?t:t.concat(n)}(t)}([...function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:5,r=[];switch(t){case"top":r.push({x:e.x-n,y:e.y+n},{x:e.x+n,y:e.y+n});break;case"bottom":r.push({x:e.x-n,y:e.y-n},{x:e.x+n,y:e.y-n});break;case"left":r.push({x:e.x+n,y:e.y-n},{x:e.x+n,y:e.y+n});break;case"right":r.push({x:e.x-n,y:e.y-n},{x:e.x-n,y:e.y+n})}return r}(r,o),...function(e){let{top:t,right:n,bottom:r,left:o}=e;return[{x:o,y:t},{x:n,y:t},{x:n,y:r},{x:o,y:r}]}(t.getBoundingClientRect())])),h(!0)},[h]);return r.useEffect(()=>()=>p(),[p]),r.useEffect(()=>{if(c&&f){let e=e=>y(e,f),t=e=>y(e,c);return c.addEventListener("pointerleave",e),f.addEventListener("pointerleave",t),()=>{c.removeEventListener("pointerleave",e),f.removeEventListener("pointerleave",t)}}},[c,f,y,p]),r.useEffect(()=>{if(s){let e=e=>{let t=e.target,n={x:e.clientX,y:e.clientY},r=(null==c?void 0:c.contains(t))||(null==f?void 0:f.contains(t)),o=!function(e,t){let{x:n,y:r}=e,o=!1;for(let e=0,i=t.length-1;e<t.length;i=e++){let a=t[e],l=t[i],s=a.x,u=a.y,c=l.x,d=l.y;u>r!=d>r&&n<(c-s)*(r-u)/(d-u)+s&&(o=!o)}return o}(n,s);r?p():o&&(p(),d())};return document.addEventListener("pointermove",e),()=>document.removeEventListener("pointermove",e)}},[c,f,s,d,p]),(0,m.jsx)(H,{...e,ref:l})}),[q,Q]=v(k,{isInside:!1}),G=(0,h.Dc)("TooltipContent"),H=r.forwardRef((e,t)=>{let{__scopeTooltip:n,children:o,"aria-label":i,onEscapeKeyDown:a,onPointerDownOutside:s,...c}=e,d=j(F,n),f=x(n),{onClose:h}=d;return r.useEffect(()=>(document.addEventListener(C,h),()=>document.removeEventListener(C,h)),[h]),r.useEffect(()=>{if(d.trigger){let e=e=>{let t=e.target;(null==t?void 0:t.contains(d.trigger))&&h()};return window.addEventListener("scroll",e,{capture:!0}),()=>window.removeEventListener("scroll",e,{capture:!0})}},[d.trigger,h]),(0,m.jsx)(l.qW,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:a,onPointerDownOutside:s,onFocusOutside:e=>e.preventDefault(),onDismiss:h,children:(0,m.jsxs)(u.UC,{"data-state":d.stateAttribute,...f,...c,ref:t,style:{...c.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[(0,m.jsx)(G,{children:o}),(0,m.jsx)(q,{scope:n,isInside:!0,children:(0,m.jsx)(g,{id:d.contentId,role:"tooltip",children:i||o})})]})})});_.displayName=F;var K="TooltipArrow",B=r.forwardRef((e,t)=>{let{__scopeTooltip:n,...r}=e,o=x(n);return Q(K,n).isInside?null:(0,m.jsx)(u.i3,{...o,...r,ref:t})});B.displayName=K;var V=A,W=D,z=T,U=I,X=_,Y=B},85339:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])}}]);