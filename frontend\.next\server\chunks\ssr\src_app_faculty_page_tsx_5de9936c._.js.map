{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/fwu_incubation%28react%2Blaravel%29/incubation/frontend/src/app/faculty/page.tsx"], "sourcesContent": ["\"use client\"\r\nimport { useState, useEffect, useRef } from 'react';\r\nimport Image from 'next/image';\r\nimport Link from 'next/link';\r\nimport { ArrowRight, Book, Users, Award, MapPin, GraduationCap, UserCheck,FlaskConical, Code, Scale, Leaf, Heart, TreePine, Globe, Check } from 'lucide-react';\r\n\r\n// Faculty type definition\r\ninterface Faculty {\r\n  id: string;\r\n  name: string;\r\n  shortName: string;\r\n  description: string;\r\n  icon: React.ReactNode;\r\n  imageUrl: string;\r\n  programs: {\r\n    bachelor: string[];\r\n    master: string[];\r\n    mphil: string[];\r\n    phd: string[];\r\n  };\r\n  stats: {\r\n    teachers: number;\r\n    students: number;\r\n    staff: number;\r\n  };\r\n  website: string;\r\n  email: string;\r\n  color: string;\r\n}\r\n\r\nexport default function FacultyPage() {\r\n  const [isVisible, setIsVisible] = useState(false);\r\n  const [selectedFaculty, setSelectedFaculty] = useState<Faculty | null>(null);\r\n  const [activeTab, setActiveTab] = useState('overview');\r\n  const heroRef = useRef(null);\r\n\r\n  // Faculty data based on FWU website\r\n  const faculties: Faculty[] = [\r\n    {\r\n      id: 'science',\r\n      name: 'Faculty of Science and Technology',\r\n      shortName: 'Science & Tech',\r\n      description: 'The Faculty of Science and Technology at Far Western University offers programs in various scientific disciplines. It focuses on providing quality education in fields like Physics, Chemistry, Mathematics, Computer Science, and Information Technology. The faculty is committed to developing skilled professionals through theoretical knowledge and practical applications.',\r\n      icon: <FlaskConical className=\"w-6 h-6\" />,\r\n      imageUrl: 'https://images.unsplash.com/photo-1532094349884-543bc11b234d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2000&q=80',\r\n      programs: {\r\n        bachelor: ['B.Sc.', 'B.Sc. CSIT'],\r\n        master: ['M.Sc. Physics', 'M.Sc. Chemistry', 'M.Sc. Mathematics'],\r\n        mphil: ['MPhil in Science'],\r\n        phd: ['PhD in Science']\r\n      },\r\n      stats: {\r\n        teachers: 43,\r\n        students: 592,\r\n        staff: 13\r\n      },\r\n      website: 'http://facultyscience.fwu.edu.np',\r\n      email: '<EMAIL>',\r\n      color: 'indigo'\r\n    },\r\n    {\r\n      id: 'management',\r\n      name: 'Faculty of Management',\r\n      shortName: 'Management',\r\n      description: 'The Faculty of Management at Far Western University is dedicated to developing skilled professionals in business and management. It offers programs that blend theoretical knowledge with practical skills to prepare students for the challenges of the business world. The faculty emphasizes research, innovation, and entrepreneurship.',\r\n      icon: <Globe className=\"w-6 h-6\" />,\r\n      imageUrl: 'https://images.unsplash.com/photo-1552664730-d307ca884978?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2000&q=80',\r\n      programs: {\r\n        bachelor: ['BBA', 'BHM', 'BIM'],\r\n        master: ['MBA', 'MBM'],\r\n        mphil: ['MPhil in Management'],\r\n        phd: ['PhD in Management']\r\n      },\r\n      stats: {\r\n        teachers: 38,\r\n        students: 745,\r\n        staff: 15\r\n      },\r\n      website: 'http://facultymanagement.fwu.edu.np',\r\n      email: '<EMAIL>',\r\n      color: 'blue'\r\n    },\r\n    {\r\n      id: 'humanities',\r\n      name: 'Faculty of Humanities and Social Sciences',\r\n      shortName: 'Humanities',\r\n      description: 'The Faculty of Humanities and Social Sciences focuses on the study of human society, culture, and relationships. It offers programs in various disciplines including sociology, political science, economics, and languages. The faculty aims to develop critical thinking, analytical skills, and a deep understanding of social issues.',\r\n      icon: <Globe className=\"w-6 h-6\" />,\r\n      imageUrl: 'https://images.unsplash.com/photo-1523050854058-8df90110c9f1?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2000&q=80',\r\n      programs: {\r\n        bachelor: ['BA', 'BSW'],\r\n        master: ['MA English', 'MA Economics', 'MA Sociology'],\r\n        mphil: ['MPhil in Humanities'],\r\n        phd: ['PhD in Humanities']\r\n      },\r\n      stats: {\r\n        teachers: 35,\r\n        students: 520,\r\n        staff: 12\r\n      },\r\n      website: 'http://facultyhumanities.fwu.edu.np',\r\n      email: '<EMAIL>',\r\n      color: 'purple'\r\n    },\r\n    {\r\n      id: 'education',\r\n      name: 'Faculty of Education',\r\n      shortName: 'Education',\r\n      description: 'The Faculty of Education is dedicated to preparing skilled and knowledgeable educators. It offers programs that combine educational theory with practical teaching experience. The faculty focuses on developing teaching methodologies, curriculum design, and educational leadership to prepare students for careers in education.',\r\n      icon: <UserCheck className=\"w-6 h-6\" />,\r\n      imageUrl: 'https://images.unsplash.com/photo-1503676260728-1c00da094a0b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2000&q=80',\r\n      programs: {\r\n        bachelor: ['B.Ed.'],\r\n        master: ['M.Ed.'],\r\n        mphil: ['MPhil in Education'],\r\n        phd: ['PhD in Education']\r\n      },\r\n      stats: {\r\n        teachers: 40,\r\n        students: 680,\r\n        staff: 14\r\n      },\r\n      website: 'http://facultyeducation.fwu.edu.np',\r\n      email: '<EMAIL>',\r\n      color: 'green'\r\n    },\r\n    {\r\n      id: 'law',\r\n      name: 'Faculty of Law',\r\n      shortName: 'Law',\r\n      description: 'The Faculty of Law provides comprehensive legal education to prepare students for careers in law. It offers programs that cover various aspects of legal studies including constitutional law, criminal law, civil law, and international law. The faculty emphasizes legal research, critical analysis, and practical legal skills.',\r\n      icon: <Scale className=\"w-6 h-6\" />,\r\n      imageUrl: 'https://images.unsplash.com/photo-1589994965851-a8f479c573a9?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2000&q=80',\r\n      programs: {\r\n        bachelor: ['LLB'],\r\n        master: ['LLM'],\r\n        mphil: [],\r\n        phd: []\r\n      },\r\n      stats: {\r\n        teachers: 25,\r\n        students: 320,\r\n        staff: 10\r\n      },\r\n      website: 'http://facultylaw.fwu.edu.np',\r\n      email: '<EMAIL>',\r\n      color: 'red'\r\n    },\r\n    {\r\n      id: 'engineering',\r\n      name: 'Faculty of Engineering',\r\n      shortName: 'Engineering',\r\n      description: 'The Faculty of Engineering is dedicated to developing skilled engineers through comprehensive education in various engineering disciplines. It offers programs that combine theoretical knowledge with practical applications to prepare students for careers in engineering. The faculty emphasizes innovation, problem-solving, and sustainable development.',\r\n      icon: <Code className=\"w-6 h-6\" />,\r\n      imageUrl: 'https://images.unsplash.com/photo-1581094794329-c8112a89af12?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2000&q=80',\r\n      programs: {\r\n        bachelor: ['B.E. Civil', 'B.E. Computer'],\r\n        master: ['M.E.'],\r\n        mphil: [],\r\n        phd: []\r\n      },\r\n      stats: {\r\n        teachers: 30,\r\n        students: 450,\r\n        staff: 15\r\n      },\r\n      website: 'http://facultyengineering.fwu.edu.np',\r\n      email: '<EMAIL>',\r\n      color: 'orange'\r\n    },\r\n    {\r\n      id: 'agriculture',\r\n      name: 'Faculty of Agriculture',\r\n      shortName: 'Agriculture',\r\n      description: 'The Faculty of Agriculture focuses on agricultural education and research to address challenges in food production and sustainable farming. It offers programs that cover various aspects of agriculture including crop science, animal husbandry, and agricultural economics. The faculty emphasizes practical skills and innovative approaches to agriculture.',\r\n      icon: <Leaf className=\"w-6 h-6\" />,\r\n      imageUrl: 'https://images.unsplash.com/photo-1592982573971-2c0d8be9bcbf?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2000&q=80',\r\n      programs: {\r\n        bachelor: ['B.Sc. Agriculture'],\r\n        master: ['M.Sc. Agriculture'],\r\n        mphil: [],\r\n        phd: []\r\n      },\r\n      stats: {\r\n        teachers: 28,\r\n        students: 380,\r\n        staff: 12\r\n      },\r\n      website: 'http://facultyagriculture.fwu.edu.np',\r\n      email: '<EMAIL>',\r\n      color: 'lime'\r\n    },\r\n    {\r\n      id: 'health',\r\n      name: 'Faculty of Health Sciences',\r\n      shortName: 'Health Sciences',\r\n      description: 'The Faculty of Health Sciences is dedicated to developing healthcare professionals through comprehensive education in various health-related disciplines. It offers programs that combine theoretical knowledge with practical clinical experience to prepare students for careers in healthcare. The faculty emphasizes patient care, research, and community health.',\r\n      icon: <Heart className=\"w-6 h-6\" />,\r\n      imageUrl: 'https://images.unsplash.com/photo-1576091160550-2173dba999ef?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2000&q=80',\r\n      programs: {\r\n        bachelor: ['B.Sc. Nursing', 'BMLT', 'BPH'],\r\n        master: ['MPH'],\r\n        mphil: [],\r\n        phd: []\r\n      },\r\n      stats: {\r\n        teachers: 32,\r\n        students: 420,\r\n        staff: 14\r\n      },\r\n      website: '#',\r\n      email: '<EMAIL>',\r\n      color: 'teal'\r\n    },\r\n    {\r\n      id: 'nrm',\r\n      name: 'Faculty of Natural Resource Management',\r\n      shortName: 'Natural Resources',\r\n      description: 'The Faculty of Natural Resource Management focuses on sustainable management of natural resources. It offers programs that cover various aspects of resource management including forestry, wildlife conservation, and environmental science. The faculty emphasizes ecological balance, conservation strategies, and sustainable development.',\r\n      icon: <TreePine className=\"w-6 h-6\" />,\r\n      imageUrl: 'https://images.unsplash.com/photo-1511497584788-876760111969?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2000&q=80',\r\n      programs: {\r\n        bachelor: ['B.Sc. Forestry', 'B.Sc. Environmental Science'],\r\n        master: ['M.Sc. Natural Resource Management'],\r\n        mphil: [],\r\n        phd: []\r\n      },\r\n      stats: {\r\n        teachers: 26,\r\n        students: 310,\r\n        staff: 11\r\n      },\r\n      website: 'http://facultynrm.fwu.edu.np',\r\n      email: '<EMAIL>',\r\n      color: 'emerald'\r\n    }\r\n  ];\r\n\r\n  useEffect(() => {\r\n    const observer = new IntersectionObserver(\r\n      ([entry]) => {\r\n        if (entry.isIntersecting) {\r\n          setIsVisible(true);\r\n          observer.disconnect();\r\n        }\r\n      },\r\n      { threshold: 0.1 }\r\n    );\r\n\r\n    // Store current ref value in a variable to use in cleanup\r\n    const currentRef = heroRef.current;\r\n\r\n    if (currentRef) {\r\n      observer.observe(currentRef);\r\n    }\r\n\r\n    return () => {\r\n      // Use the stored ref value in cleanup\r\n      if (currentRef) {\r\n        observer.unobserve(currentRef);\r\n      }\r\n    };\r\n  }, []);\r\n\r\n  const handleFacultyClick = (faculty: Faculty) => {\r\n    setSelectedFaculty(faculty);\r\n    setActiveTab('overview');\r\n    window.scrollTo({\r\n      top: 0,\r\n      behavior: 'smooth'\r\n    });\r\n  };\r\n\r\n  const getColorClass = (color: string, type: 'bg' | 'text' | 'border' | 'hover-bg' | 'hover-border' | 'hover-text') => {\r\n    const colorMap: Record<string, Record<string, string>> = {\r\n      indigo: {\r\n        'bg': 'bg-indigo-600',\r\n        'text': 'text-indigo-600',\r\n        'border': 'border-indigo-600',\r\n        'hover-bg': 'hover:bg-indigo-700',\r\n        'hover-border': 'hover:border-indigo-700',\r\n        'hover-text': 'hover:text-indigo-700'\r\n      },\r\n      blue: {\r\n        'bg': 'bg-blue-600',\r\n        'text': 'text-blue-600',\r\n        'border': 'border-blue-600',\r\n        'hover-bg': 'hover:bg-blue-700',\r\n        'hover-border': 'hover:border-blue-700',\r\n        'hover-text': 'hover:text-blue-700'\r\n      },\r\n      purple: {\r\n        'bg': 'bg-purple-600',\r\n        'text': 'text-purple-600',\r\n        'border': 'border-purple-600',\r\n        'hover-bg': 'hover:bg-purple-700',\r\n        'hover-border': 'hover:border-purple-700',\r\n        'hover-text': 'hover:text-purple-700'\r\n      },\r\n      green: {\r\n        'bg': 'bg-green-600',\r\n        'text': 'text-green-600',\r\n        'border': 'border-green-600',\r\n        'hover-bg': 'hover:bg-green-700',\r\n        'hover-border': 'hover:border-green-700',\r\n        'hover-text': 'hover:text-green-700'\r\n      },\r\n      red: {\r\n        'bg': 'bg-red-600',\r\n        'text': 'text-red-600',\r\n        'border': 'border-red-600',\r\n        'hover-bg': 'hover:bg-red-700',\r\n        'hover-border': 'hover:border-red-700',\r\n        'hover-text': 'hover:text-red-700'\r\n      },\r\n      orange: {\r\n        'bg': 'bg-orange-600',\r\n        'text': 'text-orange-600',\r\n        'border': 'border-orange-600',\r\n        'hover-bg': 'hover:bg-orange-700',\r\n        'hover-border': 'hover:border-orange-700',\r\n        'hover-text': 'hover:text-orange-700'\r\n      },\r\n      lime: {\r\n        'bg': 'bg-lime-600',\r\n        'text': 'text-lime-600',\r\n        'border': 'border-lime-600',\r\n        'hover-bg': 'hover:bg-lime-700',\r\n        'hover-border': 'hover:border-lime-700',\r\n        'hover-text': 'hover:text-lime-700'\r\n      },\r\n      teal: {\r\n        'bg': 'bg-teal-600',\r\n        'text': 'text-teal-600',\r\n        'border': 'border-teal-600',\r\n        'hover-bg': 'hover:bg-teal-700',\r\n        'hover-border': 'hover:border-teal-700',\r\n        'hover-text': 'hover:text-teal-700'\r\n      },\r\n      emerald: {\r\n        'bg': 'bg-emerald-600',\r\n        'text': 'text-emerald-600',\r\n        'border': 'border-emerald-600',\r\n        'hover-bg': 'hover:bg-emerald-700',\r\n        'hover-border': 'hover:border-emerald-700',\r\n        'hover-text': 'hover:text-emerald-700'\r\n      }\r\n    };\r\n\r\n    return colorMap[color][type] || colorMap['indigo'][type];\r\n  };\r\n\r\n  return (\r\n    <main className=\"bg-white\">\r\n      {/* Hero Section */}\r\n      <div\r\n        ref={heroRef}\r\n        className=\"relative py-20 md:py-28 bg-gradient-to-r from-indigo-900 via-indigo-800 to-blue-900 text-white overflow-hidden\"\r\n      >\r\n        {/* Background elements */}\r\n        <div className=\"absolute inset-0 overflow-hidden\">\r\n          <div className=\"absolute top-20 left-10 w-64 h-64 rounded-full bg-indigo-500 opacity-10 animate-float-slow\"></div>\r\n          <div className=\"absolute bottom-20 right-10 w-96 h-96 rounded-full bg-blue-500 opacity-10 animate-float-reverse\"></div>\r\n          <div className=\"absolute top-1/2 left-1/4 w-32 h-32 rounded-full bg-purple-500 opacity-5 animate-pulse\"></div>\r\n          <div\r\n            className=\"absolute inset-0 opacity-10\"\r\n            style={{\r\n              backgroundImage: 'radial-gradient(circle, rgba(255,255,255,0.1) 1px, transparent 1px)',\r\n              backgroundSize: '30px 30px'\r\n            }}\r\n          ></div>\r\n        </div>\r\n\r\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 relative z-10\">\r\n          <div className={`max-w-4xl mx-auto text-center transition-all duration-1000 transform ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>\r\n            <div className=\"inline-block mb-6 p-2 bg-indigo-800/30 rounded-full\">\r\n              <div className=\"px-4 py-1 bg-indigo-700/50 rounded-full\">\r\n                <span className=\"text-indigo-100 font-medium\">Far Western University</span>\r\n              </div>\r\n            </div>\r\n\r\n            <h1 className=\"text-4xl sm:text-5xl md:text-6xl font-bold mb-6 leading-tight\">\r\n              Our <span className=\"text-transparent bg-clip-text bg-gradient-to-r from-blue-300 to-indigo-300\">Faculties</span>\r\n            </h1>\r\n\r\n            <p className=\"text-xl text-indigo-100 max-w-3xl mx-auto mb-10 leading-relaxed\">\r\n              Explore the diverse academic faculties at Far Western University, each dedicated to excellence in education, research, and innovation.\r\n            </p>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Wave divider */}\r\n        <div className=\"absolute bottom-0 left-0 right-0\">\r\n          <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 1440 120\" className=\"w-full h-auto\">\r\n            <path\r\n              fill=\"#ffffff\"\r\n              fillOpacity=\"1\"\r\n              d=\"M0,64L80,69.3C160,75,320,85,480,80C640,75,800,53,960,48C1120,43,1280,53,1360,58.7L1440,64L1440,120L1360,120C1280,120,1120,120,960,120C800,120,640,120,480,120C320,120,160,120,80,120L0,120Z\"\r\n            ></path>\r\n          </svg>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Selected Faculty Detail Section */}\r\n      {selectedFaculty && (\r\n        <section className=\"py-16 bg-gray-50\">\r\n          <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\r\n            <div className=\"max-w-6xl mx-auto\">\r\n              {/* Faculty Header */}\r\n              <div className=\"relative rounded-2xl overflow-hidden mb-8\">\r\n                <div className=\"h-64 md:h-80 relative\">\r\n                  <Image\r\n                    src={selectedFaculty.imageUrl}\r\n                    alt={selectedFaculty.name}\r\n                    fill\r\n                    className=\"object-cover\"\r\n                  />\r\n                  <div className={`absolute inset-0 bg-gradient-to-r from-${selectedFaculty.color}-900/80 to-${selectedFaculty.color}-800/70`}></div>\r\n                </div>\r\n\r\n                <div className=\"absolute bottom-0 left-0 right-0 p-6 md:p-8\">\r\n                  <div className=\"flex flex-col md:flex-row md:items-center justify-between\">\r\n                    <div>\r\n                      <h2 className=\"text-3xl md:text-4xl font-bold text-white mb-2\">{selectedFaculty.name}</h2>\r\n                      <div className=\"flex items-center text-white/80 text-sm\">\r\n                        <MapPin className=\"mr-2\" />\r\n                        <span>Far Western University, Mahendranagar, Nepal</span>\r\n                      </div>\r\n                    </div>\r\n                    <div className=\"mt-4 md:mt-0\">\r\n                      <a\r\n                        href={selectedFaculty.website}\r\n                        target=\"_blank\"\r\n                        rel=\"noopener noreferrer\"\r\n                        className={`inline-flex items-center px-4 py-2 rounded-lg bg-white text-${selectedFaculty.color}-600 font-medium hover:bg-${selectedFaculty.color}-50 transition-colors`}\r\n                      >\r\n                        Visit Faculty Website\r\n                        <ArrowRight className=\"ml-2\" />\r\n                      </a>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Tabs */}\r\n              <div className=\"mb-8 border-b border-gray-200\">\r\n                <div className=\"flex overflow-x-auto scrollbar-hide space-x-4\">\r\n                  <button\r\n                    onClick={() => setActiveTab('overview')}\r\n                    className={`px-4 py-3 font-medium text-sm border-b-2 transition-colors whitespace-nowrap ${\r\n                      activeTab === 'overview'\r\n                        ? `border-${selectedFaculty.color}-600 text-${selectedFaculty.color}-600`\r\n                        : 'border-transparent text-gray-500 hover:text-gray-700'\r\n                    }`}\r\n                  >\r\n                    Overview\r\n                  </button>\r\n                  <button\r\n                    onClick={() => setActiveTab('programs')}\r\n                    className={`px-4 py-3 font-medium text-sm border-b-2 transition-colors whitespace-nowrap ${\r\n                      activeTab === 'programs'\r\n                        ? `border-${selectedFaculty.color}-600 text-${selectedFaculty.color}-600`\r\n                        : 'border-transparent text-gray-500 hover:text-gray-700'\r\n                    }`}\r\n                  >\r\n                    Programs\r\n                  </button>\r\n                  <button\r\n                    onClick={() => setActiveTab('stats')}\r\n                    className={`px-4 py-3 font-medium text-sm border-b-2 transition-colors whitespace-nowrap ${\r\n                      activeTab === 'stats'\r\n                        ? `border-${selectedFaculty.color}-600 text-${selectedFaculty.color}-600`\r\n                        : 'border-transparent text-gray-500 hover:text-gray-700'\r\n                    }`}\r\n                  >\r\n                    Statistics\r\n                  </button>\r\n                  <button\r\n                    onClick={() => setActiveTab('contact')}\r\n                    className={`px-4 py-3 font-medium text-sm border-b-2 transition-colors whitespace-nowrap ${\r\n                      activeTab === 'contact'\r\n                        ? `border-${selectedFaculty.color}-600 text-${selectedFaculty.color}-600`\r\n                        : 'border-transparent text-gray-500 hover:text-gray-700'\r\n                    }`}\r\n                  >\r\n                    Contact\r\n                  </button>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Tab Content */}\r\n              <div className=\"bg-white rounded-xl shadow-md p-6 md:p-8\">\r\n                {/* Overview Tab */}\r\n                {activeTab === 'overview' && (\r\n                  <div className=\"space-y-6\">\r\n                    <h3 className=\"text-2xl font-bold text-gray-900\">About the Faculty</h3>\r\n                    <p className=\"text-gray-700 leading-relaxed\">{selectedFaculty.description}</p>\r\n\r\n                    <div className=\"grid md:grid-cols-3 gap-6 mt-8\">\r\n                      <div className={`p-6 rounded-lg bg-${selectedFaculty.color}-50 border border-${selectedFaculty.color}-100`}>\r\n                        <div className={`w-12 h-12 rounded-full bg-${selectedFaculty.color}-100 flex items-center justify-center mb-4`}>\r\n                          <GraduationCap className={`text-${selectedFaculty.color}-600 text-xl`} />\r\n                        </div>\r\n                        <h4 className=\"text-lg font-semibold text-gray-900 mb-2\">Academic Excellence</h4>\r\n                        <p className=\"text-gray-600 text-sm\">Committed to providing high-quality education through innovative teaching methods and comprehensive curricula.</p>\r\n                      </div>\r\n\r\n                      <div className={`p-6 rounded-lg bg-${selectedFaculty.color}-50 border border-${selectedFaculty.color}-100`}>\r\n                        <div className={`w-12 h-12 rounded-full bg-${selectedFaculty.color}-100 flex items-center justify-center mb-4`}>\r\n                          <Book className={`text-${selectedFaculty.color}-600 text-xl`} />\r\n                        </div>\r\n                        <h4 className=\"text-lg font-semibold text-gray-900 mb-2\">Research Focus</h4>\r\n                        <p className=\"text-gray-600 text-sm\">Dedicated to advancing knowledge through research and scholarly activities in various disciplines.</p>\r\n                      </div>\r\n\r\n                      <div className={`p-6 rounded-lg bg-${selectedFaculty.color}-50 border border-${selectedFaculty.color}-100`}>\r\n                        <div className={`w-12 h-12 rounded-full bg-${selectedFaculty.color}-100 flex items-center justify-center mb-4`}>\r\n                          <Users className={`text-${selectedFaculty.color}-600 text-xl`} />\r\n                        </div>\r\n                        <h4 className=\"text-lg font-semibold text-gray-900 mb-2\">Community Engagement</h4>\r\n                        <p className=\"text-gray-600 text-sm\">Actively involved in community service and outreach programs to address local and regional challenges.</p>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                )}\r\n\r\n                {/* Programs Tab */}\r\n                {activeTab === 'programs' && (\r\n                  <div className=\"space-y-8\">\r\n                    <h3 className=\"text-2xl font-bold text-gray-900\">Academic Programs</h3>\r\n\r\n                    {/* Bachelor Programs */}\r\n                    {selectedFaculty.programs.bachelor.length > 0 && (\r\n                      <div>\r\n                        <h4 className={`text-lg font-semibold ${getColorClass(selectedFaculty.color, 'text')} mb-4 flex items-center`}>\r\n                          <GraduationCap className=\"mr-2\" />\r\n                          Bachelor Programs\r\n                        </h4>\r\n                        <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-4\">\r\n                          {selectedFaculty.programs.bachelor.map((program, index) => (\r\n                            <div key={index} className=\"p-4 bg-gray-50 rounded-lg border border-gray-200 hover:border-gray-300 transition-colors\">\r\n                              <h5 className=\"font-medium text-gray-900\">{program}</h5>\r\n                              <p className=\"text-sm text-gray-500 mt-1\">4-Year Program</p>\r\n                            </div>\r\n                          ))}\r\n                        </div>\r\n                      </div>\r\n                    )}\r\n\r\n                    {/* Master Programs */}\r\n                    {selectedFaculty.programs.master.length > 0 && (\r\n                      <div>\r\n                        <h4 className={`text-lg font-semibold ${getColorClass(selectedFaculty.color, 'text')} mb-4 flex items-center`}>\r\n                          <GraduationCap className=\"mr-2\" />\r\n                          Master Programs\r\n                        </h4>\r\n                        <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-4\">\r\n                          {selectedFaculty.programs.master.map((program, index) => (\r\n                            <div key={index} className=\"p-4 bg-gray-50 rounded-lg border border-gray-200 hover:border-gray-300 transition-colors\">\r\n                              <h5 className=\"font-medium text-gray-900\">{program}</h5>\r\n                              <p className=\"text-sm text-gray-500 mt-1\">2-Year Program</p>\r\n                            </div>\r\n                          ))}\r\n                        </div>\r\n                      </div>\r\n                    )}\r\n\r\n                    {/* MPhil Programs */}\r\n                    {selectedFaculty.programs.mphil.length > 0 && (\r\n                      <div>\r\n                        <h4 className={`text-lg font-semibold ${getColorClass(selectedFaculty.color, 'text')} mb-4 flex items-center`}>\r\n                          <GraduationCap className=\"mr-2\" />\r\n                          MPhil Programs\r\n                        </h4>\r\n                        <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-4\">\r\n                          {selectedFaculty.programs.mphil.map((program, index) => (\r\n                            <div key={index} className=\"p-4 bg-gray-50 rounded-lg border border-gray-200 hover:border-gray-300 transition-colors\">\r\n                              <h5 className=\"font-medium text-gray-900\">{program}</h5>\r\n                              <p className=\"text-sm text-gray-500 mt-1\">1.5-Year Program</p>\r\n                            </div>\r\n                          ))}\r\n                        </div>\r\n                      </div>\r\n                    )}\r\n\r\n                    {/* PhD Programs */}\r\n                    {selectedFaculty.programs.phd.length > 0 && (\r\n                      <div>\r\n                        <h4 className={`text-lg font-semibold ${getColorClass(selectedFaculty.color, 'text')} mb-4 flex items-center`}>\r\n                          <GraduationCap className=\"mr-2\" />\r\n                          PhD Programs\r\n                        </h4>\r\n                        <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-4\">\r\n                          {selectedFaculty.programs.phd.map((program, index) => (\r\n                            <div key={index} className=\"p-4 bg-gray-50 rounded-lg border border-gray-200 hover:border-gray-300 transition-colors\">\r\n                              <h5 className=\"font-medium text-gray-900\">{program}</h5>\r\n                              <p className=\"text-sm text-gray-500 mt-1\">3-Year Program</p>\r\n                            </div>\r\n                          ))}\r\n                        </div>\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                )}\r\n\r\n                {/* Stats Tab */}\r\n                {activeTab === 'stats' && (\r\n                  <div className=\"space-y-6\">\r\n                    <h3 className=\"text-2xl font-bold text-gray-900\">Faculty Statistics</h3>\r\n\r\n                    <div className=\"grid md:grid-cols-3 gap-6 mt-8\">\r\n                      <div className={`p-6 rounded-lg bg-white border border-gray-200 shadow-sm text-center`}>\r\n                        <div className={`w-16 h-16 rounded-full ${getColorClass(selectedFaculty.color, 'bg')} flex items-center justify-center mx-auto mb-4`}>\r\n                          <Check className=\"text-white text-2xl\" />\r\n                        </div>\r\n                        <div className=\"text-3xl font-bold text-gray-900 mb-1\">{selectedFaculty.stats.teachers}</div>\r\n                        <p className=\"text-gray-500\">Faculty Members</p>\r\n                      </div>\r\n\r\n                      <div className={`p-6 rounded-lg bg-white border border-gray-200 shadow-sm text-center`}>\r\n                        <div className={`w-16 h-16 rounded-full ${getColorClass(selectedFaculty.color, 'bg')} flex items-center justify-center mx-auto mb-4`}>\r\n                          <GraduationCap className=\"text-white text-2xl\" />\r\n                        </div>\r\n                        <div className=\"text-3xl font-bold text-gray-900 mb-1\">{selectedFaculty.stats.students}</div>\r\n                        <p className=\"text-gray-500\">Students</p>\r\n                      </div>\r\n\r\n                      <div className={`p-6 rounded-lg bg-white border border-gray-200 shadow-sm text-center`}>\r\n                        <div className={`w-16 h-16 rounded-full ${getColorClass(selectedFaculty.color, 'bg')} flex items-center justify-center mx-auto mb-4`}>\r\n                          <Users className=\"text-white text-2xl\" />\r\n                        </div>\r\n                        <div className=\"text-3xl font-bold text-gray-900 mb-1\">{selectedFaculty.stats.staff}</div>\r\n                        <p className=\"text-gray-500\">Administrative Staff</p>\r\n                      </div>\r\n                    </div>\r\n\r\n                    <div className=\"mt-8 p-6 bg-gray-50 rounded-lg border border-gray-200\">\r\n                      <h4 className=\"text-lg font-semibold text-gray-900 mb-4\">Key Achievements</h4>\r\n                      <ul className=\"space-y-3\">\r\n                        <li className=\"flex items-start\">\r\n                          <div className={`flex-shrink-0 w-5 h-5 rounded-full ${getColorClass(selectedFaculty.color, 'bg')} flex items-center justify-center mt-0.5 mr-3`}>\r\n                            <Award className=\"text-white text-xs\" />\r\n                          </div>\r\n                          <p className=\"text-gray-700\">Recognized for excellence in academic programs and research initiatives.</p>\r\n                        </li>\r\n                        <li className=\"flex items-start\">\r\n                          <div className={`flex-shrink-0 w-5 h-5 rounded-full ${getColorClass(selectedFaculty.color, 'bg')} flex items-center justify-center mt-0.5 mr-3`}>\r\n                            <Award className=\"text-white text-xs\" />\r\n                          </div>\r\n                          <p className=\"text-gray-700\">Established partnerships with national and international institutions for collaborative research.</p>\r\n                        </li>\r\n                        <li className=\"flex items-start\">\r\n                          <div className={`flex-shrink-0 w-5 h-5 rounded-full ${getColorClass(selectedFaculty.color, 'bg')} flex items-center justify-center mt-0.5 mr-3`}>\r\n                            <Award className=\"text-white text-xs\" />\r\n                          </div>\r\n                          <p className=\"text-gray-700\">Contributed to community development through various outreach programs and initiatives.</p>\r\n                        </li>\r\n                      </ul>\r\n                    </div>\r\n                  </div>\r\n                )}\r\n\r\n                {/* Contact Tab */}\r\n                {activeTab === 'contact' && (\r\n                  <div className=\"space-y-6\">\r\n                    <h3 className=\"text-2xl font-bold text-gray-900\">Contact Information</h3>\r\n\r\n                    <div className=\"grid md:grid-cols-2 gap-8\">\r\n                      <div>\r\n                        <div className=\"space-y-4\">\r\n                          <div className=\"flex items-start\">\r\n                            <div className={`flex-shrink-0 w-10 h-10 rounded-full ${getColorClass(selectedFaculty.color, 'bg')} flex items-center justify-center mr-4`}>\r\n                              <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 text-white\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\" />\r\n                              </svg>\r\n                            </div>\r\n                            <div>\r\n                              <h4 className=\"text-lg font-medium text-gray-900\">Email</h4>\r\n                              <a href={`mailto:${selectedFaculty.email}`} className={`text-${selectedFaculty.color}-600 hover:underline`}>\r\n                                {selectedFaculty.email}\r\n                              </a>\r\n                            </div>\r\n                          </div>\r\n\r\n                          <div className=\"flex items-start\">\r\n                            <div className={`flex-shrink-0 w-10 h-10 rounded-full ${getColorClass(selectedFaculty.color, 'bg')} flex items-center justify-center mr-4`}>\r\n                              <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 text-white\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z\" />\r\n                              </svg>\r\n                            </div>\r\n                            <div>\r\n                              <h4 className=\"text-lg font-medium text-gray-900\">Phone</h4>\r\n                              <p className=\"text-gray-700\">+977-099-520729</p>\r\n                            </div>\r\n                          </div>\r\n\r\n                          <div className=\"flex items-start\">\r\n                            <div className={`flex-shrink-0 w-10 h-10 rounded-full ${getColorClass(selectedFaculty.color, 'bg')} flex items-center justify-center mr-4`}>\r\n                              <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 text-white\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\" />\r\n                                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 11a3 3 0 11-6 0 3 3 0 016 0z\" />\r\n                              </svg>\r\n                            </div>\r\n                            <div>\r\n                              <h4 className=\"text-lg font-medium text-gray-900\">Address</h4>\r\n                              <p className=\"text-gray-700\">Bheemdatta Municipality-18, Mahendranagar, Kanchanpur, Nepal</p>\r\n                            </div>\r\n                          </div>\r\n\r\n                          <div className=\"flex items-start\">\r\n                            <div className={`flex-shrink-0 w-10 h-10 rounded-full ${getColorClass(selectedFaculty.color, 'bg')} flex items-center justify-center mr-4`}>\r\n                              <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 text-white\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9\" />\r\n                              </svg>\r\n                            </div>\r\n                            <div>\r\n                              <h4 className=\"text-lg font-medium text-gray-900\">Website</h4>\r\n                              <a href={selectedFaculty.website} target=\"_blank\" rel=\"noopener noreferrer\" className={`text-${selectedFaculty.color}-600 hover:underline`}>\r\n                                {selectedFaculty.website.replace('http://', '')}\r\n                              </a>\r\n                            </div>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n\r\n                      <div>\r\n                        <div className=\"bg-gray-100 p-6 rounded-lg h-full\">\r\n                          <h4 className=\"text-lg font-medium text-gray-900 mb-4\">Office Hours</h4>\r\n                          <div className=\"space-y-2\">\r\n                            <div className=\"flex justify-between\">\r\n                              <span className=\"text-gray-600\">Sunday - Friday</span>\r\n                              <span className=\"text-gray-900 font-medium\">10:00 AM - 5:00 PM</span>\r\n                            </div>\r\n                            <div className=\"flex justify-between\">\r\n                              <span className=\"text-gray-600\">Saturday</span>\r\n                              <span className=\"text-gray-900 font-medium\">Closed</span>\r\n                            </div>\r\n                          </div>\r\n\r\n                          <div className=\"mt-6\">\r\n                            <h4 className=\"text-lg font-medium text-gray-900 mb-4\">Get in Touch</h4>\r\n                            <p className=\"text-gray-600 mb-4\">\r\n                              For more information about programs, admissions, or any other inquiries, please feel free to contact us.\r\n                            </p>\r\n                            <Link\r\n                              href=\"/contact\"\r\n                              className={`inline-flex items-center px-4 py-2 rounded-lg ${getColorClass(selectedFaculty.color, 'bg')} text-white font-medium hover:${getColorClass(selectedFaculty.color, 'hover-bg')} transition-colors`}\r\n                            >\r\n                              Contact Us\r\n                              <ArrowRight className=\"ml-2\" />\r\n                            </Link>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </section>\r\n      )}\r\n\r\n      {/* Faculty Grid Section */}\r\n      <section className=\"py-16 md:py-24\">\r\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\r\n          <div className=\"max-w-3xl mx-auto text-center mb-16\">\r\n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">Explore Our Faculties</h2>\r\n            <div className=\"w-24 h-1 bg-indigo-600 mx-auto mb-6 rounded-full\"></div>\r\n            <p className=\"text-xl text-gray-600 leading-relaxed\">\r\n              Far Western University offers a diverse range of academic faculties, each dedicated to excellence in education, research, and innovation.\r\n            </p>\r\n          </div>\r\n\r\n          <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-8\">\r\n            {faculties.map((faculty) => (\r\n              <div\r\n                key={faculty.id}\r\n                className=\"bg-white rounded-xl shadow-md overflow-hidden border border-gray-100 hover:border-indigo-100 transition-all duration-300 hover:shadow-indigo-100/20 group cursor-pointer\"\r\n                onClick={() => handleFacultyClick(faculty)}\r\n              >\r\n                <div className=\"relative h-48 overflow-hidden\">\r\n                  <Image\r\n                    src={faculty.imageUrl}\r\n                    alt={faculty.name}\r\n                    fill\r\n                    className=\"object-cover transition-transform duration-700 group-hover:scale-110\"\r\n                  />\r\n                  <div className={`absolute inset-0 bg-gradient-to-t from-${faculty.color}-900/80 to-transparent`}></div>\r\n                  <div className=\"absolute bottom-0 left-0 right-0 p-4\">\r\n                    <p className=\"text-white font-bold text-lg\">{faculty.shortName}</p>\r\n                  </div>\r\n                </div>\r\n                <div className=\"p-6\">\r\n                  <div className=\"flex items-center mb-4\">\r\n                    <div className={`w-10 h-10 rounded-full ${getColorClass(faculty.color, 'bg')} flex items-center justify-center mr-3`}>\r\n                      {faculty.icon}\r\n                    </div>\r\n                    <h3 className=\"text-xl font-bold text-gray-900\">{faculty.name}</h3>\r\n                  </div>\r\n                  <p className=\"text-gray-600 mb-4 line-clamp-3\">{faculty.description}</p>\r\n                  <div className=\"flex justify-between items-center\">\r\n                    <div className=\"flex items-center text-sm text-gray-500\">\r\n                      <GraduationCap className=\"mr-1\" />\r\n                      <span>{faculty.programs.bachelor.length + faculty.programs.master.length} Programs</span>\r\n                    </div>\r\n                    <button\r\n                      className={`inline-flex items-center text-${faculty.color}-600 font-medium group-hover:text-${faculty.color}-700 transition-colors`}\r\n                    >\r\n                      Learn More\r\n                      <ArrowRight className=\"ml-1 group-hover:translate-x-1 transition-transform duration-300\" />\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* CTA Section */}\r\n      <section className=\"py-16 bg-gradient-to-r from-indigo-900 to-blue-900 text-white relative overflow-hidden\">\r\n        {/* Background elements */}\r\n        <div className=\"absolute inset-0 overflow-hidden\">\r\n          <div className=\"absolute top-0 right-0 w-64 h-64 rounded-full bg-indigo-500 opacity-10\"></div>\r\n          <div className=\"absolute bottom-0 left-0 w-96 h-96 rounded-full bg-blue-500 opacity-10\"></div>\r\n        </div>\r\n\r\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 relative z-10\">\r\n          <div className=\"max-w-4xl mx-auto text-center\">\r\n            <h2 className=\"text-3xl md:text-4xl font-bold mb-6\">Ready to Start Your Academic Journey?</h2>\r\n            <p className=\"text-xl text-indigo-100 mb-10 leading-relaxed\">\r\n              Join Far Western University and become part of our vibrant academic community. Explore our programs and take the first step towards a successful future.\r\n            </p>\r\n            <div className=\"flex flex-wrap justify-center gap-6\">\r\n              <Link\r\n                href=\"/programs\"\r\n                className=\"bg-white text-indigo-900 hover:bg-indigo-50 font-bold py-4 px-8 rounded-lg shadow-lg transition-all duration-300 hover:scale-105 transform\"\r\n              >\r\n                Explore Programs\r\n              </Link>\r\n              <Link\r\n                href=\"/apply\"\r\n                className=\"bg-transparent border-2 border-white text-white hover:bg-white/10 font-bold py-4 px-8 rounded-lg shadow-lg transition-all duration-300 hover:scale-105 transform\"\r\n              >\r\n                Apply Now\r\n              </Link>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n    </main>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;;AA8Be,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IACvE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAEvB,oCAAoC;IACpC,MAAM,YAAuB;QAC3B;YACE,IAAI;YACJ,MAAM;YACN,WAAW;YACX,aAAa;YACb,oBAAM,8OAAC,sNAAA,CAAA,eAAY;gBAAC,WAAU;;;;;;YAC9B,UAAU;YACV,UAAU;gBACR,UAAU;oBAAC;oBAAS;iBAAa;gBACjC,QAAQ;oBAAC;oBAAiB;oBAAmB;iBAAoB;gBACjE,OAAO;oBAAC;iBAAmB;gBAC3B,KAAK;oBAAC;iBAAiB;YACzB;YACA,OAAO;gBACL,UAAU;gBACV,UAAU;gBACV,OAAO;YACT;YACA,SAAS;YACT,OAAO;YACP,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM;YACN,WAAW;YACX,aAAa;YACb,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,UAAU;YACV,UAAU;gBACR,UAAU;oBAAC;oBAAO;oBAAO;iBAAM;gBAC/B,QAAQ;oBAAC;oBAAO;iBAAM;gBACtB,OAAO;oBAAC;iBAAsB;gBAC9B,KAAK;oBAAC;iBAAoB;YAC5B;YACA,OAAO;gBACL,UAAU;gBACV,UAAU;gBACV,OAAO;YACT;YACA,SAAS;YACT,OAAO;YACP,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM;YACN,WAAW;YACX,aAAa;YACb,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,UAAU;YACV,UAAU;gBACR,UAAU;oBAAC;oBAAM;iBAAM;gBACvB,QAAQ;oBAAC;oBAAc;oBAAgB;iBAAe;gBACtD,OAAO;oBAAC;iBAAsB;gBAC9B,KAAK;oBAAC;iBAAoB;YAC5B;YACA,OAAO;gBACL,UAAU;gBACV,UAAU;gBACV,OAAO;YACT;YACA,SAAS;YACT,OAAO;YACP,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM;YACN,WAAW;YACX,aAAa;YACb,oBAAM,8OAAC,gNAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;YAC3B,UAAU;YACV,UAAU;gBACR,UAAU;oBAAC;iBAAQ;gBACnB,QAAQ;oBAAC;iBAAQ;gBACjB,OAAO;oBAAC;iBAAqB;gBAC7B,KAAK;oBAAC;iBAAmB;YAC3B;YACA,OAAO;gBACL,UAAU;gBACV,UAAU;gBACV,OAAO;YACT;YACA,SAAS;YACT,OAAO;YACP,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM;YACN,WAAW;YACX,aAAa;YACb,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,UAAU;YACV,UAAU;gBACR,UAAU;oBAAC;iBAAM;gBACjB,QAAQ;oBAAC;iBAAM;gBACf,OAAO,EAAE;gBACT,KAAK,EAAE;YACT;YACA,OAAO;gBACL,UAAU;gBACV,UAAU;gBACV,OAAO;YACT;YACA,SAAS;YACT,OAAO;YACP,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM;YACN,WAAW;YACX,aAAa;YACb,oBAAM,8OAAC,kMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;YACtB,UAAU;YACV,UAAU;gBACR,UAAU;oBAAC;oBAAc;iBAAgB;gBACzC,QAAQ;oBAAC;iBAAO;gBAChB,OAAO,EAAE;gBACT,KAAK,EAAE;YACT;YACA,OAAO;gBACL,UAAU;gBACV,UAAU;gBACV,OAAO;YACT;YACA,SAAS;YACT,OAAO;YACP,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM;YACN,WAAW;YACX,aAAa;YACb,oBAAM,8OAAC,kMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;YACtB,UAAU;YACV,UAAU;gBACR,UAAU;oBAAC;iBAAoB;gBAC/B,QAAQ;oBAAC;iBAAoB;gBAC7B,OAAO,EAAE;gBACT,KAAK,EAAE;YACT;YACA,OAAO;gBACL,UAAU;gBACV,UAAU;gBACV,OAAO;YACT;YACA,SAAS;YACT,OAAO;YACP,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM;YACN,WAAW;YACX,aAAa;YACb,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,UAAU;YACV,UAAU;gBACR,UAAU;oBAAC;oBAAiB;oBAAQ;iBAAM;gBAC1C,QAAQ;oBAAC;iBAAM;gBACf,OAAO,EAAE;gBACT,KAAK,EAAE;YACT;YACA,OAAO;gBACL,UAAU;gBACV,UAAU;gBACV,OAAO;YACT;YACA,SAAS;YACT,OAAO;YACP,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM;YACN,WAAW;YACX,aAAa;YACb,oBAAM,8OAAC,8MAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;YAC1B,UAAU;YACV,UAAU;gBACR,UAAU;oBAAC;oBAAkB;iBAA8B;gBAC3D,QAAQ;oBAAC;iBAAoC;gBAC7C,OAAO,EAAE;gBACT,KAAK,EAAE;YACT;YACA,OAAO;gBACL,UAAU;gBACV,UAAU;gBACV,OAAO;YACT;YACA,SAAS;YACT,OAAO;YACP,OAAO;QACT;KACD;IAED,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,IAAI,qBACnB,CAAC,CAAC,MAAM;YACN,IAAI,MAAM,cAAc,EAAE;gBACxB,aAAa;gBACb,SAAS,UAAU;YACrB;QACF,GACA;YAAE,WAAW;QAAI;QAGnB,0DAA0D;QAC1D,MAAM,aAAa,QAAQ,OAAO;QAElC,IAAI,YAAY;YACd,SAAS,OAAO,CAAC;QACnB;QAEA,OAAO;YACL,sCAAsC;YACtC,IAAI,YAAY;gBACd,SAAS,SAAS,CAAC;YACrB;QACF;IACF,GAAG,EAAE;IAEL,MAAM,qBAAqB,CAAC;QAC1B,mBAAmB;QACnB,aAAa;QACb,OAAO,QAAQ,CAAC;YACd,KAAK;YACL,UAAU;QACZ;IACF;IAEA,MAAM,gBAAgB,CAAC,OAAe;QACpC,MAAM,WAAmD;YACvD,QAAQ;gBACN,MAAM;gBACN,QAAQ;gBACR,UAAU;gBACV,YAAY;gBACZ,gBAAgB;gBAChB,cAAc;YAChB;YACA,MAAM;gBACJ,MAAM;gBACN,QAAQ;gBACR,UAAU;gBACV,YAAY;gBACZ,gBAAgB;gBAChB,cAAc;YAChB;YACA,QAAQ;gBACN,MAAM;gBACN,QAAQ;gBACR,UAAU;gBACV,YAAY;gBACZ,gBAAgB;gBAChB,cAAc;YAChB;YACA,OAAO;gBACL,MAAM;gBACN,QAAQ;gBACR,UAAU;gBACV,YAAY;gBACZ,gBAAgB;gBAChB,cAAc;YAChB;YACA,KAAK;gBACH,MAAM;gBACN,QAAQ;gBACR,UAAU;gBACV,YAAY;gBACZ,gBAAgB;gBAChB,cAAc;YAChB;YACA,QAAQ;gBACN,MAAM;gBACN,QAAQ;gBACR,UAAU;gBACV,YAAY;gBACZ,gBAAgB;gBAChB,cAAc;YAChB;YACA,MAAM;gBACJ,MAAM;gBACN,QAAQ;gBACR,UAAU;gBACV,YAAY;gBACZ,gBAAgB;gBAChB,cAAc;YAChB;YACA,MAAM;gBACJ,MAAM;gBACN,QAAQ;gBACR,UAAU;gBACV,YAAY;gBACZ,gBAAgB;gBAChB,cAAc;YAChB;YACA,SAAS;gBACP,MAAM;gBACN,QAAQ;gBACR,UAAU;gBACV,YAAY;gBACZ,gBAAgB;gBAChB,cAAc;YAChB;QACF;QAEA,OAAO,QAAQ,CAAC,MAAM,CAAC,KAAK,IAAI,QAAQ,CAAC,SAAS,CAAC,KAAK;IAC1D;IAEA,qBACE,8OAAC;QAAK,WAAU;;0BAEd,8OAAC;gBACC,KAAK;gBACL,WAAU;;kCAGV,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCACC,WAAU;gCACV,OAAO;oCACL,iBAAiB;oCACjB,gBAAgB;gCAClB;;;;;;;;;;;;kCAIJ,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAW,CAAC,qEAAqE,EAAE,YAAY,8BAA8B,4BAA4B;;8CAC5J,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDAA8B;;;;;;;;;;;;;;;;8CAIlD,8OAAC;oCAAG,WAAU;;wCAAgE;sDACxE,8OAAC;4CAAK,WAAU;sDAA6E;;;;;;;;;;;;8CAGnG,8OAAC;oCAAE,WAAU;8CAAkE;;;;;;;;;;;;;;;;;kCAOnF,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,OAAM;4BAA6B,SAAQ;4BAAe,WAAU;sCACvE,cAAA,8OAAC;gCACC,MAAK;gCACL,aAAY;gCACZ,GAAE;;;;;;;;;;;;;;;;;;;;;;YAOT,iCACC,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,6HAAA,CAAA,UAAK;gDACJ,KAAK,gBAAgB,QAAQ;gDAC7B,KAAK,gBAAgB,IAAI;gDACzB,IAAI;gDACJ,WAAU;;;;;;0DAEZ,8OAAC;gDAAI,WAAW,CAAC,uCAAuC,EAAE,gBAAgB,KAAK,CAAC,WAAW,EAAE,gBAAgB,KAAK,CAAC,OAAO,CAAC;;;;;;;;;;;;kDAG7H,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAkD,gBAAgB,IAAI;;;;;;sEACpF,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,0MAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;8EAClB,8OAAC;8EAAK;;;;;;;;;;;;;;;;;;8DAGV,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDACC,MAAM,gBAAgB,OAAO;wDAC7B,QAAO;wDACP,KAAI;wDACJ,WAAW,CAAC,4DAA4D,EAAE,gBAAgB,KAAK,CAAC,0BAA0B,EAAE,gBAAgB,KAAK,CAAC,qBAAqB,CAAC;;4DACzK;0EAEC,8OAAC,kNAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQhC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,SAAS,IAAM,aAAa;4CAC5B,WAAW,CAAC,6EAA6E,EACvF,cAAc,aACV,CAAC,OAAO,EAAE,gBAAgB,KAAK,CAAC,UAAU,EAAE,gBAAgB,KAAK,CAAC,IAAI,CAAC,GACvE,wDACJ;sDACH;;;;;;sDAGD,8OAAC;4CACC,SAAS,IAAM,aAAa;4CAC5B,WAAW,CAAC,6EAA6E,EACvF,cAAc,aACV,CAAC,OAAO,EAAE,gBAAgB,KAAK,CAAC,UAAU,EAAE,gBAAgB,KAAK,CAAC,IAAI,CAAC,GACvE,wDACJ;sDACH;;;;;;sDAGD,8OAAC;4CACC,SAAS,IAAM,aAAa;4CAC5B,WAAW,CAAC,6EAA6E,EACvF,cAAc,UACV,CAAC,OAAO,EAAE,gBAAgB,KAAK,CAAC,UAAU,EAAE,gBAAgB,KAAK,CAAC,IAAI,CAAC,GACvE,wDACJ;sDACH;;;;;;sDAGD,8OAAC;4CACC,SAAS,IAAM,aAAa;4CAC5B,WAAW,CAAC,6EAA6E,EACvF,cAAc,YACV,CAAC,OAAO,EAAE,gBAAgB,KAAK,CAAC,UAAU,EAAE,gBAAgB,KAAK,CAAC,IAAI,CAAC,GACvE,wDACJ;sDACH;;;;;;;;;;;;;;;;;0CAOL,8OAAC;gCAAI,WAAU;;oCAEZ,cAAc,4BACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAmC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;0DAAiC,gBAAgB,WAAW;;;;;;0DAEzE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAW,CAAC,kBAAkB,EAAE,gBAAgB,KAAK,CAAC,kBAAkB,EAAE,gBAAgB,KAAK,CAAC,IAAI,CAAC;;0EACxG,8OAAC;gEAAI,WAAW,CAAC,0BAA0B,EAAE,gBAAgB,KAAK,CAAC,0CAA0C,CAAC;0EAC5G,cAAA,8OAAC,wNAAA,CAAA,gBAAa;oEAAC,WAAW,CAAC,KAAK,EAAE,gBAAgB,KAAK,CAAC,YAAY,CAAC;;;;;;;;;;;0EAEvE,8OAAC;gEAAG,WAAU;0EAA2C;;;;;;0EACzD,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;kEAGvC,8OAAC;wDAAI,WAAW,CAAC,kBAAkB,EAAE,gBAAgB,KAAK,CAAC,kBAAkB,EAAE,gBAAgB,KAAK,CAAC,IAAI,CAAC;;0EACxG,8OAAC;gEAAI,WAAW,CAAC,0BAA0B,EAAE,gBAAgB,KAAK,CAAC,0CAA0C,CAAC;0EAC5G,cAAA,8OAAC,kMAAA,CAAA,OAAI;oEAAC,WAAW,CAAC,KAAK,EAAE,gBAAgB,KAAK,CAAC,YAAY,CAAC;;;;;;;;;;;0EAE9D,8OAAC;gEAAG,WAAU;0EAA2C;;;;;;0EACzD,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;kEAGvC,8OAAC;wDAAI,WAAW,CAAC,kBAAkB,EAAE,gBAAgB,KAAK,CAAC,kBAAkB,EAAE,gBAAgB,KAAK,CAAC,IAAI,CAAC;;0EACxG,8OAAC;gEAAI,WAAW,CAAC,0BAA0B,EAAE,gBAAgB,KAAK,CAAC,0CAA0C,CAAC;0EAC5G,cAAA,8OAAC,oMAAA,CAAA,QAAK;oEAAC,WAAW,CAAC,KAAK,EAAE,gBAAgB,KAAK,CAAC,YAAY,CAAC;;;;;;;;;;;0EAE/D,8OAAC;gEAAG,WAAU;0EAA2C;;;;;;0EACzD,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;;;;;;;;;;;;;oCAO5C,cAAc,4BACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAmC;;;;;;4CAGhD,gBAAgB,QAAQ,CAAC,QAAQ,CAAC,MAAM,GAAG,mBAC1C,8OAAC;;kEACC,8OAAC;wDAAG,WAAW,CAAC,sBAAsB,EAAE,cAAc,gBAAgB,KAAK,EAAE,QAAQ,uBAAuB,CAAC;;0EAC3G,8OAAC,wNAAA,CAAA,gBAAa;gEAAC,WAAU;;;;;;4DAAS;;;;;;;kEAGpC,8OAAC;wDAAI,WAAU;kEACZ,gBAAgB,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBAC/C,8OAAC;gEAAgB,WAAU;;kFACzB,8OAAC;wEAAG,WAAU;kFAA6B;;;;;;kFAC3C,8OAAC;wEAAE,WAAU;kFAA6B;;;;;;;+DAFlC;;;;;;;;;;;;;;;;4CAUjB,gBAAgB,QAAQ,CAAC,MAAM,CAAC,MAAM,GAAG,mBACxC,8OAAC;;kEACC,8OAAC;wDAAG,WAAW,CAAC,sBAAsB,EAAE,cAAc,gBAAgB,KAAK,EAAE,QAAQ,uBAAuB,CAAC;;0EAC3G,8OAAC,wNAAA,CAAA,gBAAa;gEAAC,WAAU;;;;;;4DAAS;;;;;;;kEAGpC,8OAAC;wDAAI,WAAU;kEACZ,gBAAgB,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,SAAS,sBAC7C,8OAAC;gEAAgB,WAAU;;kFACzB,8OAAC;wEAAG,WAAU;kFAA6B;;;;;;kFAC3C,8OAAC;wEAAE,WAAU;kFAA6B;;;;;;;+DAFlC;;;;;;;;;;;;;;;;4CAUjB,gBAAgB,QAAQ,CAAC,KAAK,CAAC,MAAM,GAAG,mBACvC,8OAAC;;kEACC,8OAAC;wDAAG,WAAW,CAAC,sBAAsB,EAAE,cAAc,gBAAgB,KAAK,EAAE,QAAQ,uBAAuB,CAAC;;0EAC3G,8OAAC,wNAAA,CAAA,gBAAa;gEAAC,WAAU;;;;;;4DAAS;;;;;;;kEAGpC,8OAAC;wDAAI,WAAU;kEACZ,gBAAgB,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,SAAS,sBAC5C,8OAAC;gEAAgB,WAAU;;kFACzB,8OAAC;wEAAG,WAAU;kFAA6B;;;;;;kFAC3C,8OAAC;wEAAE,WAAU;kFAA6B;;;;;;;+DAFlC;;;;;;;;;;;;;;;;4CAUjB,gBAAgB,QAAQ,CAAC,GAAG,CAAC,MAAM,GAAG,mBACrC,8OAAC;;kEACC,8OAAC;wDAAG,WAAW,CAAC,sBAAsB,EAAE,cAAc,gBAAgB,KAAK,EAAE,QAAQ,uBAAuB,CAAC;;0EAC3G,8OAAC,wNAAA,CAAA,gBAAa;gEAAC,WAAU;;;;;;4DAAS;;;;;;;kEAGpC,8OAAC;wDAAI,WAAU;kEACZ,gBAAgB,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,SAAS,sBAC1C,8OAAC;gEAAgB,WAAU;;kFACzB,8OAAC;wEAAG,WAAU;kFAA6B;;;;;;kFAC3C,8OAAC;wEAAE,WAAU;kFAA6B;;;;;;;+DAFlC;;;;;;;;;;;;;;;;;;;;;;oCAYrB,cAAc,yBACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAmC;;;;;;0DAEjD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAW,CAAC,oEAAoE,CAAC;;0EACpF,8OAAC;gEAAI,WAAW,CAAC,uBAAuB,EAAE,cAAc,gBAAgB,KAAK,EAAE,MAAM,8CAA8C,CAAC;0EAClI,cAAA,8OAAC,oMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;;;;;;0EAEnB,8OAAC;gEAAI,WAAU;0EAAyC,gBAAgB,KAAK,CAAC,QAAQ;;;;;;0EACtF,8OAAC;gEAAE,WAAU;0EAAgB;;;;;;;;;;;;kEAG/B,8OAAC;wDAAI,WAAW,CAAC,oEAAoE,CAAC;;0EACpF,8OAAC;gEAAI,WAAW,CAAC,uBAAuB,EAAE,cAAc,gBAAgB,KAAK,EAAE,MAAM,8CAA8C,CAAC;0EAClI,cAAA,8OAAC,wNAAA,CAAA,gBAAa;oEAAC,WAAU;;;;;;;;;;;0EAE3B,8OAAC;gEAAI,WAAU;0EAAyC,gBAAgB,KAAK,CAAC,QAAQ;;;;;;0EACtF,8OAAC;gEAAE,WAAU;0EAAgB;;;;;;;;;;;;kEAG/B,8OAAC;wDAAI,WAAW,CAAC,oEAAoE,CAAC;;0EACpF,8OAAC;gEAAI,WAAW,CAAC,uBAAuB,EAAE,cAAc,gBAAgB,KAAK,EAAE,MAAM,8CAA8C,CAAC;0EAClI,cAAA,8OAAC,oMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;;;;;;0EAEnB,8OAAC;gEAAI,WAAU;0EAAyC,gBAAgB,KAAK,CAAC,KAAK;;;;;;0EACnF,8OAAC;gEAAE,WAAU;0EAAgB;;;;;;;;;;;;;;;;;;0DAIjC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAA2C;;;;;;kEACzD,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;gEAAG,WAAU;;kFACZ,8OAAC;wEAAI,WAAW,CAAC,mCAAmC,EAAE,cAAc,gBAAgB,KAAK,EAAE,MAAM,6CAA6C,CAAC;kFAC7I,cAAA,8OAAC,oMAAA,CAAA,QAAK;4EAAC,WAAU;;;;;;;;;;;kFAEnB,8OAAC;wEAAE,WAAU;kFAAgB;;;;;;;;;;;;0EAE/B,8OAAC;gEAAG,WAAU;;kFACZ,8OAAC;wEAAI,WAAW,CAAC,mCAAmC,EAAE,cAAc,gBAAgB,KAAK,EAAE,MAAM,6CAA6C,CAAC;kFAC7I,cAAA,8OAAC,oMAAA,CAAA,QAAK;4EAAC,WAAU;;;;;;;;;;;kFAEnB,8OAAC;wEAAE,WAAU;kFAAgB;;;;;;;;;;;;0EAE/B,8OAAC;gEAAG,WAAU;;kFACZ,8OAAC;wEAAI,WAAW,CAAC,mCAAmC,EAAE,cAAc,gBAAgB,KAAK,EAAE,MAAM,6CAA6C,CAAC;kFAC7I,cAAA,8OAAC,oMAAA,CAAA,QAAK;4EAAC,WAAU;;;;;;;;;;;kFAEnB,8OAAC;wEAAE,WAAU;kFAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oCAQtC,cAAc,2BACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAmC;;;;;;0DAEjD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;kEACC,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAW,CAAC,qCAAqC,EAAE,cAAc,gBAAgB,KAAK,EAAE,MAAM,sCAAsC,CAAC;sFACxI,cAAA,8OAAC;gFAAI,OAAM;gFAA6B,WAAU;gFAAqB,MAAK;gFAAO,SAAQ;gFAAY,QAAO;0FAC5G,cAAA,8OAAC;oFAAK,eAAc;oFAAQ,gBAAe;oFAAQ,aAAa;oFAAG,GAAE;;;;;;;;;;;;;;;;sFAGzE,8OAAC;;8FACC,8OAAC;oFAAG,WAAU;8FAAoC;;;;;;8FAClD,8OAAC;oFAAE,MAAM,CAAC,OAAO,EAAE,gBAAgB,KAAK,EAAE;oFAAE,WAAW,CAAC,KAAK,EAAE,gBAAgB,KAAK,CAAC,oBAAoB,CAAC;8FACvG,gBAAgB,KAAK;;;;;;;;;;;;;;;;;;8EAK5B,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAW,CAAC,qCAAqC,EAAE,cAAc,gBAAgB,KAAK,EAAE,MAAM,sCAAsC,CAAC;sFACxI,cAAA,8OAAC;gFAAI,OAAM;gFAA6B,WAAU;gFAAqB,MAAK;gFAAO,SAAQ;gFAAY,QAAO;0FAC5G,cAAA,8OAAC;oFAAK,eAAc;oFAAQ,gBAAe;oFAAQ,aAAa;oFAAG,GAAE;;;;;;;;;;;;;;;;sFAGzE,8OAAC;;8FACC,8OAAC;oFAAG,WAAU;8FAAoC;;;;;;8FAClD,8OAAC;oFAAE,WAAU;8FAAgB;;;;;;;;;;;;;;;;;;8EAIjC,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAW,CAAC,qCAAqC,EAAE,cAAc,gBAAgB,KAAK,EAAE,MAAM,sCAAsC,CAAC;sFACxI,cAAA,8OAAC;gFAAI,OAAM;gFAA6B,WAAU;gFAAqB,MAAK;gFAAO,SAAQ;gFAAY,QAAO;;kGAC5G,8OAAC;wFAAK,eAAc;wFAAQ,gBAAe;wFAAQ,aAAa;wFAAG,GAAE;;;;;;kGACrE,8OAAC;wFAAK,eAAc;wFAAQ,gBAAe;wFAAQ,aAAa;wFAAG,GAAE;;;;;;;;;;;;;;;;;sFAGzE,8OAAC;;8FACC,8OAAC;oFAAG,WAAU;8FAAoC;;;;;;8FAClD,8OAAC;oFAAE,WAAU;8FAAgB;;;;;;;;;;;;;;;;;;8EAIjC,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAW,CAAC,qCAAqC,EAAE,cAAc,gBAAgB,KAAK,EAAE,MAAM,sCAAsC,CAAC;sFACxI,cAAA,8OAAC;gFAAI,OAAM;gFAA6B,WAAU;gFAAqB,MAAK;gFAAO,SAAQ;gFAAY,QAAO;0FAC5G,cAAA,8OAAC;oFAAK,eAAc;oFAAQ,gBAAe;oFAAQ,aAAa;oFAAG,GAAE;;;;;;;;;;;;;;;;sFAGzE,8OAAC;;8FACC,8OAAC;oFAAG,WAAU;8FAAoC;;;;;;8FAClD,8OAAC;oFAAE,MAAM,gBAAgB,OAAO;oFAAE,QAAO;oFAAS,KAAI;oFAAsB,WAAW,CAAC,KAAK,EAAE,gBAAgB,KAAK,CAAC,oBAAoB,CAAC;8FACvI,gBAAgB,OAAO,CAAC,OAAO,CAAC,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kEAOtD,8OAAC;kEACC,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAG,WAAU;8EAAyC;;;;;;8EACvD,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;oFAAK,WAAU;8FAAgB;;;;;;8FAChC,8OAAC;oFAAK,WAAU;8FAA4B;;;;;;;;;;;;sFAE9C,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;oFAAK,WAAU;8FAAgB;;;;;;8FAChC,8OAAC;oFAAK,WAAU;8FAA4B;;;;;;;;;;;;;;;;;;8EAIhD,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAG,WAAU;sFAAyC;;;;;;sFACvD,8OAAC;4EAAE,WAAU;sFAAqB;;;;;;sFAGlC,8OAAC,4JAAA,CAAA,UAAI;4EACH,MAAK;4EACL,WAAW,CAAC,8CAA8C,EAAE,cAAc,gBAAgB,KAAK,EAAE,MAAM,8BAA8B,EAAE,cAAc,gBAAgB,KAAK,EAAE,YAAY,kBAAkB,CAAC;;gFAC5M;8FAEC,8OAAC,kNAAA,CAAA,aAAU;oFAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAe9C,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAoD;;;;;;8CAClE,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAE,WAAU;8CAAwC;;;;;;;;;;;;sCAKvD,8OAAC;4BAAI,WAAU;sCACZ,UAAU,GAAG,CAAC,CAAC,wBACd,8OAAC;oCAEC,WAAU;oCACV,SAAS,IAAM,mBAAmB;;sDAElC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,6HAAA,CAAA,UAAK;oDACJ,KAAK,QAAQ,QAAQ;oDACrB,KAAK,QAAQ,IAAI;oDACjB,IAAI;oDACJ,WAAU;;;;;;8DAEZ,8OAAC;oDAAI,WAAW,CAAC,uCAAuC,EAAE,QAAQ,KAAK,CAAC,sBAAsB,CAAC;;;;;;8DAC/F,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAE,WAAU;kEAAgC,QAAQ,SAAS;;;;;;;;;;;;;;;;;sDAGlE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAW,CAAC,uBAAuB,EAAE,cAAc,QAAQ,KAAK,EAAE,MAAM,sCAAsC,CAAC;sEACjH,QAAQ,IAAI;;;;;;sEAEf,8OAAC;4DAAG,WAAU;sEAAmC,QAAQ,IAAI;;;;;;;;;;;;8DAE/D,8OAAC;oDAAE,WAAU;8DAAmC,QAAQ,WAAW;;;;;;8DACnE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,wNAAA,CAAA,gBAAa;oEAAC,WAAU;;;;;;8EACzB,8OAAC;;wEAAM,QAAQ,QAAQ,CAAC,QAAQ,CAAC,MAAM,GAAG,QAAQ,QAAQ,CAAC,MAAM,CAAC,MAAM;wEAAC;;;;;;;;;;;;;sEAE3E,8OAAC;4DACC,WAAW,CAAC,8BAA8B,EAAE,QAAQ,KAAK,CAAC,kCAAkC,EAAE,QAAQ,KAAK,CAAC,sBAAsB,CAAC;;gEACpI;8EAEC,8OAAC,kNAAA,CAAA,aAAU;oEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;mCAjCvB,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;0BA4CzB,8OAAC;gBAAQ,WAAU;;kCAEjB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;;;;;;;kCAGjB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAsC;;;;;;8CACpD,8OAAC;oCAAE,WAAU;8CAAgD;;;;;;8CAG7D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;sDAGD,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf", "debugId": null}}]}