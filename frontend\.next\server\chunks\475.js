exports.id=475,exports.ids=[475],exports.modules={1601:(e,t,a)=>{"use strict";a.d(t,{default:()=>s});let s=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\fwu_incubation(react+laravel)\\\\incubation\\\\frontend\\\\src\\\\app\\\\dashboard\\\\Provider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\fwu_incubation(react+laravel)\\incubation\\frontend\\src\\app\\dashboard\\Provider.tsx","default")},4780:(e,t,a)=>{"use strict";a.d(t,{cn:()=>r});var s=a(49384),i=a(82348);function r(...e){return(0,i.QP)((0,s.$)(e))}},5544:(e,t,a)=>{Promise.resolve().then(a.bind(a,1601))},23521:(e,t,a)=>{"use strict";a.d(t,{default:()=>el});var s=a(60687),i=a(43210),r=a(8730),n=a(24224),d=a(51214),o=a(4780);let l=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function c({className:e,variant:t,size:a,asChild:i=!1,...n}){let d=i?r.DX:"button";return(0,s.jsx)(d,{"data-slot":"button",className:(0,o.cn)(l({variant:t,size:a,className:e})),...n})}var u=a(26134),f=a(11860);function h({...e}){return(0,s.jsx)(u.bL,{"data-slot":"sheet",...e})}function p({...e}){return(0,s.jsx)(u.ZL,{"data-slot":"sheet-portal",...e})}function b({className:e,...t}){return(0,s.jsx)(u.hJ,{"data-slot":"sheet-overlay",className:(0,o.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...t})}function x({className:e,children:t,side:a="right",...i}){return(0,s.jsxs)(p,{children:[(0,s.jsx)(b,{}),(0,s.jsxs)(u.UC,{"data-slot":"sheet-content",className:(0,o.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500","right"===a&&"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm","left"===a&&"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm","top"===a&&"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b","bottom"===a&&"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t",e),...i,children:[t,(0,s.jsxs)(u.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none",children:[(0,s.jsx)(f.A,{className:"size-4"}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function m({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"sheet-header",className:(0,o.cn)("flex flex-col gap-1.5 p-4",e),...t})}function g({className:e,...t}){return(0,s.jsx)(u.hE,{"data-slot":"sheet-title",className:(0,o.cn)("text-foreground font-semibold",e),...t})}function v({className:e,...t}){return(0,s.jsx)(u.VY,{"data-slot":"sheet-description",className:(0,o.cn)("text-muted-foreground text-sm",e),...t})}var w=a(20158);function j({delayDuration:e=0,...t}){return(0,s.jsx)(w.Kq,{"data-slot":"tooltip-provider",delayDuration:e,...t})}function y({...e}){return(0,s.jsx)(j,{children:(0,s.jsx)(w.bL,{"data-slot":"tooltip",...e})})}function N({...e}){return(0,s.jsx)(w.l9,{"data-slot":"tooltip-trigger",...e})}function k({className:e,sideOffset:t=0,children:a,...i}){return(0,s.jsx)(w.ZL,{children:(0,s.jsxs)(w.UC,{"data-slot":"tooltip-content",sideOffset:t,className:(0,o.cn)("bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance",e),...i,children:[a,(0,s.jsx)(w.i3,{className:"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]"})]})})}let z=i.createContext(null);function C(){let e=i.useContext(z);if(!e)throw Error("useSidebar must be used within a SidebarProvider.");return e}function _({defaultOpen:e=!0,open:t,onOpenChange:a,className:r,style:n,children:d,...l}){let c=function(){let[e,t]=i.useState(void 0);return i.useEffect(()=>{let e=window.matchMedia("(max-width: 767px)"),a=()=>{t(window.innerWidth<768)};return e.addEventListener("change",a),t(window.innerWidth<768),()=>e.removeEventListener("change",a)},[]),!!e}(),[u,f]=i.useState(!1),[h,p]=i.useState(e),b=t??h,x=i.useCallback(e=>{let t="function"==typeof e?e(b):e;a?a(t):p(t),document.cookie=`sidebar_state=${t}; path=/; max-age=604800`},[a,b]),m=i.useCallback(()=>c?f(e=>!e):x(e=>!e),[c,x,f]);i.useEffect(()=>{let e=e=>{"b"===e.key&&(e.metaKey||e.ctrlKey)&&(e.preventDefault(),m())};return window.addEventListener("keydown",e),()=>window.removeEventListener("keydown",e)},[m]);let g=b?"expanded":"collapsed",v=i.useMemo(()=>({state:g,open:b,setOpen:x,isMobile:c,openMobile:u,setOpenMobile:f,toggleSidebar:m}),[g,b,x,c,u,f,m]);return(0,s.jsx)(z.Provider,{value:v,children:(0,s.jsx)(j,{delayDuration:0,children:(0,s.jsx)("div",{"data-slot":"sidebar-wrapper",style:{"--sidebar-width":"16rem","--sidebar-width-icon":"3rem",...n},className:(0,o.cn)("group/sidebar-wrapper has-data-[variant=inset]:bg-sidebar flex min-h-svh w-full",r),...l,children:d})})})}function A({side:e="left",variant:t="sidebar",collapsible:a="offcanvas",className:i,children:r,...n}){let{isMobile:d,state:l,openMobile:c,setOpenMobile:u}=C();return"none"===a?(0,s.jsx)("div",{"data-slot":"sidebar",className:(0,o.cn)("bg-sidebar text-sidebar-foreground flex h-full w-(--sidebar-width) flex-col",i),...n,children:r}):d?(0,s.jsx)(h,{open:c,onOpenChange:u,...n,children:(0,s.jsxs)(x,{"data-sidebar":"sidebar","data-slot":"sidebar","data-mobile":"true",className:"bg-sidebar text-sidebar-foreground w-(--sidebar-width) p-0 [&>button]:hidden",style:{"--sidebar-width":"18rem"},side:e,children:[(0,s.jsxs)(m,{className:"sr-only",children:[(0,s.jsx)(g,{children:"Sidebar"}),(0,s.jsx)(v,{children:"Displays the mobile sidebar."})]}),(0,s.jsx)("div",{className:"flex h-full w-full flex-col",children:r})]})}):(0,s.jsxs)("div",{className:"group peer text-sidebar-foreground hidden md:block","data-state":l,"data-collapsible":"collapsed"===l?a:"","data-variant":t,"data-side":e,"data-slot":"sidebar",children:[(0,s.jsx)("div",{"data-slot":"sidebar-gap",className:(0,o.cn)("relative w-(--sidebar-width) bg-transparent transition-[width] duration-200 ease-linear","group-data-[collapsible=offcanvas]:w-0","group-data-[side=right]:rotate-180","floating"===t||"inset"===t?"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4)))]":"group-data-[collapsible=icon]:w-(--sidebar-width-icon)")}),(0,s.jsx)("div",{"data-slot":"sidebar-container",className:(0,o.cn)("fixed inset-y-0 z-10 hidden h-svh w-(--sidebar-width) transition-[left,right,width] duration-200 ease-linear md:flex","left"===e?"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]":"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]","floating"===t||"inset"===t?"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4))+2px)]":"group-data-[collapsible=icon]:w-(--sidebar-width-icon) group-data-[side=left]:border-r group-data-[side=right]:border-l",i),...n,children:(0,s.jsx)("div",{"data-sidebar":"sidebar","data-slot":"sidebar-inner",className:"bg-sidebar group-data-[variant=floating]:border-sidebar-border flex h-full w-full flex-col group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:shadow-sm",children:r})})]})}function D({className:e,onClick:t,...a}){let{toggleSidebar:i}=C();return(0,s.jsxs)(c,{"data-sidebar":"trigger","data-slot":"sidebar-trigger",variant:"ghost",size:"icon",className:(0,o.cn)("size-7",e),onClick:e=>{t?.(e),i()},...a,children:[(0,s.jsx)(d.A,{}),(0,s.jsx)("span",{className:"sr-only",children:"Toggle Sidebar"})]})}function E({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"sidebar-content","data-sidebar":"content",className:(0,o.cn)("flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden",e),...t})}function L({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"sidebar-group","data-sidebar":"group",className:(0,o.cn)("relative flex w-full min-w-0 flex-col p-2",e),...t})}function P({className:e,asChild:t=!1,...a}){let i=t?r.DX:"div";return(0,s.jsx)(i,{"data-slot":"sidebar-group-label","data-sidebar":"group-label",className:(0,o.cn)("text-sidebar-foreground/70 ring-sidebar-ring flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium outline-hidden transition-[margin,opacity] duration-200 ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0","group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0",e),...a})}function S({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"sidebar-group-content","data-sidebar":"group-content",className:(0,o.cn)("w-full text-sm",e),...t})}function U({className:e,...t}){return(0,s.jsx)("ul",{"data-slot":"sidebar-menu","data-sidebar":"menu",className:(0,o.cn)("flex w-full min-w-0 flex-col gap-1",e),...t})}function M({className:e,...t}){return(0,s.jsx)("li",{"data-slot":"sidebar-menu-item","data-sidebar":"menu-item",className:(0,o.cn)("group/menu-item relative",e),...t})}let O=(0,n.F)("peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-hidden ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-data-[sidebar=menu-action]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:size-8! group-data-[collapsible=icon]:p-2! [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0",{variants:{variant:{default:"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",outline:"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]"},size:{default:"h-8 text-sm",sm:"h-7 text-xs",lg:"h-12 text-sm group-data-[collapsible=icon]:p-0!"}},defaultVariants:{variant:"default",size:"default"}});function q({asChild:e=!1,isActive:t=!1,variant:a="default",size:i="default",tooltip:n,className:d,...l}){let c=e?r.DX:"button",{isMobile:u,state:f}=C(),h=(0,s.jsx)(c,{"data-slot":"sidebar-menu-button","data-sidebar":"menu-button","data-size":i,"data-active":t,className:(0,o.cn)(O({variant:a,size:i}),d),...l});return n?("string"==typeof n&&(n={children:n}),(0,s.jsxs)(y,{children:[(0,s.jsx)(N,{asChild:!0,children:h}),(0,s.jsx)(k,{side:"right",align:"center",hidden:"collapsed"!==f||u,...n})]})):h}var F=a(32192),I=a(45547),K=a(93613),V=a(10022),W=a(41312),X=a(93508),Z=a(9005),$=a(83002),G=a(30474),J=a(16189),T=a(85814),B=a.n(T);let H=[{title:"Dashboard",url:"/dashboard",icon:F.A},{title:"News",url:"/dashboard/news",icon:I.A},{title:"Notices",url:"/dashboard/notice",icon:K.A},{title:"Applications",url:"/dashboard/application",icon:V.A},{title:"Applicants",url:"/dashboard/applicants",icon:W.A},{title:"Committee",url:"/dashboard/committee",icon:X.A},{title:"Photo Gallery",url:"/dashboard/photo-gallery",icon:Z.A},{title:"Gallery Images",url:"/dashboard/gallery-images",icon:$.A}];function Q(){let e=(0,J.usePathname)();return console.log(e),(0,s.jsx)(A,{children:(0,s.jsx)(E,{children:(0,s.jsxs)(L,{children:[(0,s.jsxs)(P,{className:"flex flex-col items-center justify-center mt-6 ",children:[(0,s.jsx)(G.default,{src:"/logo.png",alt:"logo",width:100,height:100,className:"h-10 w-full",priority:!0,quality:100,sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"}),(0,s.jsx)("h1",{className:" text-[8px] font-bold text-end",children:"(FWU) (Incubation Center )"})]}),(0,s.jsx)(S,{children:(0,s.jsx)(U,{className:" mt-6",children:H.map(t=>(0,s.jsx)(M,{children:(0,s.jsx)(q,{className:` ${e===t.url?" bg-blue-500 text-white transition-all duration-200  hover:text-white  hover:bg-blue-600":""}`,asChild:!0,children:(0,s.jsxs)(B(),{href:t.url,children:[(0,s.jsx)(t.icon,{}),(0,s.jsx)("span",{className:" ",children:t.title})]})})},t.title))})})]})})})}var R=a(25217),Y=a(8693),ee=a(92930);function et({...e}){return(0,s.jsx)(ee.bL,{"data-slot":"dropdown-menu",...e})}function ea({...e}){return(0,s.jsx)(ee.l9,{"data-slot":"dropdown-menu-trigger",...e})}function es({className:e,sideOffset:t=4,...a}){return(0,s.jsx)(ee.ZL,{children:(0,s.jsx)(ee.UC,{"data-slot":"dropdown-menu-content",sideOffset:t,className:(0,o.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",e),...a})})}function ei({className:e,inset:t,variant:a="default",...i}){return(0,s.jsx)(ee.q7,{"data-slot":"dropdown-menu-item","data-inset":t,"data-variant":a,className:(0,o.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...i})}function er({className:e,inset:t,...a}){return(0,s.jsx)(ee.JU,{"data-slot":"dropdown-menu-label","data-inset":t,className:(0,o.cn)("px-2 py-1.5 text-sm font-medium data-[inset]:pl-8",e),...a})}function en({className:e,...t}){return(0,s.jsx)(ee.wv,{"data-slot":"dropdown-menu-separator",className:(0,o.cn)("bg-border -mx-1 my-1 h-px",e),...t})}var ed=a(97051);let eo=function(){return(0,s.jsx)("div",{children:(0,s.jsxs)("div",{className:"border-b shadow-md flex justify-between  items-center gap-1 p-2",children:[(0,s.jsx)(D,{size:"lg"}),(0,s.jsxs)("div",{className:" flex gap-2 justify-center items-center",children:[(0,s.jsxs)("div",{className:"bg-white/20 flex gap-1 backdrop-blur-sm rounded-xl p-3",children:[(0,s.jsx)(ed.A,{className:"w-6 h-6"}),(0,s.jsx)("span",{className:" text-sm  rounded-[5px] w-[10px] h-[10px] ",children:"2"})]}),(0,s.jsxs)(et,{children:[(0,s.jsx)(ea,{asChild:!0,children:(0,s.jsx)("button",{className:"bg-white/20 backdrop-blur-sm rounded-xl p-3",children:(0,s.jsx)(G.default,{height:32,width:32,src:"https://github.com/shadcn.png",alt:"avatar",className:"w-8 h-8 rounded-full"})})}),(0,s.jsxs)(es,{align:"end",children:[(0,s.jsx)(er,{children:"My Account"}),(0,s.jsx)(en,{}),(0,s.jsx)(ei,{children:"Profile"}),(0,s.jsx)(ei,{children:"Team"}),(0,s.jsx)(ei,{children:"Billing"}),(0,s.jsx)(ei,{children:"Subscription"})]})]})]})]})})},el=function({children:e}){let t=new R.E;return(0,s.jsx)(Y.Ht,{client:t,children:(0,s.jsxs)(_,{children:[(0,s.jsx)(Q,{}),(0,s.jsxs)("div",{className:" w-full bg-gray-200",children:[(0,s.jsx)("div",{className:"sticky top-0 z-30  backdrop-blur-2xl",children:(0,s.jsx)(eo,{})}),(0,s.jsxs)("div",{className:"fixed inset-0 overflow-hidden pointer-events-none",children:[(0,s.jsx)("div",{className:"absolute -top-4 -right-4 w-72 h-72 bg-gradient-to-br from-blue-400 to-purple-400 rounded-full opacity-10 animate-pulse"}),(0,s.jsx)("div",{className:"absolute -bottom-8 -left-8 w-96 h-96 bg-gradient-to-br from-pink-400 to-red-400 rounded-full opacity-10 animate-pulse",style:{animationDelay:"2s"}})]}),e]})]})})}},52744:(e,t,a)=>{Promise.resolve().then(a.bind(a,23521))},63144:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>r});var s=a(37413);a(61120);var i=a(1601);let r=function({children:e}){return(0,s.jsx)(i.default,{children:e})}},70440:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>i});var s=a(31658);let i=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]}};