{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/fwu_incubation%28react%2Blaravel%29/incubation/frontend/src/app/components/contact/ContactFormSection.tsx"], "sourcesContent": ["// components/contact/ContactFormSection.tsx\r\nimport { useState } from 'react';\r\nimport { useForm, SubmitHandler } from 'react-hook-form';\r\nimport { Send, CheckCircle, AlertCircle } from 'lucide-react';\r\n// Assuming FormField is in a shared location or copy it here if it's specific\r\n// For this example, let's assume a simplified FormField or define inputs directly.\r\n\r\ninterface ContactFormValues {\r\n  name: string;\r\n  email: string;\r\n  message: string;\r\n}\r\n\r\nconst ContactFormSection = () => {\r\n  const [submissionStatus, setSubmissionStatus] = useState<'idle' | 'submitting' | 'success' | 'error'>('idle');\r\n  const {\r\n    register,\r\n    handleSubmit,\r\n    formState: { errors, isSubmitting },\r\n    reset,\r\n  } = useForm<ContactFormValues>();\r\n\r\n  const onSubmit: SubmitHandler<ContactFormValues> = async (data) => {\r\n    setSubmissionStatus('submitting');\r\n    console.log(\"Contact Form Data:\", data);\r\n\r\n    // --- SIMULATE API CALL ---\r\n    await new Promise(resolve => setTimeout(resolve, 1500));\r\n    if (Math.random() > 0.2) { // Simulate success\r\n      setSubmissionStatus('success');\r\n      reset();\r\n    } else { // Simulate error\r\n      setSubmissionStatus('error');\r\n    }\r\n    // --- END SIMULATION ---\r\n  };\r\n\r\n  if (submissionStatus === 'success') {\r\n    return (\r\n      <div className=\"bg-white p-8 md:p-10 rounded-2xl shadow-xl text-center border border-gray-100\">\r\n        <div className=\"w-20 h-20 mx-auto bg-green-100 rounded-full flex items-center justify-center mb-6\">\r\n          <CheckCircle className=\"text-green-500 text-4xl\" />\r\n        </div>\r\n        <h3 className=\"text-2xl font-bold text-gray-900 mb-3\">Message Sent!</h3>\r\n        <p className=\"text-gray-600 mb-8 max-w-md mx-auto leading-relaxed\">\r\n          Thank you for contacting the FWU Incubation Center. We&apos;ve received your message and will get back to you as soon as possible.\r\n        </p>\r\n        <div className=\"flex flex-col items-center space-y-4\">\r\n          <p className=\"text-sm text-gray-500 mb-2\">\r\n            You will receive a confirmation email shortly.\r\n          </p>\r\n          <button\r\n            onClick={() => setSubmissionStatus('idle')}\r\n            className=\"bg-indigo-600 hover:bg-indigo-700 text-white font-semibold py-3 px-8 rounded-lg shadow-md transition-all duration-300 hover:scale-105 transform\"\r\n          >\r\n            Send Another Message\r\n          </button>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"bg-white p-8 md:p-10 rounded-2xl shadow-xl border border-gray-100 hover:border-indigo-100 transition-all duration-300 hover:shadow-indigo-100/20\">\r\n      <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-8\">\r\n        {/* Name Field */}\r\n        <div>\r\n          <label htmlFor=\"name\" className=\"flex items-center text-sm font-medium text-gray-700 mb-2\">\r\n            Full Name <span className=\"text-red-500 ml-1\">*</span>\r\n          </label>\r\n          <div className=\"relative\">\r\n            <input\r\n              type=\"text\"\r\n              id=\"name\"\r\n              className={`w-full px-4 py-3 pl-10 border-2 ${\r\n                errors.name ? 'border-red-300 bg-red-50' : 'border-gray-200 focus:border-indigo-300'\r\n              } rounded-lg ${\r\n                errors.name ? 'focus:ring-red-200' : 'focus:ring-indigo-100'\r\n              } focus:ring-4 outline-none transition-all`}\r\n              placeholder=\"e.g., Rajesh Sharma\"\r\n              {...register(\"name\", { required: \"Full name is required.\" })}\r\n            />\r\n            <div className=\"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none\">\r\n              <svg xmlns=\"http://www.w3.org/2000/svg\" className={`h-5 w-5 ${errors.name ? 'text-red-400' : 'text-indigo-400'}`} fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\r\n              </svg>\r\n            </div>\r\n          </div>\r\n          {errors.name && (\r\n            <div className=\"mt-2 flex items-start text-sm text-red-600\">\r\n              <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-4 w-4 text-red-500 mt-0.5 mr-1.5 flex-shrink-0\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z\" />\r\n              </svg>\r\n              <span>{errors.name.message}</span>\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        {/* Email Field */}\r\n        <div>\r\n          <label htmlFor=\"email\" className=\"flex items-center text-sm font-medium text-gray-700 mb-2\">\r\n            Email Address <span className=\"text-red-500 ml-1\">*</span>\r\n          </label>\r\n          <div className=\"relative\">\r\n            <input\r\n              type=\"email\"\r\n              id=\"email\"\r\n              className={`w-full px-4 py-3 pl-10 border-2 ${\r\n                errors.email ? 'border-red-300 bg-red-50' : 'border-gray-200 focus:border-indigo-300'\r\n              } rounded-lg ${\r\n                errors.email ? 'focus:ring-red-200' : 'focus:ring-indigo-100'\r\n              } focus:ring-4 outline-none transition-all`}\r\n              placeholder=\"<EMAIL>\"\r\n              {...register(\"email\", {\r\n                required: \"Email is required.\",\r\n                pattern: {\r\n                  value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\\.[A-Z]{2,}$/i,\r\n                  message: \"Invalid email address.\",\r\n                },\r\n              })}\r\n            />\r\n            <div className=\"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none\">\r\n              <svg xmlns=\"http://www.w3.org/2000/svg\" className={`h-5 w-5 ${errors.email ? 'text-red-400' : 'text-indigo-400'}`} fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\" />\r\n              </svg>\r\n            </div>\r\n          </div>\r\n          {errors.email && (\r\n            <div className=\"mt-2 flex items-start text-sm text-red-600\">\r\n              <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-4 w-4 text-red-500 mt-0.5 mr-1.5 flex-shrink-0\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z\" />\r\n              </svg>\r\n              <span>{errors.email.message}</span>\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        {/* Message Field */}\r\n        <div>\r\n          <label htmlFor=\"message\" className=\"flex items-center text-sm font-medium text-gray-700 mb-2\">\r\n            Your Message <span className=\"text-red-500 ml-1\">*</span>\r\n          </label>\r\n          <div className=\"relative\">\r\n            <textarea\r\n              id=\"message\"\r\n              rows={6}\r\n              className={`w-full px-4 py-3 border-2 ${\r\n                errors.message ? 'border-red-300 bg-red-50' : 'border-gray-200 focus:border-indigo-300'\r\n              } rounded-lg ${\r\n                errors.message ? 'focus:ring-red-200' : 'focus:ring-indigo-100'\r\n              } focus:ring-4 outline-none transition-all`}\r\n              placeholder=\"Write your message here... Please include details about your inquiry or how we can help you.\"\r\n              {...register(\"message\", {\r\n                required: \"Message is required.\",\r\n                minLength: { value: 10, message: \"Message must be at least 10 characters.\" }\r\n              })}\r\n            />\r\n          </div>\r\n          {errors.message && (\r\n            <div className=\"mt-2 flex items-start text-sm text-red-600\">\r\n              <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-4 w-4 text-red-500 mt-0.5 mr-1.5 flex-shrink-0\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z\" />\r\n              </svg>\r\n              <span>{errors.message.message}</span>\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        {/* Error Message */}\r\n        {submissionStatus === 'error' && (\r\n          <div className=\"flex items-center p-5 text-sm text-red-700 bg-red-100 rounded-xl border border-red-200\" role=\"alert\">\r\n            <AlertCircle className=\"text-xl mr-3 text-red-500\" />\r\n            <div>\r\n              <p className=\"font-bold\">Message Not Sent</p>\r\n              <p>There was an error sending your message. Please try again or contact us directly.</p>\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {/* Privacy Policy */}\r\n        <div className=\"text-sm text-gray-500\">\r\n          <p>\r\n            By submitting this form, you agree to our <a href=\"#\" className=\"text-indigo-600 hover:underline\">Privacy Policy</a> and consent to being contacted regarding your inquiry.\r\n          </p>\r\n        </div>\r\n\r\n        {/* Submit Button */}\r\n        <button\r\n          type=\"submit\"\r\n          disabled={isSubmitting}\r\n          className=\"w-full flex justify-center items-center bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-4 px-6 rounded-xl shadow-md transition-all duration-300 hover:scale-105 transform disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100\"\r\n        >\r\n          {isSubmitting ? (\r\n            <>\r\n              <svg className=\"animate-spin -ml-1 mr-3 h-5 w-5 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\r\n                <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\r\n                <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\r\n              </svg>\r\n              Sending Message...\r\n            </>\r\n          ) : (\r\n            <>\r\n              <Send className=\"mr-2\" /> Send Message\r\n            </>\r\n          )}\r\n        </button>\r\n      </form>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ContactFormSection;"], "names": [], "mappings": "AAAA,4CAA4C;;;;;AAC5C;;;;;;AAEA;AAAA;AAAA;;;;;AAUA,MAAM,qBAAqB;IACzB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA+C;IACtG,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,WAAW,EAAE,MAAM,EAAE,YAAY,EAAE,EACnC,KAAK,EACN,GAAG;IAEJ,MAAM,WAA6C,OAAO;QACxD,oBAAoB;QACpB,QAAQ,GAAG,CAAC,sBAAsB;QAElC,4BAA4B;QAC5B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QACjD,IAAI,KAAK,MAAM,KAAK,KAAK;YACvB,oBAAoB;YACpB;QACF,OAAO;YACL,oBAAoB;QACtB;IACA,yBAAyB;IAC3B;IAEA,IAAI,qBAAqB,WAAW;QAClC,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,2NAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;;;;;;8BAEzB,8OAAC;oBAAG,WAAU;8BAAwC;;;;;;8BACtD,8OAAC;oBAAE,WAAU;8BAAsD;;;;;;8BAGnE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;sCAA6B;;;;;;sCAG1C,8OAAC;4BACC,SAAS,IAAM,oBAAoB;4BACnC,WAAU;sCACX;;;;;;;;;;;;;;;;;;IAMT;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAK,UAAU,aAAa;YAAW,WAAU;;8BAEhD,8OAAC;;sCACC,8OAAC;4BAAM,SAAQ;4BAAO,WAAU;;gCAA2D;8CAC/E,8OAAC;oCAAK,WAAU;8CAAoB;;;;;;;;;;;;sCAEhD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,MAAK;oCACL,IAAG;oCACH,WAAW,CAAC,gCAAgC,EAC1C,OAAO,IAAI,GAAG,6BAA6B,0CAC5C,YAAY,EACX,OAAO,IAAI,GAAG,uBAAuB,wBACtC,yCAAyC,CAAC;oCAC3C,aAAY;oCACX,GAAG,SAAS,QAAQ;wCAAE,UAAU;oCAAyB,EAAE;;;;;;8CAE9D,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,OAAM;wCAA6B,WAAW,CAAC,QAAQ,EAAE,OAAO,IAAI,GAAG,iBAAiB,mBAAmB;wCAAE,MAAK;wCAAO,SAAQ;wCAAY,QAAO;kDACvJ,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;wBAI1E,OAAO,IAAI,kBACV,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,OAAM;oCAA6B,WAAU;oCAAmD,MAAK;oCAAO,SAAQ;oCAAY,QAAO;8CAC1I,cAAA,8OAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;8CAEvE,8OAAC;8CAAM,OAAO,IAAI,CAAC,OAAO;;;;;;;;;;;;;;;;;;8BAMhC,8OAAC;;sCACC,8OAAC;4BAAM,SAAQ;4BAAQ,WAAU;;gCAA2D;8CAC5E,8OAAC;oCAAK,WAAU;8CAAoB;;;;;;;;;;;;sCAEpD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,MAAK;oCACL,IAAG;oCACH,WAAW,CAAC,gCAAgC,EAC1C,OAAO,KAAK,GAAG,6BAA6B,0CAC7C,YAAY,EACX,OAAO,KAAK,GAAG,uBAAuB,wBACvC,yCAAyC,CAAC;oCAC3C,aAAY;oCACX,GAAG,SAAS,SAAS;wCACpB,UAAU;wCACV,SAAS;4CACP,OAAO;4CACP,SAAS;wCACX;oCACF,EAAE;;;;;;8CAEJ,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,OAAM;wCAA6B,WAAW,CAAC,QAAQ,EAAE,OAAO,KAAK,GAAG,iBAAiB,mBAAmB;wCAAE,MAAK;wCAAO,SAAQ;wCAAY,QAAO;kDACxJ,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;wBAI1E,OAAO,KAAK,kBACX,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,OAAM;oCAA6B,WAAU;oCAAmD,MAAK;oCAAO,SAAQ;oCAAY,QAAO;8CAC1I,cAAA,8OAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;8CAEvE,8OAAC;8CAAM,OAAO,KAAK,CAAC,OAAO;;;;;;;;;;;;;;;;;;8BAMjC,8OAAC;;sCACC,8OAAC;4BAAM,SAAQ;4BAAU,WAAU;;gCAA2D;8CAC/E,8OAAC;oCAAK,WAAU;8CAAoB;;;;;;;;;;;;sCAEnD,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,IAAG;gCACH,MAAM;gCACN,WAAW,CAAC,0BAA0B,EACpC,OAAO,OAAO,GAAG,6BAA6B,0CAC/C,YAAY,EACX,OAAO,OAAO,GAAG,uBAAuB,wBACzC,yCAAyC,CAAC;gCAC3C,aAAY;gCACX,GAAG,SAAS,WAAW;oCACtB,UAAU;oCACV,WAAW;wCAAE,OAAO;wCAAI,SAAS;oCAA0C;gCAC7E,EAAE;;;;;;;;;;;wBAGL,OAAO,OAAO,kBACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,OAAM;oCAA6B,WAAU;oCAAmD,MAAK;oCAAO,SAAQ;oCAAY,QAAO;8CAC1I,cAAA,8OAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;8CAEvE,8OAAC;8CAAM,OAAO,OAAO,CAAC,OAAO;;;;;;;;;;;;;;;;;;gBAMlC,qBAAqB,yBACpB,8OAAC;oBAAI,WAAU;oBAAyF,MAAK;;sCAC3G,8OAAC,oNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;sCACvB,8OAAC;;8CACC,8OAAC;oCAAE,WAAU;8CAAY;;;;;;8CACzB,8OAAC;8CAAE;;;;;;;;;;;;;;;;;;8BAMT,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;;4BAAE;0CACyC,8OAAC;gCAAE,MAAK;gCAAI,WAAU;0CAAkC;;;;;;4BAAkB;;;;;;;;;;;;8BAKxH,8OAAC;oBACC,MAAK;oBACL,UAAU;oBACV,WAAU;8BAET,6BACC;;0CACE,8OAAC;gCAAI,WAAU;gCAA6C,OAAM;gCAA6B,MAAK;gCAAO,SAAQ;;kDACjH,8OAAC;wCAAO,WAAU;wCAAa,IAAG;wCAAK,IAAG;wCAAK,GAAE;wCAAK,QAAO;wCAAe,aAAY;;;;;;kDACxF,8OAAC;wCAAK,WAAU;wCAAa,MAAK;wCAAe,GAAE;;;;;;;;;;;;4BAC/C;;qDAIR;;0CACE,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAS;;;;;;;;;;;;;;;;;;;AAOvC;uCAEe", "debugId": null}}, {"offset": {"line": 577, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/fwu_incubation%28react%2Blaravel%29/incubation/frontend/src/app/components/contact/ContactInfoSection.tsx"], "sourcesContent": ["// components/contact/ContactInfoSection.tsx\r\nimport { Phone, Mail, Clock, MapPin, Briefcase } from 'lucide-react';\r\n\r\ninterface ContactDetails {\r\n  phone: string;\r\n  email: string;\r\n  incubationEmail: string;\r\n  address: string;\r\n  officeHours: { day: string; hours: string }[];\r\n}\r\n\r\ninterface ContactInfoSectionProps {\r\n  details: ContactDetails;\r\n}\r\n\r\nconst ContactInfoItem: React.FC<{ icon: React.ReactNode; label: string; value: string; href?: string }> = ({ icon, label, value, href }) => (\r\n  <div className=\"flex items-start space-x-4 p-4 rounded-xl hover:bg-indigo-50 transition-colors group\">\r\n    <div className=\"flex-shrink-0 w-12 h-12 bg-indigo-100 text-indigo-600 rounded-full flex items-center justify-center group-hover:bg-indigo-200 transition-colors\">\r\n      {icon}\r\n    </div>\r\n    <div>\r\n      <h4 className=\"text-sm font-semibold text-indigo-600 uppercase tracking-wider mb-1\">{label}</h4>\r\n      {href ? (\r\n        <a href={href} className=\"text-md text-gray-900 hover:text-indigo-700 transition-colors break-words font-medium\">\r\n          {value}\r\n        </a>\r\n      ) : (\r\n        <p className=\"text-md text-gray-900 break-words\">{value}</p>\r\n      )}\r\n    </div>\r\n  </div>\r\n);\r\n\r\n\r\nconst ContactInfoSection: React.FC<ContactInfoSectionProps> = ({ details }) => {\r\n  return (\r\n    <div className=\"bg-white p-8 md:p-10 rounded-2xl shadow-xl border border-gray-100 hover:border-indigo-100 transition-all duration-300 hover:shadow-indigo-100/20\">\r\n      <div className=\"flex items-center mb-8\">\r\n        <div className=\"w-12 h-12 bg-indigo-100 rounded-full flex items-center justify-center mr-4\">\r\n          <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6 text-indigo-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\r\n          </svg>\r\n        </div>\r\n        <h2 className=\"text-2xl font-bold text-gray-900\">Contact Information</h2>\r\n      </div>\r\n\r\n      <div className=\"space-y-2\">\r\n        <ContactInfoItem\r\n          icon={<MapPin size={22} />}\r\n          label=\"Our Address\"\r\n          value={details.address}\r\n        />\r\n        <ContactInfoItem\r\n          icon={<Phone size={22} />}\r\n          label=\"Phone Number\"\r\n          value={details.phone}\r\n          href={`tel:${details.phone.replace(/\\s+/g, '')}`}\r\n        />\r\n        <ContactInfoItem\r\n          icon={<Mail size={22} />}\r\n          label=\"General Inquiries\"\r\n          value={details.email}\r\n          href={`mailto:${details.email}`}\r\n        />\r\n        <ContactInfoItem\r\n          icon={<Briefcase size={22} />}\r\n          label=\"Incubation Center\"\r\n          value={details.incubationEmail}\r\n          href={`mailto:${details.incubationEmail}`}\r\n        />\r\n\r\n        {/* Office Hours */}\r\n        <div className=\"flex items-start space-x-4 p-4 rounded-xl hover:bg-indigo-50 transition-colors group\">\r\n          <div className=\"flex-shrink-0 w-12 h-12 bg-indigo-100 text-indigo-600 rounded-full flex items-center justify-center group-hover:bg-indigo-200 transition-colors\">\r\n            <Clock size={22} />\r\n          </div>\r\n          <div>\r\n            <h4 className=\"text-sm font-semibold text-indigo-600 uppercase tracking-wider mb-2\">Office Hours</h4>\r\n            <div className=\"space-y-1\">\r\n              {details.officeHours.map((oh, index) => (\r\n                <div key={index} className=\"flex items-center\">\r\n                  <div className={`w-2 h-2 rounded-full mr-2 ${\r\n                    oh.hours.includes('Closed') ? 'bg-red-400' : 'bg-green-400'\r\n                  }`}></div>\r\n                  <p className=\"text-md text-gray-900\">\r\n                    <span className=\"font-medium\">{oh.day}:</span> {oh.hours}\r\n                  </p>\r\n                </div>\r\n              ))}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Social Media Links */}\r\n      <div className=\"mt-8 pt-6 border-t border-gray-100\">\r\n        <h4 className=\"text-sm font-semibold text-gray-500 uppercase tracking-wider mb-4\">Connect With Us</h4>\r\n        <div className=\"flex space-x-4\">\r\n          <a href=\"#\" className=\"w-10 h-10 bg-indigo-100 text-indigo-600 rounded-full flex items-center justify-center hover:bg-indigo-200 transition-colors\">\r\n            <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\r\n              <path d=\"M22.675 0h-21.35c-.732 0-1.325.593-1.325 1.325v21.351c0 .731.593 1.324 1.325 1.324h11.495v-9.294h-3.128v-3.622h3.128v-2.671c0-3.1 1.893-4.788 4.659-4.788 1.325 0 2.463.099 2.795.143v3.24l-1.918.001c-1.504 0-1.795.715-1.795 1.763v2.313h3.587l-.467 3.622h-3.12v9.293h6.116c.73 0 1.323-.593 1.323-1.325v-21.35c0-.732-.593-1.325-1.325-1.325z\" />\r\n            </svg>\r\n          </a>\r\n          <a href=\"#\" className=\"w-10 h-10 bg-indigo-100 text-indigo-600 rounded-full flex items-center justify-center hover:bg-indigo-200 transition-colors\">\r\n            <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\r\n              <path d=\"M12 0c-6.627 0-12 5.373-12 12s5.373 12 12 12 12-5.373 12-12-5.373-12-12-12zm6.066 9.645c.183 4.04-2.83 8.544-8.164 8.544-1.622 0-3.131-.476-4.402-1.291 1.524.18 3.045-.244 4.252-1.189-1.256-.023-2.317-.854-2.684-1.995.451.086.895.061 1.298-.049-1.381-.278-2.335-1.522-2.304-2.853.388.215.83.344 1.301.359-1.279-.855-1.641-2.544-.889-3.835 1.416 1.738 3.533 2.881 5.92 3.001-.419-1.796.944-3.527 2.799-3.527.825 0 1.572.349 2.096.907.654-.128 1.27-.368 1.824-.697-.215.671-.67 1.233-1.263 1.589.581-.07 1.135-.224 1.649-.453-.384.578-.87 1.084-1.433 1.489z\" />\r\n            </svg>\r\n          </a>\r\n          <a href=\"#\" className=\"w-10 h-10 bg-indigo-100 text-indigo-600 rounded-full flex items-center justify-center hover:bg-indigo-200 transition-colors\">\r\n            <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\r\n              <path d=\"M12 0c-6.627 0-12 5.373-12 12s5.373 12 12 12 12-5.373 12-12-5.373-12-12-12zm-2 16h-2v-6h2v6zm-1-6.891c-.607 0-1.1-.496-1.1-1.109 0-.612.492-1.109 1.1-1.109s1.1.497 1.1 1.109c0 .613-.493 1.109-1.1 1.109zm8 6.891h-1.998v-2.861c0-1.881-2.002-1.722-2.002 0v2.861h-2v-6h2v1.093c.872-1.616 4-1.736 4 1.548v3.359z\" />\r\n            </svg>\r\n          </a>\r\n          <a href=\"#\" className=\"w-10 h-10 bg-indigo-100 text-indigo-600 rounded-full flex items-center justify-center hover:bg-indigo-200 transition-colors\">\r\n            <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\r\n              <path d=\"M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z\" />\r\n            </svg>\r\n          </a>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ContactInfoSection;"], "names": [], "mappings": "AAAA,4CAA4C;;;;;AAC5C;AAAA;AAAA;AAAA;AAAA;;;AAcA,MAAM,kBAAoG,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,iBACrI,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACZ;;;;;;0BAEH,8OAAC;;kCACC,8OAAC;wBAAG,WAAU;kCAAuE;;;;;;oBACpF,qBACC,8OAAC;wBAAE,MAAM;wBAAM,WAAU;kCACtB;;;;;6CAGH,8OAAC;wBAAE,WAAU;kCAAqC;;;;;;;;;;;;;;;;;;AAO1D,MAAM,qBAAwD,CAAC,EAAE,OAAO,EAAE;IACxE,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,OAAM;4BAA6B,WAAU;4BAA0B,MAAK;4BAAO,SAAQ;4BAAY,QAAO;sCACjH,cAAA,8OAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;;;;;;;kCAGzE,8OAAC;wBAAG,WAAU;kCAAmC;;;;;;;;;;;;0BAGnD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,oBAAM,8OAAC,0MAAA,CAAA,SAAM;4BAAC,MAAM;;;;;;wBACpB,OAAM;wBACN,OAAO,QAAQ,OAAO;;;;;;kCAExB,8OAAC;wBACC,oBAAM,8OAAC,oMAAA,CAAA,QAAK;4BAAC,MAAM;;;;;;wBACnB,OAAM;wBACN,OAAO,QAAQ,KAAK;wBACpB,MAAM,CAAC,IAAI,EAAE,QAAQ,KAAK,CAAC,OAAO,CAAC,QAAQ,KAAK;;;;;;kCAElD,8OAAC;wBACC,oBAAM,8OAAC,kMAAA,CAAA,OAAI;4BAAC,MAAM;;;;;;wBAClB,OAAM;wBACN,OAAO,QAAQ,KAAK;wBACpB,MAAM,CAAC,OAAO,EAAE,QAAQ,KAAK,EAAE;;;;;;kCAEjC,8OAAC;wBACC,oBAAM,8OAAC,4MAAA,CAAA,YAAS;4BAAC,MAAM;;;;;;wBACvB,OAAM;wBACN,OAAO,QAAQ,eAAe;wBAC9B,MAAM,CAAC,OAAO,EAAE,QAAQ,eAAe,EAAE;;;;;;kCAI3C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;oCAAC,MAAM;;;;;;;;;;;0CAEf,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAsE;;;;;;kDACpF,8OAAC;wCAAI,WAAU;kDACZ,QAAQ,WAAW,CAAC,GAAG,CAAC,CAAC,IAAI,sBAC5B,8OAAC;gDAAgB,WAAU;;kEACzB,8OAAC;wDAAI,WAAW,CAAC,0BAA0B,EACzC,GAAG,KAAK,CAAC,QAAQ,CAAC,YAAY,eAAe,gBAC7C;;;;;;kEACF,8OAAC;wDAAE,WAAU;;0EACX,8OAAC;gEAAK,WAAU;;oEAAe,GAAG,GAAG;oEAAC;;;;;;;4DAAQ;4DAAE,GAAG,KAAK;;;;;;;;+CALlD;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAepB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAoE;;;;;;kCAClF,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,MAAK;gCAAI,WAAU;0CACpB,cAAA,8OAAC;oCAAI,OAAM;oCAA6B,OAAM;oCAAK,QAAO;oCAAK,MAAK;oCAAe,SAAQ;8CACzF,cAAA,8OAAC;wCAAK,GAAE;;;;;;;;;;;;;;;;0CAGZ,8OAAC;gCAAE,MAAK;gCAAI,WAAU;0CACpB,cAAA,8OAAC;oCAAI,OAAM;oCAA6B,OAAM;oCAAK,QAAO;oCAAK,MAAK;oCAAe,SAAQ;8CACzF,cAAA,8OAAC;wCAAK,GAAE;;;;;;;;;;;;;;;;0CAGZ,8OAAC;gCAAE,MAAK;gCAAI,WAAU;0CACpB,cAAA,8OAAC;oCAAI,OAAM;oCAA6B,OAAM;oCAAK,QAAO;oCAAK,MAAK;oCAAe,SAAQ;8CACzF,cAAA,8OAAC;wCAAK,GAAE;;;;;;;;;;;;;;;;0CAGZ,8OAAC;gCAAE,MAAK;gCAAI,WAAU;0CACpB,cAAA,8OAAC;oCAAI,OAAM;oCAA6B,OAAM;oCAAK,QAAO;oCAAK,MAAK;oCAAe,SAAQ;8CACzF,cAAA,8OAAC;wCAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOtB;uCAEe", "debugId": null}}, {"offset": {"line": 987, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/fwu_incubation%28react%2Blaravel%29/incubation/frontend/src/app/components/contact/MapSection.tsx"], "sourcesContent": ["// components/contact/MapSection.tsx\r\nimport { MapPin } from 'lucide-react';\r\n\r\ninterface MapSectionProps {\r\n  embedUrl: string;\r\n  address: string;\r\n}\r\n\r\nconst MapSection: React.FC<MapSectionProps> = ({ embedUrl, address }) => {\r\n  return (\r\n    <div className=\"bg-white p-6 rounded-2xl shadow-xl border border-gray-100 hover:border-indigo-100 transition-all duration-300 hover:shadow-indigo-100/20 overflow-hidden\">\r\n      {/* Section Header */}\r\n      <div className=\"flex items-center mb-6\">\r\n        <div className=\"w-12 h-12 bg-indigo-100 rounded-full flex items-center justify-center mr-4\">\r\n          <MapPin className=\"text-indigo-600 text-xl\" />\r\n        </div>\r\n        <h2 className=\"text-2xl font-bold text-gray-900\">Find Us</h2>\r\n      </div>\r\n\r\n      {/* Google Map Iframe with styled container */}\r\n      <div className=\"rounded-xl overflow-hidden border-4 border-indigo-50 shadow-inner relative\">\r\n        <div className=\"aspect-w-16 aspect-h-9\"> {/* Requires @tailwindcss/aspect-ratio */}\r\n          <iframe\r\n            src={embedUrl}\r\n            width=\"100%\"\r\n            height=\"100%\"\r\n            style={{ border: 0 }}\r\n            allowFullScreen={false}\r\n            loading=\"lazy\"\r\n            referrerPolicy=\"no-referrer-when-downgrade\"\r\n            title={`Map of ${address}`}\r\n            className=\"grayscale hover:grayscale-0 transition-all duration-500\"\r\n          ></iframe>\r\n        </div>\r\n\r\n        {/* Map overlay with address */}\r\n        <div className=\"absolute bottom-0 left-0 right-0 bg-gradient-to-t from-indigo-900/80 to-transparent p-4 text-white\">\r\n          <div className=\"flex items-start\">\r\n            <MapPin className=\"mt-1 mr-2 flex-shrink-0\" />\r\n            <p className=\"text-sm\">{address}</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Get directions button */}\r\n      <div className=\"mt-6 text-center\">\r\n        <a\r\n          href={`https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(address)}`}\r\n          target=\"_blank\"\r\n          rel=\"noopener noreferrer\"\r\n          className=\"inline-flex items-center text-indigo-600 font-semibold hover:text-indigo-800 transition-colors\"\r\n        >\r\n          Get Directions\r\n          <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-4 w-4 ml-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14\" />\r\n          </svg>\r\n        </a>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default MapSection;"], "names": [], "mappings": "AAAA,oCAAoC;;;;;AACpC;;;AAOA,MAAM,aAAwC,CAAC,EAAE,QAAQ,EAAE,OAAO,EAAE;IAClE,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,0MAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;;;;;;kCAEpB,8OAAC;wBAAG,WAAU;kCAAmC;;;;;;;;;;;;0BAInD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;4BAAyB;0CACtC,8OAAC;gCACC,KAAK;gCACL,OAAM;gCACN,QAAO;gCACP,OAAO;oCAAE,QAAQ;gCAAE;gCACnB,iBAAiB;gCACjB,SAAQ;gCACR,gBAAe;gCACf,OAAO,CAAC,OAAO,EAAE,SAAS;gCAC1B,WAAU;;;;;;;;;;;;kCAKd,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,0MAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,8OAAC;oCAAE,WAAU;8CAAW;;;;;;;;;;;;;;;;;;;;;;;0BAM9B,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,MAAM,CAAC,gDAAgD,EAAE,mBAAmB,UAAU;oBACtF,QAAO;oBACP,KAAI;oBACJ,WAAU;;wBACX;sCAEC,8OAAC;4BAAI,OAAM;4BAA6B,WAAU;4BAAe,MAAK;4BAAO,SAAQ;4BAAY,QAAO;sCACtG,cAAA,8OAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMjF;uCAEe", "debugId": null}}, {"offset": {"line": 1152, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/fwu_incubation%28react%2Blaravel%29/incubation/frontend/src/app/contact/page.tsx"], "sourcesContent": ["\"use client\"\r\nimport { ArrowR<PERSON>, MapPin, Mail, Phone, MessageSquare } from 'lucide-react';\r\nimport ContactFormSection from \"../components/contact/ContactFormSection\";\r\nimport ContactInfoSection from \"../components/contact/ContactInfoSection\";\r\nimport MapSection from \"../components/contact/MapSection\";\r\nimport Image from 'next/image';\r\nimport Link from 'next/link';\r\n\r\n// Updated data for contact info based on FWU website\r\nconst contactDetails = {\r\n  phone: '+977-099-520729',\r\n  email: '<EMAIL>',\r\n  incubationEmail: '<EMAIL>',\r\n  address: 'Bheemdatta Municipality-18, Mahendranagar, Kanchanpur, Nepal',\r\n  officeHours: [\r\n    { day: 'Monday - Friday', hours: '9:00 AM - 5:00 PM' },\r\n    { day: 'Saturday', hours: '9:00 AM - 2:00 PM' },\r\n    { day: 'Sunday', hours: 'Closed' },\r\n  ],\r\n  mapEmbedUrl: \"https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3499.8943315606097!2d80.18761937532953!3d28.69999997561701!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x39a1a0f8c01ebb33%3A0x7a2b35cd7920b47f!2sFar%20Western%20University!5e0!3m2!1sen!2snp!4v1715000000000!5m2!1sen!2snp\"\r\n};\r\n\r\n// Information officer details\r\nconst infoOfficer = {\r\n  name: \"Santosh Bist\",\r\n  phone: \"9858751161\",\r\n  email: \"<EMAIL>\",\r\n  photoUrl: \"https://fwu.edu.np/assets/uploads/employee-photo/photo-1624353722-sms.jpg\"\r\n};\r\n\r\nexport default function ContactPage() {\r\n  return (\r\n    <main className=\"bg-white relative overflow-hidden\">\r\n      {/* Background decorative elements */}\r\n      <div className=\"absolute top-0 right-0 w-[600px] h-[600px] bg-indigo-100 rounded-full opacity-30 blur-3xl -translate-x-1/3 -translate-y-1/3\"></div>\r\n      <div className=\"absolute bottom-0 left-0 w-[500px] h-[500px] bg-blue-100 rounded-full opacity-30 blur-3xl translate-x-1/4 translate-y-1/4\"></div>\r\n\r\n      {/* Subtle pattern overlay */}\r\n      <div className=\"absolute inset-0 opacity-5\"\r\n        style={{\r\n          backgroundImage: 'radial-gradient(#4338ca 1px, transparent 1px)',\r\n          backgroundSize: '40px 40px'\r\n        }}>\r\n      </div>\r\n\r\n      {/* Page Hero Section */}\r\n      <section className=\"relative py-20 md:py-28 bg-gradient-to-r from-indigo-900 via-indigo-800 to-blue-900 text-white overflow-hidden\">\r\n        {/* Hero background elements */}\r\n        <div className=\"absolute inset-0 overflow-hidden\">\r\n          <div className=\"absolute top-20 left-10 w-64 h-64 rounded-full bg-indigo-500 opacity-10 animate-float-slow\"></div>\r\n          <div className=\"absolute bottom-20 right-10 w-96 h-96 rounded-full bg-blue-500 opacity-10 animate-float-reverse\"></div>\r\n          <div className=\"absolute top-1/2 left-1/4 w-32 h-32 rounded-full bg-purple-500 opacity-5 animate-pulse\"></div>\r\n          <div\r\n            className=\"absolute inset-0 opacity-10\"\r\n            style={{\r\n              backgroundImage: 'radial-gradient(circle, rgba(255,255,255,0.1) 1px, transparent 1px)',\r\n              backgroundSize: '30px 30px'\r\n            }}\r\n          ></div>\r\n        </div>\r\n\r\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 relative z-10\">\r\n          <div className=\"max-w-4xl mx-auto text-center\">\r\n            <div className=\"inline-block mb-6 p-2 bg-indigo-800/30 rounded-full\">\r\n              <div className=\"px-4 py-1 bg-indigo-700/50 rounded-full\">\r\n                <span className=\"text-indigo-100 font-medium\">FWU Incubation Center</span>\r\n              </div>\r\n            </div>\r\n\r\n            <h1 className=\"text-4xl sm:text-5xl md:text-6xl font-extrabold mb-6 leading-tight\">\r\n              Get In <span className=\"text-transparent bg-clip-text bg-gradient-to-r from-blue-300 to-indigo-300\">Touch</span>\r\n            </h1>\r\n\r\n            <p className=\"text-xl text-indigo-100 max-w-3xl mx-auto mb-10 leading-relaxed\">\r\n              We&apos;re here to help and answer any questions you might have about the FWU Incubation Center.\r\n              Connect with us to learn more about our programs and opportunities.\r\n            </p>\r\n\r\n            <div className=\"flex flex-wrap justify-center gap-4 mb-8\">\r\n              <div className=\"flex items-center bg-white/10 backdrop-blur-sm px-4 py-2 rounded-full\">\r\n                <Phone className=\"text-blue-300 mr-2\" />\r\n                <span className=\"text-white text-sm\">{contactDetails.phone}</span>\r\n              </div>\r\n              <div className=\"flex items-center bg-white/10 backdrop-blur-sm px-4 py-2 rounded-full\">\r\n                <Mail className=\"text-blue-300 mr-2\" />\r\n                <span className=\"text-white text-sm\">{contactDetails.email}</span>\r\n              </div>\r\n              <div className=\"flex items-center bg-white/10 backdrop-blur-sm px-4 py-2 rounded-full\">\r\n                <MapPin className=\"text-blue-300 mr-2\" />\r\n                <span className=\"text-white text-sm\">Mahendranagar, Kanchanpur</span>\r\n              </div>\r\n            </div>\r\n\r\n            <a\r\n              href=\"#contact-form\"\r\n              className=\"inline-flex items-center bg-blue-600 hover:bg-blue-700 text-white font-bold py-4 px-8 rounded-lg shadow-lg transition-all duration-300 hover:scale-105 transform\"\r\n            >\r\n              Send Message <ArrowRight className=\"ml-2\" />\r\n            </a>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Wave divider */}\r\n        <div className=\"absolute bottom-0 left-0 right-0\">\r\n          <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 1440 120\" className=\"w-full h-auto\">\r\n            <path\r\n              fill=\"#ffffff\"\r\n              fillOpacity=\"1\"\r\n              d=\"M0,64L80,69.3C160,75,320,85,480,80C640,75,800,53,960,48C1120,43,1280,53,1360,58.7L1440,64L1440,120L1360,120C1280,120,1120,120,960,120C800,120,640,120,480,120C320,120,160,120,80,120L0,120Z\"\r\n            ></path>\r\n          </svg>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Quick Contact Cards */}\r\n      <section className=\"py-16 relative z-10\">\r\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\r\n          <div className=\"grid md:grid-cols-3 gap-8\">\r\n            {/* Card 1 */}\r\n            <div className=\"bg-white rounded-2xl shadow-xl p-8 border border-gray-100 hover:border-indigo-100 transition-all duration-300 hover:shadow-indigo-100/20 group\">\r\n              <div className=\"w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mb-6 group-hover:bg-indigo-200 transition-colors\">\r\n                <MapPin className=\"text-indigo-600 text-2xl\" />\r\n              </div>\r\n              <h3 className=\"text-xl font-bold text-gray-900 mb-3\">Visit Us</h3>\r\n              <p className=\"text-gray-600 mb-4\">\r\n                {contactDetails.address}\r\n              </p>\r\n              <a\r\n                href=\"https://maps.app.goo.gl/Ehu1U2FZzjRUsGEB6\"\r\n                target=\"_blank\"\r\n                rel=\"noopener noreferrer\"\r\n                className=\"inline-flex items-center text-indigo-600 font-semibold hover:text-indigo-800 transition-colors group\"\r\n              >\r\n                Get Directions <ArrowRight className=\"ml-2 group-hover:translate-x-1 transition-transform\" />\r\n              </a>\r\n            </div>\r\n\r\n            {/* Card 2 */}\r\n            <div className=\"bg-white rounded-2xl shadow-xl p-8 border border-gray-100 hover:border-indigo-100 transition-all duration-300 hover:shadow-indigo-100/20 group\">\r\n              <div className=\"w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mb-6 group-hover:bg-indigo-200 transition-colors\">\r\n                <Phone className=\"text-indigo-600 text-2xl\" />\r\n              </div>\r\n              <h3 className=\"text-xl font-bold text-gray-900 mb-3\">Call Us</h3>\r\n              <p className=\"text-gray-600 mb-4\">\r\n                Have questions? Call us directly at:\r\n              </p>\r\n              <a\r\n                href={`tel:${contactDetails.phone.replace(/\\s+/g, '')}`}\r\n                className=\"text-xl font-bold text-indigo-600 hover:text-indigo-800 transition-colors block mb-4\"\r\n              >\r\n                {contactDetails.phone}\r\n              </a>\r\n              <p className=\"text-sm text-gray-500\">\r\n                Available during office hours\r\n              </p>\r\n            </div>\r\n\r\n            {/* Card 3 */}\r\n            <div className=\"bg-white rounded-2xl shadow-xl p-8 border border-gray-100 hover:border-indigo-100 transition-all duration-300 hover:shadow-indigo-100/20 group\">\r\n              <div className=\"w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mb-6 group-hover:bg-indigo-200 transition-colors\">\r\n                <Mail className=\"text-indigo-600 text-2xl\" />\r\n              </div>\r\n              <h3 className=\"text-xl font-bold text-gray-900 mb-3\">Email Us</h3>\r\n              <p className=\"text-gray-600 mb-4\">\r\n                For general inquiries:\r\n              </p>\r\n              <a\r\n                href={`mailto:${contactDetails.email}`}\r\n                className=\"text-indigo-600 hover:text-indigo-800 transition-colors block mb-4 font-medium\"\r\n              >\r\n                {contactDetails.email}\r\n              </a>\r\n              <p className=\"text-gray-600 mb-2\">\r\n                For incubation center:\r\n              </p>\r\n              <a\r\n                href={`mailto:${contactDetails.incubationEmail}`}\r\n                className=\"text-indigo-600 hover:text-indigo-800 transition-colors block font-medium\"\r\n              >\r\n                {contactDetails.incubationEmail}\r\n              </a>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Main Contact Section */}\r\n      <section id=\"contact-form\" className=\"py-16 md:py-24 relative z-10\">\r\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\r\n          <div className=\"max-w-3xl mx-auto text-center mb-16\">\r\n            <div className=\"inline-block mb-4\">\r\n              <div className=\"flex items-center justify-center w-16 h-16 mx-auto bg-indigo-100 rounded-full mb-4\">\r\n                <MessageSquare className=\"h-8 w-8 text-indigo-600\" />\r\n              </div>\r\n            </div>\r\n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">Send Us a Message</h2>\r\n            <div className=\"w-24 h-1 bg-indigo-600 mx-auto mb-6 rounded-full\"></div>\r\n            <p className=\"text-xl text-gray-600 leading-relaxed\">\r\n              Have questions about our incubation programs or want to learn more? Fill out the form below and we&apos;ll get back to you as soon as possible.\r\n            </p>\r\n          </div>\r\n\r\n          <div className=\"grid lg:grid-cols-2 gap-12 items-start\">\r\n            {/* Left Column: Contact Form */}\r\n            <ContactFormSection />\r\n\r\n            {/* Right Column: Contact Info & Map */}\r\n            <div className=\"space-y-12\">\r\n              <ContactInfoSection details={contactDetails} />\r\n\r\n              {/* Information Officer Card */}\r\n              <div className=\"bg-white p-8 md:p-10 rounded-2xl shadow-xl border border-gray-100 hover:border-indigo-100 transition-all duration-300 hover:shadow-indigo-100/20\">\r\n                <h3 className=\"text-2xl font-bold text-gray-900 mb-6\">Information Officer</h3>\r\n                <div className=\"flex items-center\">\r\n                  <div className=\"relative w-20 h-20 rounded-full overflow-hidden mr-6 border-2 border-indigo-100\">\r\n                    <Image\r\n                      src={infoOfficer.photoUrl}\r\n                      alt={infoOfficer.name}\r\n                      fill\r\n                      className=\"object-cover\"\r\n                    />\r\n                  </div>\r\n                  <div>\r\n                    <h4 className=\"text-xl font-bold text-gray-900\">{infoOfficer.name}</h4>\r\n                    <p className=\"text-gray-600 mb-2\">Information Officer</p>\r\n                    <div className=\"flex items-center text-indigo-600 mb-1\">\r\n                      <Phone className=\"mr-2 text-sm\" />\r\n                      <a href={`tel:${infoOfficer.phone}`} className=\"hover:text-indigo-800 transition-colors\">\r\n                        {infoOfficer.phone}\r\n                      </a>\r\n                    </div>\r\n                    <div className=\"flex items-center text-indigo-600\">\r\n                      <Mail className=\"mr-2 text-sm\" />\r\n                      <a href={`mailto:${infoOfficer.email}`} className=\"hover:text-indigo-800 transition-colors\">\r\n                        {infoOfficer.email}\r\n                      </a>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <MapSection embedUrl={contactDetails.mapEmbedUrl} address={contactDetails.address} />\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* CTA Section */}\r\n      <section className=\"py-16 bg-gradient-to-r from-indigo-900 to-blue-900 text-white relative overflow-hidden\">\r\n        {/* Background elements */}\r\n        <div className=\"absolute inset-0 overflow-hidden\">\r\n          <div className=\"absolute top-0 right-0 w-64 h-64 rounded-full bg-indigo-500 opacity-10\"></div>\r\n          <div className=\"absolute bottom-0 left-0 w-96 h-96 rounded-full bg-blue-500 opacity-10\"></div>\r\n        </div>\r\n\r\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 relative z-10\">\r\n          <div className=\"max-w-4xl mx-auto text-center\">\r\n            <h2 className=\"text-3xl md:text-4xl font-bold mb-6\">Ready to Join Our Incubation Center?</h2>\r\n            <p className=\"text-xl text-indigo-100 mb-10 leading-relaxed\">\r\n              Apply now to transform your innovative idea into a successful business with our expert mentorship, resources, and collaborative ecosystem.\r\n            </p>\r\n            <div className=\"flex flex-wrap justify-center gap-6\">\r\n              <Link\r\n                href=\"/apply\"\r\n                className=\"bg-white text-indigo-900 hover:bg-indigo-50 font-bold py-4 px-8 rounded-lg shadow-lg transition-all duration-300 hover:scale-105 transform\"\r\n              >\r\n                Apply Now\r\n              </Link>\r\n              <Link\r\n                href=\"/programs\"\r\n                className=\"bg-transparent border-2 border-white text-white hover:bg-white/10 font-bold py-4 px-8 rounded-lg shadow-lg transition-all duration-300 hover:scale-105 transform\"\r\n              >\r\n                Explore Programs\r\n              </Link>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n    </main>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AANA;;;;;;;;AAQA,qDAAqD;AACrD,MAAM,iBAAiB;IACrB,OAAO;IACP,OAAO;IACP,iBAAiB;IACjB,SAAS;IACT,aAAa;QACX;YAAE,KAAK;YAAmB,OAAO;QAAoB;QACrD;YAAE,KAAK;YAAY,OAAO;QAAoB;QAC9C;YAAE,KAAK;YAAU,OAAO;QAAS;KAClC;IACD,aAAa;AACf;AAEA,8BAA8B;AAC9B,MAAM,cAAc;IAClB,MAAM;IACN,OAAO;IACP,OAAO;IACP,UAAU;AACZ;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAK,WAAU;;0BAEd,8OAAC;gBAAI,WAAU;;;;;;0BACf,8OAAC;gBAAI,WAAU;;;;;;0BAGf,8OAAC;gBAAI,WAAU;gBACb,OAAO;oBACL,iBAAiB;oBACjB,gBAAgB;gBAClB;;;;;;0BAIF,8OAAC;gBAAQ,WAAU;;kCAEjB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCACC,WAAU;gCACV,OAAO;oCACL,iBAAiB;oCACjB,gBAAgB;gCAClB;;;;;;;;;;;;kCAIJ,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDAA8B;;;;;;;;;;;;;;;;8CAIlD,8OAAC;oCAAG,WAAU;;wCAAqE;sDAC1E,8OAAC;4CAAK,WAAU;sDAA6E;;;;;;;;;;;;8CAGtG,8OAAC;oCAAE,WAAU;8CAAkE;;;;;;8CAK/E,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,8OAAC;oDAAK,WAAU;8DAAsB,eAAe,KAAK;;;;;;;;;;;;sDAE5D,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,8OAAC;oDAAK,WAAU;8DAAsB,eAAe,KAAK;;;;;;;;;;;;sDAE5D,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,0MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC;oDAAK,WAAU;8DAAqB;;;;;;;;;;;;;;;;;;8CAIzC,8OAAC;oCACC,MAAK;oCACL,WAAU;;wCACX;sDACc,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;kCAMzC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,OAAM;4BAA6B,SAAQ;4BAAe,WAAU;sCACvE,cAAA,8OAAC;gCACC,MAAK;gCACL,aAAY;gCACZ,GAAE;;;;;;;;;;;;;;;;;;;;;;0BAOV,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,0MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;;;;;;kDAEpB,8OAAC;wCAAG,WAAU;kDAAuC;;;;;;kDACrD,8OAAC;wCAAE,WAAU;kDACV,eAAe,OAAO;;;;;;kDAEzB,8OAAC;wCACC,MAAK;wCACL,QAAO;wCACP,KAAI;wCACJ,WAAU;;4CACX;0DACgB,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;;;;;;;0CAKzC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;kDAEnB,8OAAC;wCAAG,WAAU;kDAAuC;;;;;;kDACrD,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;kDAGlC,8OAAC;wCACC,MAAM,CAAC,IAAI,EAAE,eAAe,KAAK,CAAC,OAAO,CAAC,QAAQ,KAAK;wCACvD,WAAU;kDAET,eAAe,KAAK;;;;;;kDAEvB,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAMvC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;kDAElB,8OAAC;wCAAG,WAAU;kDAAuC;;;;;;kDACrD,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;kDAGlC,8OAAC;wCACC,MAAM,CAAC,OAAO,EAAE,eAAe,KAAK,EAAE;wCACtC,WAAU;kDAET,eAAe,KAAK;;;;;;kDAEvB,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;kDAGlC,8OAAC;wCACC,MAAM,CAAC,OAAO,EAAE,eAAe,eAAe,EAAE;wCAChD,WAAU;kDAET,eAAe,eAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQzC,8OAAC;gBAAQ,IAAG;gBAAe,WAAU;0BACnC,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,wNAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;;;;;;;;;;;8CAG7B,8OAAC;oCAAG,WAAU;8CAAoD;;;;;;8CAClE,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAE,WAAU;8CAAwC;;;;;;;;;;;;sCAKvD,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC,0JAAA,CAAA,UAAkB;;;;;8CAGnB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,0JAAA,CAAA,UAAkB;4CAAC,SAAS;;;;;;sDAG7B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAwC;;;;;;8DACtD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;gEACJ,KAAK,YAAY,QAAQ;gEACzB,KAAK,YAAY,IAAI;gEACrB,IAAI;gEACJ,WAAU;;;;;;;;;;;sEAGd,8OAAC;;8EACC,8OAAC;oEAAG,WAAU;8EAAmC,YAAY,IAAI;;;;;;8EACjE,8OAAC;oEAAE,WAAU;8EAAqB;;;;;;8EAClC,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,oMAAA,CAAA,QAAK;4EAAC,WAAU;;;;;;sFACjB,8OAAC;4EAAE,MAAM,CAAC,IAAI,EAAE,YAAY,KAAK,EAAE;4EAAE,WAAU;sFAC5C,YAAY,KAAK;;;;;;;;;;;;8EAGtB,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,kMAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;sFAChB,8OAAC;4EAAE,MAAM,CAAC,OAAO,EAAE,YAAY,KAAK,EAAE;4EAAE,WAAU;sFAC/C,YAAY,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAO5B,8OAAC,kJAAA,CAAA,UAAU;4CAAC,UAAU,eAAe,WAAW;4CAAE,SAAS,eAAe,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOzF,8OAAC;gBAAQ,WAAU;;kCAEjB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;;;;;;;kCAGjB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAsC;;;;;;8CACpD,8OAAC;oCAAE,WAAU;8CAAgD;;;;;;8CAG7D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;sDAGD,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf", "debugId": null}}]}