(()=>{var e={};e.id=910,e.ids=[910],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},24243:(e,t,s)=>{Promise.resolve().then(s.bind(s,45408))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},45408:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>C});var r=s(60687),a=s(43210),i=s(97051),n=s(41312),l=s(10022),o=s(25541),c=s(48927),d=s(99270),x=s(80462);let u=({searchTerm:e,setSearchTerm:t,selectedCategory:s,setSelectedCategory:a,categories:i})=>(0,r.jsx)("div",{className:"bg-white rounded-2xl p-6 shadow-lg border border-gray-100 mb-8",children:(0,r.jsxs)("div",{className:"flex flex-col lg:flex-row gap-4",children:[(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(d.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"}),(0,r.jsx)("input",{type:"text",placeholder:"Search notices...",value:e,onChange:e=>t(e.target.value),className:"w-full pl-10 pr-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(x.A,{className:"text-gray-400 w-5 h-5"}),(0,r.jsxs)("select",{value:s,onChange:e=>a(e.target.value),className:"px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white",children:[(0,r.jsx)("option",{value:"all",children:"All Categories"}),i.map(e=>(0,r.jsx)("option",{value:e,children:e.charAt(0).toUpperCase()+e.slice(1)},e))]})]})]})});var m=s(47033),p=s(14952);let h=({currentPage:e,totalPages:t,onPageChange:s,noticeData:a})=>(0,r.jsxs)("div",{className:"flex items-center justify-between bg-white px-6 py-4 rounded-2xl shadow-lg border border-gray-100",children:[(0,r.jsx)("div",{className:"flex items-center space-x-2 text-sm text-gray-600",children:(0,r.jsxs)("span",{children:["Showing 1 to ",a.data.length," of ",a.total," results"]})}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("button",{onClick:()=>s(e-1),disabled:1===e,className:"p-2 rounded-lg border border-gray-300 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-all",children:(0,r.jsx)(m.A,{className:"w-5 h-5"})}),(0,r.jsx)("span",{className:"px-4 py-2 bg-blue-600 text-white rounded-lg font-medium",children:e}),(0,r.jsx)("button",{onClick:()=>s(e+1),disabled:e===t,className:"p-2 rounded-lg border border-gray-300 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-all",children:(0,r.jsx)(p.A,{className:"w-5 h-5"})})]})]});var g=s(78464),f=s(58869),b=s(40228),j=s(13861),v=s(31158),y=s(75034),w=s(88233);let N=({notices:e})=>{let t=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),s=(e,t)=>{console.log(`Downloading: ${e}`),alert(`Downloading: ${t}`)};return(0,r.jsx)("div",{className:"bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden",children:(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"w-full",children:[(0,r.jsx)("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{className:"px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Notice Details"}),(0,r.jsx)("th",{className:"px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Added By"}),(0,r.jsx)("th",{className:"px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Dates"}),(0,r.jsx)("th",{className:"px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:e.map(e=>(0,r.jsxs)("tr",{className:"hover:bg-gray-50 transition-colors",children:[(0,r.jsx)("td",{className:"px-6 py-4",children:(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("div",{className:"w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center",children:(0,r.jsx)(i.A,{className:"w-5 h-5 text-blue-600"})})}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("h4",{className:"text-sm font-semibold text-gray-900 mb-1",children:e.title}),(0,r.jsx)("p",{className:"text-sm text-gray-600 line-clamp-2",children:e.description}),e.file&&(0,r.jsxs)("div",{className:"mt-2 flex items-center text-xs text-blue-600",children:[(0,r.jsx)(g.A,{className:"w-4 h-4 mr-1"}),"PDF Attachment Available"]})]})]})}),(0,r.jsx)("td",{className:"px-6 py-4",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0 h-8 w-8",children:(0,r.jsx)("div",{className:"h-8 w-8 rounded-full bg-gradient-to-r from-blue-400 to-blue-600 flex items-center justify-center",children:(0,r.jsx)(f.A,{className:"w-4 h-4 text-white"})})}),(0,r.jsxs)("div",{className:"ml-3",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-900",children:e.admin.name}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:e.admin.role})]})]})}),(0,r.jsx)("td",{className:"px-6 py-4",children:(0,r.jsxs)("div",{className:"text-sm text-gray-900",children:[(0,r.jsxs)("div",{className:"flex items-center mb-1",children:[(0,r.jsx)(b.A,{className:"w-4 h-4 mr-1 text-gray-400"}),(0,r.jsx)("span",{className:"text-xs text-gray-500",children:"Added:"})]}),(0,r.jsx)("p",{className:"text-xs font-medium",children:t(e.created_at)}),e.created_at!==e.updated_at&&(0,r.jsxs)("div",{className:"mt-2",children:[(0,r.jsxs)("div",{className:"flex items-center mb-1",children:[(0,r.jsx)(b.A,{className:"w-4 h-4 mr-1 text-gray-400"}),(0,r.jsx)("span",{className:"text-xs text-gray-500",children:"Updated:"})]}),(0,r.jsx)("p",{className:"text-xs font-medium",children:t(e.updated_at)})]})]})}),(0,r.jsx)("td",{className:"px-6 py-4",children:(0,r.jsxs)("div",{className:"flex items-center flex-wrap gap-x-2 gap-y-2",children:[(0,r.jsxs)("button",{className:"inline-flex cursor-pointer items-center px-3 py-1.5 text-xs font-medium rounded-lg text-white bg-gray-600 hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors w-18",children:[(0,r.jsx)(j.A,{className:"w-4 h-4 mr-1"}),"View"]}),e.file&&(0,r.jsxs)("button",{onClick:()=>s(e.file,e.title),className:"inline-flex cursor-pointer items-center px-3 py-1.5 text-xs font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors w-18",children:[(0,r.jsx)(v.A,{className:"w-4 h-4 mr-1"}),"PDF"]}),(0,r.jsxs)("button",{className:"inline-flex cursor-pointer items-center px-3 py-1.5 text-xs font-medium rounded-lg text-white bg-yellow-500 hover:bg-yellow-600 focus:outline-none focus:ring-2 focus:ring-yellow-400 transition-colors w-18",children:[(0,r.jsx)(y.A,{className:"w-4 h-4 mr-1"}),"Edit"]}),(0,r.jsxs)("button",{className:"inline-flex cursor-pointer items-center px-3 py-1.5 text-xs font-medium rounded-lg text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 transition-colors w-18",children:[(0,r.jsx)(w.A,{className:"w-4 h-4 mr-1"}),"Delete"]})]})})]},e.notice_id))})]})})})},A=({icon:e,title:t,value:s,change:a,color:i})=>(0,r.jsx)("div",{className:"bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 group",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-gray-600 text-sm font-medium",children:t}),(0,r.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mt-1",children:s}),void 0!==a&&(0,r.jsxs)("p",{className:`text-sm mt-2 flex items-center ${a>0?"text-green-600":"text-red-600"}`,children:[(0,r.jsx)(o.A,{className:"w-4 h-4 mr-1"}),a>0?"+":"",a,"% from last month"]})]}),(0,r.jsx)("div",{className:`p-3 rounded-xl ${i} group-hover:scale-110 transition-transform`,children:(0,r.jsx)(e,{className:"w-6 h-6 text-white"})})]})});var _=s(51423),q=s(51060);let C=()=>{let[e,t]=(0,a.useState)(""),[s,d]=(0,a.useState)("all"),[x,m]=(0,a.useState)(1),{data:p,isLoading:g,error:f}=(0,_.I)({queryKey:["notices"],queryFn:async()=>(await q.A.get("http://127.0.0.1:8000/api/notice")).data,select:e=>e});if(g)return(0,r.jsx)("div",{children:"Loading..."});if(f)return(0,r.jsx)("div",{children:"Error..."});console.log(p);let b=p?.data?p.data.filter(t=>{let r=t.title.toLowerCase().includes(e.toLowerCase())||t.description.toLowerCase().includes(e.toLowerCase()),a="all"===s||t.title.toLowerCase().includes(s.toLowerCase());return r&&a}):[];return(0,r.jsxs)("div",{className:" bg-gray-50",children:[(0,r.jsx)(c.A,{pageTitle:"Incubation Center - Official Notices",pageSubtitle:"Stay updated with the latest announcements, programs, and opportunities from our innovation hub. Access important documents and guidelines for startups and entrepreneurs.",breadcrumb:["Home","Incubation Center","Notices"],showStats:!0}),(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-6 py-8",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[(0,r.jsx)(A,{icon:i.A,title:"Total Notices",value:p?.total??0,change:15,color:"bg-gradient-to-br from-blue-500 to-blue-600"}),(0,r.jsx)(A,{icon:n.A,title:"Active Admins",value:"3",change:0,color:"bg-gradient-to-br from-green-500 to-green-600"}),(0,r.jsx)(A,{icon:l.A,title:"With Attachments",value:"5",change:25,color:"bg-gradient-to-br from-purple-500 to-purple-600"}),(0,r.jsx)(A,{icon:o.A,title:"This Month",value:"12",change:20,color:"bg-gradient-to-br from-orange-500 to-orange-600"})]}),(0,r.jsx)(u,{searchTerm:e,setSearchTerm:t,selectedCategory:s,setSelectedCategory:d,categories:["proposal","mentorship","workshop","funding","facility","program","application"]}),(0,r.jsx)("div",{className:"mb-8",children:(0,r.jsx)(N,{notices:b})}),p&&(0,r.jsx)(h,{noticeData:p,currentPage:x,totalPages:p.last_page,onPageChange:m})]})]})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},71099:(e,t,s)=>{Promise.resolve().then(s.bind(s,86550))},74075:e=>{"use strict";e.exports=require("zlib")},75694:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>d,routeModule:()=>u,tree:()=>c});var r=s(65239),a=s(48088),i=s(88170),n=s.n(i),l=s(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(t,o);let c={children:["",{children:["dashboard",{children:["notice",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,86550)),"D:\\fwu_incubation(react+laravel)\\incubation\\frontend\\src\\app\\dashboard\\notice\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,63144)),"D:\\fwu_incubation(react+laravel)\\incubation\\frontend\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,53862)),"D:\\fwu_incubation(react+laravel)\\incubation\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,54413)),"D:\\fwu_incubation(react+laravel)\\incubation\\frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["D:\\fwu_incubation(react+laravel)\\incubation\\frontend\\src\\app\\dashboard\\notice\\page.tsx"],x={require:s,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/notice/page",pathname:"/dashboard/notice",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},78464:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("file",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}]])},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},86550:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\fwu_incubation(react+laravel)\\\\incubation\\\\frontend\\\\src\\\\app\\\\dashboard\\\\notice\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\fwu_incubation(react+laravel)\\incubation\\frontend\\src\\app\\dashboard\\notice\\page.tsx","default")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[447,693,585,208,464,935,171,884,927],()=>s(75694));module.exports=r})();