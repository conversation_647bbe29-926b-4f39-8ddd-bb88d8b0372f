"use client"
import React, { useState } from 'react'
import { Eye, EyeOff, Mail, Lock, ArrowRight, Users, Trophy, Rocket } from 'lucide-react'
import Image from 'next/image'
import axios from 'axios'
import { useRouter } from 'next/navigation'
import { toast } from "sonner"
function Login() {
  const [showPassword, setShowPassword] = useState(false)
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const router=useRouter();
  const handleSubmit =async () => {
    setIsLoading(true)
    try {
     const respon=(await axios.post ("http://localhost:8000/api/login",{
        email,
        password,
      }) ).data;
      if (respon.ok) {
         if (typeof window !== 'undefined') {
           localStorage.setItem('token', respon?.token)
           router.push('/dashboard');
         }
        console.log(respon)
        toast.success('Login successful')
      } else {
         toast.error("Lo<PERSON> failed")
        throw new Error('Lo<PERSON> failed')
        
      }
    } catch (error) {
      toast.error(error instanceof Error ? error.message : String(error))
      // console.log(error)
    }finally{
      setIsLoading(false)
    }
  }
  


  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900 flex items-center justify-center p-4 relative overflow-hidden">
      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-blue-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse animation-delay-2000"></div>
        <div className="absolute top-40 left-40 w-60 h-60 bg-indigo-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse animation-delay-4000"></div>
      </div>

      {/* Main Container */}
      <div className="relative z-10 w-full max-w-6xl mx-auto grid lg:grid-cols-2 gap-8 items-center">
        
        {/* Left Side - Branding and Info */}
        <div className="text-white space-y-8 lg:pr-8">
          {/* Logo and Title */}
          <div className="text-center lg:text-left">
            <div className="flex items-center justify-center lg:justify-start mb-6">
              <div className="w-16 h-16 bg-gradient-to-br rounded-xl flex items-center justify-center shadow-2xl">
                {/* <Lightbulb className="w-8 h-8 text-white" /> */}
                <Image
                  src={"/circlelogo.png"}
                  alt="Logo"
                  width={100}
                  height={100}
                  className="h-16 w-16 object-contain rounded-md"
                  priority
                  quality={100}
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                />
              </div>
              <div className="ml-4">
                <h1 className="text-2xl lg:text-3xl font-bold bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent">
                  Far Western University
                </h1>
                <p className="text-lg text-blue-200 font-medium">Incubation Center</p>
              </div>
            </div>
            
            <h2 className="text-4xl lg:text-5xl font-bold mb-4 leading-tight">
              Innovate.<br />
              <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
                Create.
              </span><br />
              Transform.
            </h2>
            
            <p className="text-xl text-blue-200 max-w-md mx-auto lg:mx-0">
              Join Nepal&#39;s premier startup ecosystem and turn your ideas into reality
            </p>
          </div>

          {/* Features */}
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-6 pt-8">
            <div className="text-center lg:text-left group">
              <div className="w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center mx-auto lg:mx-0 mb-3 group-hover:bg-blue-500/30 transition-colors">
                <Users className="w-6 h-6 text-blue-300" />
              </div>
              <h3 className="font-semibold text-white mb-1">Mentorship</h3>
              <p className="text-sm text-blue-200">Expert guidance from industry leaders</p>
            </div>
            
            <div className="text-center lg:text-left group">
              <div className="w-12 h-12 bg-purple-500/20 rounded-lg flex items-center justify-center mx-auto lg:mx-0 mb-3 group-hover:bg-purple-500/30 transition-colors">
                <Rocket className="w-6 h-6 text-purple-300" />
              </div>
              <h3 className="font-semibold text-white mb-1">Acceleration</h3>
              <p className="text-sm text-blue-200">Fast-track your startup journey</p>
            </div>
            
            <div className="text-center lg:text-left group">
              <div className="w-12 h-12 bg-yellow-500/20 rounded-lg flex items-center justify-center mx-auto lg:mx-0 mb-3 group-hover:bg-yellow-500/30 transition-colors">
                <Trophy className="w-6 h-6 text-yellow-300" />
              </div>
              <h3 className="font-semibold text-white mb-1">Success</h3>
              <p className="text-sm text-blue-200">Proven track record of winners</p>
            </div>
          </div>
        </div>

        {/* Right Side - Login Form */}
        <div className="w-full max-w-md mx-auto">
          <div className="bg-white/10 backdrop-blur-xl rounded-3xl p-8 shadow-2xl border border-white/20">
            <div className="text-center mb-8">
              <h3 className="text-2xl font-bold text-white mb-2">Welcome Back</h3>
              <p className="text-blue-200">Sign in to your incubator account</p>
            </div>

            <div className="space-y-6">
              {/* Email Field */}
              <div className="space-y-2">
                <label htmlFor="email" className="text-sm font-medium text-white">
                  Email Address
                </label>
                <div className="relative">
                  <Mail className="absolute left-3 top-3 w-5 h-5 text-gray-400" />
                  <input
                    id="email"
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="w-full pl-10 pr-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent backdrop-blur-sm transition-all"
                    placeholder="Enter your email"
                    required
                  />
                </div>
              </div>

              {/* Password Field */}
              <div className="space-y-2">
                <label htmlFor="password" className="text-sm font-medium text-white">
                  Password
                </label>
                <div className="relative">
                  <Lock className="absolute left-3 top-3 w-5 h-5 text-gray-400" />
                  <input
                    id="password"
                    type={showPassword ? 'text' : 'password'}
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className="w-full pl-10 pr-12 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent backdrop-blur-sm transition-all"
                    placeholder="Enter your password"
                    required
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-3 text-gray-400 hover:text-white transition-colors"
                  >
                    {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                  </button>
                </div>
              </div>

              {/* Remember Me & Forgot Password */}
              <div className="flex items-center justify-between text-sm">
                <label className="flex items-center text-white">
                  <input type="checkbox" className="mr-2 rounded" />
                  Remember me
                </label>
                <a href="#" className="text-blue-400 hover:text-blue-300 transition-colors">
                  Forgot password?
                </a>
              </div>

              {/* Submit Button */}
              <button
                type="button"
                onClick={handleSubmit}
                disabled={isLoading}
                className="w-full cursor-pointer bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-200 flex items-center justify-center space-x-2 shadow-lg hover:shadow-xl disabled:opacity-70 disabled:cursor-not-allowed group"
              >
                {isLoading ? (
                  <>
                    <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                    <span>Signing In...</span>
                  </>
                ) : (
                  <>
                    <span>Sign In</span>
                    <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
                  </>
                )}
              </button>
            </div>

            {/* Sign Up Link */}
            <div className="mt-8 text-center">
              <p className="text-blue-200">
                Don&#39;t have an account?{' '}
                <a href="#" className="text-blue-400 hover:text-blue-300 font-semibold transition-colors">
                  Join the incubator
                </a>
              </p>
            </div>
          </div>

          {/* Additional Info */}
          <div className="mt-6 text-center text-blue-200 text-sm">
            <p>© 2024 Far Western University Incubation Center</p>
            <p>Empowering Nepal&#39;s Next Generation of Entrepreneurs</p>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Login