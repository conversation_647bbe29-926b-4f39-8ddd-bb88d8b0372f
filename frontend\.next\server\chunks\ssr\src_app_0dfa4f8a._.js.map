{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/fwu_incubation%28react%2Blaravel%29/incubation/frontend/src/app/components/shared/SectionTitle.tsx"], "sourcesContent": ["// components/shared/SectionTitle.tsx\r\ninterface SectionTitleProps {\r\n  title: string;\r\n  subtitle?: string;\r\n  align?: 'left' | 'center' | 'right';\r\n  accentColor?: string;\r\n}\r\n\r\nconst SectionTitle: React.FC<SectionTitleProps> = ({\r\n  title,\r\n  subtitle,\r\n  align = 'center',\r\n  accentColor = 'indigo'\r\n}) => {\r\n  const alignmentClass = {\r\n    left: 'text-left',\r\n    center: 'text-center',\r\n    right: 'text-right',\r\n  };\r\n\r\n  const accentColorClass = {\r\n    indigo: 'bg-indigo-600',\r\n    blue: 'bg-blue-600',\r\n    teal: 'bg-teal-600',\r\n    purple: 'bg-purple-600',\r\n    green: 'bg-green-600',\r\n  };\r\n\r\n  const textColorClass = {\r\n    indigo: 'text-indigo-600',\r\n    blue: 'text-blue-600',\r\n    teal: 'text-teal-600',\r\n    purple: 'text-purple-600',\r\n    green: 'text-green-600',\r\n  };\r\n\r\n  return (\r\n    <div className={`mb-12 md:mb-16 ${alignmentClass[align]}`}>\r\n      {subtitle && (\r\n        <p className={`${textColorClass[accentColor as keyof typeof textColorClass]} font-semibold text-sm md:text-base uppercase tracking-wider mb-2`}>\r\n          {subtitle}\r\n        </p>\r\n      )}\r\n      <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900\">\r\n        {title}\r\n      </h2>\r\n      <div className={`mt-4 h-1.5 w-24 ${align === 'center' ? 'mx-auto' : (align === 'right' ? 'ml-auto' : '')} ${accentColorClass[accentColor as keyof typeof accentColorClass]} rounded-full`}></div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default SectionTitle;"], "names": [], "mappings": "AAAA,qCAAqC;;;;;;AAQrC,MAAM,eAA4C,CAAC,EACjD,KAAK,EACL,QAAQ,EACR,QAAQ,QAAQ,EAChB,cAAc,QAAQ,EACvB;IACC,MAAM,iBAAiB;QACrB,MAAM;QACN,QAAQ;QACR,OAAO;IACT;IAEA,MAAM,mBAAmB;QACvB,QAAQ;QACR,MAAM;QACN,MAAM;QACN,QAAQ;QACR,OAAO;IACT;IAEA,MAAM,iBAAiB;QACrB,QAAQ;QACR,MAAM;QACN,MAAM;QACN,QAAQ;QACR,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,eAAe,EAAE,cAAc,CAAC,MAAM,EAAE;;YACtD,0BACC,8OAAC;gBAAE,WAAW,GAAG,cAAc,CAAC,YAA2C,CAAC,iEAAiE,CAAC;0BAC3I;;;;;;0BAGL,8OAAC;gBAAG,WAAU;0BACX;;;;;;0BAEH,8OAAC;gBAAI,WAAW,CAAC,gBAAgB,EAAE,UAAU,WAAW,YAAa,UAAU,UAAU,YAAY,GAAI,CAAC,EAAE,gBAAgB,CAAC,YAA6C,CAAC,aAAa,CAAC;;;;;;;;;;;;AAG/L;uCAEe", "debugId": null}}, {"offset": {"line": 73, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/fwu_incubation%28react%2Blaravel%29/incubation/frontend/src/app/components/about/TeamMemberCard.tsx"], "sourcesContent": ["// components/about/TeamMemberCard.tsx\r\nimport Image from 'next/image';\r\nimport { Linkedin, Twitter, Mail, Quote } from 'lucide-react';\r\n\r\nexport interface Member {\r\n  id: string;\r\n  name: string;\r\n  role: string;\r\n  imageUrl: string;\r\n  bio?: string; // Optional short bio\r\n  linkedin?: string;\r\n  twitter?: string;\r\n  email?: string;\r\n}\r\n\r\ninterface TeamMemberCardProps {\r\n  member: Member;\r\n}\r\n\r\nconst TeamMemberCard: React.FC<TeamMemberCardProps> = ({ member }) => {\r\n  return (\r\n    <div className=\"bg-white rounded-xl shadow-lg p-6 text-center transform hover:scale-105 hover:shadow-2xl transition-all duration-500 flex flex-col items-center h-full border border-gray-100 hover:border-blue-200 relative group overflow-hidden\">\r\n      {/* Background pattern */}\r\n      <div className=\"absolute inset-0 opacity-0 group-hover:opacity-5 transition-opacity duration-500\" style={{\r\n        backgroundImage: 'radial-gradient(circle, #4a90e2 1px, transparent 1px)',\r\n        backgroundSize: '20px 20px'\r\n      }}></div>\r\n\r\n      {/* Decorative corner accent */}\r\n      <div className=\"absolute top-0 right-0 w-16 h-16 overflow-hidden\">\r\n        <div className=\"absolute transform rotate-45 bg-blue-600 w-16 h-3 -top-2 -right-8 opacity-70\"></div>\r\n      </div>\r\n\r\n      {/* Profile Image with Glow Effect */}\r\n      <div className=\"relative w-32 h-32 md:w-40 md:h-40 mb-6 rounded-full overflow-hidden border-4 border-white shadow-lg group-hover:shadow-blue-200 transition-all duration-500\">\r\n        <div className=\"absolute inset-0 bg-gradient-to-br from-blue-50 to-white\"></div>\r\n        <Image\r\n          src={member.imageUrl}\r\n          alt={member.name}\r\n          width={160}\r\n          height={160}\r\n          className=\"relative z-10 object-cover\"\r\n        />\r\n        <div className=\"absolute inset-0 bg-blue-400/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500\"></div>\r\n      </div>\r\n\r\n      {/* Name and Role */}\r\n      <h3 className=\"text-xl md:text-2xl font-bold text-blue-900 mb-1 group-hover:text-blue-700 transition-colors duration-300\">{member.name}</h3>\r\n      <p className=\"text-blue-600 font-medium mb-4 px-2\">{member.role}</p>\r\n\r\n      {/* Bio with Quote Icon */}\r\n      {member.bio && (\r\n        <div className=\"relative text-sm text-gray-600 mb-6 px-2 flex-grow\">\r\n          <Quote className=\"text-blue-100 absolute top-0 left-0 transform -translate-x-1/2 -translate-y-1/2 opacity-50\" />\r\n          <p className=\"relative z-10\">{member.bio}</p>\r\n        </div>\r\n      )}\r\n\r\n      {/* Social Links with Enhanced Styling */}\r\n      {(member.linkedin || member.twitter || member.email) && (\r\n        <div className=\"flex space-x-4 mt-auto pt-4 border-t border-gray-200 w-full justify-center\">\r\n          {member.linkedin && (\r\n            <a\r\n              href={member.linkedin}\r\n              target=\"_blank\"\r\n              rel=\"noopener noreferrer\"\r\n              className=\"text-gray-400 hover:text-blue-600 transition-colors duration-300 transform hover:scale-110\"\r\n              aria-label={`LinkedIn profile of ${member.name}`}\r\n            >\r\n              <Linkedin size={22} />\r\n            </a>\r\n          )}\r\n          {member.twitter && (\r\n            <a\r\n              href={member.twitter}\r\n              target=\"_blank\"\r\n              rel=\"noopener noreferrer\"\r\n              className=\"text-gray-400 hover:text-blue-400 transition-colors duration-300 transform hover:scale-110\"\r\n              aria-label={`Twitter profile of ${member.name}`}\r\n            >\r\n              <Twitter size={22} />\r\n            </a>\r\n          )}\r\n          {member.email && (\r\n            <a\r\n              href={`mailto:${member.email}`}\r\n              className=\"text-gray-400 hover:text-teal-500 transition-colors duration-300 transform hover:scale-110\"\r\n              aria-label={`Email ${member.name}`}\r\n            >\r\n              <Mail size={22} />\r\n            </a>\r\n          )}\r\n        </div>\r\n      )}\r\n\r\n      {/* Hover effect overlay */}\r\n      <div className=\"absolute inset-0 bg-gradient-to-t from-blue-50 to-transparent opacity-0 group-hover:opacity-10 transition-opacity duration-500\"></div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default TeamMemberCard;"], "names": [], "mappings": "AAAA,sCAAsC;;;;;AACtC;AACA;AAAA;AAAA;AAAA;;;;AAiBA,MAAM,iBAAgD,CAAC,EAAE,MAAM,EAAE;IAC/D,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;gBAAmF,OAAO;oBACvG,iBAAiB;oBACjB,gBAAgB;gBAClB;;;;;;0BAGA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;;;;;;;;;;0BAIjB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC,6HAAA,CAAA,UAAK;wBACJ,KAAK,OAAO,QAAQ;wBACpB,KAAK,OAAO,IAAI;wBAChB,OAAO;wBACP,QAAQ;wBACR,WAAU;;;;;;kCAEZ,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAIjB,8OAAC;gBAAG,WAAU;0BAA6G,OAAO,IAAI;;;;;;0BACtI,8OAAC;gBAAE,WAAU;0BAAuC,OAAO,IAAI;;;;;;YAG9D,OAAO,GAAG,kBACT,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,oMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;kCACjB,8OAAC;wBAAE,WAAU;kCAAiB,OAAO,GAAG;;;;;;;;;;;;YAK3C,CAAC,OAAO,QAAQ,IAAI,OAAO,OAAO,IAAI,OAAO,KAAK,mBACjD,8OAAC;gBAAI,WAAU;;oBACZ,OAAO,QAAQ,kBACd,8OAAC;wBACC,MAAM,OAAO,QAAQ;wBACrB,QAAO;wBACP,KAAI;wBACJ,WAAU;wBACV,cAAY,CAAC,oBAAoB,EAAE,OAAO,IAAI,EAAE;kCAEhD,cAAA,8OAAC,0MAAA,CAAA,WAAQ;4BAAC,MAAM;;;;;;;;;;;oBAGnB,OAAO,OAAO,kBACb,8OAAC;wBACC,MAAM,OAAO,OAAO;wBACpB,QAAO;wBACP,KAAI;wBACJ,WAAU;wBACV,cAAY,CAAC,mBAAmB,EAAE,OAAO,IAAI,EAAE;kCAE/C,cAAA,8OAAC,wMAAA,CAAA,UAAO;4BAAC,MAAM;;;;;;;;;;;oBAGlB,OAAO,KAAK,kBACX,8OAAC;wBACC,MAAM,CAAC,OAAO,EAAE,OAAO,KAAK,EAAE;wBAC9B,WAAU;wBACV,cAAY,CAAC,MAAM,EAAE,OAAO,IAAI,EAAE;kCAElC,cAAA,8OAAC,kMAAA,CAAA,OAAI;4BAAC,MAAM;;;;;;;;;;;;;;;;;0BAOpB,8OAAC;gBAAI,WAAU;;;;;;;;;;;;AAGrB;uCAEe", "debugId": null}}, {"offset": {"line": 271, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/fwu_incubation%28react%2Blaravel%29/incubation/frontend/src/app/components/about/AdvisoryBoardSection.tsx"], "sourcesContent": ["\"use client\"\r\nimport SectionTitle from '../shared/SectionTitle';\r\nimport TeamMemberCard, { Member } from './TeamMemberCard'; // Reusing the card\r\nimport { useEffect, useState, useRef } from 'react';\r\n\r\n// Advisory board members data\r\nconst advisoryBoardData: Member[] = [\r\n  {\r\n    id: 'ab1',\r\n    name: 'Dr. <PERSON><PERSON><PERSON>',\r\n    role: 'Chairman, University Grants Commission',\r\n    imageUrl: '/images/advisors/ugc-chairman.jpg',\r\n    bio: 'Providing strategic guidance on higher education policy and development.',\r\n    linkedin: 'https://linkedin.com/in/bishnuupreti'\r\n  },\r\n  {\r\n    id: 'ab2',\r\n    name: 'Prof. Dr. <PERSON><PERSON>',\r\n    role: 'Former Dean, Tribhuvan University',\r\n    imageUrl: '/images/advisors/tu-dean.jpg',\r\n    bio: 'Offering expertise in academic program development and quality assurance.',\r\n    linkedin: 'https://linkedin.com/in/bhimsubedi'\r\n  },\r\n  {\r\n    id: 'ab3',\r\n    name: 'Dr. <PERSON><PERSON>',\r\n    role: 'International Education Consultant',\r\n    imageUrl: '/images/advisors/education-consultant.jpg',\r\n    bio: 'Advising on international partnerships and global education standards.',\r\n    twitter: 'https://twitter.com/meenasharma'\r\n  },\r\n  {\r\n    id: 'ab4',\r\n    name: 'Mr. <PERSON>endra <PERSON>',\r\n    role: 'Industry Representative',\r\n    imageUrl: '/images/advisors/industry-rep.jpg',\r\n    bio: 'Bridging academia and industry to enhance employment opportunities for graduates.',\r\n    email: '<EMAIL>'\r\n  },\r\n  {\r\n    id: 'ab5',\r\n    name: 'Dr. Sushila Thapa',\r\n    role: 'Research & Innovation Expert',\r\n    imageUrl: '/images/advisors/research-expert.jpg',\r\n    bio: 'Guiding research initiatives and promoting innovation across disciplines.',\r\n    linkedin: 'https://linkedin.com/in/sushilathapa'\r\n  },\r\n  {\r\n    id: 'ab6',\r\n    name: 'Mr. Dipak Bhatta',\r\n    role: 'Community Representative',\r\n    imageUrl: '/images/advisors/community-rep.jpg',\r\n    bio: 'Representing community interests and facilitating university-community engagement.',\r\n    email: '<EMAIL>'\r\n  },\r\n];\r\n\r\nconst AdvisoryBoardSection = () => {\r\n  const [isVisible, setIsVisible] = useState(false);\r\n  const sectionRef = useRef(null);\r\n\r\n  useEffect(() => {\r\n    const observer = new IntersectionObserver(\r\n      ([entry]) => {\r\n        if (entry.isIntersecting) {\r\n          setIsVisible(true);\r\n          observer.disconnect();\r\n        }\r\n      },\r\n      { threshold: 0.1 }\r\n    );\r\n\r\n    // Store current ref value in a variable to use in cleanup\r\n    const currentRef = sectionRef.current;\r\n\r\n    if (currentRef) {\r\n      observer.observe(currentRef);\r\n    }\r\n\r\n    return () => {\r\n      // Use the stored ref value in cleanup\r\n      if (currentRef) {\r\n        observer.unobserve(currentRef);\r\n      }\r\n    };\r\n  }, []);\r\n\r\n  return (\r\n    <section ref={sectionRef} className=\"py-16 md:py-24 bg-gradient-to-b from-white to-blue-50 relative overflow-hidden\">\r\n      {/* Decorative elements */}\r\n      <div className=\"absolute top-0 left-0 w-full h-full opacity-5\" style={{\r\n        backgroundImage: 'url(\"data:image/svg+xml,%3Csvg width=\\'100\\' height=\\'100\\' viewBox=\\'0 0 100 100\\' xmlns=\\'http://www.w3.org/2000/svg\\'%3E%3Cpath d=\\'M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z\\' fill=\\'%234a90e2\\' fill-opacity=\\'0.1\\' fill-rule=\\'evenodd\\'/%3E%3C/svg%3E\")',\r\n      }}></div>\r\n\r\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 relative z-10\">\r\n        <div className={`transition-all duration-700 transform ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>\r\n          <SectionTitle title=\"Our Esteemed Advisory Board\" subtitle=\"Guidance & Expertise\" />\r\n          <p className=\"text-center text-gray-700 max-w-3xl mx-auto mb-12\">\r\n            Our advisory board brings together distinguished professionals from academia, industry, and the community\r\n            to provide strategic guidance and ensure the university&#39;s continued growth and relevance.\r\n          </p>\r\n        </div>\r\n\r\n        <div className=\"grid sm:grid-cols-2 lg:grid-cols-3 gap-8\">\r\n          {advisoryBoardData.map((member, index) => (\r\n            <div\r\n              key={member.id}\r\n              className={`transform transition-all duration-700 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-20'}`}\r\n              style={{ transitionDelay: `${index * 150}ms` }}\r\n            >\r\n              <TeamMemberCard member={member} />\r\n            </div>\r\n          ))}\r\n        </div>\r\n\r\n        {/* Quote section */}\r\n        <div className={`mt-16 text-center transition-all duration-700 delay-1000 transform ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>\r\n          <blockquote className=\"bg-white p-8 rounded-xl shadow-lg border border-blue-100 max-w-3xl mx-auto relative\">\r\n            {/* Large quote marks */}\r\n            <div className=\"absolute top-4 left-4 text-blue-200 opacity-50 text-6xl font-serif\">&ldquo;</div>\r\n            <div className=\"absolute bottom-4 right-4 text-blue-200 opacity-50 text-6xl font-serif\">&rdquo;</div>\r\n\r\n            <p className=\"text-xl text-gray-700 italic relative z-10 mb-4\">\r\n              The advisory board is committed to supporting Far Western University in its mission to provide quality education\r\n              and enhance research and innovation to meet the needs of society and contribute to national development.\r\n            </p>\r\n            <footer className=\"text-gray-600\">\r\n              <strong>Advisory Board Statement</strong>\r\n            </footer>\r\n          </blockquote>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\nexport default AdvisoryBoardSection;"], "names": [], "mappings": ";;;;AACA;AACA,oQAA2D,mBAAmB;AAC9E;AAHA;;;;;AAKA,8BAA8B;AAC9B,MAAM,oBAA8B;IAClC;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,UAAU;QACV,KAAK;QACL,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,UAAU;QACV,KAAK;QACL,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,UAAU;QACV,KAAK;QACL,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,UAAU;QACV,KAAK;QACL,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,UAAU;QACV,KAAK;QACL,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,UAAU;QACV,KAAK;QACL,OAAO;IACT;CACD;AAED,MAAM,uBAAuB;IAC3B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAE1B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,IAAI,qBACnB,CAAC,CAAC,MAAM;YACN,IAAI,MAAM,cAAc,EAAE;gBACxB,aAAa;gBACb,SAAS,UAAU;YACrB;QACF,GACA;YAAE,WAAW;QAAI;QAGnB,0DAA0D;QAC1D,MAAM,aAAa,WAAW,OAAO;QAErC,IAAI,YAAY;YACd,SAAS,OAAO,CAAC;QACnB;QAEA,OAAO;YACL,sCAAsC;YACtC,IAAI,YAAY;gBACd,SAAS,SAAS,CAAC;YACrB;QACF;IACF,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAQ,KAAK;QAAY,WAAU;;0BAElC,8OAAC;gBAAI,WAAU;gBAAgD,OAAO;oBACpE,iBAAiB;gBACnB;;;;;;0BAEA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAW,CAAC,sCAAsC,EAAE,YAAY,8BAA8B,4BAA4B;;0CAC7H,8OAAC,mJAAA,CAAA,UAAY;gCAAC,OAAM;gCAA8B,UAAS;;;;;;0CAC3D,8OAAC;gCAAE,WAAU;0CAAoD;;;;;;;;;;;;kCAMnE,8OAAC;wBAAI,WAAU;kCACZ,kBAAkB,GAAG,CAAC,CAAC,QAAQ,sBAC9B,8OAAC;gCAEC,WAAW,CAAC,sCAAsC,EAAE,YAAY,8BAA8B,4BAA4B;gCAC1H,OAAO;oCAAE,iBAAiB,GAAG,QAAQ,IAAI,EAAE,CAAC;gCAAC;0CAE7C,cAAA,8OAAC,oJAAA,CAAA,UAAc;oCAAC,QAAQ;;;;;;+BAJnB,OAAO,EAAE;;;;;;;;;;kCAUpB,8OAAC;wBAAI,WAAW,CAAC,mEAAmE,EAAE,YAAY,8BAA8B,4BAA4B;kCAC1J,cAAA,8OAAC;4BAAW,WAAU;;8CAEpB,8OAAC;oCAAI,WAAU;8CAAqE;;;;;;8CACpF,8OAAC;oCAAI,WAAU;8CAAyE;;;;;;8CAExF,8OAAC;oCAAE,WAAU;8CAAkD;;;;;;8CAI/D,8OAAC;oCAAO,WAAU;8CAChB,cAAA,8OAAC;kDAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOtB;uCAEe", "debugId": null}}, {"offset": {"line": 498, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/fwu_incubation%28react%2Blaravel%29/incubation/frontend/src/app/components/about/TeamSection.tsx"], "sourcesContent": ["\"use client\"\r\nimport SectionTitle from '../shared/SectionTitle';\r\nimport TeamMemberCard, { Member } from './TeamMemberCard';\r\nimport { useEffect, useState, useRef } from 'react';\r\n\r\n// University leadership team data\r\nconst teamMembersData: Member[] = [\r\n  {\r\n    id: 'tm1',\r\n    name: 'Prof. Dr. <PERSON><PERSON>',\r\n    role: 'Vice Chancellor',\r\n    imageUrl: '/images/team/vice-chancellor.jpg',\r\n    bio: 'Leading the university with a vision for academic excellence and innovation.',\r\n    linkedin: 'https://linkedin.com/in/hemrajpant',\r\n    email: '<EMAIL>'\r\n  },\r\n  {\r\n    id: 'tm2',\r\n    name: 'Prof. Dr. <PERSON><PERSON><PERSON>',\r\n    role: 'Registrar',\r\n    imageUrl: '/images/team/registrar.jpg',\r\n    bio: 'Overseeing administrative functions and institutional operations.',\r\n    linkedin: 'https://linkedin.com/in/narendradhami',\r\n    email: '<EMAIL>'\r\n  },\r\n  {\r\n    id: 'tm3',\r\n    name: 'Prof. Dr. <PERSON>',\r\n    role: 'Dean of Academic Affairs',\r\n    imageUrl: '/images/team/dean-academic.jpg',\r\n    bio: 'Coordinating academic programs and ensuring educational quality.',\r\n    twitter: 'https://twitter.com/santoshthapa',\r\n    email: '<EMAIL>'\r\n  },\r\n  {\r\n    id: 'tm4',\r\n    name: 'Dr. <PERSON>esh Joshi',\r\n    role: 'Director of Research',\r\n    imageUrl: '/images/team/research-director.jpg',\r\n    bio: 'Leading research initiatives and fostering innovation across disciplines.',\r\n    email: '<EMAIL>'\r\n  },\r\n];\r\n\r\nconst TeamSection = () => {\r\n  const [isVisible, setIsVisible] = useState(false);\r\n  const sectionRef = useRef(null);\r\n\r\n  useEffect(() => {\r\n    const observer = new IntersectionObserver(\r\n      ([entry]) => {\r\n        if (entry.isIntersecting) {\r\n          setIsVisible(true);\r\n          observer.disconnect();\r\n        }\r\n      },\r\n      { threshold: 0.1 }\r\n    );\r\n\r\n    // Store current ref value in a variable to use in cleanup\r\n    const currentRef = sectionRef.current;\r\n\r\n    if (currentRef) {\r\n      observer.observe(currentRef);\r\n    }\r\n\r\n    return () => {\r\n      // Use the stored ref value in cleanup\r\n      if (currentRef) {\r\n        observer.unobserve(currentRef);\r\n      }\r\n    };\r\n  }, []);\r\n\r\n  return (\r\n    <section ref={sectionRef} className=\"py-16 md:py-24 bg-gradient-to-b from-blue-50 to-white relative overflow-hidden\">\r\n      {/* Background pattern */}\r\n      <div className=\"absolute inset-0 opacity-5\">\r\n        <div className=\"absolute inset-0\" style={{\r\n          backgroundImage: 'radial-gradient(circle, #4a90e2 1px, transparent 1px)',\r\n          backgroundSize: '30px 30px'\r\n        }}></div>\r\n      </div>\r\n\r\n      {/* Decorative elements */}\r\n      <div className=\"absolute -top-20 -left-20 w-96 h-96 bg-blue-200 rounded-full opacity-20 blur-3xl\"></div>\r\n      <div className=\"absolute -bottom-20 -right-20 w-96 h-96 bg-teal-200 rounded-full opacity-20 blur-3xl\"></div>\r\n\r\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 relative z-10\">\r\n        <div className={`transition-all duration-700 transform ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>\r\n          <SectionTitle title=\"University Leadership\" subtitle=\"Guiding Excellence\" />\r\n          <p className=\"text-center text-gray-700 max-w-3xl mx-auto mb-12\">\r\n            Our dedicated leadership team brings together decades of academic and administrative experience\r\n            to guide Far Western University toward its vision of excellence in education and research.\r\n          </p>\r\n        </div>\r\n\r\n        <div className=\"grid sm:grid-cols-2 lg:grid-cols-4 gap-8\">\r\n          {teamMembersData.map((member, index) => (\r\n            <div\r\n              key={member.id}\r\n              className={`transform transition-all duration-700 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-20'}`}\r\n              style={{ transitionDelay: `${index * 200}ms` }}\r\n            >\r\n              <TeamMemberCard member={member} />\r\n            </div>\r\n          ))}\r\n        </div>\r\n\r\n        {/* Call to action */}\r\n        <div className={`mt-16 text-center transition-all duration-700 delay-1000 transform ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>\r\n          <div className=\"inline-block bg-white p-6 rounded-xl shadow-lg border border-blue-100 hover:border-blue-300 transition-all duration-300\">\r\n            <h3 className=\"text-xl font-bold text-blue-900 mb-2\">Join Our Academic Community</h3>\r\n            <p className=\"text-gray-700 mb-4\">\r\n              Far Western University welcomes talented faculty and staff to join our mission of educational excellence.\r\n            </p>\r\n            <a\r\n              href=\"/careers\"\r\n              className=\"inline-flex items-center bg-gradient-to-r from-blue-600 to-blue-700 text-white py-2 px-6 rounded-md hover:shadow-lg transition-all duration-300\"\r\n            >\r\n              Explore Opportunities\r\n              <svg className=\"w-4 h-4 ml-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M14 5l7 7m0 0l-7 7m7-7H3\"></path>\r\n              </svg>\r\n            </a>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\nexport default TeamSection;"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAHA;;;;;AAKA,kCAAkC;AAClC,MAAM,kBAA4B;IAChC;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,UAAU;QACV,KAAK;QACL,UAAU;QACV,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,UAAU;QACV,KAAK;QACL,UAAU;QACV,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,UAAU;QACV,KAAK;QACL,SAAS;QACT,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,UAAU;QACV,KAAK;QACL,OAAO;IACT;CACD;AAED,MAAM,cAAc;IAClB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAE1B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,IAAI,qBACnB,CAAC,CAAC,MAAM;YACN,IAAI,MAAM,cAAc,EAAE;gBACxB,aAAa;gBACb,SAAS,UAAU;YACrB;QACF,GACA;YAAE,WAAW;QAAI;QAGnB,0DAA0D;QAC1D,MAAM,aAAa,WAAW,OAAO;QAErC,IAAI,YAAY;YACd,SAAS,OAAO,CAAC;QACnB;QAEA,OAAO;YACL,sCAAsC;YACtC,IAAI,YAAY;gBACd,SAAS,SAAS,CAAC;YACrB;QACF;IACF,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAQ,KAAK;QAAY,WAAU;;0BAElC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;oBAAmB,OAAO;wBACvC,iBAAiB;wBACjB,gBAAgB;oBAClB;;;;;;;;;;;0BAIF,8OAAC;gBAAI,WAAU;;;;;;0BACf,8OAAC;gBAAI,WAAU;;;;;;0BAEf,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAW,CAAC,sCAAsC,EAAE,YAAY,8BAA8B,4BAA4B;;0CAC7H,8OAAC,mJAAA,CAAA,UAAY;gCAAC,OAAM;gCAAwB,UAAS;;;;;;0CACrD,8OAAC;gCAAE,WAAU;0CAAoD;;;;;;;;;;;;kCAMnE,8OAAC;wBAAI,WAAU;kCACZ,gBAAgB,GAAG,CAAC,CAAC,QAAQ,sBAC5B,8OAAC;gCAEC,WAAW,CAAC,sCAAsC,EAAE,YAAY,8BAA8B,4BAA4B;gCAC1H,OAAO;oCAAE,iBAAiB,GAAG,QAAQ,IAAI,EAAE,CAAC;gCAAC;0CAE7C,cAAA,8OAAC,oJAAA,CAAA,UAAc;oCAAC,QAAQ;;;;;;+BAJnB,OAAO,EAAE;;;;;;;;;;kCAUpB,8OAAC;wBAAI,WAAW,CAAC,mEAAmE,EAAE,YAAY,8BAA8B,4BAA4B;kCAC1J,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAuC;;;;;;8CACrD,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAGlC,8OAAC;oCACC,MAAK;oCACL,WAAU;;wCACX;sDAEC,8OAAC;4CAAI,WAAU;4CAAe,MAAK;4CAAO,QAAO;4CAAe,SAAQ;4CAAY,OAAM;sDACxF,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAY;gDAAI,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQrF;uCAEe", "debugId": null}}, {"offset": {"line": 744, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/fwu_incubation%28react%2Blaravel%29/incubation/frontend/src/app/components/about/HistoryTimeline.tsx"], "sourcesContent": ["\"use client\"\r\nimport SectionTitle from '../shared/SectionTitle';\r\nimport { useEffect, useState, useRef } from 'react';\r\nimport { Building2, GraduationCap, Handshake, Globe } from 'lucide-react';\r\n\r\nconst timelineEvents = [\r\n  {\r\n    year: '2010',\r\n    title: 'Establishment',\r\n    description: 'Far Western University was established by the Act of Parliament as a government-funded national university.',\r\n    icon: <Building2 />,\r\n    color: 'bg-blue-600'\r\n  },\r\n  {\r\n    year: '2012',\r\n    title: 'Academic Expansion',\r\n    description: 'Expanded academic programs with the introduction of multiple faculties and departments.',\r\n    icon: <GraduationCap />,\r\n    color: 'bg-teal-500'\r\n  },\r\n  {\r\n    year: '2015',\r\n    title: 'Research Center',\r\n    description: 'Established the Research, Innovation & Development Center to promote scholarly activities.',\r\n    icon: <Globe />,\r\n    color: 'bg-yellow-500'\r\n  },\r\n  {\r\n    year: '2020',\r\n    title: 'International Partnerships',\r\n    description: 'Formed strategic partnerships with international universities for academic collaboration and exchange programs.',\r\n    icon: <Handshake />,\r\n    color: 'bg-indigo-600'\r\n  },\r\n  {\r\n    year: '2023',\r\n    title: 'Digital Transformation',\r\n    description: 'Implemented comprehensive digital infrastructure to enhance teaching, learning, and administrative processes.',\r\n    icon: <Globe />,\r\n    color: 'bg-purple-600'\r\n  }\r\n];\r\n\r\nconst HistoryTimeline = () => {\r\n  const [isVisible, setIsVisible] = useState(false);\r\n  const sectionRef = useRef(null);\r\n\r\n  useEffect(() => {\r\n    const observer = new IntersectionObserver(\r\n      ([entry]) => {\r\n        if (entry.isIntersecting) {\r\n          setIsVisible(true);\r\n          observer.disconnect();\r\n        }\r\n      },\r\n      { threshold: 0.1 }\r\n    );\r\n\r\n    // Store current ref value in a variable to use in cleanup\r\n    const currentRef = sectionRef.current;\r\n\r\n    if (currentRef) {\r\n      observer.observe(currentRef);\r\n    }\r\n\r\n    return () => {\r\n      // Use the stored ref value in cleanup\r\n      if (currentRef) {\r\n        observer.unobserve(currentRef);\r\n      }\r\n    };\r\n  }, []);\r\n\r\n  return (\r\n    <section ref={sectionRef} className=\"py-16 md:py-24 bg-blue-50 relative overflow-hidden\">\r\n      {/* Background decorative elements */}\r\n      <div className=\"absolute top-0 left-0 w-full h-full\" style={{\r\n        backgroundImage: 'url(\"data:image/svg+xml,%3Csvg width=\\'60\\' height=\\'60\\' viewBox=\\'0 0 60 60\\' xmlns=\\'http://www.w3.org/2000/svg\\'%3E%3Cg fill=\\'none\\' fill-rule=\\'evenodd\\'%3E%3Cg fill=\\'%234a90e2\\' fill-opacity=\\'0.05\\'%3E%3Cpath d=\\'M36 34h4v1h-4v-1zm0-2h1v4h-1v-4zm2-2h1v1h-1v-1zm-2 2h1v1h-1v-1zm-2-2h1v1h-1v-1zm2-2h1v1h-1v-1zm-2 2h1v1h-1v-1zm-2-2h1v1h-1v-1z\\'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\")',\r\n      }}></div>\r\n\r\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 relative z-10\">\r\n        <div className={`transition-all duration-700 transform ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>\r\n          <SectionTitle title=\"Our Journey\" subtitle=\"History & Milestones\" />\r\n        </div>\r\n\r\n        <div className=\"mt-16 relative\">\r\n          {/* Vertical line */}\r\n          <div className=\"absolute left-1/2 transform -translate-x-1/2 h-full w-1 bg-blue-200 rounded-full\"></div>\r\n\r\n          {/* Timeline events */}\r\n          {timelineEvents.map((event, index) => (\r\n            <div\r\n              key={index}\r\n              className={`flex items-center mb-16 last:mb-0 transition-all duration-700 transform ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}\r\n              style={{ transitionDelay: `${index * 200}ms` }}\r\n            >\r\n              {/* Left side (odd indices) */}\r\n              {index % 2 === 0 ? (\r\n                <>\r\n                  <div className=\"w-1/2 pr-8 md:pr-16 text-right\">\r\n                    <div className=\"mb-2 text-3xl font-bold text-blue-700\">{event.year}</div>\r\n                    <h3 className=\"text-xl font-semibold text-blue-900 mb-2\">{event.title}</h3>\r\n                    <p className=\"text-gray-600\">{event.description}</p>\r\n                  </div>\r\n\r\n                  {/* Center icon */}\r\n                  <div className={`absolute left-1/2 transform -translate-x-1/2 w-12 h-12 rounded-full ${event.color} text-white flex items-center justify-center z-10 shadow-lg border-4 border-white`}>\r\n                    {event.icon}\r\n                  </div>\r\n\r\n                  {/* Right side (empty for odd indices) */}\r\n                  <div className=\"w-1/2 pl-8 md:pl-16\"></div>\r\n                </>\r\n              ) : (\r\n                <>\r\n                  {/* Left side (empty for even indices) */}\r\n                  <div className=\"w-1/2 pr-8 md:pr-16\"></div>\r\n\r\n                  {/* Center icon */}\r\n                  <div className={`absolute left-1/2 transform -translate-x-1/2 w-12 h-12 rounded-full ${event.color} text-white flex items-center justify-center z-10 shadow-lg border-4 border-white`}>\r\n                    {event.icon}\r\n                  </div>\r\n\r\n                  {/* Right side (even indices) */}\r\n                  <div className=\"w-1/2 pl-8 md:pl-16\">\r\n                    <div className=\"mb-2 text-3xl font-bold text-blue-700\">{event.year}</div>\r\n                    <h3 className=\"text-xl font-semibold text-blue-900 mb-2\">{event.title}</h3>\r\n                    <p className=\"text-gray-600\">{event.description}</p>\r\n                  </div>\r\n                </>\r\n              )}\r\n            </div>\r\n          ))}\r\n        </div>\r\n\r\n        {/* Future vision */}\r\n        <div className={`mt-16 text-center transition-all duration-700 delay-1000 transform ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>\r\n          <div className=\"inline-block bg-white p-6 rounded-xl shadow-lg border border-blue-100\">\r\n            <h3 className=\"text-2xl font-bold text-blue-900 mb-2\">Looking to the Future</h3>\r\n            <p className=\"text-gray-700\">\r\n              Far Western University continues to grow and evolve, committed to providing quality education\r\n              and enhancing research and innovation to meet the needs of society.\r\n            </p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\nexport default HistoryTimeline;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAHA;;;;;AAKA,MAAM,iBAAiB;IACrB;QACE,MAAM;QACN,OAAO;QACP,aAAa;QACb,oBAAM,8OAAC,gNAAA,CAAA,YAAS;;;;;QAChB,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;QACb,oBAAM,8OAAC,wNAAA,CAAA,gBAAa;;;;;QACpB,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;QACb,oBAAM,8OAAC,oMAAA,CAAA,QAAK;;;;;QACZ,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;QACb,oBAAM,8OAAC,4MAAA,CAAA,YAAS;;;;;QAChB,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;QACb,oBAAM,8OAAC,oMAAA,CAAA,QAAK;;;;;QACZ,OAAO;IACT;CACD;AAED,MAAM,kBAAkB;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAE1B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,IAAI,qBACnB,CAAC,CAAC,MAAM;YACN,IAAI,MAAM,cAAc,EAAE;gBACxB,aAAa;gBACb,SAAS,UAAU;YACrB;QACF,GACA;YAAE,WAAW;QAAI;QAGnB,0DAA0D;QAC1D,MAAM,aAAa,WAAW,OAAO;QAErC,IAAI,YAAY;YACd,SAAS,OAAO,CAAC;QACnB;QAEA,OAAO;YACL,sCAAsC;YACtC,IAAI,YAAY;gBACd,SAAS,SAAS,CAAC;YACrB;QACF;IACF,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAQ,KAAK;QAAY,WAAU;;0BAElC,8OAAC;gBAAI,WAAU;gBAAsC,OAAO;oBAC1D,iBAAiB;gBACnB;;;;;;0BAEA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAW,CAAC,sCAAsC,EAAE,YAAY,8BAA8B,4BAA4B;kCAC7H,cAAA,8OAAC,mJAAA,CAAA,UAAY;4BAAC,OAAM;4BAAc,UAAS;;;;;;;;;;;kCAG7C,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;;;;;4BAGd,eAAe,GAAG,CAAC,CAAC,OAAO,sBAC1B,8OAAC;oCAEC,WAAW,CAAC,wEAAwE,EAAE,YAAY,8BAA8B,4BAA4B;oCAC5J,OAAO;wCAAE,iBAAiB,GAAG,QAAQ,IAAI,EAAE,CAAC;oCAAC;8CAG5C,QAAQ,MAAM,kBACb;;0DACE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEAAyC,MAAM,IAAI;;;;;;kEAClE,8OAAC;wDAAG,WAAU;kEAA4C,MAAM,KAAK;;;;;;kEACrE,8OAAC;wDAAE,WAAU;kEAAiB,MAAM,WAAW;;;;;;;;;;;;0DAIjD,8OAAC;gDAAI,WAAW,CAAC,oEAAoE,EAAE,MAAM,KAAK,CAAC,iFAAiF,CAAC;0DAClL,MAAM,IAAI;;;;;;0DAIb,8OAAC;gDAAI,WAAU;;;;;;;qEAGjB;;0DAEE,8OAAC;gDAAI,WAAU;;;;;;0DAGf,8OAAC;gDAAI,WAAW,CAAC,oEAAoE,EAAE,MAAM,KAAK,CAAC,iFAAiF,CAAC;0DAClL,MAAM,IAAI;;;;;;0DAIb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEAAyC,MAAM,IAAI;;;;;;kEAClE,8OAAC;wDAAG,WAAU;kEAA4C,MAAM,KAAK;;;;;;kEACrE,8OAAC;wDAAE,WAAU;kEAAiB,MAAM,WAAW;;;;;;;;;;;;;;mCAnChD;;;;;;;;;;;kCA4CX,8OAAC;wBAAI,WAAW,CAAC,mEAAmE,EAAE,YAAY,8BAA8B,4BAA4B;kCAC1J,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,8OAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASzC;uCAEe", "debugId": null}}, {"offset": {"line": 1053, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/fwu_incubation%28react%2Blaravel%29/incubation/frontend/src/app/components/about/FacultyShowcase.tsx"], "sourcesContent": ["\"use client\"\r\nimport SectionTitle from '../shared/SectionTitle';\r\nimport { useEffect, useState, useRef } from 'react';\r\nimport Image from 'next/image';\r\nimport Link from 'next/link';\r\n// import { FiArrowRight } from 'react-icons/fi';\r\nimport { FlaskRound, Globe, UserCheck, Scale, Code, Leaf, Heart, TreePine, Users, ArrowRight } from 'lucide-react';\r\n\r\nconst faculties = [\r\n  {\r\n    id: 'science',\r\n    name: 'Faculty of Science & Technology',\r\n    description: 'Offering programs in Physics, Chemistry, Mathematics, Computer Science, and Information Technology.',\r\n    image: 'https://images.unsplash.com/photo-1532094349884-543bc11b234d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2000&q=80',\r\n    color: 'indigo',\r\n    icon: <FlaskRound className=\"w-5 h-5\" />\r\n  },\r\n  {\r\n    id: 'humanities',\r\n    name: 'Faculty of Humanities & Social Sciences',\r\n    description: 'Exploring human culture and society through programs in Literature, Sociology, Economics, and Philosophy.',\r\n    image: 'https://images.unsplash.com/photo-1523050854058-8df90110c9f1?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2000&q=80',\r\n    color: 'purple',\r\n    icon: <Users className=\"w-5 h-5\" />\r\n  },\r\n  {\r\n    id: 'education',\r\n    name: 'Faculty of Education',\r\n    description: 'Training the next generation of educators with innovative teaching methodologies and practices.',\r\n    image: 'https://images.unsplash.com/photo-1503676260728-1c00da094a0b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2000&q=80',\r\n    color: 'green',\r\n    icon: <UserCheck className=\"w-5 h-5\" />\r\n  },\r\n  {\r\n    id: 'management',\r\n    name: 'Faculty of Management',\r\n    description: 'Preparing future business leaders with programs in Business Administration, Finance, and Marketing.',\r\n    image: 'https://images.unsplash.com/photo-1552664730-d307ca884978?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2000&q=80',\r\n    color: 'blue',\r\n    icon: <Globe className=\"w-5 h-5\" />\r\n  },\r\n  {\r\n    id: 'law',\r\n    name: 'Faculty of Law',\r\n    description: 'Providing comprehensive legal education to develop skilled legal professionals.',\r\n    image: 'https://images.unsplash.com/photo-1589994965851-a8f479c573a9?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2000&q=80',\r\n    color: 'red',\r\n    icon: <Scale className=\"w-5 h-5\" />\r\n  },\r\n  {\r\n    id: 'engineering',\r\n    name: 'Faculty of Engineering',\r\n    description: 'Developing technical expertise through programs in Civil, Electrical, and Computer Engineering.',\r\n    image: 'https://images.unsplash.com/photo-1581094794329-c8112a89af12?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2000&q=80',\r\n    color: 'orange',\r\n    icon: <Code className=\"w-5 h-5\" />\r\n  },\r\n  {\r\n    id: 'agriculture',\r\n    name: 'Faculty of Agriculture',\r\n    description: 'Advancing agricultural knowledge and practices through research and education.',\r\n    image: 'https://images.unsplash.com/photo-1592982573971-2c0d8be9bcbf?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2000&q=80',\r\n    color: 'lime',\r\n    icon: <Leaf className=\"w-5 h-5\" />\r\n  },\r\n  {\r\n    id: 'health',\r\n    name: 'Faculty of Health Sciences',\r\n    description: 'Preparing healthcare professionals through programs in Nursing, Public Health, and more.',\r\n    image: 'https://images.unsplash.com/photo-1576091160550-2173dba999ef?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2000&q=80',\r\n    color: 'teal',\r\n    icon: <Heart className=\"w-5 h-5\" />\r\n  },\r\n  {\r\n    id: 'nrm',\r\n    name: 'Faculty of Natural Resource Management',\r\n    description: 'Focusing on sustainable management of natural resources and environmental conservation.',\r\n    image: 'https://images.unsplash.com/photo-1511497584788-876760111969?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2000&q=80',\r\n    color: 'emerald',\r\n    icon: <TreePine className=\"w-5 h-5\" />\r\n  }\r\n];\r\n\r\nconst FacultyShowcase = () => {\r\n  const [isVisible, setIsVisible] = useState(false);\r\n  const [activeIndex, setActiveIndex] = useState(0);\r\n  const sectionRef = useRef(null);\r\n\r\n  useEffect(() => {\r\n    const observer = new IntersectionObserver(\r\n      ([entry]) => {\r\n        if (entry.isIntersecting) {\r\n          setIsVisible(true);\r\n          observer.disconnect();\r\n        }\r\n      },\r\n      { threshold: 0.1 }\r\n    );\r\n\r\n    // Store current ref value in a variable to use in cleanup\r\n    const currentRef = sectionRef.current;\r\n\r\n    if (currentRef) {\r\n      observer.observe(currentRef);\r\n    }\r\n\r\n    return () => {\r\n      // Use the stored ref value in cleanup\r\n      if (currentRef) {\r\n        observer.unobserve(currentRef);\r\n      }\r\n    };\r\n  }, []);\r\n\r\n  // Auto-rotate faculties\r\n  useEffect(() => {\r\n    if (!isVisible) return;\r\n\r\n    const interval = setInterval(() => {\r\n      setActiveIndex((prev) => (prev + 1) % faculties.length);\r\n    }, 4000);\r\n\r\n    return () => clearInterval(interval);\r\n  }, [isVisible]);\r\n\r\n  const getColorClass = (color: string, type: 'bg' | 'text' | 'border' | 'hover-bg' | 'hover-text') => {\r\n    const colorMap: Record<string, Record<string, string>> = {\r\n      indigo: {\r\n        'bg': 'bg-indigo-600',\r\n        'text': 'text-indigo-600',\r\n        'border': 'border-indigo-600',\r\n        'hover-bg': 'hover:bg-indigo-700',\r\n        'hover-text': 'hover:text-indigo-700'\r\n      },\r\n      blue: {\r\n        'bg': 'bg-blue-600',\r\n        'text': 'text-blue-600',\r\n        'border': 'border-blue-600',\r\n        'hover-bg': 'hover:bg-blue-700',\r\n        'hover-text': 'hover:text-blue-700'\r\n      },\r\n      purple: {\r\n        'bg': 'bg-purple-600',\r\n        'text': 'text-purple-600',\r\n        'border': 'border-purple-600',\r\n        'hover-bg': 'hover:bg-purple-700',\r\n        'hover-text': 'hover:text-purple-700'\r\n      },\r\n      green: {\r\n        'bg': 'bg-green-600',\r\n        'text': 'text-green-600',\r\n        'border': 'border-green-600',\r\n        'hover-bg': 'hover:bg-green-700',\r\n        'hover-text': 'hover:text-green-700'\r\n      },\r\n      red: {\r\n        'bg': 'bg-red-600',\r\n        'text': 'text-red-600',\r\n        'border': 'border-red-600',\r\n        'hover-bg': 'hover:bg-red-700',\r\n        'hover-text': 'hover:text-red-700'\r\n      },\r\n      orange: {\r\n        'bg': 'bg-orange-600',\r\n        'text': 'text-orange-600',\r\n        'border': 'border-orange-600',\r\n        'hover-bg': 'hover:bg-orange-700',\r\n        'hover-text': 'hover:text-orange-700'\r\n      },\r\n      lime: {\r\n        'bg': 'bg-lime-600',\r\n        'text': 'text-lime-600',\r\n        'border': 'border-lime-600',\r\n        'hover-bg': 'hover:bg-lime-700',\r\n        'hover-text': 'hover:text-lime-700'\r\n      },\r\n      teal: {\r\n        'bg': 'bg-teal-600',\r\n        'text': 'text-teal-600',\r\n        'border': 'border-teal-600',\r\n        'hover-bg': 'hover:bg-teal-700',\r\n        'hover-text': 'hover:text-teal-700'\r\n      },\r\n      emerald: {\r\n        'bg': 'bg-emerald-600',\r\n        'text': 'text-emerald-600',\r\n        'border': 'border-emerald-600',\r\n        'hover-bg': 'hover:bg-emerald-700',\r\n        'hover-text': 'hover:text-emerald-700'\r\n      }\r\n    };\r\n\r\n    return colorMap[color][type] || colorMap['indigo'][type];\r\n  };\r\n\r\n  return (\r\n    <section ref={sectionRef} className=\"py-16 md:py-24 bg-gradient-to-b from-white to-indigo-50/50 relative overflow-hidden\">\r\n      {/* Background decorative elements */}\r\n      <div className=\"absolute top-0 right-0 w-[600px] h-[600px] bg-indigo-100 rounded-full opacity-20 blur-3xl -translate-x-1/3 -translate-y-1/3\"></div>\r\n      <div className=\"absolute bottom-0 left-0 w-[500px] h-[500px] bg-blue-100 rounded-full opacity-20 blur-3xl translate-x-1/4 translate-y-1/4\"></div>\r\n\r\n      {/* Subtle pattern overlay */}\r\n      <div className=\"absolute inset-0 opacity-5\"\r\n        style={{\r\n          backgroundImage: 'radial-gradient(#4338ca 1px, transparent 1px)',\r\n          backgroundSize: '40px 40px'\r\n        }}>\r\n      </div>\r\n\r\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 relative z-10\">\r\n        <div className={`transition-all duration-700 transform ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>\r\n          <SectionTitle\r\n            title=\"Our Academic Faculties\"\r\n            subtitle=\"Excellence in Diversity\"\r\n            accentColor=\"indigo\"\r\n          />\r\n          <p className=\"text-center text-lg text-gray-700 max-w-3xl mx-auto mb-12\">\r\n            Far Western University offers a diverse range of academic programs through nine specialized faculties,\r\n            providing comprehensive educational opportunities to students from all backgrounds.\r\n          </p>\r\n        </div>\r\n\r\n        {/* Faculty Cards Grid */}\r\n        <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-8\">\r\n          {faculties.map((faculty, index) => (\r\n            <div\r\n              key={faculty.id}\r\n              className={`bg-white rounded-xl shadow-lg overflow-hidden border border-gray-100 hover:border-${faculty.color}-200 transition-all duration-500 transform ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-20'} ${activeIndex === index ? `scale-105 shadow-xl z-10 border-${faculty.color}-300` : 'scale-100'} hover:-translate-y-1 group`}\r\n              style={{\r\n                transitionDelay: `${index * 100}ms`,\r\n              }}\r\n              onMouseEnter={() => setActiveIndex(index)}\r\n            >\r\n              {/* Faculty Image */}\r\n              <div className=\"relative h-48 overflow-hidden\">\r\n                <Image\r\n                  src={faculty.image}\r\n                  alt={faculty.name}\r\n                  fill\r\n                  sizes=\"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw\"\r\n                  className=\"object-cover object-center group-hover:scale-110 transition-transform duration-500\"\r\n                />\r\n                <div className={`absolute inset-0 bg-gradient-to-t from-${faculty.color}-900/80 to-transparent`}></div>\r\n                <div className=\"absolute bottom-0 left-0 right-0 p-4\">\r\n                  <div className=\"flex items-center\">\r\n                    <div className={`w-8 h-8 rounded-full ${getColorClass(faculty.color, 'bg')} flex items-center justify-center mr-3 shadow-lg`}>\r\n                      {faculty.icon}\r\n                    </div>\r\n                    <h3 className=\"text-white font-bold text-lg drop-shadow-md\">\r\n                      {faculty.name.split(' ').slice(-1)[0]}\r\n                    </h3>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Faculty Description */}\r\n              <div className=\"p-5\">\r\n                <h3 className={`text-lg font-semibold ${getColorClass(faculty.color, 'text')} mb-2 line-clamp-1`}>\r\n                  {faculty.name}\r\n                </h3>\r\n                <p className=\"text-gray-600 text-sm mb-4 line-clamp-2\">\r\n                  {faculty.description}\r\n                </p>\r\n                <Link\r\n                  href=\"/faculty\"\r\n                  className={`inline-flex items-center ${getColorClass(faculty.color, 'text')} font-medium text-sm ${getColorClass(faculty.color, 'hover-text')} transition-colors group/btn`}\r\n                >\r\n                  Learn More <ArrowRight className=\"ml-1 group-hover/btn:translate-x-1 transition-transform duration-300\" />\r\n                </Link>\r\n              </div>\r\n\r\n              {/* Decorative corner accent */}\r\n              <div className=\"absolute top-0 right-0 w-16 h-16 overflow-hidden\">\r\n                <div className={`absolute transform rotate-45 ${getColorClass(faculty.color, 'bg')} w-16 h-3 -top-2 -right-8 opacity-80`}></div>\r\n              </div>\r\n            </div>\r\n          ))}\r\n        </div>\r\n\r\n        <div className={`text-center mt-12 transition-all duration-700 delay-700 transform ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>\r\n          <Link\r\n            href=\"/faculty\"\r\n            className=\"inline-flex items-center bg-indigo-600 hover:bg-indigo-700 text-white font-medium py-3 px-8 rounded-lg transition-colors shadow-md hover:shadow-lg\"\r\n          >\r\n            Explore All Faculties <ArrowRight className=\"ml-2\" />\r\n          </Link>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\nexport default FacultyShowcase;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA,iDAAiD;AACjD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AANA;;;;;;;AAQA,MAAM,YAAY;IAChB;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,OAAO;QACP,oBAAM,8OAAC,kNAAA,CAAA,aAAU;YAAC,WAAU;;;;;;IAC9B;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,OAAO;QACP,oBAAM,8OAAC,oMAAA,CAAA,QAAK;YAAC,WAAU;;;;;;IACzB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,OAAO;QACP,oBAAM,8OAAC,gNAAA,CAAA,YAAS;YAAC,WAAU;;;;;;IAC7B;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,OAAO;QACP,oBAAM,8OAAC,oMAAA,CAAA,QAAK;YAAC,WAAU;;;;;;IACzB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,OAAO;QACP,oBAAM,8OAAC,oMAAA,CAAA,QAAK;YAAC,WAAU;;;;;;IACzB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,OAAO;QACP,oBAAM,8OAAC,kMAAA,CAAA,OAAI;YAAC,WAAU;;;;;;IACxB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,OAAO;QACP,oBAAM,8OAAC,kMAAA,CAAA,OAAI;YAAC,WAAU;;;;;;IACxB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,OAAO;QACP,oBAAM,8OAAC,oMAAA,CAAA,QAAK;YAAC,WAAU;;;;;;IACzB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,OAAO;QACP,oBAAM,8OAAC,8MAAA,CAAA,WAAQ;YAAC,WAAU;;;;;;IAC5B;CACD;AAED,MAAM,kBAAkB;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAE1B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,IAAI,qBACnB,CAAC,CAAC,MAAM;YACN,IAAI,MAAM,cAAc,EAAE;gBACxB,aAAa;gBACb,SAAS,UAAU;YACrB;QACF,GACA;YAAE,WAAW;QAAI;QAGnB,0DAA0D;QAC1D,MAAM,aAAa,WAAW,OAAO;QAErC,IAAI,YAAY;YACd,SAAS,OAAO,CAAC;QACnB;QAEA,OAAO;YACL,sCAAsC;YACtC,IAAI,YAAY;gBACd,SAAS,SAAS,CAAC;YACrB;QACF;IACF,GAAG,EAAE;IAEL,wBAAwB;IACxB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,WAAW;QAEhB,MAAM,WAAW,YAAY;YAC3B,eAAe,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,UAAU,MAAM;QACxD,GAAG;QAEH,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC;KAAU;IAEd,MAAM,gBAAgB,CAAC,OAAe;QACpC,MAAM,WAAmD;YACvD,QAAQ;gBACN,MAAM;gBACN,QAAQ;gBACR,UAAU;gBACV,YAAY;gBACZ,cAAc;YAChB;YACA,MAAM;gBACJ,MAAM;gBACN,QAAQ;gBACR,UAAU;gBACV,YAAY;gBACZ,cAAc;YAChB;YACA,QAAQ;gBACN,MAAM;gBACN,QAAQ;gBACR,UAAU;gBACV,YAAY;gBACZ,cAAc;YAChB;YACA,OAAO;gBACL,MAAM;gBACN,QAAQ;gBACR,UAAU;gBACV,YAAY;gBACZ,cAAc;YAChB;YACA,KAAK;gBACH,MAAM;gBACN,QAAQ;gBACR,UAAU;gBACV,YAAY;gBACZ,cAAc;YAChB;YACA,QAAQ;gBACN,MAAM;gBACN,QAAQ;gBACR,UAAU;gBACV,YAAY;gBACZ,cAAc;YAChB;YACA,MAAM;gBACJ,MAAM;gBACN,QAAQ;gBACR,UAAU;gBACV,YAAY;gBACZ,cAAc;YAChB;YACA,MAAM;gBACJ,MAAM;gBACN,QAAQ;gBACR,UAAU;gBACV,YAAY;gBACZ,cAAc;YAChB;YACA,SAAS;gBACP,MAAM;gBACN,QAAQ;gBACR,UAAU;gBACV,YAAY;gBACZ,cAAc;YAChB;QACF;QAEA,OAAO,QAAQ,CAAC,MAAM,CAAC,KAAK,IAAI,QAAQ,CAAC,SAAS,CAAC,KAAK;IAC1D;IAEA,qBACE,8OAAC;QAAQ,KAAK;QAAY,WAAU;;0BAElC,8OAAC;gBAAI,WAAU;;;;;;0BACf,8OAAC;gBAAI,WAAU;;;;;;0BAGf,8OAAC;gBAAI,WAAU;gBACb,OAAO;oBACL,iBAAiB;oBACjB,gBAAgB;gBAClB;;;;;;0BAGF,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAW,CAAC,sCAAsC,EAAE,YAAY,8BAA8B,4BAA4B;;0CAC7H,8OAAC,mJAAA,CAAA,UAAY;gCACX,OAAM;gCACN,UAAS;gCACT,aAAY;;;;;;0CAEd,8OAAC;gCAAE,WAAU;0CAA4D;;;;;;;;;;;;kCAO3E,8OAAC;wBAAI,WAAU;kCACZ,UAAU,GAAG,CAAC,CAAC,SAAS,sBACvB,8OAAC;gCAEC,WAAW,CAAC,kFAAkF,EAAE,QAAQ,KAAK,CAAC,2CAA2C,EAAE,YAAY,8BAA8B,2BAA2B,CAAC,EAAE,gBAAgB,QAAQ,CAAC,gCAAgC,EAAE,QAAQ,KAAK,CAAC,IAAI,CAAC,GAAG,YAAY,2BAA2B,CAAC;gCAC5V,OAAO;oCACL,iBAAiB,GAAG,QAAQ,IAAI,EAAE,CAAC;gCACrC;gCACA,cAAc,IAAM,eAAe;;kDAGnC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,6HAAA,CAAA,UAAK;gDACJ,KAAK,QAAQ,KAAK;gDAClB,KAAK,QAAQ,IAAI;gDACjB,IAAI;gDACJ,OAAM;gDACN,WAAU;;;;;;0DAEZ,8OAAC;gDAAI,WAAW,CAAC,uCAAuC,EAAE,QAAQ,KAAK,CAAC,sBAAsB,CAAC;;;;;;0DAC/F,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAW,CAAC,qBAAqB,EAAE,cAAc,QAAQ,KAAK,EAAE,MAAM,gDAAgD,CAAC;sEACzH,QAAQ,IAAI;;;;;;sEAEf,8OAAC;4DAAG,WAAU;sEACX,QAAQ,IAAI,CAAC,KAAK,CAAC,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE;;;;;;;;;;;;;;;;;;;;;;;kDAO7C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAW,CAAC,sBAAsB,EAAE,cAAc,QAAQ,KAAK,EAAE,QAAQ,kBAAkB,CAAC;0DAC7F,QAAQ,IAAI;;;;;;0DAEf,8OAAC;gDAAE,WAAU;0DACV,QAAQ,WAAW;;;;;;0DAEtB,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAW,CAAC,yBAAyB,EAAE,cAAc,QAAQ,KAAK,EAAE,QAAQ,qBAAqB,EAAE,cAAc,QAAQ,KAAK,EAAE,cAAc,4BAA4B,CAAC;;oDAC5K;kEACY,8OAAC,kNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;;;;;;;;kDAKrC,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAW,CAAC,6BAA6B,EAAE,cAAc,QAAQ,KAAK,EAAE,MAAM,oCAAoC,CAAC;;;;;;;;;;;;+BA/CrH,QAAQ,EAAE;;;;;;;;;;kCAqDrB,8OAAC;wBAAI,WAAW,CAAC,kEAAkE,EAAE,YAAY,8BAA8B,4BAA4B;kCACzJ,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;;gCACX;8CACuB,8OAAC,kNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMxD;uCAEe", "debugId": null}}, {"offset": {"line": 1549, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/fwu_incubation%28react%2Blaravel%29/incubation/frontend/src/app/components/home/<USER>"], "sourcesContent": ["// components/home/<USER>\n\"use client\"\r\nimport Link from 'next/link';\r\nimport Image from 'next/image';\r\nimport { useEffect, useState } from 'react';\r\nimport { ArrowRight, ChevronDown } from 'lucide-react';\r\n\r\nconst HeroBanner = () => {\r\n  const [isVisible, setIsVisible] = useState(false);\r\n\r\n  useEffect(() => {\r\n    setIsVisible(true);\r\n\r\n    // Add keyframes for animations\r\n    const styleElement = document.createElement('style');\r\n    styleElement.innerHTML = `\r\n      @keyframes float {\r\n        0%, 100% { transform: translateY(0); }\r\n        50% { transform: translateY(-20px); }\r\n      }\r\n      @keyframes pulse {\r\n        0%, 100% { opacity: 0.8; transform: scale(1); }\r\n        50% { opacity: 0.4; transform: scale(0.95); }\r\n      }\r\n      @keyframes scale {\r\n        0% { transform: scale(1.1); }\r\n        100% { transform: scale(1.2); }\r\n      }\r\n      @keyframes moveUpRight {\r\n        0% { transform: translate(0, 0); opacity: 0; }\r\n        10% { opacity: 1; }\r\n        90% { opacity: 1; }\r\n        100% { transform: translate(100px, -300px); opacity: 0; }\r\n      }\r\n      @keyframes moveLeftRight {\r\n        0%, 100% { transform: translateX(0); }\r\n        50% { transform: translateX(-2%); }\r\n      }\r\n      @keyframes spin-slow {\r\n        from { transform: rotate(0deg); }\r\n        to { transform: rotate(360deg); }\r\n      }\r\n    `;\r\n    document.head.appendChild(styleElement);\r\n\r\n    return () => {\r\n      document.head.removeChild(styleElement);\r\n    };\r\n  }, []);\r\n\r\n  return (\r\n    <div className=\"relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-indigo-900 via-blue-900 to-indigo-800\">\r\n      {/* Animated background elements */}\r\n      <div className=\"absolute inset-0 z-0\">\r\n        {/* Gradient overlay */}\r\n        <div className=\"absolute inset-0 bg-gradient-to-b from-transparent via-blue-900/50 to-indigo-900/80\"></div>\r\n\r\n        {/* Animated mesh grid */}\r\n        <div className=\"absolute inset-0 opacity-10\"\r\n          style={{\r\n            backgroundImage: 'linear-gradient(#4f46e5 1px, transparent 1px), linear-gradient(to right, #4f46e5 1px, transparent 1px)',\r\n            backgroundSize: '40px 40px'\r\n          }}>\r\n        </div>\r\n\r\n        {/* Animated circles */}\r\n        <div className=\"absolute top-1/4 left-10 w-64 h-64 bg-blue-500/20 rounded-full blur-3xl\"\r\n          style={{animation: 'pulse 8s ease-in-out infinite'}}></div>\r\n        <div className=\"absolute bottom-1/4 right-10 w-80 h-80 bg-indigo-500/20 rounded-full blur-3xl\"\r\n          style={{animation: 'pulse 10s ease-in-out infinite 1s'}}></div>\r\n        <div className=\"absolute top-1/3 right-1/4 w-40 h-40 bg-purple-500/20 rounded-full blur-2xl\"\r\n          style={{animation: 'pulse 7s ease-in-out infinite 0.5s'}}></div>\r\n\r\n        {/* Rotating gradient circle */}\r\n        <div className=\"absolute -top-40 -left-40 w-96 h-96 opacity-30\"\r\n          style={{\r\n            background: 'conic-gradient(from 0deg, #4f46e5, #3b82f6, #0ea5e9, #4f46e5)',\r\n            borderRadius: '50%',\r\n            animation: 'spin-slow 20s linear infinite'\r\n          }}>\r\n        </div>\r\n\r\n        {/* Animated particles */}\r\n        <div className=\"absolute inset-0 z-0\">\r\n          {[...Array(30)].map((_, i) => (\r\n            <div\r\n              key={i}\r\n              className=\"absolute rounded-full\"\r\n              style={{\r\n                width: `${Math.random() * 4 + 1}px`,\r\n                height: `${Math.random() * 4 + 1}px`,\r\n                backgroundColor: i % 3 === 0 ? '#93c5fd' : i % 3 === 1 ? '#c4b5fd' : '#bae6fd',\r\n                top: `${Math.random() * 100}%`,\r\n                left: `${Math.random() * 100}%`,\r\n                animation: `moveUpRight ${Math.random() * 15 + 15}s linear infinite`,\r\n                animationDelay: `${Math.random() * 5}s`,\r\n                opacity: Math.random() * 0.5 + 0.3\r\n              }}\r\n            ></div>\r\n          ))}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Main content */}\r\n      <div className=\"relative z-10 max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-20\">\r\n        <div className=\"flex flex-col lg:flex-row items-center gap-12\">\r\n          {/* Left content */}\r\n          <div className=\"lg:w-1/2 text-center lg:text-left\">\r\n            <div className={`transition-all duration-1000 transform ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>\r\n              {/* Badge */}\r\n              <div className=\"inline-block mb-6 px-3 py-1 bg-white/10 backdrop-blur-md rounded-full\">\r\n                <span className=\"text-blue-200 font-medium text-sm\">FWU Incubation Center</span>\r\n              </div>\r\n\r\n              {/* Heading */}\r\n              <h1 className=\"text-4xl sm:text-5xl md:text-6xl font-extrabold leading-tight mb-6\">\r\n                <span className=\"block mb-2 text-white\">Far Western University</span>\r\n                <span className=\"block text-transparent bg-clip-text bg-gradient-to-r from-blue-300 via-indigo-300 to-purple-300\">\r\n                  Empowering Innovation\r\n                </span>\r\n              </h1>\r\n\r\n              {/* Description */}\r\n              <p className=\"text-lg sm:text-xl text-blue-100 mb-8 max-w-xl mx-auto lg:mx-0 leading-relaxed\">\r\n                Join our incubation center and transform your innovative ideas into successful ventures with expert mentorship, resources, and a collaborative ecosystem.\r\n              </p>\r\n\r\n              {/* Stats */}\r\n              <div className=\"flex flex-wrap justify-center lg:justify-start gap-6 mb-8\">\r\n                <div className=\"flex flex-col items-center lg:items-start\">\r\n                  <span className=\"text-3xl font-bold text-white\">20+</span>\r\n                  <span className=\"text-blue-200 text-sm\">Startups Incubated</span>\r\n                </div>\r\n                <div className=\"flex flex-col items-center lg:items-start\">\r\n                  <span className=\"text-3xl font-bold text-white\">50+</span>\r\n                  <span className=\"text-blue-200 text-sm\">Expert Mentors</span>\r\n                </div>\r\n                <div className=\"flex flex-col items-center lg:items-start\">\r\n                  <span className=\"text-3xl font-bold text-white\">₹5M+</span>\r\n                  <span className=\"text-blue-200 text-sm\">Funding Secured</span>\r\n                </div>\r\n              </div>\r\n\r\n              {/* CTA Buttons */}\r\n              <div className={`flex flex-wrap gap-4 justify-center lg:justify-start transition-all duration-1000 delay-300 transform ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>\r\n                <Link href=\"/apply\" className=\"px-8 py-4 bg-gradient-to-r from-blue-500 to-indigo-600 text-white font-bold rounded-xl shadow-lg hover:shadow-indigo-500/30 transition-all duration-300 hover:-translate-y-1 flex items-center group\">\r\n                  Apply Now\r\n                  <ArrowRight className=\"ml-2 group-hover:translate-x-1 transition-transform\" />\r\n                </Link>\r\n                <Link href=\"/programs\" className=\"px-8 py-4 bg-white/10 backdrop-blur-md text-white font-bold rounded-xl border border-white/20 hover:bg-white/20 transition-all duration-300 hover:-translate-y-1\">\r\n                  Explore Programs\r\n                </Link>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Right content - 3D floating illustration */}\r\n          <div className={`lg:w-1/2 transition-all duration-1000 delay-500 transform ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>\r\n            <div className=\"relative h-[400px] md:h-[500px] w-full\">\r\n              {/* Main image with floating animation */}\r\n              <div className=\"absolute inset-0 flex items-center justify-center\" style={{animation: 'float 6s ease-in-out infinite'}}>\r\n                <div className=\"relative w-full h-full max-w-md mx-auto\">\r\n                  <Image\r\n                    src=\"https://images.unsplash.com/photo-1521737604893-d14cc237f11d?q=80&w=1000&auto=format&fit=crop\"\r\n                    alt=\"FWU Incubation Center\"\r\n                    fill\r\n                    className=\"object-cover rounded-2xl shadow-2xl\"\r\n                    priority\r\n                  />\r\n                  {/* Gradient overlay */}\r\n                  <div className=\"absolute inset-0 bg-gradient-to-tr from-indigo-900/40 to-transparent rounded-2xl\"></div>\r\n\r\n                  {/* Floating elements */}\r\n                  <div className=\"absolute -top-6 -right-6 bg-white rounded-2xl shadow-xl p-4 animate-float-slow\">\r\n                    <div className=\"flex items-center gap-3\">\r\n                      <div className=\"w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center\">\r\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6 text-blue-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\r\n                        </svg>\r\n                      </div>\r\n                      <div>\r\n                        <p className=\"text-sm font-bold text-gray-900\">Innovation Hub</p>\r\n                        <p className=\"text-xs text-gray-500\">Cutting-edge facilities</p>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n\r\n                  <div className=\"absolute -bottom-6 -left-6 bg-white rounded-2xl shadow-xl p-4\" style={{animation: 'float 5s ease-in-out infinite 1s'}}>\r\n                    <div className=\"flex items-center gap-3\">\r\n                      <div className=\"w-10 h-10 bg-indigo-100 rounded-full flex items-center justify-center\">\r\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6 text-indigo-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\" />\r\n                        </svg>\r\n                      </div>\r\n                      <div>\r\n                        <p className=\"text-sm font-bold text-gray-900\">Mentorship</p>\r\n                        <p className=\"text-xs text-gray-500\">Expert guidance</p>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Background glow effect */}\r\n              <div className=\"absolute inset-0 -z-10 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-full blur-3xl opacity-20 transform scale-75\"></div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Scroll Down Indicator */}\r\n      <div className={`absolute bottom-8 left-1/2 transform -translate-x-1/2 transition-all duration-1000 delay-700 ${isVisible ? 'opacity-100' : 'opacity-0'}`}>\r\n        <div className=\"flex flex-col items-center\">\r\n          <span className=\"text-blue-200 text-sm mb-2\">Scroll Down</span>\r\n          <div className=\"w-10 h-10 rounded-full border-2 border-blue-200 flex items-center justify-center animate-bounce\">\r\n            <ChevronDown className=\"text-blue-200\" />\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Bottom Wave */}\r\n      <div className=\"absolute bottom-0 left-0 right-0\">\r\n        <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 1440 120\" className=\"w-full h-auto\">\r\n          <path\r\n            fill=\"#ffffff\"\r\n            fillOpacity=\"1\"\r\n            d=\"M0,64L48,80C96,96,192,128,288,128C384,128,480,96,576,80C672,64,768,64,864,74.7C960,85,1056,107,1152,112C1248,117,1344,107,1392,101.3L1440,96L1440,120L1392,120C1344,120,1248,120,1152,120C1056,120,960,120,864,120C768,120,672,120,576,120C480,120,384,120,288,120C192,120,96,120,48,120L0,120Z\"\r\n          ></path>\r\n        </svg>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\n// Add CSS keyframes for animations\r\nconst styles = `\r\n@keyframes float {\r\n  0%, 100% { transform: translateY(0); }\r\n  50% { transform: translateY(-10px); }\r\n}\r\n@keyframes pulse {\r\n  0%, 100% { opacity: 1; transform: scale(1); }\r\n  50% { opacity: 0.8; transform: scale(0.95); }\r\n}\r\n@keyframes scale {\r\n  0% { transform: scale(1.1); }\r\n  100% { transform: scale(1.2); }\r\n}\r\n@keyframes moveUpRight {\r\n  0% { transform: translate(0, 0); opacity: 0; }\r\n  10% { opacity: 1; }\r\n  90% { opacity: 1; }\r\n  100% { transform: translate(100px, -300px); opacity: 0; }\r\n}\r\n@keyframes moveLeftRight {\r\n  0%, 100% { transform: translateX(0); }\r\n  50% { transform: translateX(-2%); }\r\n}\r\n`;\r\n\r\n// Add the styles to the document\r\nif (typeof document !== 'undefined') {\r\n  const styleElement = document.createElement('style');\r\n  styleElement.innerHTML = styles;\r\n  document.head.appendChild(styleElement);\r\n}\r\n\r\nexport default HeroBanner;"], "names": [], "mappings": "AAAA,iCAAiC;;;;;AAEjC;AACA;AACA;AACA;AAAA;AAJA;;;;;;AAMA,MAAM,aAAa;IACjB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,aAAa;QAEb,+BAA+B;QAC/B,MAAM,eAAe,SAAS,aAAa,CAAC;QAC5C,aAAa,SAAS,GAAG,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;IA2B1B,CAAC;QACD,SAAS,IAAI,CAAC,WAAW,CAAC;QAE1B,OAAO;YACL,SAAS,IAAI,CAAC,WAAW,CAAC;QAC5B;IACF,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;;;;;kCAGf,8OAAC;wBAAI,WAAU;wBACb,OAAO;4BACL,iBAAiB;4BACjB,gBAAgB;wBAClB;;;;;;kCAIF,8OAAC;wBAAI,WAAU;wBACb,OAAO;4BAAC,WAAW;wBAA+B;;;;;;kCACpD,8OAAC;wBAAI,WAAU;wBACb,OAAO;4BAAC,WAAW;wBAAmC;;;;;;kCACxD,8OAAC;wBAAI,WAAU;wBACb,OAAO;4BAAC,WAAW;wBAAoC;;;;;;kCAGzD,8OAAC;wBAAI,WAAU;wBACb,OAAO;4BACL,YAAY;4BACZ,cAAc;4BACd,WAAW;wBACb;;;;;;kCAIF,8OAAC;wBAAI,WAAU;kCACZ;+BAAI,MAAM;yBAAI,CAAC,GAAG,CAAC,CAAC,GAAG,kBACtB,8OAAC;gCAEC,WAAU;gCACV,OAAO;oCACL,OAAO,GAAG,KAAK,MAAM,KAAK,IAAI,EAAE,EAAE,CAAC;oCACnC,QAAQ,GAAG,KAAK,MAAM,KAAK,IAAI,EAAE,EAAE,CAAC;oCACpC,iBAAiB,IAAI,MAAM,IAAI,YAAY,IAAI,MAAM,IAAI,YAAY;oCACrE,KAAK,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;oCAC9B,MAAM,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;oCAC/B,WAAW,CAAC,YAAY,EAAE,KAAK,MAAM,KAAK,KAAK,GAAG,iBAAiB,CAAC;oCACpE,gBAAgB,GAAG,KAAK,MAAM,KAAK,EAAE,CAAC,CAAC;oCACvC,SAAS,KAAK,MAAM,KAAK,MAAM;gCACjC;+BAXK;;;;;;;;;;;;;;;;0BAkBb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAW,CAAC,uCAAuC,EAAE,YAAY,8BAA8B,4BAA4B;;kDAE9H,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDAAoC;;;;;;;;;;;kDAItD,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;gDAAK,WAAU;0DAAwB;;;;;;0DACxC,8OAAC;gDAAK,WAAU;0DAAkG;;;;;;;;;;;;kDAMpH,8OAAC;wCAAE,WAAU;kDAAiF;;;;;;kDAK9F,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAAgC;;;;;;kEAChD,8OAAC;wDAAK,WAAU;kEAAwB;;;;;;;;;;;;0DAE1C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAAgC;;;;;;kEAChD,8OAAC;wDAAK,WAAU;kEAAwB;;;;;;;;;;;;0DAE1C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAAgC;;;;;;kEAChD,8OAAC;wDAAK,WAAU;kEAAwB;;;;;;;;;;;;;;;;;;kDAK5C,8OAAC;wCAAI,WAAW,CAAC,sGAAsG,EAAE,YAAY,8BAA8B,4BAA4B;;0DAC7L,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;;oDAAuM;kEAEnO,8OAAC,kNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;;0DAExB,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAY,WAAU;0DAAmK;;;;;;;;;;;;;;;;;;;;;;;sCAQ1M,8OAAC;4BAAI,WAAW,CAAC,0DAA0D,EAAE,YAAY,8BAA8B,4BAA4B;sCACjJ,cAAA,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;wCAAoD,OAAO;4CAAC,WAAW;wCAA+B;kDACnH,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,6HAAA,CAAA,UAAK;oDACJ,KAAI;oDACJ,KAAI;oDACJ,IAAI;oDACJ,WAAU;oDACV,QAAQ;;;;;;8DAGV,8OAAC;oDAAI,WAAU;;;;;;8DAGf,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAI,OAAM;oEAA6B,WAAU;oEAAwB,MAAK;oEAAO,SAAQ;oEAAY,QAAO;8EAC/G,cAAA,8OAAC;wEAAK,eAAc;wEAAQ,gBAAe;wEAAQ,aAAa;wEAAG,GAAE;;;;;;;;;;;;;;;;0EAGzE,8OAAC;;kFACC,8OAAC;wEAAE,WAAU;kFAAkC;;;;;;kFAC/C,8OAAC;wEAAE,WAAU;kFAAwB;;;;;;;;;;;;;;;;;;;;;;;8DAK3C,8OAAC;oDAAI,WAAU;oDAAgE,OAAO;wDAAC,WAAW;oDAAkC;8DAClI,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAI,OAAM;oEAA6B,WAAU;oEAA0B,MAAK;oEAAO,SAAQ;oEAAY,QAAO;8EACjH,cAAA,8OAAC;wEAAK,eAAc;wEAAQ,gBAAe;wEAAQ,aAAa;wEAAG,GAAE;;;;;;;;;;;;;;;;0EAGzE,8OAAC;;kFACC,8OAAC;wEAAE,WAAU;kFAAkC;;;;;;kFAC/C,8OAAC;wEAAE,WAAU;kFAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAQ/C,8OAAC;wCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOvB,8OAAC;gBAAI,WAAW,CAAC,6FAA6F,EAAE,YAAY,gBAAgB,aAAa;0BACvJ,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAK,WAAU;sCAA6B;;;;;;sCAC7C,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,oNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;0BAM7B,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,OAAM;oBAA6B,SAAQ;oBAAe,WAAU;8BACvE,cAAA,8OAAC;wBACC,MAAK;wBACL,aAAY;wBACZ,GAAE;;;;;;;;;;;;;;;;;;;;;;AAMd;AAEA,mCAAmC;AACnC,MAAM,SAAS,CAAC;;;;;;;;;;;;;;;;;;;;;;;AAuBhB,CAAC;AAED,iCAAiC;AACjC,IAAI,OAAO,aAAa,aAAa;IACnC,MAAM,eAAe,SAAS,aAAa,CAAC;IAC5C,aAAa,SAAS,GAAG;IACzB,SAAS,IAAI,CAAC,WAAW,CAAC;AAC5B;uCAEe", "debugId": null}}, {"offset": {"line": 2209, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/fwu_incubation%28react%2Blaravel%29/incubation/frontend/src/app/about/page.tsx"], "sourcesContent": ["\"use client\"\r\nimport { useEffect, useState, useRef } from 'react';\r\nimport AdvisoryBoardSection from '../components/about/AdvisoryBoardSection';\r\nimport IntroAbout from '../components/about/IntroAbout';\r\nimport TeamSection from '../components/about/TeamSection';\r\nimport HistoryTimeline from '../components/about/HistoryTimeline';\r\nimport FacultyShowcase from '../components/about/FacultyShowcase';\r\nimport Image from 'next/image';\r\nimport Link from 'next/link';\r\nimport { Building2, GraduationCap, Users, ChevronDown, ArrowRight, Book, Award, MapPin, Calendar } from 'lucide-react';\r\nimport HeroBanner from '../components/home/<USER>';\r\n\r\nexport default function AboutPage() {\r\n  const [isVisible, setIsVisible] = useState(false);\r\n  const [statsVisible, setStatsVisible] = useState(false);\r\n  const heroRef = useRef(null);\r\n  const statsRef = useRef(null);\r\n\r\n  useEffect(() => {\r\n    setIsVisible(true);\r\n\r\n    const heroObserver = new IntersectionObserver(\r\n      ([entry]) => {\r\n        if (entry.isIntersecting) {\r\n          setIsVisible(true);\r\n          heroObserver.disconnect();\r\n        }\r\n      },\r\n      { threshold: 0.1 }\r\n    );\r\n\r\n    const statsObserver = new IntersectionObserver(\r\n      ([entry]) => {\r\n        if (entry.isIntersecting) {\r\n          setStatsVisible(true);\r\n          statsObserver.disconnect();\r\n        }\r\n      },\r\n      { threshold: 0.1 }\r\n    );\r\n\r\n    // Store current ref values in variables to use in cleanup\r\n    const currentHeroRef = heroRef.current;\r\n    const currentStatsRef = statsRef.current;\r\n\r\n    if (currentHeroRef) {\r\n      heroObserver.observe(currentHeroRef);\r\n    }\r\n\r\n    if (currentStatsRef) {\r\n      statsObserver.observe(currentStatsRef);\r\n    }\r\n\r\n    return () => {\r\n      // Use the stored ref values in cleanup\r\n      if (currentHeroRef) {\r\n        heroObserver.unobserve(currentHeroRef);\r\n      }\r\n      if (currentStatsRef) {\r\n        statsObserver.unobserve(currentStatsRef);\r\n      }\r\n    };\r\n  }, []);\r\n\r\n  return (\r\n    <>\r\n      <main className=\"overflow-hidden\">\r\n        {/* Hero Banner with Parallax and Animation Effects */}\r\n       \r\n         <HeroBanner/>\r\n        <HistoryTimeline />\r\n        <FacultyShowcase />\r\n\r\n        {/* Recent News & Events Section */}\r\n        <section className=\"py-20 bg-gradient-to-b from-indigo-50 to-white relative overflow-hidden\">\r\n          <div className=\"absolute top-0 right-0 w-96 h-96 bg-indigo-100 rounded-full opacity-30 -translate-x-1/2 -translate-y-1/2\"></div>\r\n          <div className=\"absolute bottom-0 left-0 w-64 h-64 bg-blue-100 rounded-full opacity-30 translate-x-1/4 translate-y-1/4\"></div>\r\n\r\n          <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 relative z-10\">\r\n            <div className=\"text-center mb-16\">\r\n              <h2 className=\"text-3xl md:text-4xl font-bold text-indigo-900 mb-4\">Latest News & Events</h2>\r\n              <div className=\"w-24 h-1 bg-indigo-600 mx-auto mb-6 rounded-full\"></div>\r\n              <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\r\n                Stay updated with the latest happenings, announcements, and events at Far Western University.\r\n              </p>\r\n            </div>\r\n\r\n            <div className=\"grid md:grid-cols-3 gap-8\">\r\n              {/* News Card 1 */}\r\n              <div className=\"bg-white rounded-xl shadow-lg overflow-hidden group hover:shadow-xl transition-all duration-300 border border-gray-100 hover:border-indigo-100\">\r\n                <div className=\"relative h-48 overflow-hidden\">\r\n                  <Image\r\n                    src=\"https://images.unsplash.com/photo-1523050854058-8df90110c9f1?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80\"\r\n                    alt=\"Vice Chancellor Visit to Israel\"\r\n                    fill\r\n                    className=\"object-cover group-hover:scale-110 transition-transform duration-500\"\r\n                  />\r\n                  <div className=\"absolute inset-0 bg-gradient-to-t from-indigo-900/80 to-transparent\"></div>\r\n                  <div className=\"absolute bottom-4 left-4 bg-white/90 text-indigo-600 text-xs font-bold px-3 py-1 rounded-full flex items-center\">\r\n                    <Calendar className=\"mr-1\" />\r\n                    Apr 6, 2025\r\n                  </div>\r\n                </div>\r\n                <div className=\"p-6\">\r\n                  <h3 className=\"text-xl font-bold text-indigo-900 mb-3 group-hover:text-indigo-600 transition-colors\">\r\n                    Vice Chancellor Visit to Israel\r\n                  </h3>\r\n                  <p className=\"text-gray-600 mb-4 line-clamp-3\">\r\n                    Vice Chancellor of Far Western University Prof. Hem Raj Pant visited Israel from march 13 to 20, 2025. During his visit to Israel, the discussion with government professionals...\r\n                  </p>\r\n                  <Link href=\"#\" className=\"text-indigo-600 font-medium flex items-center group/link\">\r\n                    Read More\r\n                    <ArrowRight className=\"ml-2 group-hover/link:translate-x-1 transition-transform duration-300\" size={14} />\r\n                  </Link>\r\n                </div>\r\n              </div>\r\n\r\n              {/* News Card 2 */}\r\n              <div className=\"bg-white rounded-xl shadow-lg overflow-hidden group hover:shadow-xl transition-all duration-300 border border-gray-100 hover:border-indigo-100\">\r\n                <div className=\"relative h-48 overflow-hidden\">\r\n                  <Image\r\n                    src=\"https://images.unsplash.com/photo-1524178232363-1fb2b075b655?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80\"\r\n                    alt=\"MOU Among Universities\"\r\n                    fill\r\n                    className=\"object-cover group-hover:scale-110 transition-transform duration-500\"\r\n                  />\r\n                  <div className=\"absolute inset-0 bg-gradient-to-t from-indigo-900/80 to-transparent\"></div>\r\n                  <div className=\"absolute bottom-4 left-4 bg-white/90 text-indigo-600 text-xs font-bold px-3 py-1 rounded-full flex items-center\">\r\n                    <Calendar className=\"mr-1\" />\r\n                    Feb 9, 2025\r\n                  </div>\r\n                </div>\r\n                <div className=\"p-6\">\r\n                  <h3 className=\"text-xl font-bold text-indigo-900 mb-3 group-hover:text-indigo-600 transition-colors\">\r\n                    MOU Among Five Universities\r\n                  </h3>\r\n                  <p className=\"text-gray-600 mb-4 line-clamp-3\">\r\n                    Far Western University signed a Memorandum of Understanding with Mid-West University, Nepal Open University, Purbanchal University and Lumbini Technological University...\r\n                  </p>\r\n                  <Link href=\"#\" className=\"text-indigo-600 font-medium flex items-center group/link\">\r\n                    Read More\r\n                    <ArrowRight className=\"ml-2 group-hover/link:translate-x-1 transition-transform duration-300\" size={14} />\r\n                  </Link>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Event Card */}\r\n              <div className=\"bg-white rounded-xl shadow-lg overflow-hidden group hover:shadow-xl transition-all duration-300 border border-gray-100 hover:border-indigo-100\">\r\n                <div className=\"relative h-48 overflow-hidden\">\r\n                  <Image\r\n                    src=\"https://images.unsplash.com/photo-1540575467063-178a50c2df87?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80\"\r\n                    alt=\"International Conference\"\r\n                    fill\r\n                    className=\"object-cover group-hover:scale-110 transition-transform duration-500\"\r\n                  />\r\n                  <div className=\"absolute inset-0 bg-gradient-to-t from-indigo-900/80 to-transparent\"></div>\r\n                  <div className=\"absolute top-4 right-4 bg-indigo-600/90 text-white text-xs font-bold px-3 py-1 rounded-full\">\r\n                    Upcoming Event\r\n                  </div>\r\n                </div>\r\n                <div className=\"p-6\">\r\n                  <div className=\"flex items-center mb-3\">\r\n                    <div className=\"mr-3 text-center\">\r\n                      <div className=\"text-2xl font-bold text-indigo-600\">25</div>\r\n                      <div className=\"text-xs text-gray-500 uppercase\">June</div>\r\n                    </div>\r\n                    <div className=\"border-l border-gray-200 pl-3\">\r\n                      <h3 className=\"text-xl font-bold text-indigo-900 group-hover:text-indigo-600 transition-colors\">\r\n                        International Conference 2025\r\n                      </h3>\r\n                      <div className=\"flex items-center text-gray-500 text-sm mt-1\">\r\n                        <MapPin className=\"mr-1\" />\r\n                        University Central Office\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                  <p className=\"text-gray-600 mb-4 line-clamp-3\">\r\n                    Join us for the International Conference on Advanced Functional Materials 2025 at Himalaya Hotel Kathmandu in partnership with Far Western University...\r\n                  </p>\r\n                  <Link href=\"#\" className=\"text-indigo-600 font-medium flex items-center group/link\">\r\n                    Learn More\r\n                    <ArrowRight className=\"ml-2 group-hover/link:translate-x-1 transition-transform duration-300\" size={14} />\r\n                  </Link>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"text-center mt-12\">\r\n              <Link\r\n                href=\"#\"\r\n                className=\"inline-flex items-center bg-indigo-600 hover:bg-indigo-700 text-white font-medium py-3 px-8 rounded-lg shadow-md transition-all duration-300 hover:-translate-y-1\"\r\n              >\r\n                View All News & Events\r\n                <ArrowRight className=\"ml-2\" />\r\n              </Link>\r\n            </div>\r\n          </div>\r\n        </section>\r\n\r\n        <TeamSection />\r\n        <AdvisoryBoardSection />\r\n\r\n        {/* CSS Animations */}\r\n        <style jsx>{`\r\n          @keyframes float {\r\n            0%, 100% { transform: translateY(0) scale(1); }\r\n            50% { transform: translateY(-20px) scale(1.2); }\r\n          }\r\n          @keyframes wave {\r\n            0%, 100% { transform: translateX(0); }\r\n            50% { transform: translateX(-2%); }\r\n          }\r\n        `}</style>\r\n      </main>\r\n    </>\r\n  );\r\n}"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAVA;;;;;;;;;;;;AAYe,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACvB,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAExB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,aAAa;QAEb,MAAM,eAAe,IAAI,qBACvB,CAAC,CAAC,MAAM;YACN,IAAI,MAAM,cAAc,EAAE;gBACxB,aAAa;gBACb,aAAa,UAAU;YACzB;QACF,GACA;YAAE,WAAW;QAAI;QAGnB,MAAM,gBAAgB,IAAI,qBACxB,CAAC,CAAC,MAAM;YACN,IAAI,MAAM,cAAc,EAAE;gBACxB,gBAAgB;gBAChB,cAAc,UAAU;YAC1B;QACF,GACA;YAAE,WAAW;QAAI;QAGnB,0DAA0D;QAC1D,MAAM,iBAAiB,QAAQ,OAAO;QACtC,MAAM,kBAAkB,SAAS,OAAO;QAExC,IAAI,gBAAgB;YAClB,aAAa,OAAO,CAAC;QACvB;QAEA,IAAI,iBAAiB;YACnB,cAAc,OAAO,CAAC;QACxB;QAEA,OAAO;YACL,uCAAuC;YACvC,IAAI,gBAAgB;gBAClB,aAAa,SAAS,CAAC;YACzB;YACA,IAAI,iBAAiB;gBACnB,cAAc,SAAS,CAAC;YAC1B;QACF;IACF,GAAG,EAAE;IAEL,qBACE;kBACE,cAAA,8OAAC;sDAAe;;8BAGb,8OAAC,+IAAA,CAAA,UAAU;;;;;8BACZ,8OAAC,qJAAA,CAAA,UAAe;;;;;8BAChB,8OAAC,qJAAA,CAAA,UAAe;;;;;8BAGhB,8OAAC;8DAAkB;;sCACjB,8OAAC;sEAAc;;;;;;sCACf,8OAAC;sEAAc;;;;;;sCAEf,8OAAC;sEAAc;;8CACb,8OAAC;8EAAc;;sDACb,8OAAC;sFAAa;sDAAsD;;;;;;sDACpE,8OAAC;sFAAc;;;;;;sDACf,8OAAC;sFAAY;sDAA0C;;;;;;;;;;;;8CAKzD,8OAAC;8EAAc;;sDAEb,8OAAC;sFAAc;;8DACb,8OAAC;8FAAc;;sEACb,8OAAC,6HAAA,CAAA,UAAK;4DACJ,KAAI;4DACJ,KAAI;4DACJ,IAAI;4DACJ,WAAU;;;;;;sEAEZ,8OAAC;sGAAc;;;;;;sEACf,8OAAC;sGAAc;;8EACb,8OAAC,0MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;gEAAS;;;;;;;;;;;;;8DAIjC,8OAAC;8FAAc;;sEACb,8OAAC;sGAAa;sEAAuF;;;;;;sEAGrG,8OAAC;sGAAY;sEAAkC;;;;;;sEAG/C,8OAAC,4JAAA,CAAA,UAAI;4DAAC,MAAK;4DAAI,WAAU;;gEAA2D;8EAElF,8OAAC,kNAAA,CAAA,aAAU;oEAAC,WAAU;oEAAwE,MAAM;;;;;;;;;;;;;;;;;;;;;;;;sDAM1G,8OAAC;sFAAc;;8DACb,8OAAC;8FAAc;;sEACb,8OAAC,6HAAA,CAAA,UAAK;4DACJ,KAAI;4DACJ,KAAI;4DACJ,IAAI;4DACJ,WAAU;;;;;;sEAEZ,8OAAC;sGAAc;;;;;;sEACf,8OAAC;sGAAc;;8EACb,8OAAC,0MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;gEAAS;;;;;;;;;;;;;8DAIjC,8OAAC;8FAAc;;sEACb,8OAAC;sGAAa;sEAAuF;;;;;;sEAGrG,8OAAC;sGAAY;sEAAkC;;;;;;sEAG/C,8OAAC,4JAAA,CAAA,UAAI;4DAAC,MAAK;4DAAI,WAAU;;gEAA2D;8EAElF,8OAAC,kNAAA,CAAA,aAAU;oEAAC,WAAU;oEAAwE,MAAM;;;;;;;;;;;;;;;;;;;;;;;;sDAM1G,8OAAC;sFAAc;;8DACb,8OAAC;8FAAc;;sEACb,8OAAC,6HAAA,CAAA,UAAK;4DACJ,KAAI;4DACJ,KAAI;4DACJ,IAAI;4DACJ,WAAU;;;;;;sEAEZ,8OAAC;sGAAc;;;;;;sEACf,8OAAC;sGAAc;sEAA8F;;;;;;;;;;;;8DAI/G,8OAAC;8FAAc;;sEACb,8OAAC;sGAAc;;8EACb,8OAAC;8GAAc;;sFACb,8OAAC;sHAAc;sFAAqC;;;;;;sFACpD,8OAAC;sHAAc;sFAAkC;;;;;;;;;;;;8EAEnD,8OAAC;8GAAc;;sFACb,8OAAC;sHAAa;sFAAkF;;;;;;sFAGhG,8OAAC;sHAAc;;8FACb,8OAAC,0MAAA,CAAA,SAAM;oFAAC,WAAU;;;;;;gFAAS;;;;;;;;;;;;;;;;;;;sEAKjC,8OAAC;sGAAY;sEAAkC;;;;;;sEAG/C,8OAAC,4JAAA,CAAA,UAAI;4DAAC,MAAK;4DAAI,WAAU;;gEAA2D;8EAElF,8OAAC,kNAAA,CAAA,aAAU;oEAAC,WAAU;oEAAwE,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAM5G,8OAAC;8EAAc;8CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;;4CACX;0DAEC,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAM9B,8OAAC,iJAAA,CAAA,UAAW;;;;;8BACZ,8OAAC,0JAAA,CAAA,UAAoB;;;;;;;;;;;;;;;;AAgB7B", "debugId": null}}]}