"use client";
import { SidebarProvider } from "@/components/ui/sidebar";
import React from "react";
import { DashBoardSidebar } from "./_components/DashBoardSidebar";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import DashboardHeader from "./_components/DashboardHeader";
function DashboardProvider({ children }: { children: React.ReactNode }) {
  const queryClient = new QueryClient();
  return (
    <QueryClientProvider client={queryClient}>
      <SidebarProvider>
        {/* You can add any additional context providers here if needed */}
        <DashBoardSidebar />
        <div className=" w-full bg-gray-200">
          <div className="sticky top-0 z-30  backdrop-blur-2xl">
            <DashboardHeader />
          </div>
          <div className="fixed inset-0 overflow-hidden pointer-events-none">
            <div className="absolute -top-4 -right-4 w-72 h-72 bg-gradient-to-br from-blue-400 to-purple-400 rounded-full opacity-10 animate-pulse"></div>
            <div
              className="absolute -bottom-8 -left-8 w-96 h-96 bg-gradient-to-br from-pink-400 to-red-400 rounded-full opacity-10 animate-pulse"
              style={{ animationDelay: "2s" }}
            ></div>
          </div>
          {children}
        </div>
      </SidebarProvider>
    </QueryClientProvider>
  );
}

export default DashboardProvider;
