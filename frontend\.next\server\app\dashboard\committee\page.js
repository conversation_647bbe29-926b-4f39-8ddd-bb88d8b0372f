(()=>{var e={};e.id=695,e.ids=[695],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},79551:e=>{"use strict";e.exports=require("url")},90538:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>o.a,__next_app__:()=>p,pages:()=>l,routeModule:()=>c,tree:()=>u});var a=t(65239),n=t(48088),i=t(88170),o=t.n(i),s=t(30893),d={};for(let e in s)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>s[e]);t.d(r,d);let u={children:["",{children:["dashboard",{children:["committee",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,98421)),"D:\\fwu_incubation(react+laravel)\\incubation\\frontend\\src\\app\\dashboard\\committee\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,63144)),"D:\\fwu_incubation(react+laravel)\\incubation\\frontend\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,53862)),"D:\\fwu_incubation(react+laravel)\\incubation\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,l=["D:\\fwu_incubation(react+laravel)\\incubation\\frontend\\src\\app\\dashboard\\committee\\page.tsx"],p={require:t,loadChunk:()=>Promise.resolve()},c=new a.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/dashboard/committee/page",pathname:"/dashboard/committee",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},96487:()=>{},98421:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>n});var a=t(37413);t(61120);let n=function(){return(0,a.jsx)("div",{children:"CommitteePage"})}}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[447,172,658,272,54,475],()=>t(90538));module.exports=a})();