{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/fwu_incubation%28react%2Blaravel%29/incubation/frontend/src/app/components/projects/ProjectHero.tsx"], "sourcesContent": ["\"use client\"\r\nimport { useState, useEffect } from 'react';\r\nimport { Search } from 'lucide-react';\r\nimport Image from 'next/image';\r\n\r\ninterface ProjectHeroProps {\r\n  searchTerm: string;\r\n  onSearchChange: (value: string) => void;\r\n}\r\n\r\nconst ProjectHero: React.FC<ProjectHeroProps> = ({ searchTerm, onSearchChange }) => {\r\n  const [isVisible, setIsVisible] = useState(false);\r\n\r\n  useEffect(() => {\r\n    setIsVisible(true);\r\n  }, []);\r\n\r\n  return (\r\n    <section className=\"relative bg-gradient-to-r from-indigo-900 via-purple-800 to-indigo-900 text-white overflow-hidden\">\r\n      {/* Background elements */}\r\n      <div className=\"absolute inset-0 overflow-hidden\">\r\n        <div className=\"absolute top-20 left-10 w-64 h-64 rounded-full bg-purple-500 opacity-10 animate-float-slow\"></div>\r\n        <div className=\"absolute bottom-20 right-10 w-96 h-96 rounded-full bg-indigo-500 opacity-10 animate-float-reverse\"></div>\r\n        <div className=\"absolute top-1/2 left-1/4 w-32 h-32 rounded-full bg-blue-500 opacity-5 animate-pulse\"></div>\r\n        <div\r\n          className=\"absolute inset-0 opacity-10\"\r\n          style={{\r\n            backgroundImage: 'radial-gradient(circle, rgba(255,255,255,0.1) 1px, transparent 1px)',\r\n            backgroundSize: '30px 30px'\r\n          }}\r\n        ></div>\r\n      </div>\r\n\r\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 relative z-10 py-20 md:py-28\">\r\n        <div className=\"max-w-4xl mx-auto\">\r\n          <div className=\"flex flex-col md:flex-row items-center\">\r\n            <div className={`md:w-1/2 text-center md:text-left transition-all duration-1000 transform ${isVisible ? 'opacity-100 translate-x-0' : 'opacity-0 -translate-x-10'}`}>\r\n              <div className=\"inline-block mb-6 p-2 bg-purple-800/30 rounded-full\">\r\n                <div className=\"px-4 py-1 bg-purple-700/50 rounded-full\">\r\n                  <span className=\"text-purple-100 font-medium\">FWU Incubation Center</span>\r\n                </div>\r\n              </div>\r\n              <h1 className=\"text-4xl sm:text-5xl font-extrabold mb-6 leading-tight\">\r\n                Innovative <span className=\"text-transparent bg-clip-text bg-gradient-to-r from-purple-300 to-indigo-300\">Projects</span>\r\n              </h1>\r\n              <p className=\"text-xl text-indigo-100 mb-8 leading-relaxed\">\r\n                Discover groundbreaking projects and innovations from the Far Western University Incubation Center.\r\n              </p>\r\n              \r\n              <div className=\"relative max-w-md mx-auto md:mx-0\">\r\n                <input\r\n                  type=\"text\"\r\n                  placeholder=\"Search projects...\"\r\n                  value={searchTerm}\r\n                  onChange={(e) => onSearchChange(e.target.value)}\r\n                  className=\"w-full pl-12 pr-4 py-4 bg-white/10 backdrop-blur-sm border border-white/20 rounded-full focus:ring-2 focus:ring-white/50 focus:border-transparent shadow-lg transition-colors text-white placeholder-white/70\"\r\n                />\r\n                <Search className=\"absolute left-4 top-1/2 transform -translate-y-1/2 text-white/70\" size={20} />\r\n              </div>\r\n            </div>\r\n            \r\n            <div className={`md:w-1/2 mt-10 md:mt-0 transition-all duration-1000 delay-300 transform ${isVisible ? 'opacity-100 translate-x-0' : 'opacity-0 translate-x-10'}`}>\r\n              <div className=\"relative h-64 md:h-80 w-full\">\r\n                <div className=\"absolute inset-0 bg-gradient-to-r from-purple-500/20 to-indigo-500/20 rounded-2xl transform rotate-3 scale-105\"></div>\r\n                <div className=\"absolute inset-0 bg-gradient-to-r from-purple-500/20 to-indigo-500/20 rounded-2xl transform -rotate-3 scale-105\"></div>\r\n                <div className=\"relative h-full w-full rounded-2xl overflow-hidden shadow-2xl border border-white/10\">\r\n                  <Image\r\n                    src=\"https://images.unsplash.com/photo-1581094794329-c8112a89af12?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80\"\r\n                    alt=\"FWU Incubation Center Projects\"\r\n                    fill\r\n                    className=\"object-cover\"\r\n                  />\r\n                  <div className=\"absolute inset-0 bg-gradient-to-t from-indigo-900/80 to-transparent\"></div>\r\n                  <div className=\"absolute bottom-0 left-0 right-0 p-6\">\r\n                    <span className=\"bg-purple-600/80 text-white text-xs font-bold px-3 py-1 rounded-full backdrop-blur-sm\">\r\n                      Innovation Hub\r\n                    </span>\r\n                    <h3 className=\"text-white text-xl font-bold mt-2\">\r\n                      Transforming Ideas into Reality\r\n                    </h3>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      \r\n      {/* Wave divider */}\r\n      <div className=\"absolute bottom-0 left-0 right-0\">\r\n        <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 1440 120\" className=\"w-full h-auto\">\r\n          <path\r\n            fill=\"#ffffff\"\r\n            fillOpacity=\"1\"\r\n            d=\"M0,64L80,69.3C160,75,320,85,480,80C640,75,800,53,960,48C1120,43,1280,53,1360,58.7L1440,64L1440,120L1360,120C1280,120,1120,120,960,120C800,120,640,120,480,120C320,120,160,120,80,120L0,120Z\"\r\n          ></path>\r\n        </svg>\r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\nexport default ProjectHero;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAHA;;;;;AAUA,MAAM,cAA0C,CAAC,EAAE,UAAU,EAAE,cAAc,EAAE;IAC7E,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,aAAa;IACf,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBACC,WAAU;wBACV,OAAO;4BACL,iBAAiB;4BACjB,gBAAgB;wBAClB;;;;;;;;;;;;0BAIJ,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAW,CAAC,yEAAyE,EAAE,YAAY,8BAA8B,6BAA6B;;kDACjK,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAA8B;;;;;;;;;;;;;;;;kDAGlD,8OAAC;wCAAG,WAAU;;4CAAyD;0DAC1D,8OAAC;gDAAK,WAAU;0DAA+E;;;;;;;;;;;;kDAE5G,8OAAC;wCAAE,WAAU;kDAA+C;;;;;;kDAI5D,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,MAAK;gDACL,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gDAC9C,WAAU;;;;;;0DAEZ,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;gDAAmE,MAAM;;;;;;;;;;;;;;;;;;0CAI/F,8OAAC;gCAAI,WAAW,CAAC,wEAAwE,EAAE,YAAY,8BAA8B,4BAA4B;0CAC/J,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,6HAAA,CAAA,UAAK;oDACJ,KAAI;oDACJ,KAAI;oDACJ,IAAI;oDACJ,WAAU;;;;;;8DAEZ,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAwF;;;;;;sEAGxG,8OAAC;4DAAG,WAAU;sEAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAYhE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,OAAM;oBAA6B,SAAQ;oBAAe,WAAU;8BACvE,cAAA,8OAAC;wBACC,MAAK;wBACL,aAAY;wBACZ,GAAE;;;;;;;;;;;;;;;;;;;;;;AAMd;uCAEe", "debugId": null}}, {"offset": {"line": 296, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/fwu_incubation%28react%2Blaravel%29/incubation/frontend/src/app/components/projects/ProjectCategoryFilter.tsx"], "sourcesContent": ["\"use client\"\r\nimport { useState, useEffect } from 'react';\r\nimport { Filter } from 'lucide-react';\r\n\r\ninterface ProjectCategoryFilterProps {\r\n  categories: string[];\r\n  selectedCategory: string;\r\n  onCategoryChange: (category: string) => void;\r\n}\r\n\r\nconst ProjectCategoryFilter: React.FC<ProjectCategoryFilterProps> = ({ \r\n  categories, \r\n  selectedCategory, \r\n  onCategoryChange \r\n}) => {\r\n  const [isVisible, setIsVisible] = useState(false);\r\n\r\n  useEffect(() => {\r\n    setIsVisible(true);\r\n  }, []);\r\n\r\n  const getCategoryColor = (category: string) => {\r\n    const colors = {\r\n      'Technology': 'bg-blue-500 border-blue-600 hover:bg-blue-600',\r\n      'Agriculture': 'bg-green-500 border-green-600 hover:bg-green-600',\r\n      'Healthcare': 'bg-red-500 border-red-600 hover:bg-red-600',\r\n      'Education': 'bg-yellow-500 border-yellow-600 hover:bg-yellow-600',\r\n      'Environment': 'bg-emerald-500 border-emerald-600 hover:bg-emerald-600',\r\n      'All': 'bg-purple-600 border-purple-700 hover:bg-purple-700'\r\n    };\r\n    \r\n    return colors[category as keyof typeof colors] || 'bg-gray-500 border-gray-600 hover:bg-gray-600';\r\n  };\r\n\r\n  const getInactiveStyle = (category: string) => {\r\n    const colors = {\r\n      'Technology': 'text-blue-700 border-blue-300 bg-blue-50 hover:bg-blue-100',\r\n      'Agriculture': 'text-green-700 border-green-300 bg-green-50 hover:bg-green-100',\r\n      'Healthcare': 'text-red-700 border-red-300 bg-red-50 hover:bg-red-100',\r\n      'Education': 'text-yellow-700 border-yellow-300 bg-yellow-50 hover:bg-yellow-100',\r\n      'Environment': 'text-emerald-700 border-emerald-300 bg-emerald-50 hover:bg-emerald-100',\r\n      'All': 'text-purple-700 border-purple-300 bg-purple-50 hover:bg-purple-100'\r\n    };\r\n    \r\n    return colors[category as keyof typeof colors] || 'text-gray-700 border-gray-300 bg-gray-50 hover:bg-gray-100';\r\n  };\r\n\r\n  return (\r\n    <section className=\"py-6 bg-white shadow-md sticky top-20 z-30\">\r\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\r\n        <div className={`transition-all duration-700 transform ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>\r\n          <div className=\"flex items-center mb-4\">\r\n            <Filter className=\"text-purple-600 mr-2\" size={20} />\r\n            <h2 className=\"text-lg font-bold text-gray-800\">Filter by Category</h2>\r\n          </div>\r\n          \r\n          <div className=\"flex flex-wrap gap-2\">\r\n            <button\r\n              onClick={() => onCategoryChange('')}\r\n              className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-300 border ${\r\n                selectedCategory === '' \r\n                  ? `text-white ${getCategoryColor('All')}` \r\n                  : getInactiveStyle('All')\r\n              }`}\r\n            >\r\n              All Categories\r\n            </button>\r\n            \r\n            {categories.map((category) => (\r\n              <button\r\n                key={category}\r\n                onClick={() => onCategoryChange(category)}\r\n                className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-300 border ${\r\n                  selectedCategory === category \r\n                    ? `text-white ${getCategoryColor(category)}` \r\n                    : getInactiveStyle(category)\r\n                }`}\r\n              >\r\n                {category}\r\n              </button>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\nexport default ProjectCategoryFilter;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AAFA;;;;AAUA,MAAM,wBAA8D,CAAC,EACnE,UAAU,EACV,gBAAgB,EAChB,gBAAgB,EACjB;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,aAAa;IACf,GAAG,EAAE;IAEL,MAAM,mBAAmB,CAAC;QACxB,MAAM,SAAS;YACb,cAAc;YACd,eAAe;YACf,cAAc;YACd,aAAa;YACb,eAAe;YACf,OAAO;QACT;QAEA,OAAO,MAAM,CAAC,SAAgC,IAAI;IACpD;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,SAAS;YACb,cAAc;YACd,eAAe;YACf,cAAc;YACd,aAAa;YACb,eAAe;YACf,OAAO;QACT;QAEA,OAAO,MAAM,CAAC,SAAgC,IAAI;IACpD;IAEA,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAW,CAAC,sCAAsC,EAAE,YAAY,8BAA8B,4BAA4B;;kCAC7H,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,sMAAA,CAAA,SAAM;gCAAC,WAAU;gCAAuB,MAAM;;;;;;0CAC/C,8OAAC;gCAAG,WAAU;0CAAkC;;;;;;;;;;;;kCAGlD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS,IAAM,iBAAiB;gCAChC,WAAW,CAAC,8EAA8E,EACxF,qBAAqB,KACjB,CAAC,WAAW,EAAE,iBAAiB,QAAQ,GACvC,iBAAiB,QACrB;0CACH;;;;;;4BAIA,WAAW,GAAG,CAAC,CAAC,yBACf,8OAAC;oCAEC,SAAS,IAAM,iBAAiB;oCAChC,WAAW,CAAC,8EAA8E,EACxF,qBAAqB,WACjB,CAAC,WAAW,EAAE,iBAAiB,WAAW,GAC1C,iBAAiB,WACrB;8CAED;mCARI;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgBrB;uCAEe", "debugId": null}}, {"offset": {"line": 416, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/fwu_incubation%28react%2Blaravel%29/incubation/frontend/src/app/components/projects/FeaturedProjects.tsx"], "sourcesContent": ["\"use client\"\r\nimport { useState, useEffect } from 'react';\r\nimport { ProjectData } from '@/app/projects/page';\r\nimport Image from 'next/image';\r\nimport Link from 'next/link';\r\nimport { ArrowRight, Calendar, Users } from 'lucide-react';\r\n\r\ninterface FeaturedProjectsProps {\r\n  featuredProjects: ProjectData[];\r\n}\r\n\r\nconst FeaturedProjects: React.FC<FeaturedProjectsProps> = ({ featuredProjects }) => {\r\n  const [isVisible, setIsVisible] = useState(false);\r\n\r\n  useEffect(() => {\r\n    setIsVisible(true);\r\n  }, []);\r\n\r\n  if (!featuredProjects || featuredProjects.length === 0) {\r\n    return null;\r\n  }\r\n\r\n  const getCategoryColor = (category: string) => {\r\n    const colors = {\r\n      'Technology': 'bg-blue-500/10 text-blue-700 border-blue-300',\r\n      'Agriculture': 'bg-green-500/10 text-green-700 border-green-300',\r\n      'Healthcare': 'bg-red-500/10 text-red-700 border-red-300',\r\n      'Education': 'bg-yellow-500/10 text-yellow-700 border-yellow-300',\r\n      'Environment': 'bg-emerald-500/10 text-emerald-700 border-emerald-300',\r\n    };\r\n    \r\n    return colors[category as keyof typeof colors] || 'bg-gray-500/10 text-gray-700 border-gray-300';\r\n  };\r\n\r\n  return (\r\n    <section className=\"py-16 bg-gray-50\">\r\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\r\n        <div className={`mb-12 text-center transition-all duration-700 transform ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>\r\n          <h2 className=\"text-3xl font-bold text-gray-900 mb-2\">Featured Projects</h2>\r\n          <div className=\"w-24 h-1 bg-purple-600 mx-auto mb-4 rounded-full\"></div>\r\n          <p className=\"text-gray-600 max-w-2xl mx-auto\">\r\n            Explore our most innovative and impactful projects from the Far Western University Incubation Center\r\n          </p>\r\n        </div>\r\n\r\n        <div className=\"grid md:grid-cols-3 gap-8\">\r\n          {featuredProjects.map((project, index) => (\r\n            <div \r\n              key={project.id}\r\n              className={`bg-white rounded-xl shadow-lg overflow-hidden group hover:shadow-xl transition-all duration-500 transform ${\r\n                isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'\r\n              }`}\r\n              style={{ transitionDelay: `${(index + 1) * 100}ms` }}\r\n            >\r\n              <div className=\"relative h-48 overflow-hidden\">\r\n                {project.imageUrl ? (\r\n                  <Image\r\n                    src={project.imageUrl}\r\n                    alt={project.title}\r\n                    fill\r\n                    className=\"object-cover transition-transform duration-700 group-hover:scale-105\"\r\n                  />\r\n                ) : (\r\n                  <div className=\"w-full h-full bg-gradient-to-r from-purple-500 to-indigo-500\"></div>\r\n                )}\r\n                <div className=\"absolute inset-0 bg-gradient-to-t from-black/60 to-transparent\"></div>\r\n                <div className=\"absolute top-4 left-4\">\r\n                  <span className={`text-xs font-semibold px-3 py-1 rounded-full border ${getCategoryColor(project.category)}`}>\r\n                    {project.category}\r\n                  </span>\r\n                </div>\r\n                <div className=\"absolute bottom-4 right-4\">\r\n                  <span className=\"bg-black/50 text-white text-xs px-2 py-1 rounded backdrop-blur-sm\">\r\n                    {project.status}\r\n                  </span>\r\n                </div>\r\n              </div>\r\n              \r\n              <div className=\"p-6\">\r\n                <h3 className=\"text-xl font-bold text-gray-900 mb-2 group-hover:text-purple-600 transition-colors\">\r\n                  {project.title}\r\n                </h3>\r\n                \r\n                <div className=\"flex items-center text-gray-500 text-sm mb-4\">\r\n                  <Calendar className=\"mr-2 text-purple-500\" />\r\n                  <span>Started: {project.startDate}</span>\r\n                </div>\r\n                \r\n                <p className=\"text-gray-600 mb-6 line-clamp-3\">\r\n                  {project.description}\r\n                </p>\r\n                \r\n                <div className=\"flex items-center justify-between\">\r\n                  <div className=\"flex items-center\">\r\n                    <Users className=\"text-purple-500 mr-2\" />\r\n                    <span className=\"text-sm text-gray-500\">{project.teamSize} Team Members</span>\r\n                  </div>\r\n                  \r\n                  <Link\r\n                    href={`/projects/${project.id}`}\r\n                    className=\"inline-flex items-center text-purple-600 font-semibold hover:text-purple-800 transition-colors group\"\r\n                  >\r\n                    Details <ArrowRight className=\"ml-2 group-hover:translate-x-1 transition-transform\" />\r\n                  </Link>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          ))}\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\nexport default FeaturedProjects;\r\n"], "names": [], "mappings": ";;;;AACA;AAEA;AACA;AACA;AAAA;AAAA;AALA;;;;;;AAWA,MAAM,mBAAoD,CAAC,EAAE,gBAAgB,EAAE;IAC7E,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,aAAa;IACf,GAAG,EAAE;IAEL,IAAI,CAAC,oBAAoB,iBAAiB,MAAM,KAAK,GAAG;QACtD,OAAO;IACT;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,SAAS;YACb,cAAc;YACd,eAAe;YACf,cAAc;YACd,aAAa;YACb,eAAe;QACjB;QAEA,OAAO,MAAM,CAAC,SAAgC,IAAI;IACpD;IAEA,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAW,CAAC,wDAAwD,EAAE,YAAY,8BAA8B,4BAA4B;;sCAC/I,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAE,WAAU;sCAAkC;;;;;;;;;;;;8BAKjD,8OAAC;oBAAI,WAAU;8BACZ,iBAAiB,GAAG,CAAC,CAAC,SAAS,sBAC9B,8OAAC;4BAEC,WAAW,CAAC,0GAA0G,EACpH,YAAY,8BAA8B,4BAC1C;4BACF,OAAO;gCAAE,iBAAiB,GAAG,CAAC,QAAQ,CAAC,IAAI,IAAI,EAAE,CAAC;4BAAC;;8CAEnD,8OAAC;oCAAI,WAAU;;wCACZ,QAAQ,QAAQ,iBACf,8OAAC,6HAAA,CAAA,UAAK;4CACJ,KAAK,QAAQ,QAAQ;4CACrB,KAAK,QAAQ,KAAK;4CAClB,IAAI;4CACJ,WAAU;;;;;iEAGZ,8OAAC;4CAAI,WAAU;;;;;;sDAEjB,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAW,CAAC,oDAAoD,EAAE,iBAAiB,QAAQ,QAAQ,GAAG;0DACzG,QAAQ,QAAQ;;;;;;;;;;;sDAGrB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DACb,QAAQ,MAAM;;;;;;;;;;;;;;;;;8CAKrB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDACX,QAAQ,KAAK;;;;;;sDAGhB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,0MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,8OAAC;;wDAAK;wDAAU,QAAQ,SAAS;;;;;;;;;;;;;sDAGnC,8OAAC;4CAAE,WAAU;sDACV,QAAQ,WAAW;;;;;;sDAGtB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;sEACjB,8OAAC;4DAAK,WAAU;;gEAAyB,QAAQ,QAAQ;gEAAC;;;;;;;;;;;;;8DAG5D,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAM,CAAC,UAAU,EAAE,QAAQ,EAAE,EAAE;oDAC/B,WAAU;;wDACX;sEACS,8OAAC,kNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;2BAtD/B,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;AAgE7B;uCAEe", "debugId": null}}, {"offset": {"line": 692, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/fwu_incubation%28react%2Blaravel%29/incubation/frontend/src/app/components/projects/ProjectListItem.tsx"], "sourcesContent": ["\"use client\"\r\nimport { useState, useEffect } from 'react';\r\nimport { ProjectData } from '@/app/projects/page';\r\nimport Image from 'next/image';\r\nimport Link from 'next/link';\r\nimport { ArrowRight, Calendar, Users, Tag, Clock } from 'lucide-react';\r\n\r\ninterface ProjectListItemProps {\r\n  project: ProjectData;\r\n  index: number;\r\n}\r\n\r\nconst ProjectListItem: React.FC<ProjectListItemProps> = ({ project, index }) => {\r\n  const [isVisible, setIsVisible] = useState(false);\r\n\r\n  useEffect(() => {\r\n    const timer = setTimeout(() => {\r\n      setIsVisible(true);\r\n    }, index * 100);\r\n    \r\n    return () => clearTimeout(timer);\r\n  }, [index]);\r\n\r\n  const getCategoryStyle = (category: string) => {\r\n    const styles = {\r\n      'Technology': 'bg-blue-500/10 text-blue-700 border-blue-300',\r\n      'Agriculture': 'bg-green-500/10 text-green-700 border-green-300',\r\n      'Healthcare': 'bg-red-500/10 text-red-700 border-red-300',\r\n      'Education': 'bg-yellow-500/10 text-yellow-700 border-yellow-300',\r\n      'Environment': 'bg-emerald-500/10 text-emerald-700 border-emerald-300',\r\n    };\r\n    \r\n    return styles[category as keyof typeof styles] || 'bg-gray-500/10 text-gray-700 border-gray-300';\r\n  };\r\n\r\n  const getStatusStyle = (status: string) => {\r\n    const styles = {\r\n      'Completed': 'bg-green-500/10 text-green-700 border-green-300',\r\n      'In Progress': 'bg-blue-500/10 text-blue-700 border-blue-300',\r\n      'Planning': 'bg-yellow-500/10 text-yellow-700 border-yellow-300',\r\n      'On Hold': 'bg-red-500/10 text-red-700 border-red-300',\r\n    };\r\n    \r\n    return styles[status as keyof typeof styles] || 'bg-gray-500/10 text-gray-700 border-gray-300';\r\n  };\r\n\r\n  return (\r\n    <article \r\n      className={`bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-500 overflow-hidden group transform ${\r\n        isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'\r\n      }`}\r\n    >\r\n      <div className=\"flex flex-col md:flex-row\">\r\n        {/* Project Image */}\r\n        {project.imageUrl && (\r\n          <div className=\"md:w-1/3 w-full h-56 md:h-auto relative flex-shrink-0 overflow-hidden\">\r\n            <Image\r\n              src={project.imageUrl}\r\n              alt={project.title}\r\n              fill\r\n              className=\"object-cover transition-transform duration-700 group-hover:scale-105 md:rounded-l-xl md:rounded-r-none rounded-t-xl\"\r\n            />\r\n            <div className=\"absolute inset-0 bg-gradient-to-t from-black/30 to-transparent md:bg-gradient-to-l\"></div>\r\n          </div>\r\n        )}\r\n\r\n        {/* Content Area */}\r\n        <div className={`p-6 md:p-8 flex flex-col flex-grow ${project.imageUrl ? 'md:w-2/3' : 'w-full'}`}>\r\n          <div className=\"mb-4 flex flex-wrap items-center gap-x-4 gap-y-2\">\r\n            <span className={`text-xs font-semibold px-3 py-1 rounded-full border ${getCategoryStyle(project.category)}`}>\r\n              <Tag className=\"inline mr-1.5 mb-0.5\" />\r\n              {project.category}\r\n            </span>\r\n            <span className={`text-xs font-semibold px-3 py-1 rounded-full border ${getStatusStyle(project.status)}`}>\r\n              <Clock className=\"inline mr-1.5 mb-0.5\" />\r\n              {project.status}\r\n            </span>\r\n            <div className=\"flex items-center text-sm text-gray-500\">\r\n              <Calendar className=\"mr-1.5 text-purple-500\" />\r\n              <span>Started: {project.startDate}</span>\r\n            </div>\r\n          </div>\r\n\r\n          <h2 className=\"text-xl lg:text-2xl font-bold text-gray-900 mb-3 group-hover:text-purple-600 transition-colors leading-tight\">\r\n            <Link href={`/projects/${project.id}`} className=\"hover:underline\">\r\n              {project.title}\r\n            </Link>\r\n          </h2>\r\n\r\n          <p className=\"text-gray-600 mb-5 line-clamp-3 flex-grow\">\r\n            {project.description}\r\n          </p>\r\n\r\n          <div className=\"mt-auto pt-4 border-t border-gray-100 flex flex-wrap justify-between items-center\">\r\n            <div className=\"flex items-center text-sm text-gray-500 mb-2 md:mb-0\">\r\n              <Users className=\"mr-1.5 text-purple-500\" />\r\n              <span>{project.teamSize} Team Members</span>\r\n            </div>\r\n            \r\n            <Link\r\n              href={`/projects/${project.id}`}\r\n              className=\"inline-flex items-center text-purple-600 hover:text-purple-800 font-semibold transition-colors\"\r\n            >\r\n              View Project <ArrowRight className=\"ml-2 group-hover:translate-x-1 transition-transform\" />\r\n            </Link>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </article>\r\n  );\r\n};\r\n\r\nexport default ProjectListItem;\r\n"], "names": [], "mappings": ";;;;AACA;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;;AAYA,MAAM,kBAAkD,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE;IACzE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,QAAQ,WAAW;YACvB,aAAa;QACf,GAAG,QAAQ;QAEX,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC;KAAM;IAEV,MAAM,mBAAmB,CAAC;QACxB,MAAM,SAAS;YACb,cAAc;YACd,eAAe;YACf,cAAc;YACd,aAAa;YACb,eAAe;QACjB;QAEA,OAAO,MAAM,CAAC,SAAgC,IAAI;IACpD;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,SAAS;YACb,aAAa;YACb,eAAe;YACf,YAAY;YACZ,WAAW;QACb;QAEA,OAAO,MAAM,CAAC,OAA8B,IAAI;IAClD;IAEA,qBACE,8OAAC;QACC,WAAW,CAAC,0GAA0G,EACpH,YAAY,8BAA8B,4BAC1C;kBAEF,cAAA,8OAAC;YAAI,WAAU;;gBAEZ,QAAQ,QAAQ,kBACf,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,6HAAA,CAAA,UAAK;4BACJ,KAAK,QAAQ,QAAQ;4BACrB,KAAK,QAAQ,KAAK;4BAClB,IAAI;4BACJ,WAAU;;;;;;sCAEZ,8OAAC;4BAAI,WAAU;;;;;;;;;;;;8BAKnB,8OAAC;oBAAI,WAAW,CAAC,mCAAmC,EAAE,QAAQ,QAAQ,GAAG,aAAa,UAAU;;sCAC9F,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAW,CAAC,oDAAoD,EAAE,iBAAiB,QAAQ,QAAQ,GAAG;;sDAC1G,8OAAC,gMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;wCACd,QAAQ,QAAQ;;;;;;;8CAEnB,8OAAC;oCAAK,WAAW,CAAC,oDAAoD,EAAE,eAAe,QAAQ,MAAM,GAAG;;sDACtG,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;wCAChB,QAAQ,MAAM;;;;;;;8CAEjB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,8OAAC;;gDAAK;gDAAU,QAAQ,SAAS;;;;;;;;;;;;;;;;;;;sCAIrC,8OAAC;4BAAG,WAAU;sCACZ,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAM,CAAC,UAAU,EAAE,QAAQ,EAAE,EAAE;gCAAE,WAAU;0CAC9C,QAAQ,KAAK;;;;;;;;;;;sCAIlB,8OAAC;4BAAE,WAAU;sCACV,QAAQ,WAAW;;;;;;sCAGtB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,8OAAC;;gDAAM,QAAQ,QAAQ;gDAAC;;;;;;;;;;;;;8CAG1B,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAM,CAAC,UAAU,EAAE,QAAQ,EAAE,EAAE;oCAC/B,WAAU;;wCACX;sDACc,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOjD;uCAEe", "debugId": null}}, {"offset": {"line": 944, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/fwu_incubation%28react%2Blaravel%29/incubation/frontend/src/app/components/shared/SectionTitle.tsx"], "sourcesContent": ["// components/shared/SectionTitle.tsx\r\ninterface SectionTitleProps {\r\n  title: string;\r\n  subtitle?: string;\r\n  align?: 'left' | 'center' | 'right';\r\n  accentColor?: string;\r\n}\r\n\r\nconst SectionTitle: React.FC<SectionTitleProps> = ({\r\n  title,\r\n  subtitle,\r\n  align = 'center',\r\n  accentColor = 'indigo'\r\n}) => {\r\n  const alignmentClass = {\r\n    left: 'text-left',\r\n    center: 'text-center',\r\n    right: 'text-right',\r\n  };\r\n\r\n  const accentColorClass = {\r\n    indigo: 'bg-indigo-600',\r\n    blue: 'bg-blue-600',\r\n    teal: 'bg-teal-600',\r\n    purple: 'bg-purple-600',\r\n    green: 'bg-green-600',\r\n  };\r\n\r\n  const textColorClass = {\r\n    indigo: 'text-indigo-600',\r\n    blue: 'text-blue-600',\r\n    teal: 'text-teal-600',\r\n    purple: 'text-purple-600',\r\n    green: 'text-green-600',\r\n  };\r\n\r\n  return (\r\n    <div className={`mb-12 md:mb-16 ${alignmentClass[align]}`}>\r\n      {subtitle && (\r\n        <p className={`${textColorClass[accentColor as keyof typeof textColorClass]} font-semibold text-sm md:text-base uppercase tracking-wider mb-2`}>\r\n          {subtitle}\r\n        </p>\r\n      )}\r\n      <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900\">\r\n        {title}\r\n      </h2>\r\n      <div className={`mt-4 h-1.5 w-24 ${align === 'center' ? 'mx-auto' : (align === 'right' ? 'ml-auto' : '')} ${accentColorClass[accentColor as keyof typeof accentColorClass]} rounded-full`}></div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default SectionTitle;"], "names": [], "mappings": "AAAA,qCAAqC;;;;;;AAQrC,MAAM,eAA4C,CAAC,EACjD,KAAK,EACL,QAAQ,EACR,QAAQ,QAAQ,EAChB,cAAc,QAAQ,EACvB;IACC,MAAM,iBAAiB;QACrB,MAAM;QACN,QAAQ;QACR,OAAO;IACT;IAEA,MAAM,mBAAmB;QACvB,QAAQ;QACR,MAAM;QACN,MAAM;QACN,QAAQ;QACR,OAAO;IACT;IAEA,MAAM,iBAAiB;QACrB,QAAQ;QACR,MAAM;QACN,MAAM;QACN,QAAQ;QACR,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,eAAe,EAAE,cAAc,CAAC,MAAM,EAAE;;YACtD,0BACC,8OAAC;gBAAE,WAAW,GAAG,cAAc,CAAC,YAA2C,CAAC,iEAAiE,CAAC;0BAC3I;;;;;;0BAGL,8OAAC;gBAAG,WAAU;0BACX;;;;;;0BAEH,8OAAC;gBAAI,WAAW,CAAC,gBAAgB,EAAE,UAAU,WAAW,YAAa,UAAU,UAAU,YAAY,GAAI,CAAC,EAAE,gBAAgB,CAAC,YAA6C,CAAC,aAAa,CAAC;;;;;;;;;;;;AAG/L;uCAEe", "debugId": null}}, {"offset": {"line": 1010, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/fwu_incubation%28react%2Blaravel%29/incubation/frontend/src/app/projects/page.tsx"], "sourcesContent": ["\"use client\"\r\nimport { useState } from 'react';\r\nimport ProjectHero from '../components/projects/ProjectHero';\r\nimport ProjectCategoryFilter from '../components/projects/ProjectCategoryFilter';\r\nimport FeaturedProjects from '../components/projects/FeaturedProjects';\r\nimport ProjectListItem from '../components/projects/ProjectListItem';\r\nimport SectionTitle from '../components/shared/SectionTitle';\r\nimport Link from 'next/link';\r\nimport { ArrowRight } from 'lucide-react';\r\n\r\n// Project Data Interface\r\nexport interface ProjectData {\r\n  id: string;\r\n  title: string;\r\n  description: string;\r\n  category: string;\r\n  status: 'Completed' | 'In Progress' | 'Planning' | 'On Hold';\r\n  startDate: string;\r\n  teamSize: number;\r\n  imageUrl?: string;\r\n}\r\n\r\n// Sample project data\r\nexport const allProjectsData: ProjectData[] = [\r\n  {\r\n    id: 'p1',\r\n    title: 'Smart Agriculture Monitoring System',\r\n    description: 'An IoT-based system for monitoring soil moisture, temperature, and other environmental factors to optimize crop growth and water usage in the Far Western region of Nepal.',\r\n    category: 'Agriculture',\r\n    status: 'In Progress',\r\n    startDate: 'June 2023',\r\n    teamSize: 5,\r\n    imageUrl: 'https://images.unsplash.com/photo-1625246333195-78d9c38ad449?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80',\r\n  },\r\n  {\r\n    id: 'p2',\r\n    title: 'Renewable Energy Solutions for Rural Communities',\r\n    description: 'Developing affordable solar and micro-hydro power solutions for remote villages in the Far Western region, providing clean energy access to underserved communities.',\r\n    category: 'Environment',\r\n    status: 'In Progress',\r\n    startDate: 'March 2023',\r\n    teamSize: 7,\r\n    imageUrl: 'https://images.unsplash.com/photo-1509391366360-2e959784a276?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80',\r\n  },\r\n  {\r\n    id: 'p3',\r\n    title: 'Mobile Health Clinic Application',\r\n    description: 'A mobile application connecting remote communities with healthcare professionals, enabling telemedicine consultations and health monitoring for areas with limited healthcare access.',\r\n    category: 'Healthcare',\r\n    status: 'Completed',\r\n    startDate: 'January 2023',\r\n    teamSize: 4,\r\n    imageUrl: 'https://images.unsplash.com/photo-1576091160550-2173dba999ef?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80',\r\n  },\r\n  {\r\n    id: 'p4',\r\n    title: 'Digital Literacy Program for Rural Schools',\r\n    description: 'An educational initiative providing computer equipment, training, and digital curriculum to schools in rural areas, bridging the digital divide for students in the Far Western region.',\r\n    category: 'Education',\r\n    status: 'In Progress',\r\n    startDate: 'August 2023',\r\n    teamSize: 6,\r\n    imageUrl: 'https://images.unsplash.com/photo-1503676260728-1c00da094a0b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80',\r\n  },\r\n  {\r\n    id: 'p5',\r\n    title: 'Water Purification Technology for Rural Areas',\r\n    description: 'Developing low-cost, sustainable water purification systems using locally available materials to provide clean drinking water to communities facing water quality challenges.',\r\n    category: 'Environment',\r\n    status: 'Planning',\r\n    startDate: 'October 2023',\r\n    teamSize: 3,\r\n    imageUrl: 'https://images.unsplash.com/photo-1581244277943-fe4a9c777189?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80',\r\n  },\r\n  {\r\n    id: 'p6',\r\n    title: 'AI-Powered Crop Disease Detection',\r\n    description: 'A machine learning application that helps farmers identify crop diseases through smartphone photos, providing immediate diagnosis and treatment recommendations to prevent crop loss.',\r\n    category: 'Technology',\r\n    status: 'In Progress',\r\n    startDate: 'May 2023',\r\n    teamSize: 4,\r\n    imageUrl: 'https://images.unsplash.com/photo-1530836369250-ef72a3f5cda8?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80',\r\n  },\r\n  {\r\n    id: 'p7',\r\n    title: 'Biodiversity Conservation and Monitoring',\r\n    description: 'A research project documenting and preserving the unique biodiversity of the Far Western region, involving local communities in conservation efforts and sustainable resource management.',\r\n    category: 'Environment',\r\n    status: 'In Progress',\r\n    startDate: 'February 2023',\r\n    teamSize: 8,\r\n    imageUrl: 'https://images.unsplash.com/photo-1500829243541-74b677fecc30?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80',\r\n  },\r\n  {\r\n    id: 'p8',\r\n    title: 'Traditional Knowledge Digital Archive',\r\n    description: 'Documenting and preserving traditional knowledge, practices, and cultural heritage of indigenous communities in the Far Western region through digital archiving and community engagement.',\r\n    category: 'Education',\r\n    status: 'On Hold',\r\n    startDate: 'April 2023',\r\n    teamSize: 5,\r\n    imageUrl: 'https://images.unsplash.com/photo-1532619675605-1ede6c2ed2b0?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80',\r\n  },\r\n];\r\n\r\n// Extract unique categories for filtering\r\nconst uniqueCategories = Array.from(new Set(allProjectsData.map(p => p.category))).sort();\r\n\r\nexport default function ProjectsPage() {\r\n  const [searchTerm, setSearchTerm] = useState('');\r\n  const [selectedCategory, setSelectedCategory] = useState('');\r\n\r\n  // Get featured projects (first 3)\r\n  const featuredProjects = allProjectsData.slice(0, 3);\r\n  \r\n  // Filter projects based on search term and category\r\n  const filteredProjects = allProjectsData\r\n    .filter(project =>\r\n      project.title.toLowerCase().includes(searchTerm.toLowerCase()) ||\r\n      project.description.toLowerCase().includes(searchTerm.toLowerCase())\r\n    )\r\n    .filter(project =>\r\n      selectedCategory ? project.category === selectedCategory : true\r\n    );\r\n\r\n  return (\r\n    <main className=\"bg-white\">\r\n      {/* Hero Section with Search */}\r\n      <ProjectHero \r\n        searchTerm={searchTerm} \r\n        onSearchChange={setSearchTerm} \r\n      />\r\n      \r\n      {/* Category Filter */}\r\n      <ProjectCategoryFilter \r\n        categories={uniqueCategories} \r\n        selectedCategory={selectedCategory} \r\n        onCategoryChange={setSelectedCategory} \r\n      />\r\n      \r\n      {/* Featured Projects Section (only show if no filters are applied) */}\r\n      {!searchTerm && !selectedCategory && (\r\n        <FeaturedProjects featuredProjects={featuredProjects} />\r\n      )}\r\n      \r\n      {/* Projects List Section */}\r\n      <section className=\"py-16 md:py-20 bg-gray-50\">\r\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\r\n          <div className=\"mb-12 text-center\">\r\n            <h2 className=\"text-3xl font-bold text-gray-900 mb-2\">\r\n              {searchTerm || selectedCategory ? 'Search Results' : 'All Projects'}\r\n            </h2>\r\n            <div className=\"w-24 h-1 bg-purple-600 mx-auto mb-4 rounded-full\"></div>\r\n            <p className=\"text-gray-600 max-w-2xl mx-auto\">\r\n              {searchTerm || selectedCategory \r\n                ? `Showing ${filteredProjects.length} result${filteredProjects.length !== 1 ? 's' : ''}`\r\n                : 'Explore all innovative projects from the Far Western University Incubation Center'\r\n              }\r\n            </p>\r\n          </div>\r\n          \r\n          {filteredProjects.length > 0 ? (\r\n            <div className=\"max-w-4xl mx-auto space-y-8\">\r\n              {filteredProjects.map((project, index) => (\r\n                <ProjectListItem key={project.id} project={project} index={index} />\r\n              ))}\r\n            </div>\r\n          ) : (\r\n            <div className=\"text-center py-12 bg-white rounded-xl shadow-md max-w-2xl mx-auto\">\r\n              <SectionTitle title=\"No Projects Found\" subtitle=\"Try adjusting your search or filters\" />\r\n              <p className=\"text-gray-600 mt-4 px-6\">\r\n                We couldn&apos;t find any projects matching your criteria. Please try different keywords or browse all categories.\r\n              </p>\r\n              <button \r\n                onClick={() => {\r\n                  setSearchTerm('');\r\n                  setSelectedCategory('');\r\n                }}\r\n                className=\"mt-6 px-6 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors\"\r\n              >\r\n                View All Projects\r\n              </button>\r\n            </div>\r\n          )}\r\n        </div>\r\n      </section>\r\n      \r\n      {/* CTA Section */}\r\n      <section className=\"py-16 bg-gradient-to-r from-purple-900 to-indigo-900 text-white\">\r\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\r\n          <div className=\"max-w-3xl mx-auto text-center\">\r\n            <h2 className=\"text-3xl font-bold mb-6\">Have an Innovative Project Idea?</h2>\r\n            <p className=\"text-xl text-indigo-100 mb-8\">\r\n              The Far Western University Incubation Center is looking for innovative project proposals. Submit your idea and join our community of innovators.\r\n            </p>\r\n            <Link \r\n              href=\"/submit-proposal\" \r\n              className=\"inline-flex items-center px-6 py-3 bg-white text-purple-900 font-bold rounded-lg hover:bg-indigo-100 transition-colors\"\r\n            >\r\n              Submit Your Proposal <ArrowRight className=\"ml-2\" />\r\n            </Link>\r\n          </div>\r\n        </div>\r\n      </section>\r\n    </main>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AARA;;;;;;;;;;AAuBO,MAAM,kBAAiC;IAC5C;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,QAAQ;QACR,WAAW;QACX,UAAU;QACV,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,QAAQ;QACR,WAAW;QACX,UAAU;QACV,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,QAAQ;QACR,WAAW;QACX,UAAU;QACV,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,QAAQ;QACR,WAAW;QACX,UAAU;QACV,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,QAAQ;QACR,WAAW;QACX,UAAU;QACV,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,QAAQ;QACR,WAAW;QACX,UAAU;QACV,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,QAAQ;QACR,WAAW;QACX,UAAU;QACV,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,QAAQ;QACR,WAAW;QACX,UAAU;QACV,UAAU;IACZ;CACD;AAED,0CAA0C;AAC1C,MAAM,mBAAmB,MAAM,IAAI,CAAC,IAAI,IAAI,gBAAgB,GAAG,CAAC,CAAA,IAAK,EAAE,QAAQ,IAAI,IAAI;AAExE,SAAS;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,kCAAkC;IAClC,MAAM,mBAAmB,gBAAgB,KAAK,CAAC,GAAG;IAElD,oDAAoD;IACpD,MAAM,mBAAmB,gBACtB,MAAM,CAAC,CAAA,UACN,QAAQ,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC3D,QAAQ,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,KAElE,MAAM,CAAC,CAAA,UACN,mBAAmB,QAAQ,QAAQ,KAAK,mBAAmB;IAG/D,qBACE,8OAAC;QAAK,WAAU;;0BAEd,8OAAC,oJAAA,CAAA,UAAW;gBACV,YAAY;gBACZ,gBAAgB;;;;;;0BAIlB,8OAAC,8JAAA,CAAA,UAAqB;gBACpB,YAAY;gBACZ,kBAAkB;gBAClB,kBAAkB;;;;;;YAInB,CAAC,cAAc,CAAC,kCACf,8OAAC,yJAAA,CAAA,UAAgB;gBAAC,kBAAkB;;;;;;0BAItC,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CACX,cAAc,mBAAmB,mBAAmB;;;;;;8CAEvD,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAE,WAAU;8CACV,cAAc,mBACX,CAAC,QAAQ,EAAE,iBAAiB,MAAM,CAAC,OAAO,EAAE,iBAAiB,MAAM,KAAK,IAAI,MAAM,IAAI,GACtF;;;;;;;;;;;;wBAKP,iBAAiB,MAAM,GAAG,kBACzB,8OAAC;4BAAI,WAAU;sCACZ,iBAAiB,GAAG,CAAC,CAAC,SAAS,sBAC9B,8OAAC,wJAAA,CAAA,UAAe;oCAAkB,SAAS;oCAAS,OAAO;mCAArC,QAAQ,EAAE;;;;;;;;;iDAIpC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,mJAAA,CAAA,UAAY;oCAAC,OAAM;oCAAoB,UAAS;;;;;;8CACjD,8OAAC;oCAAE,WAAU;8CAA0B;;;;;;8CAGvC,8OAAC;oCACC,SAAS;wCACP,cAAc;wCACd,oBAAoB;oCACtB;oCACA,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;0BAST,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA0B;;;;;;0CACxC,8OAAC;gCAAE,WAAU;0CAA+B;;;;;;0CAG5C,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;oCACX;kDACsB,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOzD", "debugId": null}}]}