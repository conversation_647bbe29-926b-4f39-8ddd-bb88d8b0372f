"use client"
import React from 'react';
import AdvisoryBoardSection from '../components/about/AdvisoryBoardSection';
import TeamSection from '../components/about/TeamSection';
import HistoryTimeline from '../components/about/HistoryTimeline';
import HeroBanner from '../components/home/<USER>';
import IntroSection from '../components/home/<USER>';

export default function AboutPage() {
    // Store current ref values in variables to use in cleanup

  return (
    <>
      <main className="overflow-hidden">
        {/* Hero Banner with Parallax and Animation Effects */}
       
         <HeroBanner/>
         <IntroSection/>
        <HistoryTimeline />
      

        <TeamSection />
        <AdvisoryBoardSection />
        
        {/* CSS Animations */}
        <style jsx>{`
          @keyframes float {
            0%, 100% { transform: translateY(0) scale(1); }
            50% { transform: translateY(-20px) scale(1.2); }
          }
          @keyframes wave {
            0%, 100% { transform: translateX(0); }
            50% { transform: translateX(-2%); }
          }
        `}</style>
      </main>
    </>
  );
}