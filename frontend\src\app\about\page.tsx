"use client"
import { useEffect, useState, useRef } from 'react';
import AdvisoryBoardSection from '../components/about/AdvisoryBoardSection';
import IntroAbout from '../components/about/IntroAbout';
import TeamSection from '../components/about/TeamSection';
import HistoryTimeline from '../components/about/HistoryTimeline';
import FacultyShowcase from '../components/about/FacultyShowcase';
import Image from 'next/image';
import Link from 'next/link';
import { Building2, GraduationCap, Users, ChevronDown, ArrowRight, Book, Award, MapPin, Calendar } from 'lucide-react';
import HeroBanner from '../components/home/<USER>';

export default function AboutPage() {
  const [isVisible, setIsVisible] = useState(false);
  const [statsVisible, setStatsVisible] = useState(false);
  const heroRef = useRef(null);
  const statsRef = useRef(null);

  useEffect(() => {
    setIsVisible(true);

    const heroObserver = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          heroObserver.disconnect();
        }
      },
      { threshold: 0.1 }
    );

    const statsObserver = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setStatsVisible(true);
          statsObserver.disconnect();
        }
      },
      { threshold: 0.1 }
    );

    // Store current ref values in variables to use in cleanup
    const currentHeroRef = heroRef.current;
    const currentStatsRef = statsRef.current;

    if (currentHeroRef) {
      heroObserver.observe(currentHeroRef);
    }

    if (currentStatsRef) {
      statsObserver.observe(currentStatsRef);
    }

    return () => {
      // Use the stored ref values in cleanup
      if (currentHeroRef) {
        heroObserver.unobserve(currentHeroRef);
      }
      if (currentStatsRef) {
        statsObserver.unobserve(currentStatsRef);
      }
    };
  }, []);

  return (
    <>
      <main className="overflow-hidden">
        {/* Hero Banner with Parallax and Animation Effects */}
       
         <HeroBanner/>
        <HistoryTimeline />
        <FacultyShowcase />

        {/* Recent News & Events Section */}
        <section className="py-20 bg-gradient-to-b from-indigo-50 to-white relative overflow-hidden">
          <div className="absolute top-0 right-0 w-96 h-96 bg-indigo-100 rounded-full opacity-30 -translate-x-1/2 -translate-y-1/2"></div>
          <div className="absolute bottom-0 left-0 w-64 h-64 bg-blue-100 rounded-full opacity-30 translate-x-1/4 translate-y-1/4"></div>

          <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-indigo-900 mb-4">Latest News & Events</h2>
              <div className="w-24 h-1 bg-indigo-600 mx-auto mb-6 rounded-full"></div>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Stay updated with the latest happenings, announcements, and events at Far Western University.
              </p>
            </div>

            <div className="grid md:grid-cols-3 gap-8">
              {/* News Card 1 */}
              <div className="bg-white rounded-xl shadow-lg overflow-hidden group hover:shadow-xl transition-all duration-300 border border-gray-100 hover:border-indigo-100">
                <div className="relative h-48 overflow-hidden">
                  <Image
                    src="https://images.unsplash.com/photo-1523050854058-8df90110c9f1?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80"
                    alt="Vice Chancellor Visit to Israel"
                    fill
                    className="object-cover group-hover:scale-110 transition-transform duration-500"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-indigo-900/80 to-transparent"></div>
                  <div className="absolute bottom-4 left-4 bg-white/90 text-indigo-600 text-xs font-bold px-3 py-1 rounded-full flex items-center">
                    <Calendar className="mr-1" />
                    Apr 6, 2025
                  </div>
                </div>
                <div className="p-6">
                  <h3 className="text-xl font-bold text-indigo-900 mb-3 group-hover:text-indigo-600 transition-colors">
                    Vice Chancellor Visit to Israel
                  </h3>
                  <p className="text-gray-600 mb-4 line-clamp-3">
                    Vice Chancellor of Far Western University Prof. Hem Raj Pant visited Israel from march 13 to 20, 2025. During his visit to Israel, the discussion with government professionals...
                  </p>
                  <Link href="#" className="text-indigo-600 font-medium flex items-center group/link">
                    Read More
                    <ArrowRight className="ml-2 group-hover/link:translate-x-1 transition-transform duration-300" size={14} />
                  </Link>
                </div>
              </div>

              {/* News Card 2 */}
              <div className="bg-white rounded-xl shadow-lg overflow-hidden group hover:shadow-xl transition-all duration-300 border border-gray-100 hover:border-indigo-100">
                <div className="relative h-48 overflow-hidden">
                  <Image
                    src="https://images.unsplash.com/photo-1524178232363-1fb2b075b655?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80"
                    alt="MOU Among Universities"
                    fill
                    className="object-cover group-hover:scale-110 transition-transform duration-500"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-indigo-900/80 to-transparent"></div>
                  <div className="absolute bottom-4 left-4 bg-white/90 text-indigo-600 text-xs font-bold px-3 py-1 rounded-full flex items-center">
                    <Calendar className="mr-1" />
                    Feb 9, 2025
                  </div>
                </div>
                <div className="p-6">
                  <h3 className="text-xl font-bold text-indigo-900 mb-3 group-hover:text-indigo-600 transition-colors">
                    MOU Among Five Universities
                  </h3>
                  <p className="text-gray-600 mb-4 line-clamp-3">
                    Far Western University signed a Memorandum of Understanding with Mid-West University, Nepal Open University, Purbanchal University and Lumbini Technological University...
                  </p>
                  <Link href="#" className="text-indigo-600 font-medium flex items-center group/link">
                    Read More
                    <ArrowRight className="ml-2 group-hover/link:translate-x-1 transition-transform duration-300" size={14} />
                  </Link>
                </div>
              </div>

              {/* Event Card */}
              <div className="bg-white rounded-xl shadow-lg overflow-hidden group hover:shadow-xl transition-all duration-300 border border-gray-100 hover:border-indigo-100">
                <div className="relative h-48 overflow-hidden">
                  <Image
                    src="https://images.unsplash.com/photo-1540575467063-178a50c2df87?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80"
                    alt="International Conference"
                    fill
                    className="object-cover group-hover:scale-110 transition-transform duration-500"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-indigo-900/80 to-transparent"></div>
                  <div className="absolute top-4 right-4 bg-indigo-600/90 text-white text-xs font-bold px-3 py-1 rounded-full">
                    Upcoming Event
                  </div>
                </div>
                <div className="p-6">
                  <div className="flex items-center mb-3">
                    <div className="mr-3 text-center">
                      <div className="text-2xl font-bold text-indigo-600">25</div>
                      <div className="text-xs text-gray-500 uppercase">June</div>
                    </div>
                    <div className="border-l border-gray-200 pl-3">
                      <h3 className="text-xl font-bold text-indigo-900 group-hover:text-indigo-600 transition-colors">
                        International Conference 2025
                      </h3>
                      <div className="flex items-center text-gray-500 text-sm mt-1">
                        <MapPin className="mr-1" />
                        University Central Office
                      </div>
                    </div>
                  </div>
                  <p className="text-gray-600 mb-4 line-clamp-3">
                    Join us for the International Conference on Advanced Functional Materials 2025 at Himalaya Hotel Kathmandu in partnership with Far Western University...
                  </p>
                  <Link href="#" className="text-indigo-600 font-medium flex items-center group/link">
                    Learn More
                    <ArrowRight className="ml-2 group-hover/link:translate-x-1 transition-transform duration-300" size={14} />
                  </Link>
                </div>
              </div>
            </div>

            <div className="text-center mt-12">
              <Link
                href="#"
                className="inline-flex items-center bg-indigo-600 hover:bg-indigo-700 text-white font-medium py-3 px-8 rounded-lg shadow-md transition-all duration-300 hover:-translate-y-1"
              >
                View All News & Events
                <ArrowRight className="ml-2" />
              </Link>
            </div>
          </div>
        </section>

        <TeamSection />
        <AdvisoryBoardSection />

        {/* CSS Animations */}
        <style jsx>{`
          @keyframes float {
            0%, 100% { transform: translateY(0) scale(1); }
            50% { transform: translateY(-20px) scale(1.2); }
          }
          @keyframes wave {
            0%, 100% { transform: translateX(0); }
            50% { transform: translateX(-2%); }
          }
        `}</style>
      </main>
    </>
  );
}