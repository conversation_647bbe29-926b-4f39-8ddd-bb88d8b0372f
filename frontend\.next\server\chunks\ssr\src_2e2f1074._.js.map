{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/fwu_incubation%28react%2Blaravel%29/incubation/frontend/src/app/components/shared/SectionTitle.tsx"], "sourcesContent": ["// components/shared/SectionTitle.tsx\r\ninterface SectionTitleProps {\r\n  title: string;\r\n  subtitle?: string;\r\n  align?: 'left' | 'center' | 'right';\r\n  accentColor?: string;\r\n}\r\n\r\nconst SectionTitle: React.FC<SectionTitleProps> = ({\r\n  title,\r\n  subtitle,\r\n  align = 'center',\r\n  accentColor = 'indigo'\r\n}) => {\r\n  const alignmentClass = {\r\n    left: 'text-left',\r\n    center: 'text-center',\r\n    right: 'text-right',\r\n  };\r\n\r\n  const accentColorClass = {\r\n    indigo: 'bg-indigo-600',\r\n    blue: 'bg-blue-600',\r\n    teal: 'bg-teal-600',\r\n    purple: 'bg-purple-600',\r\n    green: 'bg-green-600',\r\n  };\r\n\r\n  const textColorClass = {\r\n    indigo: 'text-indigo-600',\r\n    blue: 'text-blue-600',\r\n    teal: 'text-teal-600',\r\n    purple: 'text-purple-600',\r\n    green: 'text-green-600',\r\n  };\r\n\r\n  return (\r\n    <div className={`mb-12 md:mb-16 ${alignmentClass[align]}`}>\r\n      {subtitle && (\r\n        <p className={`${textColorClass[accentColor as keyof typeof textColorClass]} font-semibold text-sm md:text-base uppercase tracking-wider mb-2`}>\r\n          {subtitle}\r\n        </p>\r\n      )}\r\n      <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900\">\r\n        {title}\r\n      </h2>\r\n      <div className={`mt-4 h-1.5 w-24 ${align === 'center' ? 'mx-auto' : (align === 'right' ? 'ml-auto' : '')} ${accentColorClass[accentColor as keyof typeof accentColorClass]} rounded-full`}></div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default SectionTitle;"], "names": [], "mappings": "AAAA,qCAAqC;;;;;;AAQrC,MAAM,eAA4C,CAAC,EACjD,KAAK,EACL,QAAQ,EACR,QAAQ,QAAQ,EAChB,cAAc,QAAQ,EACvB;IACC,MAAM,iBAAiB;QACrB,MAAM;QACN,QAAQ;QACR,OAAO;IACT;IAEA,MAAM,mBAAmB;QACvB,QAAQ;QACR,MAAM;QACN,MAAM;QACN,QAAQ;QACR,OAAO;IACT;IAEA,MAAM,iBAAiB;QACrB,QAAQ;QACR,MAAM;QACN,MAAM;QACN,QAAQ;QACR,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,eAAe,EAAE,cAAc,CAAC,MAAM,EAAE;;YACtD,0BACC,8OAAC;gBAAE,WAAW,GAAG,cAAc,CAAC,YAA2C,CAAC,iEAAiE,CAAC;0BAC3I;;;;;;0BAGL,8OAAC;gBAAG,WAAU;0BACX;;;;;;0BAEH,8OAAC;gBAAI,WAAW,CAAC,gBAAgB,EAAE,UAAU,WAAW,YAAa,UAAU,UAAU,YAAY,GAAI,CAAC,EAAE,gBAAgB,CAAC,YAA6C,CAAC,aAAa,CAAC;;;;;;;;;;;;AAG/L;uCAEe", "debugId": null}}, {"offset": {"line": 73, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/fwu_incubation%28react%2Blaravel%29/incubation/frontend/src/app/components/programs/GalleryImageCard.tsx"], "sourcesContent": ["\"use client\"\r\nimport Image from 'next/image';\r\nimport Link from 'next/link';\r\n\r\nexport interface GalleryImageCardProps {\r\n  imageUrl: string;\r\n  altText: string;\r\n  caption?: string;\r\n  eventDate?: string;\r\n  isLoaded?: boolean;\r\n}\r\n\r\nconst GalleryImageCard: React.FC<GalleryImageCardProps> = ({\r\n  imageUrl,\r\n  altText,\r\n  caption,\r\n  eventDate,\r\n  isLoaded = true\r\n}) => {\r\n  return (\r\n    <Link href=\"/gallery\" className=\"block\">\r\n      <div className=\"relative group rounded-lg overflow-hidden shadow-lg h-64 transform hover:shadow-2xl transition-all duration-300 hover:-translate-y-1 bg-gray-200\">\r\n        <div className=\"w-full h-full relative\">\r\n          {isLoaded && (\r\n            <Image\r\n              src={imageUrl}\r\n              alt={altText}\r\n              fill\r\n              className=\"object-cover transform group-hover:scale-110 transition-transform duration-500 ease-in-out\"\r\n              onError={(e) => {\r\n                // Fallback to a placeholder on error\r\n                const target = e.target as HTMLImageElement;\r\n                target.src = \"https://via.placeholder.com/800x600/e2e8f0/475569?text=FWU+Incubation\";\r\n              }}\r\n            />\r\n          )}\r\n        </div>\r\n        {(caption || eventDate) && (\r\n          <div className=\"absolute inset-0 bg-gradient-to-t from-black/70 via-black/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex flex-col justify-end p-4\">\r\n            {caption && <h4 className=\"text-white text-lg font-semibold\">{caption}</h4>}\r\n            {eventDate && <p className=\"text-gray-300 text-sm\">{eventDate}</p>}\r\n          </div>\r\n        )}\r\n      </div>\r\n    </Link>\r\n  );\r\n};\r\n\r\nexport default GalleryImageCard;"], "names": [], "mappings": ";;;;AACA;AACA;AAFA;;;;AAYA,MAAM,mBAAoD,CAAC,EACzD,QAAQ,EACR,OAAO,EACP,OAAO,EACP,SAAS,EACT,WAAW,IAAI,EAChB;IACC,qBACE,8OAAC,4JAAA,CAAA,UAAI;QAAC,MAAK;QAAW,WAAU;kBAC9B,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACZ,0BACC,8OAAC,6HAAA,CAAA,UAAK;wBACJ,KAAK;wBACL,KAAK;wBACL,IAAI;wBACJ,WAAU;wBACV,SAAS,CAAC;4BACR,qCAAqC;4BACrC,MAAM,SAAS,EAAE,MAAM;4BACvB,OAAO,GAAG,GAAG;wBACf;;;;;;;;;;;gBAIL,CAAC,WAAW,SAAS,mBACpB,8OAAC;oBAAI,WAAU;;wBACZ,yBAAW,8OAAC;4BAAG,WAAU;sCAAoC;;;;;;wBAC7D,2BAAa,8OAAC;4BAAE,WAAU;sCAAyB;;;;;;;;;;;;;;;;;;;;;;;AAMhE;uCAEe", "debugId": null}}, {"offset": {"line": 156, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/fwu_incubation%28react%2Blaravel%29/incubation/frontend/src/app/components/programs/PastEventsGallerySection%20.tsx"], "sourcesContent": ["\"use client\"\r\nimport SectionTitle from '../shared/SectionTitle';\r\nimport GalleryImageCard from './GalleryImageCard';\r\nimport Link from 'next/link';\r\nimport { useEffect, useState } from 'react';\r\n\r\n// Gallery images data with Unsplash images\r\nconst pastEventsData = [\r\n  {\r\n    id: 'pe1',\r\n    imageUrl: 'https://images.unsplash.com/photo-1517245386807-bb43f82c33c4?q=80&w=800&auto=format&fit=crop',\r\n    altText: 'Incubation Center Planning Meeting',\r\n    caption: 'Incubation Center Planning Meeting',\r\n    eventDate: 'March 15, 2025',\r\n  },\r\n  {\r\n    id: 'pe2',\r\n    imageUrl: 'https://images.unsplash.com/photo-1519389950473-47ba0277781c?q=80&w=800&auto=format&fit=crop',\r\n    altText: 'Startup Mentorship Session',\r\n    caption: 'Startup Mentorship Session',\r\n    eventDate: 'March 20, 2025',\r\n  },\r\n  {\r\n    id: 'pe3',\r\n    imageUrl: 'https://images.unsplash.com/photo-1531482615713-2afd69097998?q=80&w=800&auto=format&fit=crop',\r\n    altText: 'Entrepreneurship Skills Development',\r\n    caption: 'Entrepreneurship Skills Development',\r\n    eventDate: 'January 25, 2025',\r\n  },\r\n  {\r\n    id: 'pe4',\r\n    imageUrl: 'https://images.unsplash.com/photo-1540575467063-178a50c2df87?q=80&w=800&auto=format&fit=crop',\r\n    altText: 'International Conference on Innovation',\r\n    caption: 'International Conference on Innovation',\r\n    eventDate: 'February 15, 2025',\r\n  },\r\n  {\r\n    id: 'pe5',\r\n    imageUrl: 'https://images.unsplash.com/photo-1504384308090-c894fdcc538d?q=80&w=800&auto=format&fit=crop',\r\n    altText: 'Innovation Hackathon',\r\n    caption: 'Innovation Hackathon',\r\n    eventDate: 'April 12-13, 2025',\r\n  },\r\n  {\r\n    id: 'pe6',\r\n    imageUrl: 'https://images.unsplash.com/photo-1560439514-4e9645039924?q=80&w=800&auto=format&fit=crop',\r\n    altText: 'MOU Signing with Industry Partners',\r\n    caption: 'MOU Signing with Industry Partners',\r\n    eventDate: 'December 10, 2024',\r\n  },\r\n];\r\n\r\nconst PastEventsGallerySection = () => {\r\n  const [isLoaded, setIsLoaded] = useState(false);\r\n\r\n  // Simulate image loading\r\n  useEffect(() => {\r\n    setIsLoaded(true);\r\n  }, []);\r\n\r\n  return (\r\n    <section className=\"py-16 md:py-24 bg-gradient-to-b from-brand-light to-white\">\r\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\r\n        <SectionTitle title=\"FWU Incubation Center Gallery\" subtitle=\"Moments & Memories\" />\r\n\r\n        <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8 mt-12\">\r\n          {pastEventsData.map((event, index) => (\r\n            <div\r\n              key={event.id}\r\n              className=\"opacity-0 animate-fadeIn\"\r\n              style={{\r\n                animationDelay: `${index * 150}ms`,\r\n                animationFillMode: 'forwards'\r\n              }}\r\n            >\r\n              <GalleryImageCard\r\n                imageUrl={event.imageUrl}\r\n                altText={event.altText}\r\n                caption={event.caption}\r\n                eventDate={event.eventDate}\r\n                isLoaded={isLoaded}\r\n              />\r\n            </div>\r\n          ))}\r\n        </div>\r\n\r\n        <div className=\"text-center mt-12 opacity-0 animate-fadeIn animation-delay-1000\" style={{ animationFillMode: 'forwards' }}>\r\n          <Link\r\n            href=\"/gallery\"\r\n            className=\"inline-block bg-brand-primary hover:bg-brand-primary-dark border border-blue-400 font-semibold py-3 px-8 rounded-lg shadow-md transition-all duration-300 hover:scale-105 transform\"\r\n          >\r\n            View Full Gallery\r\n          </Link>\r\n          <p className=\"text-gray-600 mt-2\">\r\n            Explore more photos from our events, workshops, and partnerships\r\n          </p>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\nexport default PastEventsGallerySection;"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AAJA;;;;;;AAMA,2CAA2C;AAC3C,MAAM,iBAAiB;IACrB;QACE,IAAI;QACJ,UAAU;QACV,SAAS;QACT,SAAS;QACT,WAAW;IACb;IACA;QACE,IAAI;QACJ,UAAU;QACV,SAAS;QACT,SAAS;QACT,WAAW;IACb;IACA;QACE,IAAI;QACJ,UAAU;QACV,SAAS;QACT,SAAS;QACT,WAAW;IACb;IACA;QACE,IAAI;QACJ,UAAU;QACV,SAAS;QACT,SAAS;QACT,WAAW;IACb;IACA;QACE,IAAI;QACJ,UAAU;QACV,SAAS;QACT,SAAS;QACT,WAAW;IACb;IACA;QACE,IAAI;QACJ,UAAU;QACV,SAAS;QACT,SAAS;QACT,WAAW;IACb;CACD;AAED,MAAM,2BAA2B;IAC/B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,yBAAyB;IACzB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,YAAY;IACd,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,mJAAA,CAAA,UAAY;oBAAC,OAAM;oBAAgC,UAAS;;;;;;8BAE7D,8OAAC;oBAAI,WAAU;8BACZ,eAAe,GAAG,CAAC,CAAC,OAAO,sBAC1B,8OAAC;4BAEC,WAAU;4BACV,OAAO;gCACL,gBAAgB,GAAG,QAAQ,IAAI,EAAE,CAAC;gCAClC,mBAAmB;4BACrB;sCAEA,cAAA,8OAAC,yJAAA,CAAA,UAAgB;gCACf,UAAU,MAAM,QAAQ;gCACxB,SAAS,MAAM,OAAO;gCACtB,SAAS,MAAM,OAAO;gCACtB,WAAW,MAAM,SAAS;gCAC1B,UAAU;;;;;;2BAZP,MAAM,EAAE;;;;;;;;;;8BAkBnB,8OAAC;oBAAI,WAAU;oBAAkE,OAAO;wBAAE,mBAAmB;oBAAW;;sCACtH,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;sCACX;;;;;;sCAGD,8OAAC;4BAAE,WAAU;sCAAqB;;;;;;;;;;;;;;;;;;;;;;;AAO5C;uCAEe", "debugId": null}}, {"offset": {"line": 311, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/fwu_incubation%28react%2Blaravel%29/incubation/frontend/src/app/components/programs/UpcomingEventCard.tsx"], "sourcesContent": ["\"use client\"\r\nimport { Calendar, Clock, MapPin, ChevronRight } from 'lucide-react';\r\nimport { useState } from 'react';\r\n\r\nexport interface UpcomingEvent {\r\n  id: string;\r\n  date: string; // e.g., \"Oct 25\"\r\n  fullDate: string; // e.g., \"October 25, 2024\"\r\n  time?: string; // e.g., \"10:00 AM - 04:00 PM\"\r\n  title: string;\r\n  type: string; // e.g., \"Workshop\", \"Deadline\", \"Networking\"\r\n  location?: string; // e.g., \"Online\" or \"FWU Auditorium\"\r\n  description: string;\r\n  color?: string; // Tailwind color class e.g. 'bg-blue-500'\r\n  detailedDescription?: string; // More details for expanded view\r\n  registrationLink?: string; // Link to register for the event\r\n}\r\n\r\ninterface UpcomingEventCardProps {\r\n  event: UpcomingEvent;\r\n}\r\n\r\nconst UpcomingEventCard: React.FC<UpcomingEventCardProps> = ({ event }) => {\r\n  const [isExpanded, setIsExpanded] = useState(false);\r\n  const typeColor = event.color || 'bg-brand-accent';\r\n\r\n  const toggleExpand = () => {\r\n    setIsExpanded(!isExpanded);\r\n  };\r\n\r\n  return (\r\n    <div className=\"bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden flex flex-col group opacity-0 animate-fadeIn hover:shadow-xl transition-shadow duration-300\">\r\n      <div className=\"flex flex-col md:flex-row\">\r\n        {/* Date Column */}\r\n        <div\r\n          className={`p-8 md:w-1/4 flex flex-col items-center justify-center text-white ${typeColor} transition-transform duration-300 relative overflow-hidden`}\r\n        >\r\n          {/* Background pattern - using CSS pattern instead of image */}\r\n          <div className=\"absolute inset-0 opacity-20\"\r\n               style={{\r\n                 backgroundImage: 'radial-gradient(#ffffff 1px, transparent 1px)',\r\n                 backgroundSize: '10px 10px'\r\n               }}>\r\n          </div>\r\n\r\n          <div className=\"relative\">\r\n            <div className=\"text-5xl font-bold mb-1\">{event.date.split(' ')[1]}</div>\r\n            <div className=\"text-lg uppercase font-medium\">{event.date.split(' ')[0]}</div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Content Column */}\r\n        <div className=\"p-8 flex-grow\">\r\n          <div className=\"flex justify-between items-start mb-4\">\r\n            <span className={`inline-block px-4 py-1 text-xs font-semibold text-white ${typeColor} rounded-full`}>\r\n              {event.type}\r\n            </span>\r\n            <button\r\n              onClick={toggleExpand}\r\n              className=\"w-8 h-8 flex items-center justify-center bg-gray-100 hover:bg-gray-200 text-gray-600 rounded-full focus:outline-none transition-all duration-200 hover:scale-110 active:scale-95\"\r\n              aria-label={isExpanded ? \"Collapse details\" : \"Expand details\"}\r\n            >\r\n              <div className={`transform transition-transform duration-300 ${isExpanded ? 'rotate-90' : 'rotate-0'}`}>\r\n                <ChevronRight size={18} />\r\n              </div>\r\n            </button>\r\n          </div>\r\n\r\n          <h3 className=\"text-xl font-bold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors\">\r\n            {event.title}\r\n          </h3>\r\n\r\n          <p className=\"text-gray-600 mb-5 leading-relaxed\">{event.description}</p>\r\n\r\n          <div className=\"flex flex-wrap gap-4 text-sm text-gray-600\">\r\n            <div className=\"flex items-center bg-gray-50 px-3 py-1 rounded-full\">\r\n              <Calendar className=\"mr-2 text-blue-500\" /> {event.fullDate}\r\n            </div>\r\n            {event.time && (\r\n              <div className=\"flex items-center bg-gray-50 px-3 py-1 rounded-full\">\r\n                <Clock className=\"mr-2 text-blue-500\" /> {event.time}\r\n              </div>\r\n            )}\r\n            {event.location && (\r\n              <div className=\"flex items-center bg-gray-50 px-3 py-1 rounded-full\">\r\n                <MapPin className=\"mr-2 text-blue-500\" /> {event.location}\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Expandable Details Section */}\r\n      <div\r\n        className={`px-8 pb-8 pt-0 border-t border-gray-100 mt-2 overflow-hidden transition-all duration-500 ease-in-out ${\r\n          isExpanded ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'\r\n        }`}\r\n      >\r\n        <div className=\"pt-6 text-gray-700 leading-relaxed\">\r\n          <p>{event.detailedDescription || \"More details about this event will be announced soon. Stay tuned for updates!\"}</p>\r\n        </div>\r\n\r\n        {event.registrationLink && (\r\n          <div className=\"mt-6 flex\">\r\n            <a\r\n              href={event.registrationLink}\r\n              className=\"inline-flex items-center bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-6 rounded-lg transition-all duration-300 hover:scale-105 active:scale-95\"\r\n            >\r\n              <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\" />\r\n              </svg>\r\n              Register Now\r\n            </a>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default UpcomingEventCard;"], "names": [], "mappings": ";;;;AACA;AAAA;AAAA;AAAA;AACA;AAFA;;;;AAsBA,MAAM,oBAAsD,CAAC,EAAE,KAAK,EAAE;IACpE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,YAAY,MAAM,KAAK,IAAI;IAEjC,MAAM,eAAe;QACnB,cAAc,CAAC;IACjB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBACC,WAAW,CAAC,kEAAkE,EAAE,UAAU,2DAA2D,CAAC;;0CAGtJ,8OAAC;gCAAI,WAAU;gCACV,OAAO;oCACL,iBAAiB;oCACjB,gBAAgB;gCAClB;;;;;;0CAGL,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAA2B,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;;;;;;kDAClE,8OAAC;wCAAI,WAAU;kDAAiC,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;;;;;;;;;;;;;;;;;;kCAK5E,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAW,CAAC,wDAAwD,EAAE,UAAU,aAAa,CAAC;kDACjG,MAAM,IAAI;;;;;;kDAEb,8OAAC;wCACC,SAAS;wCACT,WAAU;wCACV,cAAY,aAAa,qBAAqB;kDAE9C,cAAA,8OAAC;4CAAI,WAAW,CAAC,4CAA4C,EAAE,aAAa,cAAc,YAAY;sDACpG,cAAA,8OAAC,sNAAA,CAAA,eAAY;gDAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;0CAK1B,8OAAC;gCAAG,WAAU;0CACX,MAAM,KAAK;;;;;;0CAGd,8OAAC;gCAAE,WAAU;0CAAsC,MAAM,WAAW;;;;;;0CAEpE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAuB;4CAAE,MAAM,QAAQ;;;;;;;oCAE5D,MAAM,IAAI,kBACT,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAAuB;4CAAE,MAAM,IAAI;;;;;;;oCAGvD,MAAM,QAAQ,kBACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,0MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAuB;4CAAE,MAAM,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;0BAQnE,8OAAC;gBACC,WAAW,CAAC,qGAAqG,EAC/G,aAAa,yBAAyB,qBACtC;;kCAEF,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;sCAAG,MAAM,mBAAmB,IAAI;;;;;;;;;;;oBAGlC,MAAM,gBAAgB,kBACrB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,MAAM,MAAM,gBAAgB;4BAC5B,WAAU;;8CAEV,8OAAC;oCAAI,OAAM;oCAA6B,WAAU;oCAAe,MAAK;oCAAO,SAAQ;oCAAY,QAAO;8CACtG,cAAA,8OAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;gCACjE;;;;;;;;;;;;;;;;;;;;;;;;AAQpB;uCAEe", "debugId": null}}, {"offset": {"line": 591, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/fwu_incubation%28react%2Blaravel%29/incubation/frontend/src/app/components/programs/ProgramCalendarSection%20.tsx"], "sourcesContent": ["\"use client\"\r\nimport UpcomingEventCard, { UpcomingEvent } from './UpcomingEventCard';\r\nimport { Calendar, Filter, Search } from 'lucide-react';\r\nimport { useState } from 'react';\r\n\r\n// Enhanced Dummy Data with more details\r\nconst upcomingEventsData: UpcomingEvent[] = [\r\n  {\r\n    id: 'ue1',\r\n    date: 'NOV 15',\r\n    fullDate: 'November 15, 2024',\r\n    time: '09:00 AM - 05:00 PM',\r\n    title: 'Design Thinking Workshop for Innovators',\r\n    type: 'Workshop',\r\n    location: 'FWU Incubation Hall A',\r\n    description: 'Learn human-centered design principles to create impactful solutions.',\r\n    color: 'bg-purple-500',\r\n    detailedDescription: 'This full-day workshop will introduce participants to the core principles of design thinking and how to apply them to solve complex problems. Led by industry experts, you will learn techniques for empathizing with users, defining problems, ideating solutions, prototyping, and testing. By the end of the workshop, you will have a practical toolkit for approaching innovation challenges with a human-centered mindset.',\r\n    registrationLink: '/register/design-thinking-workshop'\r\n  },\r\n  {\r\n    id: 'ue2',\r\n    date: 'NOV 28',\r\n    fullDate: 'November 28, 2024',\r\n    title: 'Application Deadline: Winter Cohort 2025',\r\n    type: 'Deadline',\r\n    description: 'Submit your startup applications for the upcoming winter incubation program.',\r\n    color: 'bg-red-500',\r\n    detailedDescription: 'The Winter Cohort 2025 is our flagship 12-week incubation program designed for early-stage startups ready to accelerate their growth. Selected startups will receive mentorship, workspace, seed funding opportunities, and access to our network of investors and industry partners. Applications must include your business plan, team information, current traction, and growth strategy.',\r\n    registrationLink: '/apply/winter-cohort-2025'\r\n  },\r\n  {\r\n    id: 'ue3',\r\n    date: 'DEC 05',\r\n    fullDate: 'December 05, 2024',\r\n    time: '02:00 PM - 04:00 PM',\r\n    title: 'Investor Connect: Meet & Greet',\r\n    type: 'Networking',\r\n    location: 'Online (Zoom)',\r\n    description: 'An opportunity for selected startups to interact with potential investors.',\r\n    color: 'bg-teal-500',\r\n    detailedDescription: 'This exclusive virtual networking event brings together promising startups and potential investors in a structured yet casual format. Each startup will have the opportunity to introduce their venture in a brief pitch, followed by breakout rooms for more in-depth conversations with interested investors. This is not a formal pitching event but rather a chance to build relationships that could lead to future investment opportunities.',\r\n    registrationLink: '/register/investor-connect'\r\n  },\r\n  {\r\n    id: 'ue4',\r\n    date: 'DEC 12',\r\n    fullDate: 'December 12-14, 2024',\r\n    title: 'FinTech Hackathon Challenge',\r\n    type: 'Hackathon',\r\n    location: 'FWU Main Auditorium',\r\n    description: 'Develop innovative solutions for the financial technology sector and win prizes.',\r\n    color: 'bg-blue-600',\r\n    detailedDescription: 'Join us for an intensive 48-hour hackathon focused on developing innovative solutions for the financial technology sector. Participants will form teams to tackle real-world challenges provided by our industry partners. Cash prizes totaling $10,000 will be awarded to the top three teams, with the first-place team also receiving incubation support to develop their solution further. All skill levels are welcome, and mentors will be available throughout the event.',\r\n    registrationLink: '/register/fintech-hackathon'\r\n  },\r\n  {\r\n    id: 'ue5',\r\n    date: 'JAN 10',\r\n    fullDate: 'January 10, 2025',\r\n    time: '10:00 AM - 12:00 PM',\r\n    title: 'Funding Strategies for Early-Stage Startups',\r\n    type: 'Workshop',\r\n    location: 'FWU Incubation Center',\r\n    description: 'Learn about different funding options and how to approach investors effectively.',\r\n    color: 'bg-purple-500',\r\n    detailedDescription: 'This workshop will cover various funding strategies available to early-stage startups, including bootstrapping, angel investment, venture capital, grants, and crowdfunding. Our expert speakers will share insights on when to pursue each option, how to prepare your startup for investment, and tactics for successful fundraising. The session will include case studies of successful funding journeys and common pitfalls to avoid.',\r\n    registrationLink: '/register/funding-strategies-workshop'\r\n  },\r\n];\r\n\r\n// Helper to sort events by fullDate (simplistic, assumes \"Month Day, Year\" format)\r\nconst sortEvents = (events: UpcomingEvent[]): UpcomingEvent[] => {\r\n  return events.sort((a, b) => new Date(a.fullDate).getTime() - new Date(b.fullDate).getTime());\r\n};\r\n\r\n// Get unique event types for filtering\r\nconst getUniqueEventTypes = (events: UpcomingEvent[]): string[] => {\r\n  return Array.from(new Set(events.map(event => event.type))).sort();\r\n};\r\n\r\nconst ProgramCalendarSection = () => {\r\n  const [searchTerm, setSearchTerm] = useState('');\r\n  const [selectedType, setSelectedType] = useState('');\r\n\r\n  const eventTypes = getUniqueEventTypes(upcomingEventsData);\r\n\r\n  // Filter events based on search term and selected type\r\n  const filteredEvents = upcomingEventsData\r\n    .filter(event =>\r\n      event.title.toLowerCase().includes(searchTerm.toLowerCase()) ||\r\n      event.description.toLowerCase().includes(searchTerm.toLowerCase())\r\n    )\r\n    .filter(event =>\r\n      selectedType ? event.type === selectedType : true\r\n    );\r\n\r\n  const sortedEvents = sortEvents(filteredEvents);\r\n\r\n  return (\r\n    <section className=\"py-20 md:py-28 bg-white\">\r\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\r\n        {/* Section header with decorative elements */}\r\n        <div className=\"relative mb-16\">\r\n          <div className=\"absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-24 h-1 bg-blue-200 rounded-full\"></div>\r\n          <div className=\"text-center\">\r\n            <p className=\"text-blue-600 font-semibold mb-2\">Stay Informed</p>\r\n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900\">Upcoming Events at FWU Incubation Center</h2>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Search and Filter Controls */}\r\n        <div className=\"mb-12 mt-8 bg-white p-6 rounded-xl shadow-lg border border-gray-100\">\r\n          <div className=\"flex flex-col md:flex-row gap-6\">\r\n            <div className=\"relative flex-grow\">\r\n              <div className=\"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none\">\r\n                <Search className=\"text-blue-500\" />\r\n              </div>\r\n              <input\r\n                type=\"text\"\r\n                placeholder=\"Search events...\"\r\n                value={searchTerm}\r\n                onChange={(e) => setSearchTerm(e.target.value)}\r\n                className=\"block w-full pl-12 pr-4 py-3 border-2 border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-400 focus:border-blue-400 transition-colors\"\r\n              />\r\n            </div>\r\n\r\n            <div className=\"relative w-full md:w-auto\">\r\n              <div className=\"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none\">\r\n                <Filter className=\"text-blue-500\" />\r\n              </div>\r\n              <select\r\n                value={selectedType}\r\n                onChange={(e) => setSelectedType(e.target.value)}\r\n                className=\"block w-full md:w-56 pl-12 pr-10 py-3 border-2 border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-400 focus:border-blue-400 transition-colors appearance-none bg-no-repeat bg-right\"\r\n                style={{ backgroundImage: \"url(\\\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e\\\")\", backgroundSize: \"1.5em 1.5em\", backgroundPosition: \"right 0.75rem center\" }}\r\n              >\r\n                <option value=\"\">All Event Types</option>\r\n                {eventTypes.map(type => (\r\n                  <option key={type} value={type}>{type}</option>\r\n                ))}\r\n              </select>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Events List */}\r\n        {sortedEvents.length > 0 ? (\r\n          <div className=\"space-y-10\">\r\n            {sortedEvents.map((event, index) => (\r\n              <div\r\n                key={event.id}\r\n                className=\"opacity-0 animate-fadeIn\"\r\n                style={{ animationDelay: `${index * 150}ms`, animationFillMode: 'forwards' }}\r\n              >\r\n                <UpcomingEventCard event={event} />\r\n              </div>\r\n            ))}\r\n          </div>\r\n        ) : (\r\n          <div className=\"text-center py-20 bg-white rounded-xl shadow-lg border border-gray-100 opacity-0 animate-fadeIn\">\r\n            <div className=\"w-20 h-20 mx-auto bg-blue-50 rounded-full flex items-center justify-center mb-6\">\r\n              <Calendar className=\"text-blue-500 text-3xl\" />\r\n            </div>\r\n            <h3 className=\"text-2xl font-bold text-gray-900 mb-3\">No Events Found</h3>\r\n            <p className=\"text-gray-600 max-w-md mx-auto\">\r\n              {searchTerm || selectedType\r\n                ? \"No events match your current search criteria. Try adjusting your filters.\"\r\n                : \"No upcoming events scheduled at the moment. Please check back soon!\"}\r\n            </p>\r\n          </div>\r\n        )}\r\n\r\n        {/* Calendar Subscription */}\r\n        <div className=\"mt-20 opacity-0 animate-fadeIn animation-delay-500\" style={{ animationFillMode: 'forwards' }}>\r\n          <div className=\"bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl shadow-lg p-10 text-center\">\r\n            <div className=\"w-16 h-16 mx-auto bg-white rounded-full flex items-center justify-center mb-6 shadow-md\">\r\n              <Calendar className=\"text-blue-600 text-2xl\" />\r\n            </div>\r\n\r\n            <h3 className=\"text-2xl font-bold text-gray-900 mb-3\">Stay Updated with Our Events</h3>\r\n            <p className=\"text-gray-700 max-w-2xl mx-auto mb-8\">\r\n              Subscribe to our calendar to receive automatic updates about upcoming events, workshops,\r\n              and programs at the FWU Incubation Center.\r\n            </p>\r\n\r\n            <a\r\n              href=\"/subscribe-calendar\" // Link to iCal feed or subscription page\r\n              className=\"inline-flex items-center bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-8 rounded-lg shadow-md transition-all duration-300 hover:scale-105 transform\"\r\n            >\r\n              <Calendar className=\"mr-2\" />\r\n              Subscribe to Calendar\r\n            </a>\r\n            <p className=\"text-sm text-gray-600 mt-4\">\r\n              Never miss an event! Add our calendar to your preferred calendar app.\r\n            </p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\nexport default ProgramCalendarSection;"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AAAA;AACA;AAHA;;;;;AAKA,wCAAwC;AACxC,MAAM,qBAAsC;IAC1C;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,MAAM;QACN,OAAO;QACP,MAAM;QACN,UAAU;QACV,aAAa;QACb,OAAO;QACP,qBAAqB;QACrB,kBAAkB;IACpB;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,OAAO;QACP,MAAM;QACN,aAAa;QACb,OAAO;QACP,qBAAqB;QACrB,kBAAkB;IACpB;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,MAAM;QACN,OAAO;QACP,MAAM;QACN,UAAU;QACV,aAAa;QACb,OAAO;QACP,qBAAqB;QACrB,kBAAkB;IACpB;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,OAAO;QACP,MAAM;QACN,UAAU;QACV,aAAa;QACb,OAAO;QACP,qBAAqB;QACrB,kBAAkB;IACpB;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,MAAM;QACN,OAAO;QACP,MAAM;QACN,UAAU;QACV,aAAa;QACb,OAAO;QACP,qBAAqB;QACrB,kBAAkB;IACpB;CACD;AAED,mFAAmF;AACnF,MAAM,aAAa,CAAC;IAClB,OAAO,OAAO,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,QAAQ,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,QAAQ,EAAE,OAAO;AAC5F;AAEA,uCAAuC;AACvC,MAAM,sBAAsB,CAAC;IAC3B,OAAO,MAAM,IAAI,CAAC,IAAI,IAAI,OAAO,GAAG,CAAC,CAAA,QAAS,MAAM,IAAI,IAAI,IAAI;AAClE;AAEA,MAAM,yBAAyB;IAC7B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,aAAa,oBAAoB;IAEvC,uDAAuD;IACvD,MAAM,iBAAiB,mBACpB,MAAM,CAAC,CAAA,QACN,MAAM,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACzD,MAAM,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,KAEhE,MAAM,CAAC,CAAA,QACN,eAAe,MAAM,IAAI,KAAK,eAAe;IAGjD,MAAM,eAAe,WAAW;IAEhC,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;8CAAmC;;;;;;8CAChD,8OAAC;oCAAG,WAAU;8CAA+C;;;;;;;;;;;;;;;;;;8BAKjE,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;;;;;;kDAEpB,8OAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wCAC7C,WAAU;;;;;;;;;;;;0CAId,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;;;;;;kDAEpB,8OAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;wCAC/C,WAAU;wCACV,OAAO;4CAAE,iBAAiB;4CAAuO,gBAAgB;4CAAe,oBAAoB;wCAAuB;;0DAE3U,8OAAC;gDAAO,OAAM;0DAAG;;;;;;4CAChB,WAAW,GAAG,CAAC,CAAA,qBACd,8OAAC;oDAAkB,OAAO;8DAAO;mDAApB;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAQtB,aAAa,MAAM,GAAG,kBACrB,8OAAC;oBAAI,WAAU;8BACZ,aAAa,GAAG,CAAC,CAAC,OAAO,sBACxB,8OAAC;4BAEC,WAAU;4BACV,OAAO;gCAAE,gBAAgB,GAAG,QAAQ,IAAI,EAAE,CAAC;gCAAE,mBAAmB;4BAAW;sCAE3E,cAAA,8OAAC,0JAAA,CAAA,UAAiB;gCAAC,OAAO;;;;;;2BAJrB,MAAM,EAAE;;;;;;;;;yCASnB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,0MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;;;;;;sCAEtB,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,8OAAC;4BAAE,WAAU;sCACV,cAAc,eACX,8EACA;;;;;;;;;;;;8BAMV,8OAAC;oBAAI,WAAU;oBAAqD,OAAO;wBAAE,mBAAmB;oBAAW;8BACzG,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;0CAGtB,8OAAC;gCAAG,WAAU;0CAAwC;;;;;;0CACtD,8OAAC;gCAAE,WAAU;0CAAuC;;;;;;0CAKpD,8OAAC;gCACC,MAAK,sBAAsB,yCAAyC;;gCACpE,WAAU;;kDAEV,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAS;;;;;;;0CAG/B,8OAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQtD;uCAEe", "debugId": null}}, {"offset": {"line": 994, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/fwu_incubation%28react%2Blaravel%29/incubation/frontend/src/app/components/programs/ProgramTypeCard.tsx"], "sourcesContent": ["import { IconType } from 'react-icons';\r\nimport { ArrowRight } from 'lucide-react';\r\nimport Link from 'next/link';\r\n\r\nexport interface ProgramTypeCardProps {\r\n  icon: React.ReactElement<IconType>;\r\n  title: string;\r\n  description: string;\r\n  linkText?: string;\r\n  bgColorClass?: string; // For varied card backgrounds\r\n  slug: string;\r\n}\r\n\r\nconst ProgramTypeCard: React.FC<ProgramTypeCardProps> = ({\r\n  icon,\r\n  title,\r\n  description,\r\n  linkText = \"Learn More\",\r\n  bgColorClass = \"bg-white\",\r\n  slug\r\n}) => {\r\n  return (\r\n    <div className={`${bgColorClass} rounded-2xl border border-gray-100 shadow-lg p-8 flex flex-col h-full relative overflow-hidden group transition-all duration-300 hover:-translate-y-2 hover:shadow-xl`}>\r\n      {/* Top accent line */}\r\n      <div className=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-blue-400 to-indigo-500 transform origin-left scale-x-0 group-hover:scale-x-100 transition-transform duration-500\"></div>\r\n\r\n      {/* Icon with animated background */}\r\n      <div className=\"relative mb-8\">\r\n        <div className=\"w-16 h-16 rounded-2xl bg-blue-50 flex items-center justify-center mb-2 group-hover:bg-blue-100 transition-colors duration-300\">\r\n          <div className=\"text-blue-600 text-3xl\">\r\n            {icon}\r\n          </div>\r\n        </div>\r\n        <div className=\"w-8 h-1 bg-blue-200 rounded-full\"></div>\r\n      </div>\r\n\r\n      <h3 className=\"text-2xl font-bold text-gray-900 mb-4 group-hover:text-blue-600 transition-colors duration-300\">{title}</h3>\r\n      <p className=\"text-gray-600 mb-8 flex-grow leading-relaxed\">{description}</p>\r\n\r\n      <Link\r\n        href={`/programs/${slug}`}\r\n        className=\"mt-auto flex items-center\"\r\n      >\r\n        <span className=\"mr-3 font-semibold text-blue-600\">{linkText}</span>\r\n        <div className=\"w-8 h-8 rounded-full bg-blue-50 flex items-center justify-center group-hover:bg-blue-100 transition-colors duration-300\">\r\n          <span className=\"transform group-hover:translate-x-1 transition-transform duration-300\">\r\n            <ArrowRight className=\"text-blue-600\" />\r\n          </span>\r\n        </div>\r\n      </Link>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ProgramTypeCard;"], "names": [], "mappings": ";;;;AACA;AACA;;;;AAWA,MAAM,kBAAkD,CAAC,EACvD,IAAI,EACJ,KAAK,EACL,WAAW,EACX,WAAW,YAAY,EACvB,eAAe,UAAU,EACzB,IAAI,EACL;IACC,qBACE,8OAAC;QAAI,WAAW,GAAG,aAAa,sKAAsK,CAAC;;0BAErM,8OAAC;gBAAI,WAAU;;;;;;0BAGf,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;kCAGL,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,8OAAC;gBAAG,WAAU;0BAAkG;;;;;;0BAChH,8OAAC;gBAAE,WAAU;0BAAgD;;;;;;0BAE7D,8OAAC,4JAAA,CAAA,UAAI;gBACH,MAAM,CAAC,UAAU,EAAE,MAAM;gBACzB,WAAU;;kCAEV,8OAAC;wBAAK,WAAU;kCAAoC;;;;;;kCACpD,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAK,WAAU;sCACd,cAAA,8OAAC,kNAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMlC;uCAEe", "debugId": null}}, {"offset": {"line": 1114, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/fwu_incubation%28react%2Blaravel%29/incubation/frontend/src/data/programsData.tsx"], "sourcesContent": ["import { Laptop, Users, Rocket, GraduationCap } from 'lucide-react';\r\n\r\nexport const programsData = [\r\n  {\r\n    id: 'bootcamp',\r\n    slug: 'intensive-bootcamps',\r\n    icon: <Laptop />,\r\n    title: 'Intensive Bootcamps',\r\n    description: 'Deep-dive, skill-based training programs designed to rapidly accelerate your knowledge in specific tech and business domains. Perfect for skill acquisition and project development.',\r\n    bgColorClass: 'bg-gradient-to-br from-indigo-50 via-white to-purple-50',\r\n    longDescription: 'Our intensive bootcamps are designed to transform beginners into skilled practitioners in a short period of time. Through hands-on projects, expert mentorship, and a carefully structured curriculum, participants gain practical experience and build a portfolio of work that demonstrates their capabilities. Each bootcamp focuses on a specific technology stack or business domain, ensuring that participants develop deep expertise in areas that are in high demand in the market. Our bootcamps are led by industry professionals who bring real-world experience and insights to the classroom, making the learning experience both practical and relevant.',\r\n    benefits: [\r\n      'Accelerated learning in a condensed timeframe',\r\n      'Hands-on project-based curriculum',\r\n      'Direct mentorship from industry professionals',\r\n      'Networking opportunities with peers and potential employers',\r\n      'Certificate of completion recognized by industry partners'\r\n    ],\r\n    duration: '4-12 weeks, depending on program',\r\n    schedule: 'Full-time (Mon-Fri, 9am-5pm) or Part-time options available',\r\n    location: 'FWU Campus and Online (hybrid model)',\r\n    capacity: '20-30 participants per cohort',\r\n    applicationProcess: [\r\n      'Submit application form with background information',\r\n      'Complete technical assessment (if applicable)',\r\n      'Interview with program coordinators',\r\n      'Receive acceptance decision within 2 weeks',\r\n      'Pay program fee to secure your spot'\r\n    ],\r\n    imageUrl: '/programs/bootcamp-hero.jpg',\r\n    upcomingDates: [\r\n      { date: 'January 15, 2025', title: 'Web Development Bootcamp' },\r\n      { date: 'March 1, 2025', title: 'Data Science Fundamentals' },\r\n      { date: 'May 10, 2025', title: 'Mobile App Development' }\r\n    ]\r\n  },\r\n  {\r\n    id: 'hackathon',\r\n    slug: 'innovation-hackathons',\r\n    icon: <Users />,\r\n    title: 'Innovation Hackathons',\r\n    description: 'High-energy, collaborative events where participants team up to solve real-world challenges and build innovative prototypes within a limited timeframe. Fuel creativity and teamwork.',\r\n    bgColorClass: 'bg-gradient-to-br from-teal-50 via-white to-cyan-50',\r\n    longDescription: 'Our hackathons bring together diverse talents to tackle real-world challenges in an intense, collaborative environment. Participants form cross-functional teams and work against the clock to develop innovative solutions, which are then presented to a panel of judges from industry and academia. These events are designed to spark creativity, foster collaboration, and accelerate the development of new ideas. Hackathons provide a unique opportunity to test your skills, learn from others, and potentially launch a new venture. We provide all the resources you need, including workspace, mentorship, and technical support throughout the event.',\r\n    benefits: [\r\n      'Develop rapid problem-solving and prototyping skills',\r\n      'Build your network with like-minded innovators',\r\n      'Gain exposure to industry challenges and opportunities',\r\n      'Win prizes and potential funding for your ideas',\r\n      'Receive feedback from industry experts and potential users'\r\n    ],\r\n    duration: '24-48 hours (weekend events)',\r\n    schedule: 'Quarterly events throughout the year',\r\n    location: 'FWU Innovation Hub',\r\n    capacity: 'Up to 100 participants (20-25 teams)',\r\n    applicationProcess: [\r\n      'Register individually or as a team (2-5 members)',\r\n      'Submit your background and areas of expertise',\r\n      'Receive confirmation and pre-event materials',\r\n      'Attend optional pre-hackathon workshops'\r\n    ],\r\n    imageUrl: '/programs/hackathon-hero.jpg',\r\n    upcomingDates: [\r\n      { date: 'December 12-14, 2024', title: 'FinTech Hackathon Challenge' },\r\n      { date: 'February 20-22, 2025', title: 'HealthTech Innovation Weekend' },\r\n      { date: 'April 15-17, 2025', title: 'Sustainability Solutions Hackathon' }\r\n    ]\r\n  },\r\n  {\r\n    id: 'demoday',\r\n    slug: 'startup-demo-days',\r\n    icon: <Rocket />,\r\n    title: 'Startup Demo Days',\r\n    description: 'An exclusive platform for our incubated startups to pitch their ventures to investors, industry leaders, and potential partners. Showcase your progress and secure opportunities.',\r\n    bgColorClass: 'bg-gradient-to-br from-amber-50 via-white to-orange-50',\r\n    longDescription: 'Demo Days are the culmination of our incubation programs, where startups showcase their progress and pitch to a curated audience of investors, industry partners, and media. These high-visibility events provide startups with the opportunity to secure funding, partnerships, and media coverage to fuel their next stage of growth. Each startup receives extensive preparation support, including pitch coaching, slide deck review, and Q&A practice sessions. Demo Days are structured to maximize engagement between startups and potential investors, with formal presentations followed by networking sessions where deeper conversations can take place.',\r\n    benefits: [\r\n      'Pitch to a curated audience of investors and partners',\r\n      'Receive professional pitch coaching and preparation',\r\n      'Network with potential investors and strategic partners',\r\n      'Media exposure and PR opportunities',\r\n      'Post-event introductions to interested stakeholders'\r\n    ],\r\n    duration: '1 full day event',\r\n    schedule: 'Bi-annual (Spring and Fall)',\r\n    location: 'FWU Auditorium and livestreamed online',\r\n    capacity: '10-15 startups per Demo Day',\r\n    applicationProcess: [\r\n      'Only open to startups in FWU Incubation programs',\r\n      'Selection based on readiness and progress metrics',\r\n      'Mandatory pitch preparation workshops',\r\n      'Final selection by incubation program directors'\r\n    ],\r\n    imageUrl: '/programs/demoday-hero.jpg',\r\n    upcomingDates: [\r\n      { date: 'November 30, 2024', title: 'Fall 2024 Demo Day' },\r\n      { date: 'May 25, 2025', title: 'Spring 2025 Demo Day' }\r\n    ]\r\n  },\r\n  {\r\n    id: 'workshops',\r\n    slug: 'expert-workshops',\r\n    icon: <GraduationCap />,\r\n    title: 'Expert Workshops',\r\n    description: 'Focused sessions led by industry experts on crucial topics like marketing, finance, legal aspects, and technology trends to refine your startup strategy.',\r\n    bgColorClass: 'bg-gradient-to-br from-pink-50 via-white to-rose-50',\r\n    longDescription: 'Our expert workshops provide targeted knowledge and skills development in specific areas critical to startup success. Led by industry practitioners and subject matter experts, these sessions combine theoretical frameworks with practical applications, allowing participants to immediately apply what they learn to their ventures. Workshops cover a wide range of topics, from technical skills like digital marketing and financial modeling to soft skills like leadership and negotiation. Each workshop is designed to be interactive, with hands-on exercises, case studies, and opportunities for personalized feedback. Participants leave with actionable insights and practical tools they can implement right away.',\r\n    benefits: [\r\n      'Learn practical skills directly applicable to your business',\r\n      'Access to industry experts and their networks',\r\n      'Receive personalized feedback on your specific challenges',\r\n      'Connect with peers facing similar challenges',\r\n      'Take home actionable templates and resources'\r\n    ],\r\n    duration: '2-4 hours per workshop',\r\n    schedule: 'Monthly workshops on rotating topics',\r\n    location: 'FWU Incubation Center and Online',\r\n    capacity: '30-50 participants per workshop',\r\n    applicationProcess: [\r\n      'Open to all entrepreneurs and startup team members',\r\n      'Registration required, with priority for FWU incubated startups',\r\n      'Some advanced workshops may have prerequisites'\r\n    ],\r\n    imageUrl: '/programs/workshop-hero.jpg',\r\n    upcomingDates: [\r\n      { date: 'November 15, 2024', title: 'Design Thinking Workshop for Innovators' },\r\n      { date: 'January 10, 2025', title: 'Funding Strategies for Early-Stage Startups' },\r\n      { date: 'February 5, 2025', title: 'Digital Marketing Essentials' },\r\n      { date: 'March 12, 2025', title: 'Legal Fundamentals for Startups' }\r\n    ]\r\n  },\r\n];\r\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAAA;AAAA;;;AAEO,MAAM,eAAe;IAC1B;QACE,IAAI;QACJ,MAAM;QACN,oBAAM,8OAAC,sMAAA,CAAA,SAAM;;;;;QACb,OAAO;QACP,aAAa;QACb,cAAc;QACd,iBAAiB;QACjB,UAAU;YACR;YACA;YACA;YACA;YACA;SACD;QACD,UAAU;QACV,UAAU;QACV,UAAU;QACV,UAAU;QACV,oBAAoB;YAClB;YACA;YACA;YACA;YACA;SACD;QACD,UAAU;QACV,eAAe;YACb;gBAAE,MAAM;gBAAoB,OAAO;YAA2B;YAC9D;gBAAE,MAAM;gBAAiB,OAAO;YAA4B;YAC5D;gBAAE,MAAM;gBAAgB,OAAO;YAAyB;SACzD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,oBAAM,8OAAC,oMAAA,CAAA,QAAK;;;;;QACZ,OAAO;QACP,aAAa;QACb,cAAc;QACd,iBAAiB;QACjB,UAAU;YACR;YACA;YACA;YACA;YACA;SACD;QACD,UAAU;QACV,UAAU;QACV,UAAU;QACV,UAAU;QACV,oBAAoB;YAClB;YACA;YACA;YACA;SACD;QACD,UAAU;QACV,eAAe;YACb;gBAAE,MAAM;gBAAwB,OAAO;YAA8B;YACrE;gBAAE,MAAM;gBAAwB,OAAO;YAAgC;YACvE;gBAAE,MAAM;gBAAqB,OAAO;YAAqC;SAC1E;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,oBAAM,8OAAC,sMAAA,CAAA,SAAM;;;;;QACb,OAAO;QACP,aAAa;QACb,cAAc;QACd,iBAAiB;QACjB,UAAU;YACR;YACA;YACA;YACA;YACA;SACD;QACD,UAAU;QACV,UAAU;QACV,UAAU;QACV,UAAU;QACV,oBAAoB;YAClB;YACA;YACA;YACA;SACD;QACD,UAAU;QACV,eAAe;YACb;gBAAE,MAAM;gBAAqB,OAAO;YAAqB;YACzD;gBAAE,MAAM;gBAAgB,OAAO;YAAuB;SACvD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,oBAAM,8OAAC,wNAAA,CAAA,gBAAa;;;;;QACpB,OAAO;QACP,aAAa;QACb,cAAc;QACd,iBAAiB;QACjB,UAAU;YACR;YACA;YACA;YACA;YACA;SACD;QACD,UAAU;QACV,UAAU;QACV,UAAU;QACV,UAAU;QACV,oBAAoB;YAClB;YACA;YACA;SACD;QACD,UAAU;QACV,eAAe;YACb;gBAAE,MAAM;gBAAqB,OAAO;YAA0C;YAC9E;gBAAE,MAAM;gBAAoB,OAAO;YAA8C;YACjF;gBAAE,MAAM;gBAAoB,OAAO;YAA+B;YAClE;gBAAE,MAAM;gBAAkB,OAAO;YAAkC;SACpE;IACH;CACD", "debugId": null}}, {"offset": {"line": 1312, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/fwu_incubation%28react%2Blaravel%29/incubation/frontend/src/app/components/programs/ProgramTypesSection.tsx"], "sourcesContent": ["\r\nimport ProgramTypeCard from './ProgramTypeCard';\r\nimport { programsData } from '../../../data/programsData';\r\n\r\nconst ProgramTypesSection = () => {\r\n  return (\r\n    <section className=\"py-20 md:py-28 bg-white\">\r\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\r\n        {/* Section header with decorative elements */}\r\n        <div className=\"relative mb-16\">\r\n          <div className=\"absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-24 h-1 bg-blue-200 rounded-full\"></div>\r\n          <div className=\"text-center\">\r\n            <p className=\"text-blue-600 font-semibold mb-2\">Innovate & Grow</p>\r\n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900\">FWU Incubation Center Programs</h2>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Intro text with decorative elements */}\r\n        <div className=\"max-w-3xl mx-auto text-center mb-16 relative\">\r\n          <div className=\"absolute -left-4 top-0 w-8 h-8 rounded-full bg-blue-100 opacity-50\"></div>\r\n          <div className=\"absolute -right-4 bottom-0 w-12 h-12 rounded-full bg-indigo-100 opacity-50\"></div>\r\n\r\n          <p className=\"text-lg text-gray-700 mb-6 leading-relaxed\">\r\n            The Far Western University Incubation Center is a hub for innovation, entrepreneurship, and technology transfer.\r\n            We provide resources, mentorship, and networking opportunities to help transform ideas into successful ventures.\r\n          </p>\r\n          <p className=\"text-lg text-gray-700 leading-relaxed\">\r\n            Explore our specialized programs designed to support entrepreneurs at every stage of their journey.\r\n            Click on any program to learn more about its details, benefits, and how to apply.\r\n          </p>\r\n        </div>\r\n\r\n        {/* Program cards with staggered animation on scroll */}\r\n        <div className=\"grid md:grid-cols-2 lg:grid-cols-2 gap-10 mt-8\">\r\n          {programsData.map((program, index) => (\r\n            <div\r\n              key={program.id}\r\n              className=\"opacity-0 animate-fadeIn\"\r\n              style={{ animationDelay: `${index * 150}ms`, animationFillMode: 'forwards' }}\r\n            >\r\n              <ProgramTypeCard\r\n                icon={program.icon}\r\n                title={program.title}\r\n                description={program.description}\r\n                bgColorClass=\"bg-white\"\r\n                slug={program.slug}\r\n              />\r\n            </div>\r\n          ))}\r\n        </div>\r\n\r\n        {/* Additional info with improved design */}\r\n        <div className=\"mt-24 relative overflow-hidden\">\r\n          <div className=\"absolute -top-10 -right-10 w-40 h-40 bg-blue-50 rounded-full opacity-50\"></div>\r\n          <div className=\"absolute -bottom-10 -left-10 w-40 h-40 bg-indigo-50 rounded-full opacity-50\"></div>\r\n\r\n          <div className=\"relative bg-white border border-gray-100 rounded-2xl shadow-xl p-10 overflow-hidden\">\r\n            <div className=\"absolute top-0 right-0 w-40 h-40 bg-blue-50 rounded-full transform translate-x-1/2 -translate-y-1/2 opacity-30\"></div>\r\n\r\n            <div className=\"flex flex-col md:flex-row items-center\">\r\n              <div className=\"md:w-2/3 mb-10 md:mb-0 md:pr-10\">\r\n                <h3 className=\"text-2xl font-bold text-gray-900 mb-6\">Join the FWU Innovation Community</h3>\r\n                <p className=\"text-gray-700 mb-6 leading-relaxed\">\r\n                  The Far Western University Incubation Center welcomes students, faculty, alumni, and external entrepreneurs\r\n                  with innovative ideas and a passion for solving real-world problems.\r\n                </p>\r\n                <p className=\"text-gray-700 leading-relaxed\">\r\n                  Don&apos;t see what you&apos;re looking for? We also offer customized programs tailored to specific industries,\r\n                  technologies, or business needs. Contact our team to discuss how we can support your entrepreneurial journey.\r\n                </p>\r\n\r\n                <div className=\"mt-8 flex flex-wrap gap-4\">\r\n                  <div className=\"flex items-center\">\r\n                    <div className=\"w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center mr-3\">\r\n                      <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 text-blue-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\r\n                      </svg>\r\n                    </div>\r\n                    <span className=\"text-gray-700\">Expert Mentorship</span>\r\n                  </div>\r\n\r\n                  <div className=\"flex items-center\">\r\n                    <div className=\"w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center mr-3\">\r\n                      <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 text-blue-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\r\n                      </svg>\r\n                    </div>\r\n                    <span className=\"text-gray-700\">Funding Opportunities</span>\r\n                  </div>\r\n\r\n                  <div className=\"flex items-center\">\r\n                    <div className=\"w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center mr-3\">\r\n                      <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 text-blue-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\r\n                      </svg>\r\n                    </div>\r\n                    <span className=\"text-gray-700\">Workspace Access</span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <div className=\"md:w-1/3 flex flex-col items-center\">\r\n                <div className=\"bg-white p-6 rounded-xl shadow-lg border border-gray-100 w-full\">\r\n                  <h4 className=\"text-xl font-semibold text-gray-900 mb-4 text-center\">Ready to Join?</h4>\r\n                  <a\r\n                    href=\"/contact\"\r\n                    className=\"w-full inline-block bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-lg shadow-md transition-all duration-300 hover:scale-105 transform text-center mb-4\"\r\n                  >\r\n                    Contact Us\r\n                  </a>\r\n                  <a\r\n                    href=\"/apply\"\r\n                    className=\"w-full inline-block bg-white border-2 border-blue-600 text-blue-600 hover:bg-blue-50 font-semibold py-3 px-6 rounded-lg shadow-sm transition-all duration-300 hover:scale-105 transform text-center\"\r\n                  >\r\n                    Apply Now\r\n                  </a>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\nexport default ProgramTypesSection;"], "names": [], "mappings": ";;;;AACA;AACA;;;;AAEA,MAAM,sBAAsB;IAC1B,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;8CAAmC;;;;;;8CAChD,8OAAC;oCAAG,WAAU;8CAA+C;;;;;;;;;;;;;;;;;;8BAKjE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAI,WAAU;;;;;;sCAEf,8OAAC;4BAAE,WAAU;sCAA6C;;;;;;sCAI1D,8OAAC;4BAAE,WAAU;sCAAwC;;;;;;;;;;;;8BAOvD,8OAAC;oBAAI,WAAU;8BACZ,4HAAA,CAAA,eAAY,CAAC,GAAG,CAAC,CAAC,SAAS,sBAC1B,8OAAC;4BAEC,WAAU;4BACV,OAAO;gCAAE,gBAAgB,GAAG,QAAQ,IAAI,EAAE,CAAC;gCAAE,mBAAmB;4BAAW;sCAE3E,cAAA,8OAAC,wJAAA,CAAA,UAAe;gCACd,MAAM,QAAQ,IAAI;gCAClB,OAAO,QAAQ,KAAK;gCACpB,aAAa,QAAQ,WAAW;gCAChC,cAAa;gCACb,MAAM,QAAQ,IAAI;;;;;;2BATf,QAAQ,EAAE;;;;;;;;;;8BAgBrB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAI,WAAU;;;;;;sCAEf,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CAEf,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAwC;;;;;;8DACtD,8OAAC;oDAAE,WAAU;8DAAqC;;;;;;8DAIlD,8OAAC;oDAAE,WAAU;8DAAgC;;;;;;8DAK7C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC;wEAAI,OAAM;wEAA6B,WAAU;wEAAwB,MAAK;wEAAO,SAAQ;wEAAY,QAAO;kFAC/G,cAAA,8OAAC;4EAAK,eAAc;4EAAQ,gBAAe;4EAAQ,aAAa;4EAAG,GAAE;;;;;;;;;;;;;;;;8EAGzE,8OAAC;oEAAK,WAAU;8EAAgB;;;;;;;;;;;;sEAGlC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC;wEAAI,OAAM;wEAA6B,WAAU;wEAAwB,MAAK;wEAAO,SAAQ;wEAAY,QAAO;kFAC/G,cAAA,8OAAC;4EAAK,eAAc;4EAAQ,gBAAe;4EAAQ,aAAa;4EAAG,GAAE;;;;;;;;;;;;;;;;8EAGzE,8OAAC;oEAAK,WAAU;8EAAgB;;;;;;;;;;;;sEAGlC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC;wEAAI,OAAM;wEAA6B,WAAU;wEAAwB,MAAK;wEAAO,SAAQ;wEAAY,QAAO;kFAC/G,cAAA,8OAAC;4EAAK,eAAc;4EAAQ,gBAAe;4EAAQ,aAAa;4EAAG,GAAE;;;;;;;;;;;;;;;;8EAGzE,8OAAC;oEAAK,WAAU;8EAAgB;;;;;;;;;;;;;;;;;;;;;;;;sDAKtC,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAuD;;;;;;kEACrE,8OAAC;wDACC,MAAK;wDACL,WAAU;kEACX;;;;;;kEAGD,8OAAC;wDACC,MAAK;wDACL,WAAU;kEACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWnB;uCAEe", "debugId": null}}, {"offset": {"line": 1722, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/fwu_incubation%28react%2Blaravel%29/incubation/frontend/src/app/programs/page.tsx"], "sourcesContent": ["\"use client\"\r\nimport PastEventsGallerySection from \"../components/programs/PastEventsGallerySection \";\r\nimport ProgramCalendarSection from \"../components/programs/ProgramCalendarSection \";\r\nimport ProgramTypesSection from \"../components/programs/ProgramTypesSection\";\r\nimport { ArrowDown } from 'lucide-react';\r\n\r\nexport default function ProgramsPage() {\r\n  // Function to scroll to the next section smoothly\r\n  const scrollToNextSection = () => {\r\n    const programsSection = document.getElementById('program-types');\r\n    if (programsSection) {\r\n      programsSection.scrollIntoView({ behavior: 'smooth' });\r\n    }\r\n  };\r\n\r\n  return (\r\n    <>\r\n      {/* Redesigned Hero Section with Tailwind Animations */}\r\n      <section className=\"relative min-h-[90vh] flex items-center bg-white overflow-hidden\">\r\n        {/* Background pattern - using CSS pattern instead of image */}\r\n        <div className=\"absolute inset-0 opacity-5 bg-white\"\r\n             style={{\r\n               backgroundImage: 'radial-gradient(#3b82f6 1px, transparent 1px)',\r\n               backgroundSize: '20px 20px'\r\n             }}></div>\r\n\r\n        {/* Accent elements */}\r\n        <div className=\"absolute top-0 right-0 w-1/3 h-1/3 bg-gradient-to-b from-blue-50 to-transparent opacity-70 rounded-bl-[100px]\"></div>\r\n        <div className=\"absolute bottom-0 left-0 w-1/3 h-1/3 bg-gradient-to-t from-indigo-50 to-transparent opacity-70 rounded-tr-[100px]\"></div>\r\n\r\n        {/* Animated accent circles */}\r\n        <div className=\"absolute top-20 left-10 w-64 h-64 rounded-full bg-blue-100 opacity-20 animate-float-slow\"></div>\r\n        <div className=\"absolute bottom-20 right-10 w-96 h-96 rounded-full bg-indigo-100 opacity-20 animate-float-reverse\"></div>\r\n\r\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 text-center relative z-10\">\r\n          <div className=\"opacity-0 animate-fadeIn\">\r\n            <div className=\"inline-block mb-6 p-2 bg-blue-50 rounded-full\">\r\n              <div className=\"px-4 py-1 bg-blue-100 rounded-full\">\r\n                <span className=\"text-blue-800 font-medium\">Innovation Hub</span>\r\n              </div>\r\n            </div>\r\n\r\n            <h1 className=\"text-5xl sm:text-6xl md:text-7xl font-extrabold mb-6 text-gray-900 leading-tight\">\r\n              FWU <span className=\"text-blue-600\">Incubation Center</span>\r\n            </h1>\r\n\r\n            <p className=\"text-xl sm:text-2xl text-gray-700 max-w-3xl mx-auto mb-6\">\r\n              Empowering innovation and entrepreneurship at Far Western University\r\n            </p>\r\n\r\n            <p className=\"text-lg text-gray-600 max-w-3xl mx-auto mb-10\">\r\n              Discover our range of specialized programs designed to support entrepreneurs at every stage of their journey,\r\n              from ideation to market launch. Join our vibrant community of innovators and change-makers.\r\n            </p>\r\n\r\n            <div className=\"flex flex-col sm:flex-row items-center justify-center gap-4 mb-12\">\r\n              <button\r\n                onClick={scrollToNextSection}\r\n                className=\"bg-blue-600 text-white hover:bg-blue-700 font-bold py-4 px-8 rounded-lg shadow-lg inline-flex items-center transition-all duration-300 hover:scale-105 active:scale-95\"\r\n              >\r\n                Explore Programs\r\n                <ArrowDown className=\"ml-2\" />\r\n              </button>\r\n\r\n              <a\r\n                href=\"/apply\"\r\n                className=\"bg-white text-blue-600 border-2 border-blue-600 hover:bg-blue-50 font-bold py-4 px-8 rounded-lg shadow-md inline-flex items-center transition-all duration-300 hover:scale-105 active:scale-95\"\r\n              >\r\n                Apply Now\r\n              </a>\r\n            </div>\r\n\r\n            {/* Stats */}\r\n            <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4 max-w-4xl mx-auto\">\r\n              <div className=\"bg-white p-4 rounded-lg shadow-sm border border-gray-100\">\r\n                <div className=\"text-3xl font-bold text-blue-600 mb-1\">20+</div>\r\n                <div className=\"text-gray-600 text-sm\">Startups Incubated</div>\r\n              </div>\r\n              <div className=\"bg-white p-4 rounded-lg shadow-sm border border-gray-100\">\r\n                <div className=\"text-3xl font-bold text-blue-600 mb-1\">50+</div>\r\n                <div className=\"text-gray-600 text-sm\">Mentors & Experts</div>\r\n              </div>\r\n              <div className=\"bg-white p-4 rounded-lg shadow-sm border border-gray-100\">\r\n                <div className=\"text-3xl font-bold text-blue-600 mb-1\">12</div>\r\n                <div className=\"text-gray-600 text-sm\">Programs Yearly</div>\r\n              </div>\r\n              <div className=\"bg-white p-4 rounded-lg shadow-sm border border-gray-100\">\r\n                <div className=\"text-3xl font-bold text-blue-600 mb-1\">500+</div>\r\n                <div className=\"text-gray-600 text-sm\">Students Engaged</div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Scroll indicator */}\r\n          <div className=\"absolute bottom-10 left-1/2 transform -translate-x-1/2 opacity-0 animate-fadeIn animation-delay-1000 animate-bounce\">\r\n            <button\r\n              onClick={scrollToNextSection}\r\n              className=\"text-blue-600 focus:outline-none\"\r\n              aria-label=\"Scroll down\"\r\n            >\r\n              <ArrowDown size={30} />\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Program Types Section with ID for scroll targeting */}\r\n      <div id=\"program-types\">\r\n        <ProgramTypesSection />\r\n      </div>\r\n\r\n      {/* Calendar Section */}\r\n      <ProgramCalendarSection />\r\n\r\n      {/* Past Events Gallery */}\r\n      <PastEventsGallerySection />\r\n    </>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AAJA;;;;;;AAMe,SAAS;IACtB,kDAAkD;IAClD,MAAM,sBAAsB;QAC1B,MAAM,kBAAkB,SAAS,cAAc,CAAC;QAChD,IAAI,iBAAiB;YACnB,gBAAgB,cAAc,CAAC;gBAAE,UAAU;YAAS;QACtD;IACF;IAEA,qBACE;;0BAEE,8OAAC;gBAAQ,WAAU;;kCAEjB,8OAAC;wBAAI,WAAU;wBACV,OAAO;4BACL,iBAAiB;4BACjB,gBAAgB;wBAClB;;;;;;kCAGL,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCAGf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCAEf,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAA4B;;;;;;;;;;;;;;;;kDAIhD,8OAAC;wCAAG,WAAU;;4CAAmF;0DAC3F,8OAAC;gDAAK,WAAU;0DAAgB;;;;;;;;;;;;kDAGtC,8OAAC;wCAAE,WAAU;kDAA2D;;;;;;kDAIxE,8OAAC;wCAAE,WAAU;kDAAgD;;;;;;kDAK7D,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,SAAS;gDACT,WAAU;;oDACX;kEAEC,8OAAC,gNAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;;;;;;;0DAGvB,8OAAC;gDACC,MAAK;gDACL,WAAU;0DACX;;;;;;;;;;;;kDAMH,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEAAwC;;;;;;kEACvD,8OAAC;wDAAI,WAAU;kEAAwB;;;;;;;;;;;;0DAEzC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEAAwC;;;;;;kEACvD,8OAAC;wDAAI,WAAU;kEAAwB;;;;;;;;;;;;0DAEzC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEAAwC;;;;;;kEACvD,8OAAC;wDAAI,WAAU;kEAAwB;;;;;;;;;;;;0DAEzC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEAAwC;;;;;;kEACvD,8OAAC;wDAAI,WAAU;kEAAwB;;;;;;;;;;;;;;;;;;;;;;;;0CAM7C,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,SAAS;oCACT,WAAU;oCACV,cAAW;8CAEX,cAAA,8OAAC,gNAAA,CAAA,YAAS;wCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOzB,8OAAC;gBAAI,IAAG;0BACN,cAAA,8OAAC,4JAAA,CAAA,UAAmB;;;;;;;;;;0BAItB,8OAAC,iKAAA,CAAA,UAAsB;;;;;0BAGvB,8OAAC,mKAAA,CAAA,UAAwB;;;;;;;AAG/B", "debugId": null}}]}