(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6150],{4516:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},5040:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("book-open",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]])},5441:(e,s,t)=>{"use strict";t.d(s,{default:()=>H});var a=t(95155),l=t(12115),r=t(6874),i=t.n(r),c=t(35169),n=t(69074),d=t(5040),x=t(17580),o=t(57434),m=t(40646);let h=(0,t(19946).A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]]);var p=t(85339),u=t(92138),b=t(91788);let g=e=>{let{program:s}=e,[t,r]=(0,l.useState)(!1);return(0,l.useEffect)(()=>{r(!0)},[]),(0,a.jsx)("section",{className:"py-12 bg-gray-50",children:(0,a.jsx)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsx)("div",{className:"transition-all duration-1000 transform ".concat(t?"opacity-100 translate-y-0":"opacity-0 translate-y-10"),children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Application Information"}),(0,a.jsxs)("div",{className:"bg-white rounded-xl shadow-md overflow-hidden",children:[(0,a.jsxs)("div",{className:"p-6 border-b border-gray-200",children:[(0,a.jsxs)("h3",{className:"text-lg font-bold text-gray-900 mb-4 flex items-center",children:[(0,a.jsx)(n.A,{className:"mr-2 text-blue-600"}),"Application Timeline"]}),(0,a.jsx)("div",{className:"space-y-6",children:s.applicationTimeline.map((e,s)=>(0,a.jsxs)("div",{className:"relative pl-8 pb-6 border-l-2 border-blue-200 last:border-l-0 last:pb-0",style:{transitionDelay:"".concat(100*s,"ms")},children:[(0,a.jsx)("div",{className:"absolute left-[-9px] top-0 w-4 h-4 bg-blue-600 rounded-full"}),(0,a.jsxs)("div",{className:"mb-1 flex items-center",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900",children:e.stage}),(0,a.jsx)("span",{className:"ml-3 text-sm bg-blue-100 text-blue-700 px-2 py-0.5 rounded",children:e.date})]}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:e.description})]},s))})]}),(0,a.jsxs)("div",{className:"p-6 border-b border-gray-200",children:[(0,a.jsxs)("h3",{className:"text-lg font-bold text-gray-900 mb-4 flex items-center",children:[(0,a.jsx)(m.A,{className:"mr-2 text-blue-600"}),"Eligibility Requirements"]}),(0,a.jsx)("ul",{className:"space-y-3",children:s.eligibilityRequirements.map((e,s)=>(0,a.jsxs)("li",{className:"flex items-start",style:{transitionDelay:"".concat(100*s,"ms")},children:[(0,a.jsx)("div",{className:"w-5 h-5 bg-green-100 rounded-full flex items-center justify-center text-green-600 mr-3 flex-shrink-0 mt-0.5",children:(0,a.jsx)("svg",{width:"12",height:"12",viewBox:"0 0 12 12",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,a.jsx)("path",{d:"M10 3L4.5 8.5L2 6",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})}),(0,a.jsx)("span",{className:"text-gray-700",children:e})]},s))})]}),(0,a.jsxs)("div",{className:"p-6 border-b border-gray-200",children:[(0,a.jsxs)("h3",{className:"text-lg font-bold text-gray-900 mb-4 flex items-center",children:[(0,a.jsx)(o.A,{className:"mr-2 text-blue-600"}),"Application Process"]}),(0,a.jsx)("ol",{className:"space-y-4",children:s.applicationProcess.map((e,s)=>(0,a.jsxs)("li",{className:"flex",style:{transitionDelay:"".concat(100*s,"ms")},children:[(0,a.jsx)("div",{className:"w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 mr-3 flex-shrink-0 mt-0.5",children:s+1}),(0,a.jsx)("div",{children:(0,a.jsx)("p",{className:"text-gray-700",children:e})})]},s))})]}),(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("h3",{className:"text-lg font-bold text-gray-900 mb-4 flex items-center",children:[(0,a.jsx)(h,{className:"mr-2 text-blue-600"}),"Fees and Funding"]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"Tuition Fees"}),(0,a.jsx)("p",{className:"text-gray-700",children:s.tuitionFees})]}),s.scholarships&&s.scholarships.length>0&&(0,a.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"Scholarships and Financial Aid"}),(0,a.jsx)("ul",{className:"space-y-2",children:s.scholarships.map((e,s)=>(0,a.jsxs)("li",{className:"text-gray-700 flex items-start",children:[(0,a.jsx)(p.A,{className:"text-blue-600 mr-2 mt-1 flex-shrink-0"}),(0,a.jsx)("span",{children:e})]},s))})]})]})]})]}),(0,a.jsxs)("div",{className:"mt-8 p-6 bg-blue-600 rounded-xl text-white text-center",children:[(0,a.jsx)("h3",{className:"text-xl font-bold mb-2",children:"Ready to Apply?"}),(0,a.jsxs)("p",{className:"mb-6",children:["Applications for the ",s.nextIntake," intake are now open. Submit your application today!"]}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row justify-center gap-4",children:[(0,a.jsxs)(i(),{href:"/submit-application",className:"px-6 py-3 bg-white text-blue-600 font-bold rounded-lg hover:bg-blue-50 transition-colors flex items-center justify-center",children:["Apply Now ",(0,a.jsx)(u.A,{className:"ml-2"})]}),(0,a.jsxs)("button",{className:"px-6 py-3 bg-blue-700 text-white font-bold rounded-lg hover:bg-blue-800 transition-colors flex items-center justify-center",children:["Download Application Form ",(0,a.jsx)(b.A,{className:"ml-2"})]})]})]}),(0,a.jsxs)("div",{className:"mt-8 p-6 bg-gray-100 rounded-xl",children:[(0,a.jsx)("h3",{className:"text-lg font-bold text-gray-900 mb-4",children:"Have Questions?"}),(0,a.jsx)("p",{className:"text-gray-700 mb-4",children:"For more information about the application process or the program, please contact the admissions office:"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("p",{className:"text-gray-700",children:[(0,a.jsx)("strong",{children:"Email:"})," ",(0,a.jsx)("a",{href:"mailto:<EMAIL>",className:"text-blue-600 hover:underline",children:"<EMAIL>"})]}),(0,a.jsxs)("p",{className:"text-gray-700",children:[(0,a.jsx)("strong",{children:"Phone:"})," +977-99-521456"]}),(0,a.jsxs)("p",{className:"text-gray-700",children:[(0,a.jsx)("strong",{children:"Office Hours:"})," Sunday to Friday, 10:00 AM to 4:00 PM"]})]})]})]})})})})};var y=t(66766),j=t(28883),f=t(74575),N=t(33786);let v=e=>{let{program:s}=e,[t,r]=(0,l.useState)(!1);return((0,l.useEffect)(()=>{r(!0)},[]),s.faculty&&0!==s.faculty.length)?(0,a.jsx)("section",{className:"py-12",children:(0,a.jsx)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsx)("div",{className:"transition-all duration-1000 transform ".concat(t?"opacity-100 translate-y-0":"opacity-0 translate-y-10"),children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Program Faculty"}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:s.faculty.map((e,s)=>(0,a.jsx)("div",{className:"bg-white rounded-xl shadow-md overflow-hidden hover:shadow-lg transition-shadow",style:{transitionDelay:"".concat(100*s,"ms")},children:(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row",children:[(0,a.jsx)("div",{className:"sm:w-1/3 h-48 sm:h-auto relative",children:(0,a.jsx)(y.default,{src:e.imageUrl||"https://images.unsplash.com/photo-1568602471122-7832951cc4c5?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=300&q=80",alt:e.name,fill:!0,className:"object-cover"})}),(0,a.jsxs)("div",{className:"sm:w-2/3 p-5",children:[(0,a.jsx)("h3",{className:"text-lg font-bold text-gray-900 mb-1",children:e.name}),(0,a.jsx)("p",{className:"text-blue-600 font-medium text-sm mb-3",children:e.position}),e.specialization&&(0,a.jsxs)("div",{className:"mb-3",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"Specialization"}),(0,a.jsx)("p",{className:"text-gray-700",children:e.specialization})]}),e.education&&(0,a.jsxs)("div",{className:"mb-3",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"Education"}),(0,a.jsx)("p",{className:"text-gray-700",children:e.education})]}),(0,a.jsxs)("div",{className:"flex items-center mt-4 space-x-3",children:[e.email&&(0,a.jsx)("a",{href:"mailto:".concat(e.email),className:"p-2 bg-gray-100 text-gray-700 rounded-full hover:bg-gray-200 transition-colors","aria-label":"Email ".concat(e.name),children:(0,a.jsx)(j.A,{size:16})}),e.linkedin&&(0,a.jsx)("a",{href:e.linkedin,target:"_blank",rel:"noopener noreferrer",className:"p-2 bg-gray-100 text-gray-700 rounded-full hover:bg-gray-200 transition-colors","aria-label":"LinkedIn profile of ".concat(e.name),children:(0,a.jsx)(f.A,{size:16})}),e.profileUrl&&(0,a.jsx)("a",{href:e.profileUrl,target:"_blank",rel:"noopener noreferrer",className:"p-2 bg-gray-100 text-gray-700 rounded-full hover:bg-gray-200 transition-colors","aria-label":"Profile page of ".concat(e.name),children:(0,a.jsx)(N.A,{size:16})})]})]})]})},e.name))}),s.departmentInfo&&(0,a.jsxs)("div",{className:"mt-10 p-6 bg-gray-50 rounded-xl border border-gray-200",children:[(0,a.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-4",children:"About the Department"}),(0,a.jsx)("div",{className:"prose max-w-none text-gray-700",children:(0,a.jsx)("p",{children:s.departmentInfo})}),s.departmentUrl&&(0,a.jsxs)("a",{href:s.departmentUrl,target:"_blank",rel:"noopener noreferrer",className:"inline-flex items-center mt-4 text-blue-600 hover:text-blue-800 font-medium transition-colors",children:["Visit Department Website ",(0,a.jsx)(N.A,{className:"ml-2"})]})]})]})})})}):null};var w=t(47863),k=t(66474),A=t(14186);let M=e=>{let{program:s}=e,[t,r]=(0,l.useState)(!1),[i,c]=(0,l.useState)({});(0,l.useEffect)(()=>{r(!0),s.curriculum&&s.curriculum.length>0&&c({[s.curriculum[0].semester]:!0})},[s.curriculum]);let n=e=>{c(s=>({...s,[e]:!s[e]}))};return s.curriculum&&0!==s.curriculum.length?(0,a.jsx)("section",{className:"py-12 bg-gray-50",children:(0,a.jsx)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsx)("div",{className:"transition-all duration-1000 transform ".concat(t?"opacity-100 translate-y-0":"opacity-0 translate-y-10"),children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Program Curriculum"}),(0,a.jsx)("div",{className:"bg-white rounded-xl shadow-md overflow-hidden",children:s.curriculum.map((e,t)=>(0,a.jsxs)("div",{className:"border-b border-gray-200 ".concat(t===s.curriculum.length-1?"border-b-0":""),children:[(0,a.jsxs)("button",{onClick:()=>n(e.semester),className:"w-full px-6 py-4 flex items-center justify-between bg-white hover:bg-gray-50 transition-colors",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 mr-3 flex-shrink-0",children:t+1}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:e.semester})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsxs)("span",{className:"text-sm text-gray-500 mr-4",children:[e.courses.length," Courses"]}),i[e.semester]?(0,a.jsx)(w.A,{className:"text-gray-500"}):(0,a.jsx)(k.A,{className:"text-gray-500"})]})]}),i[e.semester]&&(0,a.jsx)("div",{className:"px-6 pb-4",children:(0,a.jsx)("div",{className:"space-y-4",children:e.courses.map((e,s)=>(0,a.jsxs)("div",{className:"p-4 border border-gray-200 rounded-lg hover:border-blue-300 transition-colors",style:{transitionDelay:"".concat(50*s,"ms")},children:[(0,a.jsxs)("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between mb-2",children:[(0,a.jsxs)("div",{className:"flex items-center mb-2 md:mb-0",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-blue-50 rounded-full flex items-center justify-center text-blue-600 mr-3 flex-shrink-0",children:(0,a.jsx)(d.A,{size:16})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900",children:e.title}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:e.code})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsxs)("div",{className:"flex items-center text-sm text-gray-500",children:[(0,a.jsx)(A.A,{className:"mr-1.5 text-blue-500"}),(0,a.jsxs)("span",{children:[e.credits," Credits"]})]}),e.isCore?(0,a.jsx)("span",{className:"text-xs font-semibold px-2 py-1 bg-blue-100 text-blue-700 rounded",children:"Core"}):(0,a.jsx)("span",{className:"text-xs font-semibold px-2 py-1 bg-amber-100 text-amber-700 rounded",children:"Elective"})]})]}),e.description&&(0,a.jsx)("div",{className:"mt-2 text-sm text-gray-600",children:(0,a.jsx)("p",{children:e.description})})]},e.code))})})]},e.semester))}),s.additionalCurriculumInfo&&(0,a.jsx)("div",{className:"mt-8 p-6 bg-blue-50 rounded-xl border border-blue-100",children:(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)(o.A,{className:"text-blue-600 mt-1 mr-3 flex-shrink-0",size:20}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium text-blue-800 mb-2",children:"Additional Curriculum Information"}),(0,a.jsx)("div",{className:"text-blue-700 space-y-2",children:s.additionalCurriculumInfo.map((e,s)=>(0,a.jsx)("p",{children:e},s))})]})]})})]})})})}):null};var C=t(69037),z=t(43332),D=t(4516);let q=e=>{let{program:s}=e,[t,r]=(0,l.useState)(!1);return(0,l.useEffect)(()=>{r(!0)},[]),(0,a.jsx)("section",{className:"py-12",children:(0,a.jsx)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsx)("div",{className:"transition-all duration-1000 transform ".concat(t?"opacity-100 translate-y-0":"opacity-0 translate-y-10"),children:(0,a.jsxs)("div",{className:"flex flex-col md:flex-row gap-8",children:[(0,a.jsxs)("div",{className:"md:w-2/3",children:[s.imageUrl&&(0,a.jsxs)("div",{className:"relative h-72 md:h-96 w-full rounded-xl overflow-hidden mb-8",children:[(0,a.jsx)(y.default,{src:s.imageUrl,alt:s.title,fill:!0,className:"object-cover",priority:!0}),(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"}),(0,a.jsxs)("div",{className:"absolute bottom-0 left-0 right-0 p-6",children:[(0,a.jsxs)("div",{className:"flex flex-wrap gap-2 mb-3",children:[(0,a.jsx)("span",{className:"text-xs font-semibold px-3 py-1 rounded-full border ".concat({Engineering:"bg-blue-500/10 text-blue-700 border-blue-300",Management:"bg-amber-500/10 text-amber-700 border-amber-300","Science & Technology":"bg-green-500/10 text-green-700 border-green-300",Humanities:"bg-red-500/10 text-red-700 border-red-300",Education:"bg-purple-500/10 text-purple-700 border-purple-300"}[s.department]||"bg-gray-500/10 text-gray-700 border-gray-300"),children:s.department}),(0,a.jsx)("span",{className:"text-xs font-semibold px-3 py-1 rounded-full border ".concat({Undergraduate:"bg-teal-500/10 text-teal-700 border-teal-300",Graduate:"bg-violet-500/10 text-violet-700 border-violet-300",Diploma:"bg-orange-500/10 text-orange-700 border-orange-300",Certificate:"bg-cyan-500/10 text-cyan-700 border-cyan-300"}[s.level]||"bg-gray-500/10 text-gray-700 border-gray-300"),children:s.level})]}),(0,a.jsx)("h1",{className:"text-3xl md:text-4xl font-bold text-white",children:s.title})]})]}),(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Program Overview"}),(0,a.jsxs)("div",{className:"prose max-w-none text-gray-700",children:[(0,a.jsx)("p",{className:"mb-4",children:s.description}),(0,a.jsx)("p",{children:s.overview})]})]}),(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Program Highlights"}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:s.highlights.map((e,s)=>(0,a.jsxs)("div",{className:"bg-blue-50 p-4 rounded-lg border border-blue-100 flex",style:{transitionDelay:"".concat((s+1)*100,"ms")},children:[(0,a.jsx)("div",{className:"w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 mr-3 flex-shrink-0",children:s+1}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium text-gray-900 mb-1",children:e.title}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:e.description})]})]},s))})]}),(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Learning Outcomes"}),(0,a.jsx)("ul",{className:"space-y-3",children:s.learningOutcomes.map((e,s)=>(0,a.jsxs)("li",{className:"flex items-start",style:{transitionDelay:"".concat((s+1)*100,"ms")},children:[(0,a.jsx)("div",{className:"w-6 h-6 bg-green-100 rounded-full flex items-center justify-center text-green-600 mr-3 flex-shrink-0 mt-0.5",children:(0,a.jsx)(Check,{size:14})}),(0,a.jsx)("span",{className:"text-gray-700",children:e})]},s))})]})]}),(0,a.jsx)("div",{className:"md:w-1/3",children:(0,a.jsxs)("div",{className:"bg-gray-50 rounded-xl p-6 border border-gray-200 sticky top-24",children:[(0,a.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-4",children:"Program Information"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)(C.A,{className:"text-blue-600 mt-1 mr-3 flex-shrink-0"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"Degree Awarded"}),(0,a.jsx)("p",{className:"text-gray-900",children:s.degreeAwarded})]})]}),(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)(A.A,{className:"text-blue-600 mt-1 mr-3 flex-shrink-0"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"Duration"}),(0,a.jsx)("p",{className:"text-gray-900",children:s.duration})]})]}),(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)(d.A,{className:"text-blue-600 mt-1 mr-3 flex-shrink-0"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"Total Credits"}),(0,a.jsxs)("p",{className:"text-gray-900",children:[s.credits," Credits"]})]})]}),(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)(n.A,{className:"text-blue-600 mt-1 mr-3 flex-shrink-0"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"Next Intake"}),(0,a.jsx)("p",{className:"text-gray-900",children:s.nextIntake})]})]}),(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)(z.A,{className:"text-blue-600 mt-1 mr-3 flex-shrink-0"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"Department"}),(0,a.jsx)("p",{className:"text-gray-900",children:s.department})]})]}),(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)(D.A,{className:"text-blue-600 mt-1 mr-3 flex-shrink-0"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"Campus"}),(0,a.jsx)("p",{className:"text-gray-900",children:s.campus})]})]}),(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)(x.A,{className:"text-blue-600 mt-1 mr-3 flex-shrink-0"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"Class Size"}),(0,a.jsxs)("p",{className:"text-gray-900",children:[s.classSize," Students"]})]})]})]}),(0,a.jsxs)("div",{className:"mt-6 pt-6 border-t border-gray-200",children:[(0,a.jsx)("button",{className:"w-full py-3 px-4 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors",children:"Apply Now"}),(0,a.jsx)("button",{className:"w-full mt-3 py-3 px-4 bg-white text-blue-600 font-medium rounded-lg border border-blue-600 hover:bg-blue-50 transition-colors",children:"Download Brochure"})]})]})})]})})})})},H=e=>{let{program:s}=e,[t,r]=(0,l.useState)("details"),[m,h]=(0,l.useState)(!1);return((0,l.useEffect)(()=>{h(!0)},[]),s)?(0,a.jsxs)("main",{className:"bg-white",children:[(0,a.jsxs)("section",{className:"relative bg-gradient-to-r from-blue-900 via-indigo-800 to-blue-900 text-white py-16 md:py-24 overflow-hidden",children:[(0,a.jsxs)("div",{className:"absolute inset-0 overflow-hidden",children:[(0,a.jsx)("div",{className:"absolute top-20 left-10 w-64 h-64 rounded-full bg-blue-500 opacity-10 animate-float-slow"}),(0,a.jsx)("div",{className:"absolute bottom-20 right-10 w-96 h-96 rounded-full bg-indigo-500 opacity-10 animate-float-reverse"}),(0,a.jsx)("div",{className:"absolute inset-0 opacity-10",style:{backgroundImage:"radial-gradient(circle, rgba(255,255,255,0.1) 1px, transparent 1px)",backgroundSize:"30px 30px"}})]}),(0,a.jsx)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8 relative z-10",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto transition-all duration-1000 transform ".concat(m?"opacity-100 translate-y-0":"opacity-0 translate-y-10"),children:[(0,a.jsxs)(i(),{href:"/programs",className:"inline-flex items-center text-blue-200 hover:text-white mb-6 transition-colors",children:[(0,a.jsx)(c.A,{className:"mr-2"})," Back to Programs"]}),(0,a.jsxs)("div",{className:"mb-4 flex flex-wrap gap-2",children:[(0,a.jsx)("span",{className:"text-xs font-semibold px-3 py-1 rounded-full bg-blue-700/50 text-blue-100 border border-blue-400/30"}),(0,a.jsx)("span",{className:"text-xs font-semibold px-3 py-1 rounded-full bg-indigo-700/50 text-indigo-100 border border-indigo-400/30"})]}),(0,a.jsx)("h1",{className:"text-3xl md:text-5xl font-extrabold mb-6 leading-tight",children:s.title}),(0,a.jsx)("p",{className:"text-xl text-indigo-100 mb-8 max-w-3xl",children:s.description}),(0,a.jsxs)("div",{className:"flex flex-wrap items-center gap-x-6 gap-y-2 text-indigo-100",children:[(0,a.jsx)("div",{className:"flex items-center",children:(0,a.jsx)(n.A,{className:"mr-2"})}),(0,a.jsx)("div",{className:"flex items-center",children:(0,a.jsx)(d.A,{className:"mr-2"})}),(0,a.jsx)("div",{className:"flex items-center",children:(0,a.jsx)(x.A,{className:"mr-2"})})]})]})}),(0,a.jsx)("div",{className:"absolute bottom-0 left-0 right-0",children:(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1440 120",className:"w-full h-auto",children:(0,a.jsx)("path",{fill:"#ffffff",fillOpacity:"1",d:"M0,64L80,69.3C160,75,320,85,480,80C640,75,800,53,960,48C1120,43,1280,53,1360,58.7L1440,64L1440,120L1360,120C1280,120,1120,120,960,120C800,120,640,120,480,120C320,120,160,120,80,120L0,120Z"})})})]}),(0,a.jsx)("section",{className:"bg-white sticky top-20 z-20 shadow-md",children:(0,a.jsx)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"flex overflow-x-auto py-4 scrollbar-hide",children:[(0,a.jsxs)("button",{onClick:()=>r("details"),className:"px-5 py-2 rounded-lg mr-2 whitespace-nowrap transition-colors ".concat("details"===t?"bg-blue-600 text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"),children:[(0,a.jsx)(d.A,{className:"inline mr-2 mb-0.5"}),"Program Details"]}),(0,a.jsxs)("button",{onClick:()=>r("curriculum"),className:"px-5 py-2 rounded-lg mr-2 whitespace-nowrap transition-colors ".concat("curriculum"===t?"bg-blue-600 text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"),children:[(0,a.jsx)(o.A,{className:"inline mr-2 mb-0.5"}),"Curriculum"]}),(0,a.jsxs)("button",{onClick:()=>r("faculty"),className:"px-5 py-2 rounded-lg mr-2 whitespace-nowrap transition-colors ".concat("faculty"===t?"bg-blue-600 text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"),children:[(0,a.jsx)(x.A,{className:"inline mr-2 mb-0.5"}),"Faculty"]}),(0,a.jsxs)("button",{onClick:()=>r("application"),className:"px-5 py-2 rounded-lg whitespace-nowrap transition-colors ".concat("application"===t?"bg-blue-600 text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"),children:[(0,a.jsx)(n.A,{className:"inline mr-2 mb-0.5"}),"Application"]})]})})}),(0,a.jsx)("div",{className:"transition-opacity duration-300 ".concat("details"===t?"opacity-100":"opacity-0 hidden"),children:(0,a.jsx)(q,{program:s})}),(0,a.jsx)("div",{className:"transition-opacity duration-300 ".concat("curriculum"===t?"opacity-100":"opacity-0 hidden"),children:(0,a.jsx)(M,{program:s})}),(0,a.jsx)("div",{className:"transition-opacity duration-300 ".concat("faculty"===t?"opacity-100":"opacity-0 hidden"),children:(0,a.jsx)(v,{program:s})}),(0,a.jsx)("div",{className:"transition-opacity duration-300 ".concat("application"===t?"opacity-100":"opacity-0 hidden"),children:(0,a.jsx)(g,{program:s})}),(0,a.jsx)("section",{className:"py-16 bg-gradient-to-r from-blue-900 to-indigo-900 text-white",children:(0,a.jsx)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"max-w-3xl mx-auto text-center",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold mb-6",children:"Ready to Apply?"}),(0,a.jsx)("p",{className:"text-xl text-indigo-100 mb-8"}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row justify-center gap-4",children:[(0,a.jsx)(i(),{href:"/submit-application",className:"px-6 py-3 bg-white text-blue-900 font-bold rounded-lg hover:bg-blue-50 transition-colors",children:"Apply Now"}),(0,a.jsx)("button",{className:"px-6 py-3 bg-blue-700 text-white font-bold rounded-lg hover:bg-blue-800 transition-colors",children:"Download Brochure"})]})]})})})]}):(0,a.jsx)("main",{className:"container mx-auto py-16 px-4 text-center",children:(0,a.jsxs)("div",{className:"max-w-lg mx-auto bg-white rounded-xl shadow-lg p-8",children:[(0,a.jsx)("div",{className:"text-blue-600 mb-4",children:(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-16 w-16 mx-auto",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Program Not Found"}),(0,a.jsx)("p",{className:"text-gray-600 mb-8",children:"The academic program you are looking for does not exist or has been moved to a different location."}),(0,a.jsxs)(i(),{href:"/programs",className:"inline-flex items-center bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors",children:[(0,a.jsx)(c.A,{className:"mr-2"})," Back to Programs"]})]})})}},14186:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},17580:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},28883:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},32502:(e,s,t)=>{Promise.resolve().then(t.bind(t,5441))},33786:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},35169:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},40646:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},43332:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]])},47863:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},57434:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},66474:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},69037:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]])},69074:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},74575:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("link-2",[["path",{d:"M9 17H7A5 5 0 0 1 7 7h2",key:"8i5ue5"}],["path",{d:"M15 7h2a5 5 0 1 1 0 10h-2",key:"1b9ql8"}],["line",{x1:"8",x2:"16",y1:"12",y2:"12",key:"1jonct"}]])},85339:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},91788:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},92138:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])}},e=>{var s=s=>e(e.s=s);e.O(0,[651,6874,8441,1684,7358],()=>s(32502)),_N_E=e.O()}]);