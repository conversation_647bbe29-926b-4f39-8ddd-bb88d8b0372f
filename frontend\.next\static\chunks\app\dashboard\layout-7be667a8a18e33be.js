(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1954],{1623:(e,t,a)=>{"use strict";a.d(t,{default:()=>el});var s=a(95155),i=a(12115),r=a(99708),n=a(74466),d=a(22432),o=a(59434);let l=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function c(e){let{className:t,variant:a,size:i,asChild:n=!1,...d}=e,c=n?r.DX:"button";return(0,s.jsx)(c,{"data-slot":"button",className:(0,o.cn)(l({variant:a,size:i,className:t})),...d})}var u=a(15452),h=a(54416);function b(e){let{...t}=e;return(0,s.jsx)(u.bL,{"data-slot":"sheet",...t})}function f(e){let{...t}=e;return(0,s.jsx)(u.ZL,{"data-slot":"sheet-portal",...t})}function p(e){let{className:t,...a}=e;return(0,s.jsx)(u.hJ,{"data-slot":"sheet-overlay",className:(0,o.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",t),...a})}function x(e){let{className:t,children:a,side:i="right",...r}=e;return(0,s.jsxs)(f,{children:[(0,s.jsx)(p,{}),(0,s.jsxs)(u.UC,{"data-slot":"sheet-content",className:(0,o.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500","right"===i&&"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm","left"===i&&"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm","top"===i&&"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b","bottom"===i&&"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t",t),...r,children:[a,(0,s.jsxs)(u.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none",children:[(0,s.jsx)(h.A,{className:"size-4"}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function g(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"sheet-header",className:(0,o.cn)("flex flex-col gap-1.5 p-4",t),...a})}function m(e){let{className:t,...a}=e;return(0,s.jsx)(u.hE,{"data-slot":"sheet-title",className:(0,o.cn)("text-foreground font-semibold",t),...a})}function v(e){let{className:t,...a}=e;return(0,s.jsx)(u.VY,{"data-slot":"sheet-description",className:(0,o.cn)("text-muted-foreground text-sm",t),...a})}var w=a(83921);function j(e){let{delayDuration:t=0,...a}=e;return(0,s.jsx)(w.Kq,{"data-slot":"tooltip-provider",delayDuration:t,...a})}function y(e){let{...t}=e;return(0,s.jsx)(j,{children:(0,s.jsx)(w.bL,{"data-slot":"tooltip",...t})})}function N(e){let{...t}=e;return(0,s.jsx)(w.l9,{"data-slot":"tooltip-trigger",...t})}function k(e){let{className:t,sideOffset:a=0,children:i,...r}=e;return(0,s.jsx)(w.ZL,{children:(0,s.jsxs)(w.UC,{"data-slot":"tooltip-content",sideOffset:a,className:(0,o.cn)("bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance",t),...r,children:[i,(0,s.jsx)(w.i3,{className:"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]"})]})})}let z=i.createContext(null);function _(){let e=i.useContext(z);if(!e)throw Error("useSidebar must be used within a SidebarProvider.");return e}function C(e){let{defaultOpen:t=!0,open:a,onOpenChange:r,className:n,style:d,children:l,...c}=e,u=function(){let[e,t]=i.useState(void 0);return i.useEffect(()=>{let e=window.matchMedia("(max-width: ".concat(767,"px)")),a=()=>{t(window.innerWidth<768)};return e.addEventListener("change",a),t(window.innerWidth<768),()=>e.removeEventListener("change",a)},[]),!!e}(),[h,b]=i.useState(!1),[f,p]=i.useState(t),x=null!=a?a:f,g=i.useCallback(e=>{let t="function"==typeof e?e(x):e;r?r(t):p(t),document.cookie="".concat("sidebar_state","=").concat(t,"; path=/; max-age=").concat(604800)},[r,x]),m=i.useCallback(()=>u?b(e=>!e):g(e=>!e),[u,g,b]);i.useEffect(()=>{let e=e=>{"b"===e.key&&(e.metaKey||e.ctrlKey)&&(e.preventDefault(),m())};return window.addEventListener("keydown",e),()=>window.removeEventListener("keydown",e)},[m]);let v=x?"expanded":"collapsed",w=i.useMemo(()=>({state:v,open:x,setOpen:g,isMobile:u,openMobile:h,setOpenMobile:b,toggleSidebar:m}),[v,x,g,u,h,b,m]);return(0,s.jsx)(z.Provider,{value:w,children:(0,s.jsx)(j,{delayDuration:0,children:(0,s.jsx)("div",{"data-slot":"sidebar-wrapper",style:{"--sidebar-width":"16rem","--sidebar-width-icon":"3rem",...d},className:(0,o.cn)("group/sidebar-wrapper has-data-[variant=inset]:bg-sidebar flex min-h-svh w-full",n),...c,children:l})})})}function A(e){let{side:t="left",variant:a="sidebar",collapsible:i="offcanvas",className:r,children:n,...d}=e,{isMobile:l,state:c,openMobile:u,setOpenMobile:h}=_();return"none"===i?(0,s.jsx)("div",{"data-slot":"sidebar",className:(0,o.cn)("bg-sidebar text-sidebar-foreground flex h-full w-(--sidebar-width) flex-col",r),...d,children:n}):l?(0,s.jsx)(b,{open:u,onOpenChange:h,...d,children:(0,s.jsxs)(x,{"data-sidebar":"sidebar","data-slot":"sidebar","data-mobile":"true",className:"bg-sidebar text-sidebar-foreground w-(--sidebar-width) p-0 [&>button]:hidden",style:{"--sidebar-width":"18rem"},side:t,children:[(0,s.jsxs)(g,{className:"sr-only",children:[(0,s.jsx)(m,{children:"Sidebar"}),(0,s.jsx)(v,{children:"Displays the mobile sidebar."})]}),(0,s.jsx)("div",{className:"flex h-full w-full flex-col",children:n})]})}):(0,s.jsxs)("div",{className:"group peer text-sidebar-foreground hidden md:block","data-state":c,"data-collapsible":"collapsed"===c?i:"","data-variant":a,"data-side":t,"data-slot":"sidebar",children:[(0,s.jsx)("div",{"data-slot":"sidebar-gap",className:(0,o.cn)("relative w-(--sidebar-width) bg-transparent transition-[width] duration-200 ease-linear","group-data-[collapsible=offcanvas]:w-0","group-data-[side=right]:rotate-180","floating"===a||"inset"===a?"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4)))]":"group-data-[collapsible=icon]:w-(--sidebar-width-icon)")}),(0,s.jsx)("div",{"data-slot":"sidebar-container",className:(0,o.cn)("fixed inset-y-0 z-10 hidden h-svh w-(--sidebar-width) transition-[left,right,width] duration-200 ease-linear md:flex","left"===t?"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]":"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]","floating"===a||"inset"===a?"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4))+2px)]":"group-data-[collapsible=icon]:w-(--sidebar-width-icon) group-data-[side=left]:border-r group-data-[side=right]:border-l",r),...d,children:(0,s.jsx)("div",{"data-sidebar":"sidebar","data-slot":"sidebar-inner",className:"bg-sidebar group-data-[variant=floating]:border-sidebar-border flex h-full w-full flex-col group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:shadow-sm",children:n})})]})}function E(e){let{className:t,onClick:a,...i}=e,{toggleSidebar:r}=_();return(0,s.jsxs)(c,{"data-sidebar":"trigger","data-slot":"sidebar-trigger",variant:"ghost",size:"icon",className:(0,o.cn)("size-7",t),onClick:e=>{null==a||a(e),r()},...i,children:[(0,s.jsx)(d.A,{}),(0,s.jsx)("span",{className:"sr-only",children:"Toggle Sidebar"})]})}function L(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"sidebar-content","data-sidebar":"content",className:(0,o.cn)("flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden",t),...a})}function D(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"sidebar-group","data-sidebar":"group",className:(0,o.cn)("relative flex w-full min-w-0 flex-col p-2",t),...a})}function S(e){let{className:t,asChild:a=!1,...i}=e,n=a?r.DX:"div";return(0,s.jsx)(n,{"data-slot":"sidebar-group-label","data-sidebar":"group-label",className:(0,o.cn)("text-sidebar-foreground/70 ring-sidebar-ring flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium outline-hidden transition-[margin,opacity] duration-200 ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0","group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0",t),...i})}function P(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"sidebar-group-content","data-sidebar":"group-content",className:(0,o.cn)("w-full text-sm",t),...a})}function O(e){let{className:t,...a}=e;return(0,s.jsx)("ul",{"data-slot":"sidebar-menu","data-sidebar":"menu",className:(0,o.cn)("flex w-full min-w-0 flex-col gap-1",t),...a})}function U(e){let{className:t,...a}=e;return(0,s.jsx)("li",{"data-slot":"sidebar-menu-item","data-sidebar":"menu-item",className:(0,o.cn)("group/menu-item relative",t),...a})}let q=(0,n.F)("peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-hidden ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-data-[sidebar=menu-action]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:size-8! group-data-[collapsible=icon]:p-2! [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0",{variants:{variant:{default:"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",outline:"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]"},size:{default:"h-8 text-sm",sm:"h-7 text-xs",lg:"h-12 text-sm group-data-[collapsible=icon]:p-0!"}},defaultVariants:{variant:"default",size:"default"}});function F(e){let{asChild:t=!1,isActive:a=!1,variant:i="default",size:n="default",tooltip:d,className:l,...c}=e,u=t?r.DX:"button",{isMobile:h,state:b}=_(),f=(0,s.jsx)(u,{"data-slot":"sidebar-menu-button","data-sidebar":"menu-button","data-size":n,"data-active":a,className:(0,o.cn)(q({variant:i,size:n}),l),...c});return d?("string"==typeof d&&(d={children:d}),(0,s.jsxs)(y,{children:[(0,s.jsx)(N,{asChild:!0,children:f}),(0,s.jsx)(k,{side:"right",align:"center",hidden:"collapsed"!==b||h,...d})]})):f}var K=a(57340),M=a(45347),V=a(85339),W=a(57434),X=a(17580),Z=a(55670),G=a(27213),I=a(42076),J=a(66766),T=a(35695),B=a(6874),H=a.n(B);let Q=[{title:"Dashboard",url:"/dashboard",icon:K.A},{title:"News",url:"/dashboard/news",icon:M.A},{title:"Notices",url:"/dashboard/notice",icon:V.A},{title:"Applications",url:"/dashboard/application",icon:W.A},{title:"Applicants",url:"/dashboard/applicants",icon:X.A},{title:"Committee",url:"/dashboard/committee",icon:Z.A},{title:"Photo Gallery",url:"/dashboard/photo-gallery",icon:G.A},{title:"Gallery Images",url:"/dashboard/gallery-images",icon:I.A}];function Y(){let e=(0,T.usePathname)();return console.log(e),(0,s.jsx)(A,{children:(0,s.jsx)(L,{children:(0,s.jsxs)(D,{children:[(0,s.jsxs)(S,{className:"flex flex-col items-center justify-center mt-6 ",children:[(0,s.jsx)(J.default,{src:"/logo.png",alt:"logo",width:100,height:100,className:"h-10 w-full",priority:!0,quality:100,sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"}),(0,s.jsx)("h1",{className:" text-[8px] font-bold text-end",children:"(FWU) (Incubation Center )"})]}),(0,s.jsx)(P,{children:(0,s.jsx)(O,{className:" mt-6",children:Q.map(t=>(0,s.jsx)(U,{children:(0,s.jsx)(F,{className:" ".concat(e===t.url?" bg-blue-500 text-white transition-all duration-200  hover:text-white  hover:bg-blue-600":""),asChild:!0,children:(0,s.jsxs)(H(),{href:t.url,children:[(0,s.jsx)(t.icon,{}),(0,s.jsx)("span",{className:" ",children:t.title})]})})},t.title))})})]})})})}var $=a(432),R=a(26715),ee=a(78578);function et(e){let{...t}=e;return(0,s.jsx)(ee.bL,{"data-slot":"dropdown-menu",...t})}function ea(e){let{...t}=e;return(0,s.jsx)(ee.l9,{"data-slot":"dropdown-menu-trigger",...t})}function es(e){let{className:t,sideOffset:a=4,...i}=e;return(0,s.jsx)(ee.ZL,{children:(0,s.jsx)(ee.UC,{"data-slot":"dropdown-menu-content",sideOffset:a,className:(0,o.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",t),...i})})}function ei(e){let{className:t,inset:a,variant:i="default",...r}=e;return(0,s.jsx)(ee.q7,{"data-slot":"dropdown-menu-item","data-inset":a,"data-variant":i,className:(0,o.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...r})}function er(e){let{className:t,inset:a,...i}=e;return(0,s.jsx)(ee.JU,{"data-slot":"dropdown-menu-label","data-inset":a,className:(0,o.cn)("px-2 py-1.5 text-sm font-medium data-[inset]:pl-8",t),...i})}function en(e){let{className:t,...a}=e;return(0,s.jsx)(ee.wv,{"data-slot":"dropdown-menu-separator",className:(0,o.cn)("bg-border -mx-1 my-1 h-px",t),...a})}var ed=a(23861);let eo=function(){return(0,s.jsx)("div",{children:(0,s.jsxs)("div",{className:"border-b shadow-md flex justify-between  items-center gap-1 p-2",children:[(0,s.jsx)(E,{size:"lg"}),(0,s.jsxs)("div",{className:" flex gap-2 justify-center items-center",children:[(0,s.jsxs)("div",{className:"bg-white/20 flex gap-1 backdrop-blur-sm rounded-xl p-3",children:[(0,s.jsx)(ed.A,{className:"w-6 h-6"}),(0,s.jsx)("span",{className:" text-sm  rounded-[5px] w-[10px] h-[10px] ",children:"2"})]}),(0,s.jsxs)(et,{children:[(0,s.jsx)(ea,{asChild:!0,children:(0,s.jsx)("button",{className:"bg-white/20 backdrop-blur-sm rounded-xl p-3",children:(0,s.jsx)(J.default,{height:32,width:32,src:"https://github.com/shadcn.png",alt:"avatar",className:"w-8 h-8 rounded-full"})})}),(0,s.jsxs)(es,{align:"end",children:[(0,s.jsx)(er,{children:"My Account"}),(0,s.jsx)(en,{}),(0,s.jsx)(ei,{children:"Profile"}),(0,s.jsx)(ei,{children:"Team"}),(0,s.jsx)(ei,{children:"Billing"}),(0,s.jsx)(ei,{children:"Subscription"})]})]})]})]})})},el=function(e){let{children:t}=e,a=new $.E;return(0,s.jsx)(R.Ht,{client:a,children:(0,s.jsxs)(C,{children:[(0,s.jsx)(Y,{}),(0,s.jsxs)("div",{className:" w-full bg-gray-200",children:[(0,s.jsx)("div",{className:"sticky top-0 z-30  backdrop-blur-2xl",children:(0,s.jsx)(eo,{})}),(0,s.jsxs)("div",{className:"fixed inset-0 overflow-hidden pointer-events-none",children:[(0,s.jsx)("div",{className:"absolute -top-4 -right-4 w-72 h-72 bg-gradient-to-br from-blue-400 to-purple-400 rounded-full opacity-10 animate-pulse"}),(0,s.jsx)("div",{className:"absolute -bottom-8 -left-8 w-96 h-96 bg-gradient-to-br from-pink-400 to-red-400 rounded-full opacity-10 animate-pulse",style:{animationDelay:"2s"}})]}),t]})]})})}},59434:(e,t,a)=>{"use strict";a.d(t,{cn:()=>r});var s=a(52596),i=a(39688);function r(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,i.QP)((0,s.$)(t))}},70640:(e,t,a)=>{Promise.resolve().then(a.bind(a,1623))}},e=>{var t=t=>e(e.s=t);e.O(0,[651,6874,6078,6279,5785,8441,1684,7358],()=>t(70640)),_N_E=e.O()}]);