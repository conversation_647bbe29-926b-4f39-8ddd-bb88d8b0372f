{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/fwu_incubation%28react%2Blaravel%29/incubation/frontend/src/app/fwu/components/common/Logo.tsx"], "sourcesContent": ["// components/common/Logo.tsx\r\nimport Image from 'next/image';\r\nimport Link from 'next/link';\r\n\r\ninterface LogoProps {\r\n  variant?: 'light' | 'dark';\r\n}\r\n\r\nconst Logo: React.FC<LogoProps> = ({ variant = 'light' }) => {\r\n  const isLight = variant === 'light';\r\n\r\n  return (\r\n    <Link href=\"/\" className=\"flex items-center space-x-3 group\">\r\n      {/* Logo container with glow effect */}\r\n      <div className=\"relative\">\r\n        {/* Glow effect */}\r\n        <div className=\"absolute -inset-0.5 bg-gradient-to-r from-indigo-500 to-blue-500 rounded-full opacity-0 group-hover:opacity-70 blur-md transition-opacity duration-300\"></div>\r\n\r\n        {/* Logo image */}\r\n        <div className=\"relative h-12 w-12 bg-gradient-to-br from-indigo-100 to-white p-0.5 rounded-full overflow-hidden border border-indigo-100 shadow-md group-hover:shadow-indigo-300/50 transition-all duration-300 z-10\">\r\n          <div className=\"absolute inset-0 bg-gradient-to-br from-indigo-500/10 to-blue-500/10 rounded-full group-hover:opacity-0 transition-opacity duration-300\"></div>\r\n          <div className=\"h-full w-full rounded-full overflow-hidden\">\r\n            <Image\r\n              src=\"/circlelogo.png\"\r\n              alt=\"Far Western University Logo\"\r\n              width={48}\r\n              height={48}\r\n              className=\"object-contain transform group-hover:scale-110 transition-transform duration-300\"\r\n            />\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Text content */}\r\n      <div className=\"flex flex-col\">\r\n        {isLight ? (\r\n          <>\r\n            {/* Light variant */}\r\n            <div className=\"relative\">\r\n              <span className=\"text-xl font-bold  transition-colors leading-tight\">\r\n                Far Western University\r\n              </span>\r\n              {/* Animated underline on hover */}\r\n              <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-indigo-500 to-blue-500 group-hover:w-full transition-all duration-500 ease-out\"></span>\r\n            </div>\r\n            <div className=\"flex items-center\">\r\n              <span className=\"text-xs text-gray-500 group-hover:text-gray-700 transition-colors\">\r\n                <span className=\"text-indigo-500 font-medium\">Incubation</span> Center\r\n              </span>\r\n              {/* Animated dot */}\r\n              <span className=\"ml-1 w-1.5 h-1.5 rounded-full bg-indigo-500 animate-pulse\"></span>\r\n            </div>\r\n          </>\r\n        ) : (\r\n          <>\r\n            {/* Dark variant */}\r\n            <div className=\"relative\">\r\n              <span className=\"text-xl font-bold text-white group-hover:text-indigo-200 transition-colors leading-tight\">\r\n                Far Western University\r\n              </span>\r\n              {/* Animated underline on hover */}\r\n              <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-indigo-400 to-blue-400 group-hover:w-full transition-all duration-500 ease-out\"></span>\r\n            </div>\r\n            <div className=\"flex items-center\">\r\n              <span className=\"text-xs text-indigo-200 group-hover:text-white transition-colors\">\r\n                <span className=\"text-indigo-300 font-medium\">Incubation</span> Center\r\n              </span>\r\n              {/* Animated dot */}\r\n              <span className=\"ml-1 w-1.5 h-1.5 rounded-full bg-indigo-400 animate-pulse\"></span>\r\n            </div>\r\n          </>\r\n        )}\r\n      </div>\r\n    </Link>\r\n  );\r\n};\r\n\r\nexport default Logo;"], "names": [], "mappings": "AAAA,6BAA6B;;;;;AAC7B;AACA;;;;AAMA,MAAM,OAA4B,CAAC,EAAE,UAAU,OAAO,EAAE;IACtD,MAAM,UAAU,YAAY;IAE5B,qBACE,6LAAC,+JAAA,CAAA,UAAI;QAAC,MAAK;QAAI,WAAU;;0BAEvB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;;;;;kCAGf,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;oCACJ,KAAI;oCACJ,KAAI;oCACJ,OAAO;oCACP,QAAQ;oCACR,WAAU;;;;;;;;;;;;;;;;;;;;;;;0BAOlB,6LAAC;gBAAI,WAAU;0BACZ,wBACC;;sCAEE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;8CAAqD;;;;;;8CAIrE,6LAAC;oCAAK,WAAU;;;;;;;;;;;;sCAElB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;;sDACd,6LAAC;4CAAK,WAAU;sDAA8B;;;;;;wCAAiB;;;;;;;8CAGjE,6LAAC;oCAAK,WAAU;;;;;;;;;;;;;iDAIpB;;sCAEE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;8CAA2F;;;;;;8CAI3G,6LAAC;oCAAK,WAAU;;;;;;;;;;;;sCAElB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;;sDACd,6LAAC;4CAAK,WAAU;sDAA8B;;;;;;wCAAiB;;;;;;;8CAGjE,6LAAC;oCAAK,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;AAO9B;KAnEM;uCAqES", "debugId": null}}, {"offset": {"line": 223, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/fwu_incubation%28react%2Blaravel%29/incubation/frontend/src/app/fwu/components/layout/Header.tsx"], "sourcesContent": ["\"use client\"\r\nimport Link from 'next/link';\r\nimport { useState, useEffect } from 'react';\r\nimport Logo from '../common/Logo';\r\nimport {\r\n  Menu,\r\n  X,\r\n  Globe,\r\n  ChevronDown,\r\n  Rocket,\r\n  Users,\r\n  Mail,\r\n  Home,\r\n  Lightbulb,\r\n  Book,\r\n  Briefcase,\r\n  GraduationCap,\r\n  Handshake\r\n} from 'lucide-react';\r\nimport { usePathname } from 'next/navigation';\r\n\r\nconst menuItems: { \r\n  name: string; \r\n  href: string; \r\n  icon: React.ReactNode; \r\n  isHighlighted?: boolean;\r\n  isNew?: boolean;\r\n  description?: string;\r\n}[] = [\r\n  {\r\n    name: 'Home',\r\n    href: '/fwu',\r\n    icon: <Home className=\"w-4 h-4\" />,\r\n    description: 'Welcome to our homepage'\r\n  },\r\n  {\r\n    name: 'About',\r\n    href: '/fwu/about',\r\n    icon: <Users className=\"w-4 h-4\" />,\r\n    description: 'Learn about our university'\r\n  },\r\n  {\r\n    name: 'Programs',\r\n    href: '/fwu/programs',\r\n    icon: <GraduationCap className=\"w-4 h-4\" />,\r\n    isHighlighted: true,\r\n    description: 'Explore our academic programs'\r\n  },\r\n  {\r\n    name: 'Research',\r\n    href: '/fwu/research',\r\n    icon: <Book className=\"w-4 h-4\" />,\r\n    description: 'Discover our research initiatives'\r\n  },\r\n  {\r\n    name: 'Proposals',\r\n    href: '/fwu/submit-proposal',\r\n    icon: <Rocket className=\"w-4 h-4\" />,\r\n    description: 'Submit your research proposals'\r\n  },\r\n  {\r\n    name: 'Projects',\r\n    href: '/fwu/projects',\r\n    icon: <Lightbulb className=\"w-4 h-4\" />,\r\n    description: 'View our ongoing projects'\r\n  },\r\n  {\r\n    name: 'Careers',\r\n    href: '/fwu/careers',\r\n    icon: <Briefcase className=\"w-4 h-4\" />,\r\n    isNew: true,\r\n    description: 'Explore job opportunities'\r\n  },\r\n  {\r\n    name: 'Startups',\r\n    href: '/fwu/startups',\r\n    icon: <Lightbulb className=\"w-4 h-4\" />,\r\n    isNew: true,\r\n    description: 'University startup initiatives'\r\n  },\r\n  {\r\n    name: 'Faculty',\r\n    href: '/fwu/faculty',\r\n    icon: <Users className=\"w-4 h-4\" />,\r\n    description: 'Meet our faculty members'\r\n  },\r\n  {\r\n         name:\"Community\",\r\n         href:\"/fwu/community\",\r\n         icon: <Handshake className=\"w-4 h-4\" />,\r\n         description: 'Join our community'\r\n  },\r\n\r\n  {\r\n    name: 'News',\r\n    href: '/fwu/news',\r\n    icon: <Lightbulb className=\"w-4 h-4\" />,\r\n    description: 'Latest university news'\r\n  },\r\n\r\n  {\r\n    name: 'Contact',\r\n    href: '/fwu/contact',\r\n    icon: <Mail className=\"w-4 h-4\" />,\r\n    description: 'Get in touch with us'\r\n  },\r\n];\r\n\r\nconst Header = () => {\r\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\r\n  const [currentLang, setCurrentLang] = useState('EN');\r\n  const [scrolled, setScrolled] = useState(false);\r\n  const [activeItem, setActiveItem] = useState('/');\r\n  const [isLangDropdownOpen, setIsLangDropdownOpen] = useState(false);\r\n  const [hoveredItem, setHoveredItem] = useState<string | null>(null);\r\n  const path=usePathname();\r\n  // Handle scroll effect\r\n  useEffect(() => {\r\n    const handleScroll = () => {\r\n      const offset = window.scrollY;\r\n      if (offset > 50) {\r\n        setScrolled(true);\r\n      } else {\r\n        setScrolled(false);\r\n      }\r\n    };\r\n\r\n    // Set active menu item based on current path\r\n    if (typeof window !== 'undefined') {\r\n      setActiveItem(window.location.pathname);\r\n    }\r\n\r\n    window.addEventListener('scroll', handleScroll);\r\n    return () => window.removeEventListener('scroll', handleScroll);\r\n  }, []);\r\n\r\n  // Close mobile menu when clicking outside\r\n  useEffect(() => {\r\n    const handleClickOutside = (event: MouseEvent) => {\r\n      const target = event.target as HTMLElement;\r\n      if (isMobileMenuOpen && !target.closest('#mobile-menu') && !target.closest('#mobile-menu-button')) {\r\n        setIsMobileMenuOpen(false);\r\n      }\r\n    };\r\n\r\n    document.addEventListener('mousedown', handleClickOutside);\r\n    return () => document.removeEventListener('mousedown', handleClickOutside);\r\n  }, [isMobileMenuOpen]);\r\n\r\n  const toggleMobileMenu = () => setIsMobileMenuOpen(!isMobileMenuOpen);\r\n\r\n  const toggleLangDropdown = () => setIsLangDropdownOpen(!isLangDropdownOpen);\r\n\r\n  const switchLanguage = (lang: string) => {\r\n    setCurrentLang(lang);\r\n    setIsLangDropdownOpen(false);\r\n    // Add actual language switching logic here\r\n    console.log(`Language switched to ${lang}`);\r\n  };\r\n\r\n  return (\r\n    <header className=\"sticky top-0 z-50 text-white\">\r\n      {/* Animated top border */}\r\n      <div className=\"h-1 w-full bg-gradient-to-r from-indigo-500 via-purple-500 to-blue-500\" style={{\r\n        backgroundSize: '200% 100%',\r\n        animation: 'gradientMove 8s linear infinite'\r\n      }}></div>\r\n\r\n      {/* Main header background with glass effect */}\r\n      <div className={`relative transition-all duration-500 ${\r\n        scrolled\r\n          ? 'bg-indigo-900/90 backdrop-blur-md shadow-lg'\r\n          : 'bg-gradient-to-r from-indigo-900 to-blue-900'\r\n      }`}>\r\n        {/* Decorative elements */}\r\n        <div className=\"absolute inset-0 overflow-hidden pointer-events-none\">\r\n          <div className=\"absolute top-0 right-1/4 w-64 h-64 bg-blue-400/10 rounded-full blur-3xl\"></div>\r\n          <div className=\"absolute bottom-0 left-1/3 w-64 h-64 bg-purple-400/10 rounded-full blur-3xl\"></div>\r\n          <div className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-indigo-500/5 rounded-full blur-3xl\"></div>\r\n\r\n          {/* Animated dots */}\r\n          <div className=\"absolute inset-0\">\r\n            {[...Array(8)].map((_, i) => (\r\n              <div\r\n                key={i}\r\n                className=\"absolute w-1.5 h-1.5 bg-white/20 rounded-full\"\r\n                style={{\r\n                  top: `${20 + Math.random() * 60}%`,\r\n                  left: `${20 + Math.random() * 60}%`,\r\n                  animation: `float ${3 + Math.random() * 4}s ease-in-out infinite ${Math.random() * 2}s`\r\n                }}\r\n              ></div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 relative\">\r\n          <div className=\"flex items-center justify-between h-20\">\r\n            {/* Logo with hover effect */}\r\n            <div className=\"relative z-10 transform transition-transform duration-300 hover:scale-105\">\r\n              <Logo />\r\n            </div>\r\n\r\n            {/* Desktop Menu - Main Items Only */}\r\n            <div className=\"hidden lg:flex items-center\">\r\n              <nav className=\"flex items-center space-x-1\">\r\n                {menuItems.slice(0, 6).map((item) => {\r\n                  const isActive = path === item.href;\r\n                  return (\r\n                    <Link\r\n                      key={item.name}\r\n                      href={item.href}\r\n                     \r\n                      className={`flex items-center px-4 py-3 rounded-lg text-base font-medium transition-all duration-300 relative group whitespace-nowrap ${\r\n                        isActive\r\n                          ? 'text-white before:absolute before:bottom-0 before:left-0 before:w-full before:h-0.5 before:bg-indigo-400'\r\n                            : 'text-gray-300 hover:text-white'\r\n                      }`}\r\n                      \r\n                      onMouseEnter={() => setHoveredItem(item.name)}\r\n                      onMouseLeave={() => setHoveredItem(null)}\r\n                    >\r\n                  \r\n\r\n                      <span className=\"relative\">\r\n                        {item.name}\r\n                      </span>\r\n\r\n                      {/* Tooltip */}\r\n                      {hoveredItem === item.name && item.description && (\r\n                        <div className=\"absolute z-40 left-1/2 transform -translate-x-1/2 mt-1 top-full w-48 p-2 bg-white/95 backdrop-blur-sm text-gray-800 text-xs rounded-lg shadow-lg border border-indigo-100 opacity-0 animate-fadeInFast\">\r\n                          {item.description}\r\n                          <div className=\"absolute -top-1 left-1/2 transform -translate-x-1/2 w-2 h-2 bg-white rotate-45\"></div>\r\n                        </div>\r\n                      )}\r\n\r\n                      {/* Bottom border animation */}\r\n                      <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-indigo-400 to-blue-400 group-hover:w-full transition-all duration-300 ease-out\"></span>\r\n                      \r\n                    </Link>\r\n                  );\r\n                })}\r\n\r\n               \r\n              </nav>\r\n\r\n              {/* Apply Button - Desktop */}\r\n              <div className=\"ml-4 flex items-center\">\r\n                <Link\r\n                  href=\"/apply\"\r\n                  className=\"relative overflow-hidden group\"\r\n                >\r\n                  <span className=\"relative z-10 flex items-center justify-center px-5 py-2.5 rounded-lg font-bold bg-gradient-to-r from-indigo-500 to-blue-600 hover:from-indigo-600 hover:to-blue-700 shadow-md transition-all duration-300\">\r\n                    Apply Now\r\n                    <svg className=\"w-4 h-4 ml-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M13 7l5 5m0 0l-5 5m5-5H6\"></path>\r\n                    </svg>\r\n                    <span className=\"absolute inset-0 w-full h-full bg-gradient-to-r from-blue-600/40 to-purple-600/40 blur-md group-hover:blur-lg transition-all duration-300 -z-10\"></span>\r\n                  </span>\r\n                </Link>\r\n\r\n                {/* Language Switcher - Desktop */}\r\n                <div className=\"relative ml-3\">\r\n                  <button\r\n                    onClick={toggleLangDropdown}\r\n                    className=\"flex items-center text-white font-medium bg-white/10 hover:bg-white/15 px-3 py-2.5 rounded-lg transition-all duration-300 border border-white/10 hover:border-white/20\"\r\n                  >\r\n                    <Globe className=\"mr-1.5\" />\r\n                    <span>{currentLang}</span>\r\n                    <ChevronDown className={`ml-1 transition-transform duration-300 ${isLangDropdownOpen ? 'rotate-180' : ''}`} />\r\n                  </button>\r\n\r\n                  {isLangDropdownOpen && (\r\n                    <div className=\"absolute right-0 mt-2 w-40 bg-white/95 backdrop-blur-md rounded-xl shadow-xl z-40 border border-indigo-100 overflow-hidden transition-all duration-300 animate-fadeIn\">\r\n                      <div className=\"h-1 w-full bg-gradient-to-r from-indigo-500 to-blue-500\"></div>\r\n                      <button\r\n                        onClick={() => switchLanguage('EN')}\r\n                        className=\"flex w-full items-center px-4 py-3 text-sm text-gray-700 hover:bg-indigo-50 cursor-pointer transition-colors duration-300 border-b border-gray-100\"\r\n                      >\r\n                        <span className=\"w-6 h-6 rounded-full bg-indigo-100 flex items-center justify-center mr-2 text-indigo-600\">EN</span>\r\n                        English\r\n                      </button>\r\n                      <button\r\n                        onClick={() => switchLanguage('NE')}\r\n                        className=\"flex w-full items-center px-4 py-3 text-sm text-gray-700 hover:bg-indigo-50 cursor-pointer transition-colors duration-300\"\r\n                      >\r\n                        <span className=\"w-6 h-6 rounded-full bg-indigo-100 flex items-center justify-center mr-2 text-indigo-600\">NE</span>\r\n                        नेपाली\r\n                      </button>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              </div>\r\n              \r\n            </div>\r\n\r\n            {/* Mobile Menu Button */}\r\n            <div className=\"lg:hidden flex items-center space-x-2\">\r\n              <Link\r\n                href=\"/apply\"\r\n                className=\"relative overflow-hidden group\"\r\n              >\r\n                <span className=\"relative z-10 flex items-center justify-center px-4 py-2 rounded-lg font-bold bg-gradient-to-r from-indigo-500 to-blue-600 hover:from-indigo-600 hover:to-blue-700 shadow-md transition-all duration-300\">\r\n                  Apply\r\n                  <svg className=\"w-4 h-4 ml-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M13 7l5 5m0 0l-5 5m5-5H6\"></path>\r\n                  </svg>\r\n                </span>\r\n              </Link>\r\n\r\n              <button\r\n                onClick={() => switchLanguage(currentLang === 'EN' ? 'NE' : 'EN')}\r\n                className=\"p-2.5 text-white bg-white/10 hover:bg-white/15 rounded-lg transition-all duration-300 border border-white/10 hover:border-white/20\"\r\n                aria-label=\"Switch Language\"\r\n              >\r\n                <Globe size={20} />\r\n              </button>\r\n\r\n              <button\r\n                id=\"mobile-menu-button\"\r\n                onClick={toggleMobileMenu}\r\n                className=\"p-2.5 text-white bg-white/10 hover:bg-white/15 rounded-lg transition-all duration-300 border border-white/10 hover:border-white/20\"\r\n                aria-label=\"Open menu\"\r\n              >\r\n                {isMobileMenuOpen ? <X size={20} /> : <Menu size={20} />}\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Subheader for additional navigation - Desktop only */}\r\n      <div className=\"hidden lg:block bg-white/95 backdrop-blur-md border-t border-indigo-100/50 shadow-sm\">\r\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\r\n          <div className=\"flex items-center justify-center py-3\">\r\n            <nav className=\"flex items-center space-x-8\">\r\n              {menuItems.slice(6).map((item) => {\r\n                const isActive = activeItem === item.href;\r\n                return (\r\n                  <Link\r\n                    key={item.name}\r\n                    href={item.href}\r\n                    className={`text-sm font-medium transition-all duration-300 relative group ${\r\n                      isActive\r\n                        ? 'text-indigo-600'\r\n                        : item.isNew\r\n                          ? 'text-green-600 hover:text-green-700'\r\n                          : 'text-gray-600 hover:text-indigo-600'\r\n                    }`}\r\n                  >\r\n                    <span className=\"flex items-center\">\r\n                      {item.name}\r\n                      {item.isNew && (\r\n                        <span className=\"ml-1.5 px-1.5 py-0.5 text-[9px] font-bold bg-green-500 text-white rounded-sm\">\r\n                          NEW\r\n                        </span>\r\n                      )}\r\n                    </span>\r\n\r\n                    {/* Bottom border animation */}\r\n                    <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-indigo-400 to-blue-400 group-hover:w-full transition-all duration-300 ease-out\"></span>\r\n                  </Link>\r\n                );\r\n              })}\r\n            </nav>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Left Side Mobile Menu with animation */}\r\n      <div\r\n        id=\"mobile-menu\"\r\n        className={`lg:hidden fixed inset-0 z-40 transition-all duration-500 ${\r\n          isMobileMenuOpen ? 'opacity-100 visible' : 'opacity-0 invisible'\r\n        }`}\r\n      >\r\n        {/* Backdrop */}\r\n        <div\r\n          className={`absolute inset-0 bg-indigo-900/80 backdrop-blur-sm transition-opacity duration-500 ${\r\n            isMobileMenuOpen ? 'opacity-100' : 'opacity-0'\r\n          }`}\r\n          onClick={() => setIsMobileMenuOpen(false)}\r\n        ></div>\r\n\r\n        {/* Menu panel - Left side */}\r\n        <div\r\n          className={`absolute top-0 left-0 bottom-0 w-full max-w-xs bg-gradient-to-b from-indigo-800 to-indigo-900 shadow-2xl transform transition-transform duration-500 ease-out ${\r\n            isMobileMenuOpen ? 'translate-x-0' : '-translate-x-full'\r\n          }`}\r\n        >\r\n          {/* Menu header */}\r\n          <div className=\"flex items-center justify-between p-6 border-b border-indigo-700/50\">\r\n            <div>\r\n              <h3 className=\"text-xl font-bold text-white\">Menu</h3>\r\n              <p className=\"text-indigo-200 text-sm\">Far Western University</p>\r\n            </div>\r\n            <button\r\n              onClick={() => setIsMobileMenuOpen(false)}\r\n              className=\"p-2 rounded-full bg-indigo-700/50 text-white hover:bg-indigo-700\"\r\n            >\r\n              <X size={20} />\r\n            </button>\r\n          </div>\r\n\r\n          {/* Menu items - Scrollable */}\r\n          <div className=\"h-[calc(100%-160px)] overflow-y-auto p-2 custom-scrollbar\">\r\n            <nav className=\"py-2\">\r\n              <div className=\"space-y-1  mb-10\">\r\n                {menuItems.map((item, index) => {\r\n                  const isActive = activeItem === item.href;\r\n                  return (\r\n                    <Link\r\n                      key={item.name}\r\n                      href={item.href}\r\n                      onClick={() => setIsMobileMenuOpen(false)}\r\n                      className={`flex items-center px-4 py-3 rounded-xl text-base font-medium transition-all duration-300 ${\r\n                        isActive\r\n                          ? 'bg-indigo-700/50 text-white'\r\n                          : item.isHighlighted\r\n                            ? 'bg-blue-700/30 text-white border border-blue-600/30'\r\n                            : 'text-indigo-100 hover:bg-indigo-700/30 hover:text-white'\r\n                      }`}\r\n                      style={{\r\n                        transitionDelay: `${100 + index * 50}ms`,\r\n                        animation: isMobileMenuOpen ? `fadeSlideIn 0.4s ease-out forwards ${100 + index * 50}ms` : 'none'\r\n                      }}\r\n                    >\r\n                      <span className={`p-2.5 rounded-lg mr-3 text-white ${\r\n                        item.isHighlighted\r\n                          ? 'bg-blue-600/70'\r\n                          : item.isNew\r\n                            ? 'bg-green-600/70 animate-pulse'\r\n                            : 'bg-indigo-600/50'\r\n                      }`}>\r\n                        {item.icon}\r\n                      </span>\r\n                      <div className=\"flex items-center justify-between w-full\">\r\n                        <div>\r\n                          <div>{item.name}</div>\r\n                          <div className=\"text-xs text-indigo-300\">{item.description}</div>\r\n                        </div>\r\n                        {(item.isHighlighted || item.isNew) && (\r\n                          <span className={`ml-2 px-1.5 py-0.5 text-[10px] font-bold ${item.isNew ? 'bg-green-500' : 'bg-blue-500'} text-white rounded-sm whitespace-nowrap`}>\r\n                            {item.isNew ? 'NEW' : 'FEATURED'}\r\n                          </span>\r\n                        )}\r\n                      </div>\r\n                    </Link>\r\n                  );\r\n                })}\r\n              </div>\r\n            </nav>\r\n          </div>\r\n\r\n          {/* Additional mobile menu items */}\r\n          <div className=\"absolute bottom-0 left-0 right-0 p-4 border-t border-indigo-700/30\">\r\n            <div className=\"mb-4\">\r\n              <p className=\"text-xs uppercase tracking-wider text-indigo-300 mb-2\">Language</p>\r\n              <div className=\"flex space-x-2\">\r\n                <button\r\n                  onClick={() => switchLanguage('EN')}\r\n                  className={`flex-1 px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-300 ${\r\n                    currentLang === 'EN'\r\n                      ? 'bg-indigo-600 text-white'\r\n                      : 'bg-indigo-800/50 text-indigo-200 hover:bg-indigo-700'\r\n                  }`}\r\n                >\r\n                  English\r\n                </button>\r\n                <button\r\n                  onClick={() => switchLanguage('NE')}\r\n                  className={`flex-1 px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-300 ${\r\n                    currentLang === 'NE'\r\n                      ? 'bg-indigo-600 text-white'\r\n                      : 'bg-indigo-800/50 text-indigo-200 hover:bg-indigo-700'\r\n                  }`}\r\n                >\r\n                  नेपाली\r\n                </button>\r\n              </div>\r\n            </div>\r\n            <p className=\"text-indigo-200 text-xs text-center\">© 2024 Far Western University</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* CSS Animations */}\r\n      <style jsx>{`\r\n        @keyframes gradientMove {\r\n          0% { background-position: 0% 0%; }\r\n          100% { background-position: 200% 0%; }\r\n        }\r\n        @keyframes fadeSlideIn {\r\n          from { opacity: 0; transform: translateX(-10px); }\r\n          to { opacity: 1; transform: translateX(0); }\r\n        }\r\n        @keyframes float {\r\n          0%, 100% { transform: translateY(0) scale(1); }\r\n          50% { transform: translateY(-10px) scale(1.2); }\r\n        }\r\n        @keyframes fadeIn {\r\n          from { opacity: 0; }\r\n          to { opacity: 1; }\r\n        }\r\n        @keyframes fadeInFast {\r\n          from { opacity: 0; transform: translateY(-5px); }\r\n          to { opacity: 1; transform: translateY(0); }\r\n        }\r\n        \r\n        /* Custom scrollbar */\r\n        .custom-scrollbar::-webkit-scrollbar {\r\n          width: 6px;\r\n        }\r\n        \r\n        .custom-scrollbar::-webkit-scrollbar-track {\r\n          background: rgba(75, 85, 150, 0.2);\r\n          border-radius: 10px;\r\n        }\r\n        \r\n        .custom-scrollbar::-webkit-scrollbar-thumb {\r\n          background: rgba(99, 102, 241, 0.6);\r\n          border-radius: 10px;\r\n        }\r\n        \r\n        .custom-scrollbar::-webkit-scrollbar-thumb:hover {\r\n          background: rgba(99, 102, 241, 0.8);\r\n        }\r\n        \r\n        /* For Firefox */\r\n        .custom-scrollbar {\r\n          scrollbar-width: thin;\r\n          scrollbar-color: rgba(99, 102, 241, 0.6) rgba(75, 85, 150, 0.2);\r\n        }\r\n        \r\n        /* Desktop horizontal scrollbar */\r\n        .scrollbar-thin::-webkit-scrollbar {\r\n          height: 4px;\r\n        }\r\n        \r\n        .scrollbar-thin::-webkit-scrollbar-track {\r\n          background: transparent;\r\n        }\r\n        \r\n        .scrollbar-thin::-webkit-scrollbar-thumb {\r\n          background: rgba(99, 102, 241, 0.6);\r\n          border-radius: 10px;\r\n        }\r\n        \r\n        .scrollbar-thin::-webkit-scrollbar-thumb:hover {\r\n          background: rgba(99, 102, 241, 0.8);\r\n        }\r\n      `}</style>\r\n    </header>\r\n  );\r\n};\r\n\r\nexport default Header;"], "names": [], "mappings": ";;;;;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAeA;;;AAnBA;;;;;;;AAqBA,MAAM,YAOA;IACJ;QACE,MAAM;QACN,MAAM;QACN,oBAAM,6LAAC,sMAAA,CAAA,OAAI;YAAC,WAAU;;;;;;QACtB,aAAa;IACf;IACA;QACE,MAAM;QACN,MAAM;QACN,oBAAM,6LAAC,uMAAA,CAAA,QAAK;YAAC,WAAU;;;;;;QACvB,aAAa;IACf;IACA;QACE,MAAM;QACN,MAAM;QACN,oBAAM,6LAAC,2NAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;QAC/B,eAAe;QACf,aAAa;IACf;IACA;QACE,MAAM;QACN,MAAM;QACN,oBAAM,6LAAC,qMAAA,CAAA,OAAI;YAAC,WAAU;;;;;;QACtB,aAAa;IACf;IACA;QACE,MAAM;QACN,MAAM;QACN,oBAAM,6LAAC,yMAAA,CAAA,SAAM;YAAC,WAAU;;;;;;QACxB,aAAa;IACf;IACA;QACE,MAAM;QACN,MAAM;QACN,oBAAM,6LAAC,+MAAA,CAAA,YAAS;YAAC,WAAU;;;;;;QAC3B,aAAa;IACf;IACA;QACE,MAAM;QACN,MAAM;QACN,oBAAM,6LAAC,+MAAA,CAAA,YAAS;YAAC,WAAU;;;;;;QAC3B,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM;QACN,MAAM;QACN,oBAAM,6LAAC,+MAAA,CAAA,YAAS;YAAC,WAAU;;;;;;QAC3B,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM;QACN,MAAM;QACN,oBAAM,6LAAC,uMAAA,CAAA,QAAK;YAAC,WAAU;;;;;;QACvB,aAAa;IACf;IACA;QACO,MAAK;QACL,MAAK;QACL,oBAAM,6LAAC,+MAAA,CAAA,YAAS;YAAC,WAAU;;;;;;QAC3B,aAAa;IACpB;IAEA;QACE,MAAM;QACN,MAAM;QACN,oBAAM,6LAAC,+MAAA,CAAA,YAAS;YAAC,WAAU;;;;;;QAC3B,aAAa;IACf;IAEA;QACE,MAAM;QACN,MAAM;QACN,oBAAM,6LAAC,qMAAA,CAAA,OAAI;YAAC,WAAU;;;;;;QACtB,aAAa;IACf;CACD;AAED,MAAM,SAAS;;IACb,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAC9D,MAAM,OAAK,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IACrB,uBAAuB;IACvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,MAAM;iDAAe;oBACnB,MAAM,SAAS,OAAO,OAAO;oBAC7B,IAAI,SAAS,IAAI;wBACf,YAAY;oBACd,OAAO;wBACL,YAAY;oBACd;gBACF;;YAEA,6CAA6C;YAC7C,wCAAmC;gBACjC,cAAc,OAAO,QAAQ,CAAC,QAAQ;YACxC;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAClC;oCAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;2BAAG,EAAE;IAEL,0CAA0C;IAC1C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,MAAM;uDAAqB,CAAC;oBAC1B,MAAM,SAAS,MAAM,MAAM;oBAC3B,IAAI,oBAAoB,CAAC,OAAO,OAAO,CAAC,mBAAmB,CAAC,OAAO,OAAO,CAAC,wBAAwB;wBACjG,oBAAoB;oBACtB;gBACF;;YAEA,SAAS,gBAAgB,CAAC,aAAa;YACvC;oCAAO,IAAM,SAAS,mBAAmB,CAAC,aAAa;;QACzD;2BAAG;QAAC;KAAiB;IAErB,MAAM,mBAAmB,IAAM,oBAAoB,CAAC;IAEpD,MAAM,qBAAqB,IAAM,sBAAsB,CAAC;IAExD,MAAM,iBAAiB,CAAC;QACtB,eAAe;QACf,sBAAsB;QACtB,2CAA2C;QAC3C,QAAQ,GAAG,CAAC,CAAC,qBAAqB,EAAE,MAAM;IAC5C;IAEA,qBACE,6LAAC;kDAAiB;;0BAEhB,6LAAC;gBAAuF,OAAO;oBAC7F,gBAAgB;oBAChB,WAAW;gBACb;0DAHe;;;;;;0BAMf,6LAAC;0DAAe,CAAC,qCAAqC,EACpD,WACI,gDACA,gDACJ;;kCAEA,6LAAC;kEAAc;;0CACb,6LAAC;0EAAc;;;;;;0CACf,6LAAC;0EAAc;;;;;;0CACf,6LAAC;0EAAc;;;;;;0CAGf,6LAAC;0EAAc;0CACZ;uCAAI,MAAM;iCAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC;wCAGC,OAAO;4CACL,KAAK,GAAG,KAAK,KAAK,MAAM,KAAK,GAAG,CAAC,CAAC;4CAClC,MAAM,GAAG,KAAK,KAAK,MAAM,KAAK,GAAG,CAAC,CAAC;4CACnC,WAAW,CAAC,MAAM,EAAE,IAAI,KAAK,MAAM,KAAK,EAAE,uBAAuB,EAAE,KAAK,MAAM,KAAK,EAAE,CAAC,CAAC;wCACzF;kFALU;uCADL;;;;;;;;;;;;;;;;kCAYb,6LAAC;kEAAc;kCACb,cAAA,6LAAC;sEAAc;;8CAEb,6LAAC;8EAAc;8CACb,cAAA,6LAAC,qJAAA,CAAA,UAAI;;;;;;;;;;8CAIP,6LAAC;8EAAc;;sDACb,6LAAC;sFAAc;sDACZ,UAAU,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;gDAC1B,MAAM,WAAW,SAAS,KAAK,IAAI;gDACnC,qBACE,6LAAC,+JAAA,CAAA,UAAI;oDAEH,MAAM,KAAK,IAAI;oDAEf,WAAW,CAAC,0HAA0H,EACpI,WACI,6GACE,kCACN;oDAEF,cAAc,IAAM,eAAe,KAAK,IAAI;oDAC5C,cAAc,IAAM,eAAe;;sEAInC,6LAAC;sGAAe;sEACb,KAAK,IAAI;;;;;;wDAIX,gBAAgB,KAAK,IAAI,IAAI,KAAK,WAAW,kBAC5C,6LAAC;sGAAc;;gEACZ,KAAK,WAAW;8EACjB,6LAAC;8GAAc;;;;;;;;;;;;sEAKnB,6LAAC;sGAAe;;;;;;;mDA3BX,KAAK,IAAI;;;;;4CA+BpB;;;;;;sDAMF,6LAAC;sFAAc;;8DACb,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;8DAEV,cAAA,6LAAC;kGAAe;;4DAA6M;0EAE3N,6LAAC;gEAA6B,MAAK;gEAAO,QAAO;gEAAe,SAAQ;gEAAY,OAAM;0GAA3E;0EACb,cAAA,6LAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAY;oEAAI,GAAE;;;;;;;;;;;;0EAEvE,6LAAC;0GAAe;;;;;;;;;;;;;;;;;8DAKpB,6LAAC;8FAAc;;sEACb,6LAAC;4DACC,SAAS;sGACC;;8EAEV,6LAAC,uMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;8EACjB,6LAAC;;8EAAM;;;;;;8EACP,6LAAC,uNAAA,CAAA,cAAW;oEAAC,WAAW,CAAC,uCAAuC,EAAE,qBAAqB,eAAe,IAAI;;;;;;;;;;;;wDAG3G,oCACC,6LAAC;sGAAc;;8EACb,6LAAC;8GAAc;;;;;;8EACf,6LAAC;oEACC,SAAS,IAAM,eAAe;8GACpB;;sFAEV,6LAAC;sHAAe;sFAA2F;;;;;;wEAAS;;;;;;;8EAGtH,6LAAC;oEACC,SAAS,IAAM,eAAe;8GACpB;;sFAEV,6LAAC;sHAAe;sFAA2F;;;;;;wEAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAWhI,6LAAC;8EAAc;;sDACb,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDAEV,cAAA,6LAAC;0FAAe;;oDAA2M;kEAEzN,6LAAC;wDAA6B,MAAK;wDAAO,QAAO;wDAAe,SAAQ;wDAAY,OAAM;kGAA3E;kEACb,cAAA,6LAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAY;4DAAI,GAAE;;;;;;;;;;;;;;;;;;;;;;;sDAK3E,6LAAC;4CACC,SAAS,IAAM,eAAe,gBAAgB,OAAO,OAAO;4CAE5D,cAAW;sFADD;sDAGV,cAAA,6LAAC,uMAAA,CAAA,QAAK;gDAAC,MAAM;;;;;;;;;;;sDAGf,6LAAC;4CACC,IAAG;4CACH,SAAS;4CAET,cAAW;sFADD;sDAGT,iCAAmB,6LAAC,+LAAA,CAAA,IAAC;gDAAC,MAAM;;;;;qEAAS,6LAAC,qMAAA,CAAA,OAAI;gDAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ5D,6LAAC;0DAAc;0BACb,cAAA,6LAAC;8DAAc;8BACb,cAAA,6LAAC;kEAAc;kCACb,cAAA,6LAAC;sEAAc;sCACZ,UAAU,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC;gCACvB,MAAM,WAAW,eAAe,KAAK,IAAI;gCACzC,qBACE,6LAAC,+JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAW,CAAC,+DAA+D,EACzE,WACI,oBACA,KAAK,KAAK,GACR,wCACA,uCACN;;sDAEF,6LAAC;sFAAe;;gDACb,KAAK,IAAI;gDACT,KAAK,KAAK,kBACT,6LAAC;8FAAe;8DAA+E;;;;;;;;;;;;sDAOnG,6LAAC;sFAAe;;;;;;;mCApBX,KAAK,IAAI;;;;;4BAuBpB;;;;;;;;;;;;;;;;;;;;;0BAOR,6LAAC;gBACC,IAAG;0DACQ,CAAC,yDAAyD,EACnE,mBAAmB,wBAAwB,uBAC3C;;kCAGF,6LAAC;wBAIC,SAAS,IAAM,oBAAoB;kEAHxB,CAAC,mFAAmF,EAC7F,mBAAmB,gBAAgB,aACnC;;;;;;kCAKJ,6LAAC;kEACY,CAAC,8JAA8J,EACxK,mBAAmB,kBAAkB,qBACrC;;0CAGF,6LAAC;0EAAc;;kDACb,6LAAC;;;0DACC,6LAAC;0FAAa;0DAA+B;;;;;;0DAC7C,6LAAC;0FAAY;0DAA0B;;;;;;;;;;;;kDAEzC,6LAAC;wCACC,SAAS,IAAM,oBAAoB;kFACzB;kDAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;4CAAC,MAAM;;;;;;;;;;;;;;;;;0CAKb,6LAAC;0EAAc;0CACb,cAAA,6LAAC;8EAAc;8CACb,cAAA,6LAAC;kFAAc;kDACZ,UAAU,GAAG,CAAC,CAAC,MAAM;4CACpB,MAAM,WAAW,eAAe,KAAK,IAAI;4CACzC,qBACE,6LAAC,+JAAA,CAAA,UAAI;gDAEH,MAAM,KAAK,IAAI;gDACf,SAAS,IAAM,oBAAoB;gDACnC,WAAW,CAAC,yFAAyF,EACnG,WACI,gCACA,KAAK,aAAa,GAChB,wDACA,2DACN;gDACF,OAAO;oDACL,iBAAiB,GAAG,MAAM,QAAQ,GAAG,EAAE,CAAC;oDACxC,WAAW,mBAAmB,CAAC,mCAAmC,EAAE,MAAM,QAAQ,GAAG,EAAE,CAAC,GAAG;gDAC7F;;kEAEA,6LAAC;kGAAgB,CAAC,iCAAiC,EACjD,KAAK,aAAa,GACd,mBACA,KAAK,KAAK,GACR,kCACA,oBACN;kEACC,KAAK,IAAI;;;;;;kEAEZ,6LAAC;kGAAc;;0EACb,6LAAC;;;kFACC,6LAAC;;kFAAK,KAAK,IAAI;;;;;;kFACf,6LAAC;kHAAc;kFAA2B,KAAK,WAAW;;;;;;;;;;;;4DAE3D,CAAC,KAAK,aAAa,IAAI,KAAK,KAAK,mBAChC,6LAAC;0GAAgB,CAAC,yCAAyC,EAAE,KAAK,KAAK,GAAG,iBAAiB,cAAc,wCAAwC,CAAC;0EAC/I,KAAK,KAAK,GAAG,QAAQ;;;;;;;;;;;;;+CA/BvB,KAAK,IAAI;;;;;wCAqCpB;;;;;;;;;;;;;;;;0CAMN,6LAAC;0EAAc;;kDACb,6LAAC;kFAAc;;0DACb,6LAAC;0FAAY;0DAAwD;;;;;;0DACrE,6LAAC;0FAAc;;kEACb,6LAAC;wDACC,SAAS,IAAM,eAAe;kGACnB,CAAC,+EAA+E,EACzF,gBAAgB,OACZ,6BACA,wDACJ;kEACH;;;;;;kEAGD,6LAAC;wDACC,SAAS,IAAM,eAAe;kGACnB,CAAC,+EAA+E,EACzF,gBAAgB,OACZ,6BACA,wDACJ;kEACH;;;;;;;;;;;;;;;;;;kDAKL,6LAAC;kFAAY;kDAAsC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyE/D;GA9bM;;QAOO,qIAAA,CAAA,cAAW;;;KAPlB;uCAgcS", "debugId": null}}, {"offset": {"line": 1187, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/fwu_incubation%28react%2Blaravel%29/incubation/frontend/src/app/fwu/components/layout/Footer.tsx"], "sourcesContent": ["\"use client\"\r\nimport Link from 'next/link';\r\nimport Logo from '../common/Logo';\r\nimport { Mail, Phone, MapPin, ChevronUp, GraduationCap, Book, Building2, Users } from 'lucide-react';\r\nimport { useEffect, useState } from 'react';\r\n\r\nconst quickLinks = [\r\n  { name: 'Apply Now', href: '/apply', icon: <GraduationCap className=\"text-yellow-400\" /> },\r\n  { name: 'Academic Programs', href: '/programs', icon: <Book className=\"text-yellow-400\" /> },\r\n  { name: 'Campus Life', href: '/campus', icon: <Building2 className=\"text-yellow-400\" /> },\r\n  { name: 'Our Faculty', href: '/faculty', icon: <Users className=\"text-yellow-400\" /> },\r\n  { name: 'Research', href: '/research', icon: <Book className=\"text-yellow-400\" /> },\r\n];\r\n\r\nconst socialLinks = [\r\n  {\r\n    icon: (\r\n      <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\r\n        <path d=\"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z\"/>\r\n      </svg>\r\n    ),\r\n    href: 'https://facebook.com/fwu',\r\n    label: 'Facebook',\r\n    color: 'bg-blue-600 hover:bg-blue-700'\r\n  },\r\n  {\r\n    icon: (\r\n      <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\r\n        <path d=\"M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z\"/>\r\n      </svg>\r\n    ),\r\n    href: 'https://twitter.com/fwu',\r\n    label: 'Twitter',\r\n    color: 'bg-sky-500 hover:bg-sky-600'\r\n  },\r\n  {\r\n    icon: (\r\n      <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\r\n        <path d=\"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z\"/>\r\n      </svg>\r\n    ),\r\n    href: 'https://linkedin.com/company/fwu',\r\n    label: 'LinkedIn',\r\n    color: 'bg-blue-700 hover:bg-blue-800'\r\n  },\r\n  {\r\n    icon: (\r\n      <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\r\n        <path d=\"M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.024-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.099.12.112.225.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.75-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24c6.624 0 11.99-5.367 11.99-12C24.007 5.367 18.641.001 12.017.001z\"/>\r\n      </svg>\r\n    ),\r\n    href: 'https://instagram.com/fwu',\r\n    label: 'Instagram',\r\n    color: 'bg-gradient-to-br from-purple-600 via-pink-500 to-orange-400 hover:from-purple-700 hover:via-pink-600 hover:to-orange-500'\r\n  },\r\n];\r\n\r\nconst Footer = () => {\r\n  const [isVisible, setIsVisible] = useState(false);\r\n  const [showScrollTop, setShowScrollTop] = useState(false);\r\n\r\n  useEffect(() => {\r\n    // Fade in animation on load\r\n    setIsVisible(true);\r\n\r\n    // Show/hide scroll to top button\r\n    const handleScroll = () => {\r\n      if (window.scrollY > 300) {\r\n        setShowScrollTop(true);\r\n      } else {\r\n        setShowScrollTop(false);\r\n      }\r\n    };\r\n\r\n    window.addEventListener('scroll', handleScroll);\r\n    return () => window.removeEventListener('scroll', handleScroll);\r\n  }, []);\r\n\r\n  const scrollToTop = () => {\r\n    window.scrollTo({\r\n      top: 0,\r\n      behavior: 'smooth'\r\n    });\r\n  };\r\n\r\n  return (\r\n    <footer className=\"relative overflow-hidden\">\r\n      {/* Wave Top Divider */}\r\n      <div className=\"absolute top-0 left-0 right-0 w-full overflow-hidden leading-none\">\r\n        <svg className=\"relative block w-full h-24\" data-name=\"Layer 1\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 1200 120\" preserveAspectRatio=\"none\">\r\n          <path d=\"M321.39,56.44c58-10.79,114.16-30.13,172-41.86,82.39-16.72,168.19-17.73,250.45-.39C823.78,31,906.67,72,985.66,92.83c70.05,18.48,146.53,26.09,214.34,3V0H0V27.35A600.21,600.21,0,0,0,321.39,56.44Z\"\r\n                className=\"fill-white\"></path>\r\n        </svg>\r\n      </div>\r\n\r\n      {/* Main Footer Content */}\r\n      <div className={`bg-gradient-to-b from-indigo-900 via-indigo-950 to-blue-950 pt-24 pb-8 relative transition-opacity duration-1000 ${isVisible ? 'opacity-100' : 'opacity-0'}`}>\r\n        {/* Animated Decorative Elements */}\r\n        <div className=\"absolute top-0 right-0 w-[600px] h-[600px] bg-indigo-500/10 rounded-full\"\r\n             style={{filter: 'blur(100px)', animation: 'pulse 8s infinite alternate'}}></div>\r\n        <div className=\"absolute bottom-0 left-0 w-[500px] h-[500px] bg-blue-500/10 rounded-full\"\r\n             style={{filter: 'blur(100px)', animation: 'float 10s infinite alternate'}}></div>\r\n        <div className=\"absolute top-1/3 left-1/4 w-[300px] h-[300px] bg-teal-500/5 rounded-full\"\r\n             style={{filter: 'blur(80px)', animation: 'pulse-slow 15s infinite alternate'}}></div>\r\n\r\n        {/* Animated Particles */}\r\n        <div className=\"absolute inset-0 overflow-hidden pointer-events-none\">\r\n          {[...Array(15)].map((_, i) => (\r\n            <div\r\n              key={i}\r\n              className=\"absolute w-1.5 h-1.5 bg-white/30 rounded-full\"\r\n              style={{\r\n                top: `${Math.random() * 100}%`,\r\n                left: `${Math.random() * 100}%`,\r\n                animation: `moveUpSlow ${Math.random() * 15 + 15}s linear infinite`,\r\n                animationDelay: `${Math.random() * 5}s`,\r\n              }}\r\n            ></div>\r\n          ))}\r\n        </div>\r\n\r\n        {/* Subtle grid pattern overlay */}\r\n        <div className=\"absolute inset-0 bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxnIGZpbGw9IiMzMzMiIGZpbGwtb3BhY2l0eT0iMC4wMiI+PHBhdGggZD0iTTM2IDM0aDR2MWgtNHYtMXptMC0yaDF2NGgtMXYtNHptMi0yaDF2MWgtMXYtMXptLTIgMmgxdjFoLTF2LTF6bS0yLTJoMXYxaC0xdi0xem0yLTJoMXYxaC0xdi0xem0tMiAyaDF2MWgtMXYtMXptLTItMmgxdjFoLTF2LTF6Ii8+PC9nPjwvZz48L3N2Zz4=')] opacity-10\"></div>\r\n\r\n        {/* Star field effect */}\r\n        <div className=\"absolute inset-0 overflow-hidden pointer-events-none\">\r\n          {[...Array(50)].map((_, i) => (\r\n            <div\r\n              key={`star-${i}`}\r\n              className=\"absolute w-0.5 h-0.5 bg-white rounded-full\"\r\n              style={{\r\n                top: `${Math.random() * 100}%`,\r\n                left: `${Math.random() * 100}%`,\r\n                opacity: Math.random() * 0.7,\r\n                animation: `twinkle ${Math.random() * 5 + 3}s ease-in-out infinite alternate`,\r\n                animationDelay: `${Math.random() * 5}s`,\r\n              }}\r\n            ></div>\r\n          ))}\r\n        </div>\r\n\r\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 relative z-10\">\r\n          {/* Main Footer Grid */}\r\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-12 mb-16\">\r\n            {/* Column 1: Logo & About */}\r\n            <div className=\"space-y-6 transform transition-all duration-700\" style={{animationDelay: '0ms'}}>\r\n              <div className=\"transform transition-transform duration-300 hover:scale-105\">\r\n                <Logo variant=\"dark\" />\r\n              </div>\r\n              <p className=\"text-gray-300 leading-relaxed\">\r\n                Empowering the next generation of innovators and entrepreneurs at Far Western University, Nepal.\r\n              </p>\r\n\r\n              {/* Newsletter Signup */}\r\n              <div className=\"pt-4\">\r\n                <h4 className=\"text-white text-sm font-semibold mb-3\">Subscribe to our newsletter</h4>\r\n                <div className=\"flex\">\r\n                  <input\r\n                    type=\"email\"\r\n                    placeholder=\"Your email\"\r\n                    className=\"bg-blue-900/50 border border-blue-700 rounded-l-md px-4 py-2 text-sm text-white placeholder-blue-300/50 focus:outline-none focus:ring-2 focus:ring-teal-400 w-full\"\r\n                  />\r\n                  <button className=\"bg-gradient-to-r from-teal-400 to-teal-500 hover:from-teal-500 hover:to-teal-600 text-blue-900 font-bold px-4 py-2 rounded-r-md transition-all duration-300\">\r\n                    Subscribe\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Column 2: Quick Links */}\r\n            <div className=\"transform transition-all duration-700\" style={{animationDelay: '200ms'}}>\r\n              <h3 className=\"text-xl font-bold text-white mb-6 relative\">\r\n                Quick Links\r\n                <span className=\"absolute bottom-0 left-0 w-12 h-1 bg-gradient-to-r from-teal-400 to-blue-400 rounded-full\"></span>\r\n              </h3>\r\n              <ul className=\"space-y-4\">\r\n                {quickLinks.map((link) => (\r\n                  <li key={link.name} className=\"transform hover:-translate-x-2 transition-transform duration-300\">\r\n                    <Link\r\n                      href={link.href}\r\n                      className=\"text-gray-300 hover:text-white transition-colors flex items-center group\"\r\n                    >\r\n                      <span className=\"w-8 h-8 bg-blue-800/50 rounded-md flex items-center justify-center mr-3 group-hover:bg-blue-700/70 transition-colors\">\r\n                        {link.icon}\r\n                      </span>\r\n                      <span>{link.name}</span>\r\n                    </Link>\r\n                  </li>\r\n                ))}\r\n              </ul>\r\n            </div>\r\n\r\n            {/* Column 3: Contact Info */}\r\n            <div className=\"transform transition-all duration-700\" style={{animationDelay: '400ms'}}>\r\n              <h3 className=\"text-xl font-bold text-white mb-6 relative\">\r\n                Contact Us\r\n                <span className=\"absolute bottom-0 left-0 w-12 h-1 bg-gradient-to-r from-teal-400 to-blue-400 rounded-full\"></span>\r\n              </h3>\r\n              <ul className=\"space-y-4\">\r\n                <li className=\"flex items-start group\">\r\n                  <div className=\"w-8 h-8 bg-blue-800/50 rounded-md flex items-center justify-center mr-3 text-teal-400 group-hover:bg-blue-700/70 transition-colors\">\r\n                    <MapPin />\r\n                  </div>\r\n                  <span className=\"text-gray-300 group-hover:text-white transition-colors\">\r\n                    Bheemdatta Municipality-18, Mahendranagar, Kanchanpur, Nepal\r\n                  </span>\r\n                </li>\r\n                <li className=\"flex items-center group\">\r\n                  <div className=\"w-8 h-8 bg-blue-800/50 rounded-md flex items-center justify-center mr-3 text-teal-400 group-hover:bg-blue-700/70 transition-colors\">\r\n                    <Phone />\r\n                  </div>\r\n                  <a href=\"tel:+97799000000\" className=\"text-gray-300 group-hover:text-white transition-colors\">\r\n                    +977 99-000000\r\n                  </a>\r\n                </li>\r\n                <li className=\"flex items-center group\">\r\n                  <div className=\"w-8 h-8 bg-blue-800/50 rounded-md flex items-center justify-center mr-3 text-teal-400 group-hover:bg-blue-700/70 transition-colors\">\r\n                    <Mail />\r\n                  </div>\r\n                  <a href=\"mailto:<EMAIL>\" className=\"text-gray-300 group-hover:text-white transition-colors\">\r\n                    <EMAIL>\r\n                  </a>\r\n                </li>\r\n              </ul>\r\n\r\n              {/* Google Maps */}\r\n              <div className=\"mt-6 rounded-lg overflow-hidden shadow-lg border-2 border-blue-700/50 hover:border-teal-400/30 transition-all duration-500 group\">\r\n                <div className=\"bg-gradient-to-r from-blue-800/80 to-indigo-800/80 text-xs text-center py-2 text-gray-300 font-medium flex items-center justify-center\">\r\n                  <MapPin className=\"text-teal-400 mr-2\" />\r\n                  <span>Campus Location</span>\r\n                </div>\r\n                <div className=\"relative h-48 w-full overflow-hidden\">\r\n                  {/* Map iframe */}\r\n                  <iframe\r\n                    src=\"https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3499.6724521063635!2d80.18915937547953!3d28.699999175628036!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x39a1a0aaaaaaaaab%3A0x0!2zMjjCsDQyJzAwLjAiTiA4MMKwMTEnMjguMCJF!5e0!3m2!1sen!2sus!4v1718193600000!5m2!1sen!2sus\"\r\n                    width=\"100%\"\r\n                    height=\"100%\"\r\n                    style={{ border: 0 }}\r\n                    allowFullScreen={false}\r\n                    loading=\"lazy\"\r\n                    referrerPolicy=\"no-referrer-when-downgrade\"\r\n                    className=\"grayscale group-hover:grayscale-0 transition-all duration-500\"\r\n                  ></iframe>\r\n\r\n                  {/* Overlay with pulse effect */}\r\n                  <div className=\"absolute inset-0 bg-gradient-to-t from-indigo-900/80 to-transparent opacity-60 group-hover:opacity-0 transition-opacity duration-500 pointer-events-none\"></div>\r\n\r\n                  {/* Pulsing location marker */}\r\n                  <div className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-10 pointer-events-none\">\r\n                    <div className=\"relative\">\r\n                      <div className=\"w-4 h-4 bg-teal-400 rounded-full\"></div>\r\n                      <div className=\"absolute inset-0 w-4 h-4 bg-teal-400 rounded-full animate-ping opacity-75\"></div>\r\n                      <div className=\"absolute -inset-2 w-8 h-8 bg-teal-400/30 rounded-full animate-pulse\"></div>\r\n                    </div>\r\n                  </div>\r\n\r\n                  {/* Get directions button */}\r\n                  <div className=\"absolute bottom-3 right-3 z-10\">\r\n                    <a\r\n                      href=\"https://www.google.com/maps/dir//28.7000000,80.1911111/@28.6999992,80.1891594,17z\"\r\n                      target=\"_blank\"\r\n                      rel=\"noopener noreferrer\"\r\n                      className=\"bg-indigo-600/90 hover:bg-indigo-700 text-white text-xs px-3 py-1.5 rounded-md flex items-center transition-all duration-300 shadow-lg hover:shadow-indigo-500/30\"\r\n                    >\r\n                      <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-3.5 w-3.5 mr-1\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7\" />\r\n                      </svg>\r\n                      Directions\r\n                    </a>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Column 4: Social Media */}\r\n            <div className=\"transform transition-all duration-700\" style={{animationDelay: '600ms'}}>\r\n              <h3 className=\"text-xl font-bold text-white mb-6 relative\">\r\n                Connect With Us\r\n                <span className=\"absolute bottom-0 left-0 w-12 h-1 bg-gradient-to-r from-teal-400 to-blue-400 rounded-full\"></span>\r\n              </h3>\r\n\r\n              {/* Social Media Icons */}\r\n              <div className=\"grid grid-cols-2 gap-4 mb-6\">\r\n                {socialLinks.map((social) => (\r\n                  <a\r\n                    key={social.label}\r\n                    href={social.href}\r\n                    target=\"_blank\"\r\n                    rel=\"noopener noreferrer\"\r\n                    aria-label={social.label}\r\n                    className={`${social.color} rounded-lg p-3 flex items-center text-white transition-all duration-300 transform hover:-translate-y-1 hover:shadow-lg group`}\r\n                  >\r\n                    <div className=\"w-8 h-8 bg-white/10 rounded-md flex items-center justify-center mr-3 group-hover:bg-white/20 transition-colors\">\r\n                      {social.icon}\r\n                    </div>\r\n                    <span className=\"text-sm font-medium\">{social.label}</span>\r\n                  </a>\r\n                ))}\r\n              </div>\r\n\r\n              {/* Working Hours */}\r\n              <div className=\"bg-blue-800/30 rounded-lg p-4 border border-blue-700/30\">\r\n                <h4 className=\"text-white text-sm font-semibold mb-3 flex items-center\">\r\n                  <svg className=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\r\n                  </svg>\r\n                  Office Hours\r\n                </h4>\r\n                <ul className=\"text-sm text-gray-300 space-y-2\">\r\n                  <li className=\"flex justify-between\">\r\n                    <span>Monday - Friday:</span>\r\n                    <span className=\"text-teal-400\">9:00 AM - 5:00 PM</span>\r\n                  </li>\r\n                  <li className=\"flex justify-between\">\r\n                    <span>Saturday:</span>\r\n                    <span className=\"text-teal-400\">9:00 AM - 1:00 PM</span>\r\n                  </li>\r\n                  <li className=\"flex justify-between\">\r\n                    <span>Sunday:</span>\r\n                    <span className=\"text-yellow-400\">Closed</span>\r\n                  </li>\r\n                </ul>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Footer Bottom */}\r\n          <div className=\"relative\">\r\n            {/* Decorative divider */}\r\n            <div className=\"absolute top-0 left-1/2 transform -translate-x-1/2 w-full max-w-4xl\">\r\n              <div className=\"h-px bg-gradient-to-r from-transparent via-indigo-500/50 to-transparent w-full\"></div>\r\n              <div className=\"absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2\">\r\n                <div className=\"w-12 h-12 rounded-full bg-gradient-to-br from-indigo-600 to-blue-600 flex items-center justify-center shadow-lg shadow-indigo-500/20\">\r\n                  <div className=\"w-10 h-10 rounded-full bg-indigo-950 flex items-center justify-center\">\r\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 text-indigo-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 14l-7 7m0 0l-7-7m7 7V3\" />\r\n                    </svg>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"pt-12 mt-8\">\r\n              {/* Copyright & Credits */}\r\n              <div className=\"flex flex-col md:flex-row justify-between items-center\">\r\n                <div className=\"text-center md:text-left mb-4 md:mb-0\">\r\n                  <div className=\"flex items-center justify-center md:justify-start mb-2\">\r\n                    <div className=\"w-8 h-8 bg-indigo-800/50 rounded-full flex items-center justify-center mr-2\">\r\n                      <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-4 w-4 text-indigo-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 11c0 3.517-1.009 6.799-2.753 9.571m-3.44-2.04l.054-.09A13.916 13.916 0 008 11a4 4 0 118 0c0 1.017-.07 2.019-.203 3m-2.118 6.844A21.88 21.88 0 0015.171 17m3.839 1.132c.645-2.266.99-4.659.99-7.132A8 8 0 008 4.07M3 15.364c.64-1.319 1-2.8 1-4.364 0-1.457.39-2.823 1.07-4\" />\r\n                      </svg>\r\n                    </div>\r\n                    <span className=\"text-indigo-300 font-semibold\">Far Western University</span>\r\n                  </div>\r\n                  <p className=\"text-gray-400 text-sm\">\r\n                    © {new Date().getFullYear()} Far Western University, Nepal. All Rights Reserved.\r\n                  </p>\r\n                  <p className=\"text-gray-500 text-xs mt-1\">\r\n                    Established by the Act of Parliament, 2010\r\n                  </p>\r\n                </div>\r\n\r\n                <div className=\"flex flex-col items-center md:items-end\">\r\n                  <div className=\"flex items-center space-x-4 mb-4\">\r\n                    <Link href=\"/privacy\" className=\"text-gray-400 hover:text-indigo-300 text-sm transition-colors\">\r\n                      Privacy Policy\r\n                    </Link>\r\n                    <span className=\"text-gray-600\">•</span>\r\n                    <Link href=\"/terms\" className=\"text-gray-400 hover:text-indigo-300 text-sm transition-colors\">\r\n                      Terms of Use\r\n                    </Link>\r\n                    <span className=\"text-gray-600\">•</span>\r\n                    <Link href=\"/sitemap\" className=\"text-gray-400 hover:text-indigo-300 text-sm transition-colors\">\r\n                      Sitemap\r\n                    </Link>\r\n                  </div>\r\n\r\n                  <div className=\"flex items-center space-x-3\">\r\n                    <a href=\"https://www.facebook.com/fwu\" target=\"_blank\" rel=\"noopener noreferrer\" className=\"text-gray-400 hover:text-indigo-300 transition-colors\">\r\n                      <svg className=\"w-4 h-4\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path d=\"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z\"/>\r\n                      </svg>\r\n                    </a>\r\n                    <a href=\"https://www.twitter.com/fwu\" target=\"_blank\" rel=\"noopener noreferrer\" className=\"text-gray-400 hover:text-indigo-300 transition-colors\">\r\n                      <svg className=\"w-4 h-4\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path d=\"M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z\"/>\r\n                      </svg>\r\n                    </a>\r\n                    <a href=\"https://www.linkedin.com/company/fwu\" target=\"_blank\" rel=\"noopener noreferrer\" className=\"text-gray-400 hover:text-indigo-300 transition-colors\">\r\n                      <svg className=\"w-4 h-4\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path d=\"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z\"/>\r\n                      </svg>\r\n                    </a>\r\n                    <a href=\"https://www.instagram.com/fwu\" target=\"_blank\" rel=\"noopener noreferrer\" className=\"text-gray-400 hover:text-indigo-300 transition-colors\">\r\n                      <svg className=\"w-4 h-4\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path d=\"M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.024-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.099.12.112.225.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.75-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24c6.624 0 11.99-5.367 11.99-12C24.007 5.367 18.641.001 12.017.001z\"/>\r\n                      </svg>\r\n                    </a>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Credits */}\r\n              <div className=\"text-center mt-8 pt-4 border-t border-indigo-800/30\">\r\n                <p className=\"text-gray-500 text-xs flex items-center justify-center\">\r\n                  Designed with <span className=\"text-red-500 mx-1 animate-pulse\">♥</span> by Innovators for Innovators\r\n                </p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Scroll to Top Button */}\r\n        <button\r\n          onClick={scrollToTop}\r\n          className={`fixed bottom-6 right-6 w-14 h-14 rounded-full bg-gradient-to-r from-indigo-600 to-blue-600 text-white flex items-center justify-center shadow-lg transform transition-all duration-500 hover:scale-110 hover:shadow-indigo-500/30 ${\r\n            showScrollTop ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10 pointer-events-none'\r\n          }`}\r\n          aria-label=\"Scroll to top\"\r\n        >\r\n          <div className=\"absolute inset-0 rounded-full bg-indigo-600 animate-ping opacity-20\"></div>\r\n          <div className=\"relative z-10 flex items-center justify-center\">\r\n            <ChevronUp className=\"text-white\" />\r\n          </div>\r\n        </button>\r\n      </div>\r\n\r\n      {/* CSS Animations */}\r\n      <style jsx>{`\r\n        @keyframes pulse {\r\n          0%, 100% { opacity: 0.3; }\r\n          50% { opacity: 0.6; }\r\n        }\r\n        @keyframes float {\r\n          0%, 100% { transform: translateY(0); }\r\n          50% { transform: translateY(-20px); }\r\n        }\r\n        @keyframes moveUpSlow {\r\n          0% { transform: translateY(0); opacity: 0; }\r\n          10% { opacity: 1; }\r\n          90% { opacity: 0.5; }\r\n          100% { transform: translateY(-1000px); opacity: 0; }\r\n        }\r\n        @keyframes twinkle {\r\n          0%, 100% { opacity: 0.1; transform: scale(1); }\r\n          50% { opacity: 0.7; transform: scale(1.2); }\r\n        }\r\n        @keyframes pulse-slow {\r\n          0%, 100% { opacity: 0.2; transform: scale(1); }\r\n          50% { opacity: 0.5; transform: scale(1.1); }\r\n        }\r\n      `}</style>\r\n    </footer>\r\n  );\r\n};\r\n\r\nexport default Footer;"], "names": [], "mappings": ";;;;;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;;;AAJA;;;;;;AAMA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAa,MAAM;QAAU,oBAAM,6LAAC,2NAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;IAAqB;IACzF;QAAE,MAAM;QAAqB,MAAM;QAAa,oBAAM,6LAAC,qMAAA,CAAA,OAAI;YAAC,WAAU;;;;;;IAAqB;IAC3F;QAAE,MAAM;QAAe,MAAM;QAAW,oBAAM,6LAAC,mNAAA,CAAA,YAAS;YAAC,WAAU;;;;;;IAAqB;IACxF;QAAE,MAAM;QAAe,MAAM;QAAY,oBAAM,6LAAC,uMAAA,CAAA,QAAK;YAAC,WAAU;;;;;;IAAqB;IACrF;QAAE,MAAM;QAAY,MAAM;QAAa,oBAAM,6LAAC,qMAAA,CAAA,OAAI;YAAC,WAAU;;;;;;IAAqB;CACnF;AAED,MAAM,cAAc;IAClB;QACE,oBACE,6LAAC;YAAI,WAAU;YAAU,MAAK;YAAe,SAAQ;sBACnD,cAAA,6LAAC;gBAAK,GAAE;;;;;;;;;;;QAGZ,MAAM;QACN,OAAO;QACP,OAAO;IACT;IACA;QACE,oBACE,6LAAC;YAAI,WAAU;YAAU,MAAK;YAAe,SAAQ;sBACnD,cAAA,6LAAC;gBAAK,GAAE;;;;;;;;;;;QAGZ,MAAM;QACN,OAAO;QACP,OAAO;IACT;IACA;QACE,oBACE,6LAAC;YAAI,WAAU;YAAU,MAAK;YAAe,SAAQ;sBACnD,cAAA,6LAAC;gBAAK,GAAE;;;;;;;;;;;QAGZ,MAAM;QACN,OAAO;QACP,OAAO;IACT;IACA;QACE,oBACE,6LAAC;YAAI,WAAU;YAAU,MAAK;YAAe,SAAQ;sBACnD,cAAA,6LAAC;gBAAK,GAAE;;;;;;;;;;;QAGZ,MAAM;QACN,OAAO;QACP,OAAO;IACT;CACD;AAED,MAAM,SAAS;;IACb,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,4BAA4B;YAC5B,aAAa;YAEb,iCAAiC;YACjC,MAAM;iDAAe;oBACnB,IAAI,OAAO,OAAO,GAAG,KAAK;wBACxB,iBAAiB;oBACnB,OAAO;wBACL,iBAAiB;oBACnB;gBACF;;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAClC;oCAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;2BAAG,EAAE;IAEL,MAAM,cAAc;QAClB,OAAO,QAAQ,CAAC;YACd,KAAK;YACL,UAAU;QACZ;IACF;IAEA,qBACE,6LAAC;kDAAiB;;0BAEhB,6LAAC;0DAAc;0BACb,cAAA,6LAAC;oBAA2C,aAAU;oBAAU,OAAM;oBAA6B,SAAQ;oBAAe,qBAAoB;8DAA/H;8BACb,cAAA,6LAAC;wBAAK,GAAE;kEACQ;;;;;;;;;;;;;;;;0BAKpB,6LAAC;0DAAe,CAAC,iHAAiH,EAAE,YAAY,gBAAgB,aAAa;;kCAE3K,6LAAC;wBACI,OAAO;4BAAC,QAAQ;4BAAe,WAAW;wBAA6B;kEAD7D;;;;;;kCAEf,6LAAC;wBACI,OAAO;4BAAC,QAAQ;4BAAe,WAAW;wBAA8B;kEAD9D;;;;;;kCAEf,6LAAC;wBACI,OAAO;4BAAC,QAAQ;4BAAc,WAAW;wBAAmC;kEADlE;;;;;;kCAIf,6LAAC;kEAAc;kCACZ;+BAAI,MAAM;yBAAI,CAAC,GAAG,CAAC,CAAC,GAAG,kBACtB,6LAAC;gCAGC,OAAO;oCACL,KAAK,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;oCAC9B,MAAM,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;oCAC/B,WAAW,CAAC,WAAW,EAAE,KAAK,MAAM,KAAK,KAAK,GAAG,iBAAiB,CAAC;oCACnE,gBAAgB,GAAG,KAAK,MAAM,KAAK,EAAE,CAAC,CAAC;gCACzC;0EANU;+BADL;;;;;;;;;;kCAaX,6LAAC;kEAAc;;;;;;kCAGf,6LAAC;kEAAc;kCACZ;+BAAI,MAAM;yBAAI,CAAC,GAAG,CAAC,CAAC,GAAG,kBACtB,6LAAC;gCAGC,OAAO;oCACL,KAAK,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;oCAC9B,MAAM,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;oCAC/B,SAAS,KAAK,MAAM,KAAK;oCACzB,WAAW,CAAC,QAAQ,EAAE,KAAK,MAAM,KAAK,IAAI,EAAE,gCAAgC,CAAC;oCAC7E,gBAAgB,GAAG,KAAK,MAAM,KAAK,EAAE,CAAC,CAAC;gCACzC;0EAPU;+BADL,CAAC,KAAK,EAAE,GAAG;;;;;;;;;;kCAatB,6LAAC;kEAAc;;0CAEb,6LAAC;0EAAc;;kDAEb,6LAAC;wCAAgE,OAAO;4CAAC,gBAAgB;wCAAK;kFAA/E;;0DACb,6LAAC;0FAAc;0DACb,cAAA,6LAAC,qJAAA,CAAA,UAAI;oDAAC,SAAQ;;;;;;;;;;;0DAEhB,6LAAC;0FAAY;0DAAgC;;;;;;0DAK7C,6LAAC;0FAAc;;kEACb,6LAAC;kGAAa;kEAAwC;;;;;;kEACtD,6LAAC;kGAAc;;0EACb,6LAAC;gEACC,MAAK;gEACL,aAAY;0GACF;;;;;;0EAEZ,6LAAC;0GAAiB;0EAA8J;;;;;;;;;;;;;;;;;;;;;;;;kDAQtL,6LAAC;wCAAsD,OAAO;4CAAC,gBAAgB;wCAAO;kFAAvE;;0DACb,6LAAC;0FAAa;;oDAA6C;kEAEzD,6LAAC;kGAAe;;;;;;;;;;;;0DAElB,6LAAC;0FAAa;0DACX,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC;kGAA6B;kEAC5B,cAAA,6LAAC,+JAAA,CAAA,UAAI;4DACH,MAAM,KAAK,IAAI;4DACf,WAAU;;8EAEV,6LAAC;8GAAe;8EACb,KAAK,IAAI;;;;;;8EAEZ,6LAAC;;8EAAM,KAAK,IAAI;;;;;;;;;;;;uDARX,KAAK,IAAI;;;;;;;;;;;;;;;;kDAgBxB,6LAAC;wCAAsD,OAAO;4CAAC,gBAAgB;wCAAO;kFAAvE;;0DACb,6LAAC;0FAAa;;oDAA6C;kEAEzD,6LAAC;kGAAe;;;;;;;;;;;;0DAElB,6LAAC;0FAAa;;kEACZ,6LAAC;kGAAa;;0EACZ,6LAAC;0GAAc;0EACb,cAAA,6LAAC,6MAAA,CAAA,SAAM;;;;;;;;;;0EAET,6LAAC;0GAAe;0EAAyD;;;;;;;;;;;;kEAI3E,6LAAC;kGAAa;;0EACZ,6LAAC;0GAAc;0EACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;;;;;;;;;;0EAER,6LAAC;gEAAE,MAAK;0GAA6B;0EAAyD;;;;;;;;;;;;kEAIhG,6LAAC;kGAAa;;0EACZ,6LAAC;0GAAc;0EACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;;;;;;;;;;0EAEP,6LAAC;gEAAE,MAAK;0GAAmC;0EAAyD;;;;;;;;;;;;;;;;;;0DAOxG,6LAAC;0FAAc;;kEACb,6LAAC;kGAAc;;0EACb,6LAAC,6MAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;0EAClB,6LAAC;;0EAAK;;;;;;;;;;;;kEAER,6LAAC;kGAAc;;0EAEb,6LAAC;gEACC,KAAI;gEACJ,OAAM;gEACN,QAAO;gEACP,OAAO;oEAAE,QAAQ;gEAAE;gEACnB,iBAAiB;gEACjB,SAAQ;gEACR,gBAAe;0GACL;;;;;;0EAIZ,6LAAC;0GAAc;;;;;;0EAGf,6LAAC;0GAAc;0EACb,cAAA,6LAAC;8GAAc;;sFACb,6LAAC;sHAAc;;;;;;sFACf,6LAAC;sHAAc;;;;;;sFACf,6LAAC;sHAAc;;;;;;;;;;;;;;;;;0EAKnB,6LAAC;0GAAc;0EACb,cAAA,6LAAC;oEACC,MAAK;oEACL,QAAO;oEACP,KAAI;8GACM;;sFAEV,6LAAC;4EAAI,OAAM;4EAA0D,MAAK;4EAAO,SAAQ;4EAAY,QAAO;sHAA1D;sFAChD,cAAA,6LAAC;gFAAK,eAAc;gFAAQ,gBAAe;gFAAQ,aAAa;gFAAG,GAAE;;;;;;;;;;;;wEACjE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAShB,6LAAC;wCAAsD,OAAO;4CAAC,gBAAgB;wCAAO;kFAAvE;;0DACb,6LAAC;0FAAa;;oDAA6C;kEAEzD,6LAAC;kGAAe;;;;;;;;;;;;0DAIlB,6LAAC;0FAAc;0DACZ,YAAY,GAAG,CAAC,CAAC,uBAChB,6LAAC;wDAEC,MAAM,OAAO,IAAI;wDACjB,QAAO;wDACP,KAAI;wDACJ,cAAY,OAAO,KAAK;kGACb,GAAG,OAAO,KAAK,CAAC,6HAA6H,CAAC;;0EAEzJ,6LAAC;0GAAc;0EACZ,OAAO,IAAI;;;;;;0EAEd,6LAAC;0GAAe;0EAAuB,OAAO,KAAK;;;;;;;uDAV9C,OAAO,KAAK;;;;;;;;;;0DAgBvB,6LAAC;0FAAc;;kEACb,6LAAC;kGAAa;;0EACZ,6LAAC;gEAA6B,MAAK;gEAAO,QAAO;gEAAe,SAAQ;gEAAY,OAAM;0GAA3E;0EACb,cAAA,6LAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAY;oEAAI,GAAE;;;;;;;;;;;;4DACjE;;;;;;;kEAGR,6LAAC;kGAAa;;0EACZ,6LAAC;0GAAa;;kFACZ,6LAAC;;kFAAK;;;;;;kFACN,6LAAC;kHAAe;kFAAgB;;;;;;;;;;;;0EAElC,6LAAC;0GAAa;;kFACZ,6LAAC;;kFAAK;;;;;;kFACN,6LAAC;kHAAe;kFAAgB;;;;;;;;;;;;0EAElC,6LAAC;0GAAa;;kFACZ,6LAAC;;kFAAK;;;;;;kFACN,6LAAC;kHAAe;kFAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQ5C,6LAAC;0EAAc;;kDAEb,6LAAC;kFAAc;;0DACb,6LAAC;0FAAc;;;;;;0DACf,6LAAC;0FAAc;0DACb,cAAA,6LAAC;8FAAc;8DACb,cAAA,6LAAC;kGAAc;kEACb,cAAA,6LAAC;4DAAI,OAAM;4DAAiE,MAAK;4DAAO,SAAQ;4DAAY,QAAO;sGAAjE;sEAChD,cAAA,6LAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAO/E,6LAAC;kFAAc;;0DAEb,6LAAC;0FAAc;;kEACb,6LAAC;kGAAc;;0EACb,6LAAC;0GAAc;;kFACb,6LAAC;kHAAc;kFACb,cAAA,6LAAC;4EAAI,OAAM;4EAAiE,MAAK;4EAAO,SAAQ;4EAAY,QAAO;sHAAjE;sFAChD,cAAA,6LAAC;gFAAK,eAAc;gFAAQ,gBAAe;gFAAQ,aAAa;gFAAG,GAAE;;;;;;;;;;;;;;;;;kFAGzE,6LAAC;kHAAe;kFAAgC;;;;;;;;;;;;0EAElD,6LAAC;0GAAY;;oEAAwB;oEAChC,IAAI,OAAO,WAAW;oEAAG;;;;;;;0EAE9B,6LAAC;0GAAY;0EAA6B;;;;;;;;;;;;kEAK5C,6LAAC;kGAAc;;0EACb,6LAAC;0GAAc;;kFACb,6LAAC,+JAAA,CAAA,UAAI;wEAAC,MAAK;wEAAW,WAAU;kFAAgE;;;;;;kFAGhG,6LAAC;kHAAe;kFAAgB;;;;;;kFAChC,6LAAC,+JAAA,CAAA,UAAI;wEAAC,MAAK;wEAAS,WAAU;kFAAgE;;;;;;kFAG9F,6LAAC;kHAAe;kFAAgB;;;;;;kFAChC,6LAAC,+JAAA,CAAA,UAAI;wEAAC,MAAK;wEAAW,WAAU;kFAAgE;;;;;;;;;;;;0EAKlG,6LAAC;0GAAc;;kFACb,6LAAC;wEAAE,MAAK;wEAA+B,QAAO;wEAAS,KAAI;kHAAgC;kFACzF,cAAA,6LAAC;4EAAwB,MAAK;4EAAe,SAAQ;sHAAtC;sFACb,cAAA,6LAAC;gFAAK,GAAE;;;;;;;;;;;;;;;;;kFAGZ,6LAAC;wEAAE,MAAK;wEAA8B,QAAO;wEAAS,KAAI;kHAAgC;kFACxF,cAAA,6LAAC;4EAAwB,MAAK;4EAAe,SAAQ;sHAAtC;sFACb,cAAA,6LAAC;gFAAK,GAAE;;;;;;;;;;;;;;;;;kFAGZ,6LAAC;wEAAE,MAAK;wEAAuC,QAAO;wEAAS,KAAI;kHAAgC;kFACjG,cAAA,6LAAC;4EAAwB,MAAK;4EAAe,SAAQ;sHAAtC;sFACb,cAAA,6LAAC;gFAAK,GAAE;;;;;;;;;;;;;;;;;kFAGZ,6LAAC;wEAAE,MAAK;wEAAgC,QAAO;wEAAS,KAAI;kHAAgC;kFAC1F,cAAA,6LAAC;4EAAwB,MAAK;4EAAe,SAAQ;sHAAtC;sFACb,cAAA,6LAAC;gFAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAQlB,6LAAC;0FAAc;0DACb,cAAA,6LAAC;8FAAY;;wDAAyD;sEACtD,6LAAC;sGAAe;sEAAkC;;;;;;wDAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQlF,6LAAC;wBACC,SAAS;wBAIT,cAAW;kEAHA,CAAC,kOAAkO,EAC5O,gBAAgB,8BAA8B,gDAC9C;;0CAGF,6LAAC;0EAAc;;;;;;0CACf,6LAAC;0EAAc;0CACb,cAAA,6LAAC,mNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgCjC;GA7YM;KAAA;uCA+YS", "debugId": null}}]}