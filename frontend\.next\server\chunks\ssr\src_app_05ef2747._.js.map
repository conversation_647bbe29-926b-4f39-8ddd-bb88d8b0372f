{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/fwu_incubation%28react%2Blaravel%29/incubation/frontend/src/app/components/proposal/ProposalHero.tsx"], "sourcesContent": ["\"use client\"\r\nimport { useState, useEffect } from 'react';\r\nimport Image from 'next/image';\r\n\r\nconst ProposalHero: React.FC = () => {\r\n  const [isVisible, setIsVisible] = useState(false);\r\n\r\n  useEffect(() => {\r\n    setIsVisible(true);\r\n  }, []);\r\n\r\n  return (\r\n    <section className=\"relative bg-gradient-to-r from-indigo-900 via-purple-800 to-indigo-900 text-white overflow-hidden\">\r\n      {/* Background elements */}\r\n      <div className=\"absolute inset-0 overflow-hidden\">\r\n        <div className=\"absolute top-20 left-10 w-64 h-64 rounded-full bg-purple-500 opacity-10 animate-float-slow\"></div>\r\n        <div className=\"absolute bottom-20 right-10 w-96 h-96 rounded-full bg-indigo-500 opacity-10 animate-float-reverse\"></div>\r\n        <div className=\"absolute top-1/2 left-1/4 w-32 h-32 rounded-full bg-blue-500 opacity-5 animate-pulse\"></div>\r\n        <div\r\n          className=\"absolute inset-0 opacity-10\"\r\n          style={{\r\n            backgroundImage: 'radial-gradient(circle, rgba(255,255,255,0.1) 1px, transparent 1px)',\r\n            backgroundSize: '30px 30px'\r\n          }}\r\n        ></div>\r\n      </div>\r\n\r\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 relative z-10 py-16 md:py-24\">\r\n        <div className=\"max-w-4xl mx-auto\">\r\n          <div className=\"flex flex-col md:flex-row items-center\">\r\n            <div className={`md:w-1/2 text-center md:text-left transition-all duration-1000 transform ${isVisible ? 'opacity-100 translate-x-0' : 'opacity-0 -translate-x-10'}`}>\r\n              <div className=\"inline-block mb-6 p-2 bg-purple-800/30 rounded-full\">\r\n                <div className=\"px-4 py-1 bg-purple-700/50 rounded-full\">\r\n                  <span className=\"text-purple-100 font-medium\">FWU Incubation Center</span>\r\n                </div>\r\n              </div>\r\n              <h1 className=\"text-4xl sm:text-5xl font-extrabold mb-6 leading-tight\">\r\n                Submit Your <span className=\"text-transparent bg-clip-text bg-gradient-to-r from-purple-300 to-indigo-300\">Project Proposal</span>\r\n              </h1>\r\n              <p className=\"text-xl text-indigo-100 mb-8 leading-relaxed\">\r\n                Turn your innovative ideas into reality with support from the Far Western University Incubation Center.\r\n              </p>\r\n            </div>\r\n            \r\n            <div className={`md:w-1/2 mt-10 md:mt-0 transition-all duration-1000 delay-300 transform ${isVisible ? 'opacity-100 translate-x-0' : 'opacity-0 translate-x-10'}`}>\r\n              <div className=\"relative h-64 md:h-80 w-full\">\r\n                <div className=\"absolute inset-0 bg-gradient-to-r from-purple-500/20 to-indigo-500/20 rounded-2xl transform rotate-3 scale-105\"></div>\r\n                <div className=\"absolute inset-0 bg-gradient-to-r from-purple-500/20 to-indigo-500/20 rounded-2xl transform -rotate-3 scale-105\"></div>\r\n                <div className=\"relative h-full w-full rounded-2xl overflow-hidden shadow-2xl border border-white/10\">\r\n                  <Image\r\n                    src=\"https://images.unsplash.com/photo-1552664730-d307ca884978?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80\"\r\n                    alt=\"Submit Your Project Proposal\"\r\n                    fill\r\n                    className=\"object-cover\"\r\n                  />\r\n                  <div className=\"absolute inset-0 bg-gradient-to-t from-indigo-900/80 to-transparent\"></div>\r\n                  <div className=\"absolute bottom-0 left-0 right-0 p-6\">\r\n                    <span className=\"bg-purple-600/80 text-white text-xs font-bold px-3 py-1 rounded-full backdrop-blur-sm\">\r\n                      Innovation Starts Here\r\n                    </span>\r\n                    <h3 className=\"text-white text-xl font-bold mt-2\">\r\n                      We Support Groundbreaking Ideas\r\n                    </h3>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      \r\n      {/* Wave divider */}\r\n      <div className=\"absolute bottom-0 left-0 right-0\">\r\n        <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 1440 120\" className=\"w-full h-auto\">\r\n          <path\r\n            fill=\"#ffffff\"\r\n            fillOpacity=\"1\"\r\n            d=\"M0,64L80,69.3C160,75,320,85,480,80C640,75,800,53,960,48C1120,43,1280,53,1360,58.7L1440,64L1440,120L1360,120C1280,120,1120,120,960,120C800,120,640,120,480,120C320,120,160,120,80,120L0,120Z\"\r\n          ></path>\r\n        </svg>\r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\nexport default ProposalHero;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AAFA;;;;AAIA,MAAM,eAAyB;IAC7B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,aAAa;IACf,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBACC,WAAU;wBACV,OAAO;4BACL,iBAAiB;4BACjB,gBAAgB;wBAClB;;;;;;;;;;;;0BAIJ,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAW,CAAC,yEAAyE,EAAE,YAAY,8BAA8B,6BAA6B;;kDACjK,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAA8B;;;;;;;;;;;;;;;;kDAGlD,8OAAC;wCAAG,WAAU;;4CAAyD;0DACzD,8OAAC;gDAAK,WAAU;0DAA+E;;;;;;;;;;;;kDAE7G,8OAAC;wCAAE,WAAU;kDAA+C;;;;;;;;;;;;0CAK9D,8OAAC;gCAAI,WAAW,CAAC,wEAAwE,EAAE,YAAY,8BAA8B,4BAA4B;0CAC/J,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,6HAAA,CAAA,UAAK;oDACJ,KAAI;oDACJ,KAAI;oDACJ,IAAI;oDACJ,WAAU;;;;;;8DAEZ,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAwF;;;;;;sEAGxG,8OAAC;4DAAG,WAAU;sEAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAYhE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,OAAM;oBAA6B,SAAQ;oBAAe,WAAU;8BACvE,cAAA,8OAAC;wBACC,MAAK;wBACL,aAAY;wBACZ,GAAE;;;;;;;;;;;;;;;;;;;;;;AAMd;uCAEe", "debugId": null}}, {"offset": {"line": 266, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/fwu_incubation%28react%2Blaravel%29/incubation/frontend/src/app/components/proposal/steps/ProjectDetailsStep.tsx"], "sourcesContent": ["\"use client\"\r\nimport { ProposalFormData } from '../MultiStepForm';\r\n\r\ninterface ProjectDetailsStepProps {\r\n  formData: ProposalFormData;\r\n  updateFormData: (data: Partial<ProposalFormData>) => void;\r\n  errors: Record<string, string>;\r\n}\r\n\r\nconst ProjectDetailsStep: React.FC<ProjectDetailsStepProps> = ({ formData, updateFormData, errors }) => {\r\n  const projectCategories = [\r\n    'Technology',\r\n    'Agriculture',\r\n    'Healthcare',\r\n    'Education',\r\n    'Environment',\r\n    'Social Enterprise',\r\n    'Other'\r\n  ];\r\n\r\n  const projectDurations = [\r\n    '3-6 months',\r\n    '6-12 months',\r\n    '1-2 years',\r\n    'More than 2 years'\r\n  ];\r\n\r\n  const budgetRanges = [\r\n    'Less than NPR 100,000',\r\n    'NPR 100,000 - 500,000',\r\n    'NPR 500,000 - 1,000,000',\r\n    'NPR 1,000,000 - 5,000,000',\r\n    'More than NPR 5,000,000'\r\n  ];\r\n\r\n  return (\r\n    <div>\r\n      <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">Project Details</h2>\r\n      <p className=\"text-gray-600 mb-8\">\r\n        Please provide detailed information about your project proposal. This will help us understand your idea better.\r\n      </p>\r\n      \r\n      <div className=\"space-y-6\">\r\n        {/* Project Title */}\r\n        <div>\r\n          <label htmlFor=\"projectTitle\" className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n            Project Title <span className=\"text-red-500\">*</span>\r\n          </label>\r\n          <input\r\n            type=\"text\"\r\n            id=\"projectTitle\"\r\n            value={formData.projectTitle}\r\n            onChange={(e) => updateFormData({ projectTitle: e.target.value })}\r\n            className={`w-full px-4 py-2 border ${errors.projectTitle ? 'border-red-500' : 'border-gray-300'} rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-colors`}\r\n            placeholder=\"Enter a descriptive title for your project\"\r\n          />\r\n          {errors.projectTitle && (\r\n            <p className=\"mt-1 text-sm text-red-500\">{errors.projectTitle}</p>\r\n          )}\r\n        </div>\r\n        \r\n        {/* Project Category */}\r\n        <div>\r\n          <label htmlFor=\"projectCategory\" className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n            Project Category <span className=\"text-red-500\">*</span>\r\n          </label>\r\n          <select\r\n            id=\"projectCategory\"\r\n            value={formData.projectCategory}\r\n            onChange={(e) => updateFormData({ projectCategory: e.target.value })}\r\n            className={`w-full px-4 py-2 border ${errors.projectCategory ? 'border-red-500' : 'border-gray-300'} rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-colors`}\r\n          >\r\n            <option value=\"\">Select a category</option>\r\n            {projectCategories.map((category) => (\r\n              <option key={category} value={category}>{category}</option>\r\n            ))}\r\n          </select>\r\n          {errors.projectCategory && (\r\n            <p className=\"mt-1 text-sm text-red-500\">{errors.projectCategory}</p>\r\n          )}\r\n        </div>\r\n        \r\n        {/* Project Summary */}\r\n        <div>\r\n          <label htmlFor=\"projectSummary\" className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n            Project Summary <span className=\"text-red-500\">*</span>\r\n          </label>\r\n          <textarea\r\n            id=\"projectSummary\"\r\n            value={formData.projectSummary}\r\n            onChange={(e) => updateFormData({ projectSummary: e.target.value })}\r\n            rows={5}\r\n            className={`w-full px-4 py-2 border ${errors.projectSummary ? 'border-red-500' : 'border-gray-300'} rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-colors`}\r\n            placeholder=\"Provide a brief summary of your project (max 500 words)\"\r\n          ></textarea>\r\n          {errors.projectSummary && (\r\n            <p className=\"mt-1 text-sm text-red-500\">{errors.projectSummary}</p>\r\n          )}\r\n          <p className=\"mt-1 text-sm text-gray-500\">\r\n            {formData.projectSummary.length > 0 ? `${formData.projectSummary.split(' ').length} words` : '0 words'}\r\n          </p>\r\n        </div>\r\n        \r\n        {/* Project Duration */}\r\n        <div>\r\n          <label htmlFor=\"projectDuration\" className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n            Estimated Project Duration <span className=\"text-red-500\">*</span>\r\n          </label>\r\n          <select\r\n            id=\"projectDuration\"\r\n            value={formData.projectDuration}\r\n            onChange={(e) => updateFormData({ projectDuration: e.target.value })}\r\n            className={`w-full px-4 py-2 border ${errors.projectDuration ? 'border-red-500' : 'border-gray-300'} rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-colors`}\r\n          >\r\n            <option value=\"\">Select duration</option>\r\n            {projectDurations.map((duration) => (\r\n              <option key={duration} value={duration}>{duration}</option>\r\n            ))}\r\n          </select>\r\n          {errors.projectDuration && (\r\n            <p className=\"mt-1 text-sm text-red-500\">{errors.projectDuration}</p>\r\n          )}\r\n        </div>\r\n        \r\n        {/* Project Budget */}\r\n        <div>\r\n          <label htmlFor=\"projectBudget\" className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n            Estimated Budget <span className=\"text-red-500\">*</span>\r\n          </label>\r\n          <select\r\n            id=\"projectBudget\"\r\n            value={formData.projectBudget}\r\n            onChange={(e) => updateFormData({ projectBudget: e.target.value })}\r\n            className={`w-full px-4 py-2 border ${errors.projectBudget ? 'border-red-500' : 'border-gray-300'} rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-colors`}\r\n          >\r\n            <option value=\"\">Select budget range</option>\r\n            {budgetRanges.map((range) => (\r\n              <option key={range} value={range}>{range}</option>\r\n            ))}\r\n          </select>\r\n          {errors.projectBudget && (\r\n            <p className=\"mt-1 text-sm text-red-500\">{errors.projectBudget}</p>\r\n          )}\r\n        </div>\r\n      </div>\r\n      \r\n      <div className=\"mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200\">\r\n        <p className=\"text-sm text-blue-800\">\r\n          <strong>Note:</strong> All fields marked with an asterisk (*) are required. Please provide accurate information to help us evaluate your proposal effectively.\r\n        </p>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ProjectDetailsStep;\r\n"], "names": [], "mappings": ";;;;AAAA;;AASA,MAAM,qBAAwD,CAAC,EAAE,QAAQ,EAAE,cAAc,EAAE,MAAM,EAAE;IACjG,MAAM,oBAAoB;QACxB;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,mBAAmB;QACvB;QACA;QACA;QACA;KACD;IAED,MAAM,eAAe;QACnB;QACA;QACA;QACA;QACA;KACD;IAED,qBACE,8OAAC;;0BACC,8OAAC;gBAAG,WAAU;0BAAwC;;;;;;0BACtD,8OAAC;gBAAE,WAAU;0BAAqB;;;;;;0BAIlC,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;;0CACC,8OAAC;gCAAM,SAAQ;gCAAe,WAAU;;oCAA+C;kDACvE,8OAAC;wCAAK,WAAU;kDAAe;;;;;;;;;;;;0CAE/C,8OAAC;gCACC,MAAK;gCACL,IAAG;gCACH,OAAO,SAAS,YAAY;gCAC5B,UAAU,CAAC,IAAM,eAAe;wCAAE,cAAc,EAAE,MAAM,CAAC,KAAK;oCAAC;gCAC/D,WAAW,CAAC,wBAAwB,EAAE,OAAO,YAAY,GAAG,mBAAmB,kBAAkB,yFAAyF,CAAC;gCAC3L,aAAY;;;;;;4BAEb,OAAO,YAAY,kBAClB,8OAAC;gCAAE,WAAU;0CAA6B,OAAO,YAAY;;;;;;;;;;;;kCAKjE,8OAAC;;0CACC,8OAAC;gCAAM,SAAQ;gCAAkB,WAAU;;oCAA+C;kDACvE,8OAAC;wCAAK,WAAU;kDAAe;;;;;;;;;;;;0CAElD,8OAAC;gCACC,IAAG;gCACH,OAAO,SAAS,eAAe;gCAC/B,UAAU,CAAC,IAAM,eAAe;wCAAE,iBAAiB,EAAE,MAAM,CAAC,KAAK;oCAAC;gCAClE,WAAW,CAAC,wBAAwB,EAAE,OAAO,eAAe,GAAG,mBAAmB,kBAAkB,yFAAyF,CAAC;;kDAE9L,8OAAC;wCAAO,OAAM;kDAAG;;;;;;oCAChB,kBAAkB,GAAG,CAAC,CAAC,yBACtB,8OAAC;4CAAsB,OAAO;sDAAW;2CAA5B;;;;;;;;;;;4BAGhB,OAAO,eAAe,kBACrB,8OAAC;gCAAE,WAAU;0CAA6B,OAAO,eAAe;;;;;;;;;;;;kCAKpE,8OAAC;;0CACC,8OAAC;gCAAM,SAAQ;gCAAiB,WAAU;;oCAA+C;kDACvE,8OAAC;wCAAK,WAAU;kDAAe;;;;;;;;;;;;0CAEjD,8OAAC;gCACC,IAAG;gCACH,OAAO,SAAS,cAAc;gCAC9B,UAAU,CAAC,IAAM,eAAe;wCAAE,gBAAgB,EAAE,MAAM,CAAC,KAAK;oCAAC;gCACjE,MAAM;gCACN,WAAW,CAAC,wBAAwB,EAAE,OAAO,cAAc,GAAG,mBAAmB,kBAAkB,yFAAyF,CAAC;gCAC7L,aAAY;;;;;;4BAEb,OAAO,cAAc,kBACpB,8OAAC;gCAAE,WAAU;0CAA6B,OAAO,cAAc;;;;;;0CAEjE,8OAAC;gCAAE,WAAU;0CACV,SAAS,cAAc,CAAC,MAAM,GAAG,IAAI,GAAG,SAAS,cAAc,CAAC,KAAK,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,GAAG;;;;;;;;;;;;kCAKjG,8OAAC;;0CACC,8OAAC;gCAAM,SAAQ;gCAAkB,WAAU;;oCAA+C;kDAC7D,8OAAC;wCAAK,WAAU;kDAAe;;;;;;;;;;;;0CAE5D,8OAAC;gCACC,IAAG;gCACH,OAAO,SAAS,eAAe;gCAC/B,UAAU,CAAC,IAAM,eAAe;wCAAE,iBAAiB,EAAE,MAAM,CAAC,KAAK;oCAAC;gCAClE,WAAW,CAAC,wBAAwB,EAAE,OAAO,eAAe,GAAG,mBAAmB,kBAAkB,yFAAyF,CAAC;;kDAE9L,8OAAC;wCAAO,OAAM;kDAAG;;;;;;oCAChB,iBAAiB,GAAG,CAAC,CAAC,yBACrB,8OAAC;4CAAsB,OAAO;sDAAW;2CAA5B;;;;;;;;;;;4BAGhB,OAAO,eAAe,kBACrB,8OAAC;gCAAE,WAAU;0CAA6B,OAAO,eAAe;;;;;;;;;;;;kCAKpE,8OAAC;;0CACC,8OAAC;gCAAM,SAAQ;gCAAgB,WAAU;;oCAA+C;kDACrE,8OAAC;wCAAK,WAAU;kDAAe;;;;;;;;;;;;0CAElD,8OAAC;gCACC,IAAG;gCACH,OAAO,SAAS,aAAa;gCAC7B,UAAU,CAAC,IAAM,eAAe;wCAAE,eAAe,EAAE,MAAM,CAAC,KAAK;oCAAC;gCAChE,WAAW,CAAC,wBAAwB,EAAE,OAAO,aAAa,GAAG,mBAAmB,kBAAkB,yFAAyF,CAAC;;kDAE5L,8OAAC;wCAAO,OAAM;kDAAG;;;;;;oCAChB,aAAa,GAAG,CAAC,CAAC,sBACjB,8OAAC;4CAAmB,OAAO;sDAAQ;2CAAtB;;;;;;;;;;;4BAGhB,OAAO,aAAa,kBACnB,8OAAC;gCAAE,WAAU;0CAA6B,OAAO,aAAa;;;;;;;;;;;;;;;;;;0BAKpE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAE,WAAU;;sCACX,8OAAC;sCAAO;;;;;;wBAAc;;;;;;;;;;;;;;;;;;AAKhC;uCAEe", "debugId": null}}, {"offset": {"line": 661, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/fwu_incubation%28react%2Blaravel%29/incubation/frontend/src/app/components/proposal/steps/TeamInfoStep.tsx"], "sourcesContent": ["\"use client\"\r\nimport { ProposalFormData } from '../MultiStepForm';\r\nimport { Plus, Trash2 } from 'lucide-react';\r\n\r\ninterface TeamInfoStepProps {\r\n  formData: ProposalFormData;\r\n  updateFormData: (data: Partial<ProposalFormData>) => void;\r\n  errors: Record<string, string>;\r\n}\r\n\r\nconst TeamInfoStep: React.FC<TeamInfoStepProps> = ({ formData, updateFormData, errors }) => {\r\n  const addTeamMember = () => {\r\n    updateFormData({\r\n      teamMembers: [...formData.teamMembers, { name: '', role: '', expertise: '' }]\r\n    });\r\n  };\r\n\r\n  const removeTeamMember = (index: number) => {\r\n    const updatedMembers = [...formData.teamMembers];\r\n    updatedMembers.splice(index, 1);\r\n    updateFormData({ teamMembers: updatedMembers });\r\n  };\r\n\r\n  const updateTeamMember = (index: number, field: string, value: string) => {\r\n    const updatedMembers = [...formData.teamMembers];\r\n    updatedMembers[index] = { ...updatedMembers[index], [field]: value };\r\n    updateFormData({ teamMembers: updatedMembers });\r\n  };\r\n\r\n  return (\r\n    <div>\r\n      <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">Team Information</h2>\r\n      <p className=\"text-gray-600 mb-8\">\r\n        Please provide details about the team members who will be working on this project.\r\n      </p>\r\n      \r\n      <div className=\"space-y-8\">\r\n        {/* Team Lead Information */}\r\n        <div className=\"p-6 bg-purple-50 rounded-lg border border-purple-200\">\r\n          <h3 className=\"text-lg font-semibold text-purple-800 mb-4\">Team Lead / Principal Investigator</h3>\r\n          \r\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n            {/* Team Lead Name */}\r\n            <div>\r\n              <label htmlFor=\"teamLeadName\" className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n                Full Name <span className=\"text-red-500\">*</span>\r\n              </label>\r\n              <input\r\n                type=\"text\"\r\n                id=\"teamLeadName\"\r\n                value={formData.teamLeadName}\r\n                onChange={(e) => updateFormData({ teamLeadName: e.target.value })}\r\n                className={`w-full px-4 py-2 border ${errors.teamLeadName ? 'border-red-500' : 'border-gray-300'} rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-colors`}\r\n                placeholder=\"Enter your full name\"\r\n              />\r\n              {errors.teamLeadName && (\r\n                <p className=\"mt-1 text-sm text-red-500\">{errors.teamLeadName}</p>\r\n              )}\r\n            </div>\r\n            \r\n            {/* Team Lead Email */}\r\n            <div>\r\n              <label htmlFor=\"teamLeadEmail\" className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n                Email Address <span className=\"text-red-500\">*</span>\r\n              </label>\r\n              <input\r\n                type=\"email\"\r\n                id=\"teamLeadEmail\"\r\n                value={formData.teamLeadEmail}\r\n                onChange={(e) => updateFormData({ teamLeadEmail: e.target.value })}\r\n                className={`w-full px-4 py-2 border ${errors.teamLeadEmail ? 'border-red-500' : 'border-gray-300'} rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-colors`}\r\n                placeholder=\"Enter your email address\"\r\n              />\r\n              {errors.teamLeadEmail && (\r\n                <p className=\"mt-1 text-sm text-red-500\">{errors.teamLeadEmail}</p>\r\n              )}\r\n            </div>\r\n            \r\n            {/* Team Lead Phone */}\r\n            <div>\r\n              <label htmlFor=\"teamLeadPhone\" className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n                Phone Number <span className=\"text-red-500\">*</span>\r\n              </label>\r\n              <input\r\n                type=\"tel\"\r\n                id=\"teamLeadPhone\"\r\n                value={formData.teamLeadPhone}\r\n                onChange={(e) => updateFormData({ teamLeadPhone: e.target.value })}\r\n                className={`w-full px-4 py-2 border ${errors.teamLeadPhone ? 'border-red-500' : 'border-gray-300'} rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-colors`}\r\n                placeholder=\"Enter your phone number\"\r\n              />\r\n              {errors.teamLeadPhone && (\r\n                <p className=\"mt-1 text-sm text-red-500\">{errors.teamLeadPhone}</p>\r\n              )}\r\n            </div>\r\n            \r\n            {/* Team Lead Affiliation */}\r\n            <div>\r\n              <label htmlFor=\"teamLeadAffiliation\" className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n                Affiliation <span className=\"text-red-500\">*</span>\r\n              </label>\r\n              <input\r\n                type=\"text\"\r\n                id=\"teamLeadAffiliation\"\r\n                value={formData.teamLeadAffiliation}\r\n                onChange={(e) => updateFormData({ teamLeadAffiliation: e.target.value })}\r\n                className={`w-full px-4 py-2 border ${errors.teamLeadAffiliation ? 'border-red-500' : 'border-gray-300'} rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-colors`}\r\n                placeholder=\"University, Department, or Organization\"\r\n              />\r\n              {errors.teamLeadAffiliation && (\r\n                <p className=\"mt-1 text-sm text-red-500\">{errors.teamLeadAffiliation}</p>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n        \r\n        {/* Team Members */}\r\n        <div>\r\n          <div className=\"flex justify-between items-center mb-4\">\r\n            <h3 className=\"text-lg font-semibold text-gray-800\">Team Members</h3>\r\n            <button\r\n              type=\"button\"\r\n              onClick={addTeamMember}\r\n              className=\"px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors flex items-center text-sm\"\r\n            >\r\n              <Plus className=\"mr-2\" /> Add Team Member\r\n            </button>\r\n          </div>\r\n          \r\n          {formData.teamMembers.map((member, index) => (\r\n            <div key={index} className=\"p-4 bg-gray-50 rounded-lg border border-gray-200 mb-4\">\r\n              <div className=\"flex justify-between items-center mb-3\">\r\n                <h4 className=\"font-medium text-gray-700\">Team Member {index + 1}</h4>\r\n                {index > 0 && (\r\n                  <button\r\n                    type=\"button\"\r\n                    onClick={() => removeTeamMember(index)}\r\n                    className=\"text-red-500 hover:text-red-700 transition-colors\"\r\n                  >\r\n                    <Trash2 size={18} />\r\n                  </button>\r\n                )}\r\n              </div>\r\n              \r\n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\r\n                {/* Member Name */}\r\n                <div>\r\n                  <label htmlFor={`memberName${index}`} className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n                    Full Name\r\n                  </label>\r\n                  <input\r\n                    type=\"text\"\r\n                    id={`memberName${index}`}\r\n                    value={member.name}\r\n                    onChange={(e) => updateTeamMember(index, 'name', e.target.value)}\r\n                    className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-colors\"\r\n                    placeholder=\"Enter full name\"\r\n                  />\r\n                </div>\r\n                \r\n                {/* Member Role */}\r\n                <div>\r\n                  <label htmlFor={`memberRole${index}`} className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n                    Role in Project\r\n                  </label>\r\n                  <input\r\n                    type=\"text\"\r\n                    id={`memberRole${index}`}\r\n                    value={member.role}\r\n                    onChange={(e) => updateTeamMember(index, 'role', e.target.value)}\r\n                    className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-colors\"\r\n                    placeholder=\"e.g., Researcher, Developer\"\r\n                  />\r\n                </div>\r\n                \r\n                {/* Member Expertise */}\r\n                <div>\r\n                  <label htmlFor={`memberExpertise${index}`} className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n                    Area of Expertise\r\n                  </label>\r\n                  <input\r\n                    type=\"text\"\r\n                    id={`memberExpertise${index}`}\r\n                    value={member.expertise}\r\n                    onChange={(e) => updateTeamMember(index, 'expertise', e.target.value)}\r\n                    className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-colors\"\r\n                    placeholder=\"e.g., Software Development, Agriculture\"\r\n                  />\r\n                </div>\r\n              </div>\r\n              \r\n              {errors[`teamMember${index}`] && (\r\n                <p className=\"mt-2 text-sm text-red-500\">{errors[`teamMember${index}`]}</p>\r\n              )}\r\n            </div>\r\n          ))}\r\n          \r\n          {formData.teamMembers.length === 0 && (\r\n            <p className=\"text-gray-500 italic\">No team members added yet.</p>\r\n          )}\r\n        </div>\r\n      </div>\r\n      \r\n      <div className=\"mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200\">\r\n        <p className=\"text-sm text-blue-800\">\r\n          <strong>Note:</strong> Team lead information is required. Additional team members are optional but recommended for collaborative projects.\r\n        </p>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default TeamInfoStep;\r\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAFA;;;AAUA,MAAM,eAA4C,CAAC,EAAE,QAAQ,EAAE,cAAc,EAAE,MAAM,EAAE;IACrF,MAAM,gBAAgB;QACpB,eAAe;YACb,aAAa;mBAAI,SAAS,WAAW;gBAAE;oBAAE,MAAM;oBAAI,MAAM;oBAAI,WAAW;gBAAG;aAAE;QAC/E;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,iBAAiB;eAAI,SAAS,WAAW;SAAC;QAChD,eAAe,MAAM,CAAC,OAAO;QAC7B,eAAe;YAAE,aAAa;QAAe;IAC/C;IAEA,MAAM,mBAAmB,CAAC,OAAe,OAAe;QACtD,MAAM,iBAAiB;eAAI,SAAS,WAAW;SAAC;QAChD,cAAc,CAAC,MAAM,GAAG;YAAE,GAAG,cAAc,CAAC,MAAM;YAAE,CAAC,MAAM,EAAE;QAAM;QACnE,eAAe;YAAE,aAAa;QAAe;IAC/C;IAEA,qBACE,8OAAC;;0BACC,8OAAC;gBAAG,WAAU;0BAAwC;;;;;;0BACtD,8OAAC;gBAAE,WAAU;0BAAqB;;;;;;0BAIlC,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA6C;;;;;;0CAE3D,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;;0DACC,8OAAC;gDAAM,SAAQ;gDAAe,WAAU;;oDAA+C;kEAC3E,8OAAC;wDAAK,WAAU;kEAAe;;;;;;;;;;;;0DAE3C,8OAAC;gDACC,MAAK;gDACL,IAAG;gDACH,OAAO,SAAS,YAAY;gDAC5B,UAAU,CAAC,IAAM,eAAe;wDAAE,cAAc,EAAE,MAAM,CAAC,KAAK;oDAAC;gDAC/D,WAAW,CAAC,wBAAwB,EAAE,OAAO,YAAY,GAAG,mBAAmB,kBAAkB,yFAAyF,CAAC;gDAC3L,aAAY;;;;;;4CAEb,OAAO,YAAY,kBAClB,8OAAC;gDAAE,WAAU;0DAA6B,OAAO,YAAY;;;;;;;;;;;;kDAKjE,8OAAC;;0DACC,8OAAC;gDAAM,SAAQ;gDAAgB,WAAU;;oDAA+C;kEACxE,8OAAC;wDAAK,WAAU;kEAAe;;;;;;;;;;;;0DAE/C,8OAAC;gDACC,MAAK;gDACL,IAAG;gDACH,OAAO,SAAS,aAAa;gDAC7B,UAAU,CAAC,IAAM,eAAe;wDAAE,eAAe,EAAE,MAAM,CAAC,KAAK;oDAAC;gDAChE,WAAW,CAAC,wBAAwB,EAAE,OAAO,aAAa,GAAG,mBAAmB,kBAAkB,yFAAyF,CAAC;gDAC5L,aAAY;;;;;;4CAEb,OAAO,aAAa,kBACnB,8OAAC;gDAAE,WAAU;0DAA6B,OAAO,aAAa;;;;;;;;;;;;kDAKlE,8OAAC;;0DACC,8OAAC;gDAAM,SAAQ;gDAAgB,WAAU;;oDAA+C;kEACzE,8OAAC;wDAAK,WAAU;kEAAe;;;;;;;;;;;;0DAE9C,8OAAC;gDACC,MAAK;gDACL,IAAG;gDACH,OAAO,SAAS,aAAa;gDAC7B,UAAU,CAAC,IAAM,eAAe;wDAAE,eAAe,EAAE,MAAM,CAAC,KAAK;oDAAC;gDAChE,WAAW,CAAC,wBAAwB,EAAE,OAAO,aAAa,GAAG,mBAAmB,kBAAkB,yFAAyF,CAAC;gDAC5L,aAAY;;;;;;4CAEb,OAAO,aAAa,kBACnB,8OAAC;gDAAE,WAAU;0DAA6B,OAAO,aAAa;;;;;;;;;;;;kDAKlE,8OAAC;;0DACC,8OAAC;gDAAM,SAAQ;gDAAsB,WAAU;;oDAA+C;kEAChF,8OAAC;wDAAK,WAAU;kEAAe;;;;;;;;;;;;0DAE7C,8OAAC;gDACC,MAAK;gDACL,IAAG;gDACH,OAAO,SAAS,mBAAmB;gDACnC,UAAU,CAAC,IAAM,eAAe;wDAAE,qBAAqB,EAAE,MAAM,CAAC,KAAK;oDAAC;gDACtE,WAAW,CAAC,wBAAwB,EAAE,OAAO,mBAAmB,GAAG,mBAAmB,kBAAkB,yFAAyF,CAAC;gDAClM,aAAY;;;;;;4CAEb,OAAO,mBAAmB,kBACzB,8OAAC;gDAAE,WAAU;0DAA6B,OAAO,mBAAmB;;;;;;;;;;;;;;;;;;;;;;;;kCAO5E,8OAAC;;0CACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAsC;;;;;;kDACpD,8OAAC;wCACC,MAAK;wCACL,SAAS;wCACT,WAAU;;0DAEV,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAS;;;;;;;;;;;;;4BAI5B,SAAS,WAAW,CAAC,GAAG,CAAC,CAAC,QAAQ,sBACjC,8OAAC;oCAAgB,WAAU;;sDACzB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;;wDAA4B;wDAAa,QAAQ;;;;;;;gDAC9D,QAAQ,mBACP,8OAAC;oDACC,MAAK;oDACL,SAAS,IAAM,iBAAiB;oDAChC,WAAU;8DAEV,cAAA,8OAAC,0MAAA,CAAA,SAAM;wDAAC,MAAM;;;;;;;;;;;;;;;;;sDAKpB,8OAAC;4CAAI,WAAU;;8DAEb,8OAAC;;sEACC,8OAAC;4DAAM,SAAS,CAAC,UAAU,EAAE,OAAO;4DAAE,WAAU;sEAA+C;;;;;;sEAG/F,8OAAC;4DACC,MAAK;4DACL,IAAI,CAAC,UAAU,EAAE,OAAO;4DACxB,OAAO,OAAO,IAAI;4DAClB,UAAU,CAAC,IAAM,iBAAiB,OAAO,QAAQ,EAAE,MAAM,CAAC,KAAK;4DAC/D,WAAU;4DACV,aAAY;;;;;;;;;;;;8DAKhB,8OAAC;;sEACC,8OAAC;4DAAM,SAAS,CAAC,UAAU,EAAE,OAAO;4DAAE,WAAU;sEAA+C;;;;;;sEAG/F,8OAAC;4DACC,MAAK;4DACL,IAAI,CAAC,UAAU,EAAE,OAAO;4DACxB,OAAO,OAAO,IAAI;4DAClB,UAAU,CAAC,IAAM,iBAAiB,OAAO,QAAQ,EAAE,MAAM,CAAC,KAAK;4DAC/D,WAAU;4DACV,aAAY;;;;;;;;;;;;8DAKhB,8OAAC;;sEACC,8OAAC;4DAAM,SAAS,CAAC,eAAe,EAAE,OAAO;4DAAE,WAAU;sEAA+C;;;;;;sEAGpG,8OAAC;4DACC,MAAK;4DACL,IAAI,CAAC,eAAe,EAAE,OAAO;4DAC7B,OAAO,OAAO,SAAS;4DACvB,UAAU,CAAC,IAAM,iBAAiB,OAAO,aAAa,EAAE,MAAM,CAAC,KAAK;4DACpE,WAAU;4DACV,aAAY;;;;;;;;;;;;;;;;;;wCAKjB,MAAM,CAAC,CAAC,UAAU,EAAE,OAAO,CAAC,kBAC3B,8OAAC;4CAAE,WAAU;sDAA6B,MAAM,CAAC,CAAC,UAAU,EAAE,OAAO,CAAC;;;;;;;mCA9DhE;;;;;4BAmEX,SAAS,WAAW,CAAC,MAAM,KAAK,mBAC/B,8OAAC;gCAAE,WAAU;0CAAuB;;;;;;;;;;;;;;;;;;0BAK1C,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAE,WAAU;;sCACX,8OAAC;sCAAO;;;;;;wBAAc;;;;;;;;;;;;;;;;;;AAKhC;uCAEe", "debugId": null}}, {"offset": {"line": 1192, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/fwu_incubation%28react%2Blaravel%29/incubation/frontend/src/app/components/proposal/steps/ProposalUploadStep.tsx"], "sourcesContent": ["\"use client\"\r\nimport { ProposalFormData } from '../MultiStepForm';\r\nimport { useState } from 'react';\r\nimport { Upload, File,Sparkle,Download } from 'lucide-react';\r\n\r\ninterface ProposalUploadStepProps {\r\n  formData: ProposalFormData;\r\n  updateFormData: (data: Partial<ProposalFormData>) => void;\r\n  errors: Record<string, string>;\r\n}\r\n\r\nconst ProposalUploadStep: React.FC<ProposalUploadStepProps> = ({ formData, updateFormData, errors }) => {\r\n  const [dragActive, setDragActive] = useState(false);\r\n\r\n  const handleDrag = (e: React.DragEvent) => {\r\n    e.preventDefault();\r\n    e.stopPropagation();\r\n    if (e.type === 'dragenter' || e.type === 'dragover') {\r\n      setDragActive(true);\r\n    } else if (e.type === 'dragleave') {\r\n      setDragActive(false);\r\n    }\r\n  };\r\n\r\n  const handleMainFileDrop = (e: React.DragEvent) => {\r\n    e.preventDefault();\r\n    e.stopPropagation();\r\n    setDragActive(false);\r\n    \r\n    if (e.dataTransfer.files && e.dataTransfer.files[0]) {\r\n      const file = e.dataTransfer.files[0];\r\n      updateFormData({ proposalFile: file });\r\n    }\r\n  };\r\n\r\n  const handleMainFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    if (e.target.files && e.target.files[0]) {\r\n      const file = e.target.files[0];\r\n      updateFormData({ proposalFile: file });\r\n    }\r\n  };\r\n\r\n  const handleAdditionalFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    if (e.target.files && e.target.files.length > 0) {\r\n      const newFiles = Array.from(e.target.files);\r\n      updateFormData({ additionalDocuments: [...formData.additionalDocuments, ...newFiles] });\r\n    }\r\n  };\r\n\r\n  const removeAdditionalFile = (index: number) => {\r\n    const updatedFiles = [...formData.additionalDocuments];\r\n    updatedFiles.splice(index, 1);\r\n    updateFormData({ additionalDocuments: updatedFiles });\r\n  };\r\n\r\n  const removeMainFile = () => {\r\n    updateFormData({ proposalFile: null });\r\n  };\r\n\r\n  const formatFileSize = (bytes: number) => {\r\n    if (bytes === 0) return '0 Bytes';\r\n    const k = 1024;\r\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\r\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\r\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\r\n  };\r\n\r\n  return (\r\n    <div>\r\n      <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">Upload Documents</h2>\r\n      <p className=\"text-gray-600 mb-8\">\r\n        Please upload your detailed project proposal and any supporting documents.\r\n      </p>\r\n      \r\n      <div className=\"space-y-8\">\r\n        {/* Main Proposal Upload */}\r\n        <div>\r\n          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n            Project Proposal Document <span className=\"text-red-500\">*</span>\r\n          </label>\r\n          <div \r\n            className={`border-2 border-dashed rounded-lg p-6 ${\r\n              dragActive ? 'border-purple-500 bg-purple-50' : errors.proposalFile ? 'border-red-300 bg-red-50' : 'border-gray-300 bg-gray-50'\r\n            } transition-colors text-center cursor-pointer`}\r\n            onDragEnter={handleDrag}\r\n            onDragLeave={handleDrag}\r\n            onDragOver={handleDrag}\r\n            onDrop={handleMainFileDrop}\r\n            onClick={() => document.getElementById('proposalFile')?.click()}\r\n          >\r\n            <input\r\n              type=\"file\"\r\n              id=\"proposalFile\"\r\n              onChange={handleMainFileChange}\r\n              className=\"hidden\"\r\n              accept=\".pdf,.doc,.docx\"\r\n            />\r\n            \r\n            {formData.proposalFile ? (\r\n              <div className=\"flex items-center justify-between bg-white p-3 rounded-lg border border-gray-200\">\r\n                <div className=\"flex items-center\">\r\n                  <File className=\"text-purple-500 mr-3\" size={24} />\r\n                  <div className=\"text-left\">\r\n                    <p className=\"font-medium text-gray-800\">{formData.proposalFile.name}</p>\r\n                    <p className=\"text-sm text-gray-500\">{formatFileSize(formData.proposalFile.size)}</p>\r\n                  </div>\r\n                </div>\r\n                <button \r\n                  type=\"button\" \r\n                  onClick={(e) => {\r\n                    e.stopPropagation();\r\n                    removeMainFile();\r\n                  }}\r\n                  className=\"p-1 bg-red-100 text-red-500 rounded-full hover:bg-red-200 transition-colors\"\r\n                >\r\n                  <Sparkle size={18} />\r\n                </button>\r\n              </div>\r\n            ) : (\r\n              <div>\r\n                <Upload className=\"mx-auto text-gray-400 mb-3\" size={36} />\r\n                <p className=\"text-gray-600 mb-1\">Drag and drop your file here, or click to browse</p>\r\n                <p className=\"text-sm text-gray-500\">Supported formats: PDF, DOC, DOCX (Max 10MB)</p>\r\n              </div>\r\n            )}\r\n          </div>\r\n          {errors.proposalFile && (\r\n            <p className=\"mt-1 text-sm text-red-500\">{errors.proposalFile}</p>\r\n          )}\r\n        </div>\r\n        \r\n        {/* Additional Documents */}\r\n        <div>\r\n          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n            Additional Supporting Documents (Optional)\r\n          </label>\r\n          <div className=\"border rounded-lg p-6 bg-gray-50\">\r\n            <div className=\"flex items-center justify-center mb-4\">\r\n              <label htmlFor=\"additionalFiles\" className=\"px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors flex items-center cursor-pointer\">\r\n                <Upload className=\"mr-2\" />\r\n                Select Files\r\n                <input\r\n                  type=\"file\"\r\n                  id=\"additionalFiles\"\r\n                  onChange={handleAdditionalFileChange}\r\n                  className=\"hidden\"\r\n                  multiple\r\n                  accept=\".pdf,.doc,.docx,.jpg,.jpeg,.png,.xlsx,.xls\"\r\n                />\r\n              </label>\r\n            </div>\r\n            \r\n            {formData.additionalDocuments.length > 0 ? (\r\n              <div className=\"space-y-2\">\r\n                {formData.additionalDocuments.map((file, index) => (\r\n                  <div key={index} className=\"flex items-center justify-between bg-white p-3 rounded-lg border border-gray-200\">\r\n                    <div className=\"flex items-center\">\r\n                      <File className=\"text-gray-500 mr-3\" size={20} />\r\n                      <div className=\"text-left\">\r\n                        <p className=\"font-medium text-gray-800\">{file.name}</p>\r\n                        <p className=\"text-sm text-gray-500\">{formatFileSize(file.size)}</p>\r\n                      </div>\r\n                    </div>\r\n                    <button \r\n                      type=\"button\" \r\n                      onClick={() => removeAdditionalFile(index)}\r\n                      className=\"p-1 bg-red-100 text-red-500 rounded-full hover:bg-red-200 transition-colors\"\r\n                    >\r\n                      <Sparkle size={16} />\r\n                    </button>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            ) : (\r\n              <p className=\"text-center text-gray-500 italic\">No additional documents uploaded</p>\r\n            )}\r\n          </div>\r\n        </div>\r\n        \r\n        {/* Download Template */}\r\n        <div className=\"p-4 bg-blue-50 rounded-lg border border-blue-200\">\r\n          <div className=\"flex items-start\">\r\n            <Download className=\"text-blue-600 mt-1 mr-3 flex-shrink-0\" size={20} />\r\n            <div>\r\n              <h3 className=\"font-medium text-blue-800 mb-1\">Need a proposal template?</h3>\r\n              <p className=\"text-sm text-blue-700 mb-2\">\r\n                Download our standard proposal template to ensure your submission includes all required information.\r\n              </p>\r\n              <a \r\n                href=\"#\" \r\n                className=\"inline-flex items-center text-sm font-medium text-blue-600 hover:text-blue-800 transition-colors\"\r\n              >\r\n                Download Template <Download className=\"ml-1\" size={14} />\r\n              </a>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        \r\n        {/* Terms Agreement */}\r\n        <div>\r\n          <div className=\"flex items-start\">\r\n            <div className=\"flex items-center h-5\">\r\n              <input\r\n                id=\"agreementChecked\"\r\n                type=\"checkbox\"\r\n                checked={formData.agreementChecked}\r\n                onChange={(e) => updateFormData({ agreementChecked: e.target.checked })}\r\n                className={`h-4 w-4 text-purple-600 border-gray-300 rounded focus:ring-purple-500 ${errors.agreementChecked ? 'border-red-500' : ''}`}\r\n              />\r\n            </div>\r\n            <div className=\"ml-3 text-sm\">\r\n              <label htmlFor=\"agreementChecked\" className=\"font-medium text-gray-700\">\r\n                I agree to the terms and conditions <span className=\"text-red-500\">*</span>\r\n              </label>\r\n              <p className=\"text-gray-500\">\r\n                By submitting this proposal, I confirm that all information provided is accurate and that I have the authority to submit this proposal on behalf of the team.\r\n              </p>\r\n              {errors.agreementChecked && (\r\n                <p className=\"mt-1 text-sm text-red-500\">{errors.agreementChecked}</p>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ProposalUploadStep;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAHA;;;;AAWA,MAAM,qBAAwD,CAAC,EAAE,QAAQ,EAAE,cAAc,EAAE,MAAM,EAAE;IACjG,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,aAAa,CAAC;QAClB,EAAE,cAAc;QAChB,EAAE,eAAe;QACjB,IAAI,EAAE,IAAI,KAAK,eAAe,EAAE,IAAI,KAAK,YAAY;YACnD,cAAc;QAChB,OAAO,IAAI,EAAE,IAAI,KAAK,aAAa;YACjC,cAAc;QAChB;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,EAAE,cAAc;QAChB,EAAE,eAAe;QACjB,cAAc;QAEd,IAAI,EAAE,YAAY,CAAC,KAAK,IAAI,EAAE,YAAY,CAAC,KAAK,CAAC,EAAE,EAAE;YACnD,MAAM,OAAO,EAAE,YAAY,CAAC,KAAK,CAAC,EAAE;YACpC,eAAe;gBAAE,cAAc;YAAK;QACtC;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,IAAI,EAAE,MAAM,CAAC,KAAK,IAAI,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE,EAAE;YACvC,MAAM,OAAO,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE;YAC9B,eAAe;gBAAE,cAAc;YAAK;QACtC;IACF;IAEA,MAAM,6BAA6B,CAAC;QAClC,IAAI,EAAE,MAAM,CAAC,KAAK,IAAI,EAAE,MAAM,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG;YAC/C,MAAM,WAAW,MAAM,IAAI,CAAC,EAAE,MAAM,CAAC,KAAK;YAC1C,eAAe;gBAAE,qBAAqB;uBAAI,SAAS,mBAAmB;uBAAK;iBAAS;YAAC;QACvF;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,MAAM,eAAe;eAAI,SAAS,mBAAmB;SAAC;QACtD,aAAa,MAAM,CAAC,OAAO;QAC3B,eAAe;YAAE,qBAAqB;QAAa;IACrD;IAEA,MAAM,iBAAiB;QACrB,eAAe;YAAE,cAAc;QAAK;IACtC;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,UAAU,GAAG,OAAO;QACxB,MAAM,IAAI;QACV,MAAM,QAAQ;YAAC;YAAS;YAAM;YAAM;SAAK;QACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;QAChD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;IACzE;IAEA,qBACE,8OAAC;;0BACC,8OAAC;gBAAG,WAAU;0BAAwC;;;;;;0BACtD,8OAAC;gBAAE,WAAU;0BAAqB;;;;;;0BAIlC,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;;oCAA+C;kDACpC,8OAAC;wCAAK,WAAU;kDAAe;;;;;;;;;;;;0CAE3D,8OAAC;gCACC,WAAW,CAAC,sCAAsC,EAChD,aAAa,mCAAmC,OAAO,YAAY,GAAG,6BAA6B,6BACpG,6CAA6C,CAAC;gCAC/C,aAAa;gCACb,aAAa;gCACb,YAAY;gCACZ,QAAQ;gCACR,SAAS,IAAM,SAAS,cAAc,CAAC,iBAAiB;;kDAExD,8OAAC;wCACC,MAAK;wCACL,IAAG;wCACH,UAAU;wCACV,WAAU;wCACV,QAAO;;;;;;oCAGR,SAAS,YAAY,iBACpB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;wDAAuB,MAAM;;;;;;kEAC7C,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAE,WAAU;0EAA6B,SAAS,YAAY,CAAC,IAAI;;;;;;0EACpE,8OAAC;gEAAE,WAAU;0EAAyB,eAAe,SAAS,YAAY,CAAC,IAAI;;;;;;;;;;;;;;;;;;0DAGnF,8OAAC;gDACC,MAAK;gDACL,SAAS,CAAC;oDACR,EAAE,eAAe;oDACjB;gDACF;gDACA,WAAU;0DAEV,cAAA,8OAAC,wMAAA,CAAA,UAAO;oDAAC,MAAM;;;;;;;;;;;;;;;;6DAInB,8OAAC;;0DACC,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;gDAA6B,MAAM;;;;;;0DACrD,8OAAC;gDAAE,WAAU;0DAAqB;;;;;;0DAClC,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;4BAI1C,OAAO,YAAY,kBAClB,8OAAC;gCAAE,WAAU;0CAA6B,OAAO,YAAY;;;;;;;;;;;;kCAKjE,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAM,SAAQ;4CAAkB,WAAU;;8DACzC,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAS;8DAE3B,8OAAC;oDACC,MAAK;oDACL,IAAG;oDACH,UAAU;oDACV,WAAU;oDACV,QAAQ;oDACR,QAAO;;;;;;;;;;;;;;;;;oCAKZ,SAAS,mBAAmB,CAAC,MAAM,GAAG,kBACrC,8OAAC;wCAAI,WAAU;kDACZ,SAAS,mBAAmB,CAAC,GAAG,CAAC,CAAC,MAAM,sBACvC,8OAAC;gDAAgB,WAAU;;kEACzB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;gEAAqB,MAAM;;;;;;0EAC3C,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAE,WAAU;kFAA6B,KAAK,IAAI;;;;;;kFACnD,8OAAC;wEAAE,WAAU;kFAAyB,eAAe,KAAK,IAAI;;;;;;;;;;;;;;;;;;kEAGlE,8OAAC;wDACC,MAAK;wDACL,SAAS,IAAM,qBAAqB;wDACpC,WAAU;kEAEV,cAAA,8OAAC,wMAAA,CAAA,UAAO;4DAAC,MAAM;;;;;;;;;;;;+CAbT;;;;;;;;;6DAmBd,8OAAC;wCAAE,WAAU;kDAAmC;;;;;;;;;;;;;;;;;;kCAMtD,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;oCAAwC,MAAM;;;;;;8CAClE,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAiC;;;;;;sDAC/C,8OAAC;4CAAE,WAAU;sDAA6B;;;;;;sDAG1C,8OAAC;4CACC,MAAK;4CACL,WAAU;;gDACX;8DACmB,8OAAC,0MAAA,CAAA,WAAQ;oDAAC,WAAU;oDAAO,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO3D,8OAAC;kCACC,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,IAAG;wCACH,MAAK;wCACL,SAAS,SAAS,gBAAgB;wCAClC,UAAU,CAAC,IAAM,eAAe;gDAAE,kBAAkB,EAAE,MAAM,CAAC,OAAO;4CAAC;wCACrE,WAAW,CAAC,sEAAsE,EAAE,OAAO,gBAAgB,GAAG,mBAAmB,IAAI;;;;;;;;;;;8CAGzI,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,SAAQ;4CAAmB,WAAU;;gDAA4B;8DAClC,8OAAC;oDAAK,WAAU;8DAAe;;;;;;;;;;;;sDAErE,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;wCAG5B,OAAO,gBAAgB,kBACtB,8OAAC;4CAAE,WAAU;sDAA6B,OAAO,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjF;uCAEe", "debugId": null}}, {"offset": {"line": 1762, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/fwu_incubation%28react%2Blaravel%29/incubation/frontend/src/app/components/proposal/steps/ReviewStep.tsx"], "sourcesContent": ["\"use client\"\r\nimport { ProposalFormData } from '../MultiStepForm';\r\nimport { Check, File } from 'lucide-react';\r\n\r\ninterface ReviewStepProps {\r\n  formData: ProposalFormData;\r\n}\r\n\r\nconst ReviewStep: React.FC<ReviewStepProps> = ({ formData }) => {\r\n  const formatFileSize = (bytes: number) => {\r\n    if (bytes === 0) return '0 Bytes';\r\n    const k = 1024;\r\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\r\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\r\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\r\n  };\r\n\r\n  return (\r\n    <div>\r\n      <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">Review Your Proposal</h2>\r\n      <p className=\"text-gray-600 mb-8\">\r\n        Please review all the information before submitting your proposal. You can go back to make changes if needed.\r\n      </p>\r\n      \r\n      <div className=\"space-y-8\">\r\n        {/* Project Details Review */}\r\n        <div className=\"border border-gray-200 rounded-lg overflow-hidden\">\r\n          <div className=\"bg-purple-50 px-6 py-4 border-b border-gray-200\">\r\n            <h3 className=\"text-lg font-semibold text-purple-800\">Project Details</h3>\r\n          </div>\r\n          <div className=\"p-6 space-y-4\">\r\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n              <div>\r\n                <p className=\"text-sm font-medium text-gray-500\">Project Title</p>\r\n                <p className=\"text-gray-800\">{formData.projectTitle}</p>\r\n              </div>\r\n              <div>\r\n                <p className=\"text-sm font-medium text-gray-500\">Category</p>\r\n                <p className=\"text-gray-800\">{formData.projectCategory}</p>\r\n              </div>\r\n              <div>\r\n                <p className=\"text-sm font-medium text-gray-500\">Estimated Duration</p>\r\n                <p className=\"text-gray-800\">{formData.projectDuration}</p>\r\n              </div>\r\n              <div>\r\n                <p className=\"text-sm font-medium text-gray-500\">Estimated Budget</p>\r\n                <p className=\"text-gray-800\">{formData.projectBudget}</p>\r\n              </div>\r\n            </div>\r\n            <div>\r\n              <p className=\"text-sm font-medium text-gray-500\">Project Summary</p>\r\n              <p className=\"text-gray-800 whitespace-pre-line\">{formData.projectSummary}</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        \r\n        {/* Team Information Review */}\r\n        <div className=\"border border-gray-200 rounded-lg overflow-hidden\">\r\n          <div className=\"bg-purple-50 px-6 py-4 border-b border-gray-200\">\r\n            <h3 className=\"text-lg font-semibold text-purple-800\">Team Information</h3>\r\n          </div>\r\n          <div className=\"p-6 space-y-6\">\r\n            {/* Team Lead */}\r\n            <div>\r\n              <h4 className=\"font-medium text-gray-700 mb-3\">Team Lead / Principal Investigator</h4>\r\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n                <div>\r\n                  <p className=\"text-sm font-medium text-gray-500\">Name</p>\r\n                  <p className=\"text-gray-800\">{formData.teamLeadName}</p>\r\n                </div>\r\n                <div>\r\n                  <p className=\"text-sm font-medium text-gray-500\">Email</p>\r\n                  <p className=\"text-gray-800\">{formData.teamLeadEmail}</p>\r\n                </div>\r\n                <div>\r\n                  <p className=\"text-sm font-medium text-gray-500\">Phone</p>\r\n                  <p className=\"text-gray-800\">{formData.teamLeadPhone}</p>\r\n                </div>\r\n                <div>\r\n                  <p className=\"text-sm font-medium text-gray-500\">Affiliation</p>\r\n                  <p className=\"text-gray-800\">{formData.teamLeadAffiliation}</p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            \r\n            {/* Team Members */}\r\n            {formData.teamMembers.length > 0 && (\r\n              <div>\r\n                <h4 className=\"font-medium text-gray-700 mb-3\">Team Members</h4>\r\n                <div className=\"space-y-4\">\r\n                  {formData.teamMembers.map((member, index) => (\r\n                    member.name && (\r\n                      <div key={index} className=\"p-3 bg-gray-50 rounded-lg\">\r\n                        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-3\">\r\n                          <div>\r\n                            <p className=\"text-sm font-medium text-gray-500\">Name</p>\r\n                            <p className=\"text-gray-800\">{member.name}</p>\r\n                          </div>\r\n                          <div>\r\n                            <p className=\"text-sm font-medium text-gray-500\">Role</p>\r\n                            <p className=\"text-gray-800\">{member.role}</p>\r\n                          </div>\r\n                          <div>\r\n                            <p className=\"text-sm font-medium text-gray-500\">Expertise</p>\r\n                            <p className=\"text-gray-800\">{member.expertise}</p>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    )\r\n                  ))}\r\n                </div>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n        \r\n        {/* Documents Review */}\r\n        <div className=\"border border-gray-200 rounded-lg overflow-hidden\">\r\n          <div className=\"bg-purple-50 px-6 py-4 border-b border-gray-200\">\r\n            <h3 className=\"text-lg font-semibold text-purple-800\">Uploaded Documents</h3>\r\n          </div>\r\n          <div className=\"p-6 space-y-4\">\r\n            {/* Main Proposal */}\r\n            <div>\r\n              <h4 className=\"font-medium text-gray-700 mb-3\">Project Proposal Document</h4>\r\n              {formData.proposalFile ? (\r\n                <div className=\"flex items-center bg-gray-50 p-3 rounded-lg border border-gray-200\">\r\n                  <File className=\"text-purple-500 mr-3\" size={20} />\r\n                  <div>\r\n                    <p className=\"font-medium text-gray-800\">{formData.proposalFile.name}</p>\r\n                    <p className=\"text-sm text-gray-500\">{formatFileSize(formData.proposalFile.size)}</p>\r\n                  </div>\r\n                </div>\r\n              ) : (\r\n                <p className=\"text-red-500\">No proposal document uploaded</p>\r\n              )}\r\n            </div>\r\n            \r\n            {/* Additional Documents */}\r\n            <div>\r\n              <h4 className=\"font-medium text-gray-700 mb-3\">Additional Documents</h4>\r\n              {formData.additionalDocuments.length > 0 ? (\r\n                <div className=\"space-y-2\">\r\n                  {formData.additionalDocuments.map((file, index) => (\r\n                    <div key={index} className=\"flex items-center bg-gray-50 p-3 rounded-lg border border-gray-200\">\r\n                      <File className=\"text-gray-500 mr-3\" size={20} />\r\n                      <div>\r\n                        <p className=\"font-medium text-gray-800\">{file.name}</p>\r\n                        <p className=\"text-sm text-gray-500\">{formatFileSize(file.size)}</p>\r\n                      </div>\r\n                    </div>\r\n                  ))}\r\n                </div>\r\n              ) : (\r\n                <p className=\"text-gray-500 italic\">No additional documents uploaded</p>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n        \r\n        {/* Terms Agreement */}\r\n        <div className=\"flex items-center p-4 bg-green-50 rounded-lg border border-green-200\">\r\n          <Check className=\"text-green-500 mr-3\" size={20} />\r\n          <p className=\"text-green-800\">\r\n            You have agreed to the terms and conditions for submitting this proposal.\r\n          </p>\r\n        </div>\r\n        \r\n        {/* Final Confirmation */}\r\n        <div className=\"p-4 bg-yellow-50 rounded-lg border border-yellow-200\">\r\n          <p className=\"text-yellow-800\">\r\n            <strong>Important:</strong> Once submitted, you will receive a confirmation email with a copy of your proposal. You can make changes to your submission by contacting the FWU Incubation Center directly.\r\n          </p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ReviewStep;\r\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAFA;;;AAQA,MAAM,aAAwC,CAAC,EAAE,QAAQ,EAAE;IACzD,MAAM,iBAAiB,CAAC;QACtB,IAAI,UAAU,GAAG,OAAO;QACxB,MAAM,IAAI;QACV,MAAM,QAAQ;YAAC;YAAS;YAAM;YAAM;SAAK;QACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;QAChD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;IACzE;IAEA,qBACE,8OAAC;;0BACC,8OAAC;gBAAG,WAAU;0BAAwC;;;;;;0BACtD,8OAAC;gBAAE,WAAU;0BAAqB;;;;;;0BAIlC,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;;;;;;0CAExD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAE,WAAU;kEAAoC;;;;;;kEACjD,8OAAC;wDAAE,WAAU;kEAAiB,SAAS,YAAY;;;;;;;;;;;;0DAErD,8OAAC;;kEACC,8OAAC;wDAAE,WAAU;kEAAoC;;;;;;kEACjD,8OAAC;wDAAE,WAAU;kEAAiB,SAAS,eAAe;;;;;;;;;;;;0DAExD,8OAAC;;kEACC,8OAAC;wDAAE,WAAU;kEAAoC;;;;;;kEACjD,8OAAC;wDAAE,WAAU;kEAAiB,SAAS,eAAe;;;;;;;;;;;;0DAExD,8OAAC;;kEACC,8OAAC;wDAAE,WAAU;kEAAoC;;;;;;kEACjD,8OAAC;wDAAE,WAAU;kEAAiB,SAAS,aAAa;;;;;;;;;;;;;;;;;;kDAGxD,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;0DAAqC,SAAS,cAAc;;;;;;;;;;;;;;;;;;;;;;;;kCAM/E,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;;;;;;0CAExD,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAiC;;;;;;0DAC/C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAE,WAAU;0EAAoC;;;;;;0EACjD,8OAAC;gEAAE,WAAU;0EAAiB,SAAS,YAAY;;;;;;;;;;;;kEAErD,8OAAC;;0EACC,8OAAC;gEAAE,WAAU;0EAAoC;;;;;;0EACjD,8OAAC;gEAAE,WAAU;0EAAiB,SAAS,aAAa;;;;;;;;;;;;kEAEtD,8OAAC;;0EACC,8OAAC;gEAAE,WAAU;0EAAoC;;;;;;0EACjD,8OAAC;gEAAE,WAAU;0EAAiB,SAAS,aAAa;;;;;;;;;;;;kEAEtD,8OAAC;;0EACC,8OAAC;gEAAE,WAAU;0EAAoC;;;;;;0EACjD,8OAAC;gEAAE,WAAU;0EAAiB,SAAS,mBAAmB;;;;;;;;;;;;;;;;;;;;;;;;oCAM/D,SAAS,WAAW,CAAC,MAAM,GAAG,mBAC7B,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAiC;;;;;;0DAC/C,8OAAC;gDAAI,WAAU;0DACZ,SAAS,WAAW,CAAC,GAAG,CAAC,CAAC,QAAQ,QACjC,OAAO,IAAI,kBACT,8OAAC;wDAAgB,WAAU;kEACzB,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;;sFACC,8OAAC;4EAAE,WAAU;sFAAoC;;;;;;sFACjD,8OAAC;4EAAE,WAAU;sFAAiB,OAAO,IAAI;;;;;;;;;;;;8EAE3C,8OAAC;;sFACC,8OAAC;4EAAE,WAAU;sFAAoC;;;;;;sFACjD,8OAAC;4EAAE,WAAU;sFAAiB,OAAO,IAAI;;;;;;;;;;;;8EAE3C,8OAAC;;sFACC,8OAAC;4EAAE,WAAU;sFAAoC;;;;;;sFACjD,8OAAC;4EAAE,WAAU;sFAAiB,OAAO,SAAS;;;;;;;;;;;;;;;;;;uDAZ1C;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAyBxB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;;;;;;0CAExD,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAiC;;;;;;4CAC9C,SAAS,YAAY,iBACpB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;wDAAuB,MAAM;;;;;;kEAC7C,8OAAC;;0EACC,8OAAC;gEAAE,WAAU;0EAA6B,SAAS,YAAY,CAAC,IAAI;;;;;;0EACpE,8OAAC;gEAAE,WAAU;0EAAyB,eAAe,SAAS,YAAY,CAAC,IAAI;;;;;;;;;;;;;;;;;qEAInF,8OAAC;gDAAE,WAAU;0DAAe;;;;;;;;;;;;kDAKhC,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAiC;;;;;;4CAC9C,SAAS,mBAAmB,CAAC,MAAM,GAAG,kBACrC,8OAAC;gDAAI,WAAU;0DACZ,SAAS,mBAAmB,CAAC,GAAG,CAAC,CAAC,MAAM,sBACvC,8OAAC;wDAAgB,WAAU;;0EACzB,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;gEAAqB,MAAM;;;;;;0EAC3C,8OAAC;;kFACC,8OAAC;wEAAE,WAAU;kFAA6B,KAAK,IAAI;;;;;;kFACnD,8OAAC;wEAAE,WAAU;kFAAyB,eAAe,KAAK,IAAI;;;;;;;;;;;;;uDAJxD;;;;;;;;;qEAUd,8OAAC;gDAAE,WAAU;0DAAuB;;;;;;;;;;;;;;;;;;;;;;;;kCAO5C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,oMAAA,CAAA,QAAK;gCAAC,WAAU;gCAAsB,MAAM;;;;;;0CAC7C,8OAAC;gCAAE,WAAU;0CAAiB;;;;;;;;;;;;kCAMhC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,WAAU;;8CACX,8OAAC;8CAAO;;;;;;gCAAmB;;;;;;;;;;;;;;;;;;;;;;;;AAMvC;uCAEe", "debugId": null}}, {"offset": {"line": 2469, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/fwu_incubation%28react%2Blaravel%29/incubation/frontend/src/app/components/proposal/steps/SuccessStep.tsx"], "sourcesContent": ["\"use client\"\r\nimport { useState, useEffect } from 'react';\r\nimport Link from 'next/link';\r\nimport { CheckCircle, ArrowRight, Mail, Calendar, Clock } from 'lucide-react';\r\n\r\nconst SuccessStep: React.FC = () => {\r\n  const [isVisible, setIsVisible] = useState(false);\r\n\r\n  useEffect(() => {\r\n    setIsVisible(true);\r\n  }, []);\r\n\r\n  // Generate a random proposal ID\r\n  const proposalId = `FWU-${Math.floor(10000 + Math.random() * 90000)}`;\r\n  \r\n  // Current date + 14 days for review period\r\n  const reviewDate = new Date();\r\n  reviewDate.setDate(reviewDate.getDate() + 14);\r\n  const formattedReviewDate = reviewDate.toLocaleDateString('en-US', {\r\n    year: 'numeric',\r\n    month: 'long',\r\n    day: 'numeric',\r\n  });\r\n\r\n  return (\r\n    <div className={`text-center transition-all duration-1000 transform ${isVisible ? 'opacity-100 scale-100' : 'opacity-0 scale-95'}`}>\r\n      <div className=\"inline-flex items-center justify-center w-20 h-20 bg-green-100 rounded-full mb-6\">\r\n        <CheckCircle className=\"text-green-500\" size={40} />\r\n      </div>\r\n      \r\n      <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">Proposal Submitted Successfully!</h2>\r\n      <p className=\"text-xl text-gray-600 mb-8\">\r\n        Thank you for submitting your project proposal to the Far Western University Incubation Center.\r\n      </p>\r\n      \r\n      <div className=\"bg-white border border-gray-200 rounded-lg p-6 mb-8 max-w-md mx-auto\">\r\n        <div className=\"space-y-4\">\r\n          <div>\r\n            <p className=\"text-sm font-medium text-gray-500\">Proposal ID</p>\r\n            <p className=\"text-lg font-bold text-purple-600\">{proposalId}</p>\r\n          </div>\r\n          \r\n          <div className=\"flex items-center justify-center space-x-6\">\r\n            <div className=\"flex items-center\">\r\n              <Calendar className=\"text-purple-500 mr-2\" />\r\n              <div className=\"text-left\">\r\n                <p className=\"text-sm font-medium text-gray-500\">Submission Date</p>\r\n                <p className=\"text-gray-800\">{new Date().toLocaleDateString()}</p>\r\n              </div>\r\n            </div>\r\n            \r\n            <div className=\"flex items-center\">\r\n              <Clock className=\"text-purple-500 mr-2\" />\r\n              <div className=\"text-left\">\r\n                <p className=\"text-sm font-medium text-gray-500\">Review By</p>\r\n                <p className=\"text-gray-800\">{formattedReviewDate}</p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      \r\n      <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8 flex items-start max-w-md mx-auto\">\r\n        <Mail className=\"text-blue-500 mt-1 mr-4 flex-shrink-0\" size={24} />\r\n        <div className=\"text-left\">\r\n          <h3 className=\"font-medium text-blue-800 mb-1\">Check Your Email</h3>\r\n          <p className=\"text-blue-700 text-sm\">\r\n            We&apos;ve sent a confirmation email with your proposal details. Please keep this for your records.\r\n          </p>\r\n        </div>\r\n      </div>\r\n      \r\n      <p className=\"text-gray-600 mb-8\">\r\n        Our team will review your proposal and get back to you within 14 business days.\r\n        If you have any questions, please contact us at <a href=\"mailto:<EMAIL>\" className=\"text-purple-600 hover:text-purple-800 transition-colors\"><EMAIL></a>.\r\n      </p>\r\n      \r\n      <div className=\"flex flex-col sm:flex-row items-center justify-center gap-4\">\r\n        <Link \r\n          href=\"/projects\" \r\n          className=\"px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors flex items-center\"\r\n        >\r\n          Explore Projects <ArrowRight className=\"ml-2\" />\r\n        </Link>\r\n        \r\n        <Link \r\n          href=\"/\" \r\n          className=\"px-6 py-3 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-colors\"\r\n        >\r\n          Return to Home\r\n        </Link>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default SuccessStep;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;;AAKA,MAAM,cAAwB;IAC5B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,aAAa;IACf,GAAG,EAAE;IAEL,gCAAgC;IAChC,MAAM,aAAa,CAAC,IAAI,EAAE,KAAK,KAAK,CAAC,QAAQ,KAAK,MAAM,KAAK,QAAQ;IAErE,2CAA2C;IAC3C,MAAM,aAAa,IAAI;IACvB,WAAW,OAAO,CAAC,WAAW,OAAO,KAAK;IAC1C,MAAM,sBAAsB,WAAW,kBAAkB,CAAC,SAAS;QACjE,MAAM;QACN,OAAO;QACP,KAAK;IACP;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,mDAAmD,EAAE,YAAY,0BAA0B,sBAAsB;;0BAChI,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,2NAAA,CAAA,cAAW;oBAAC,WAAU;oBAAiB,MAAM;;;;;;;;;;;0BAGhD,8OAAC;gBAAG,WAAU;0BAAwC;;;;;;0BACtD,8OAAC;gBAAE,WAAU;0BAA6B;;;;;;0BAI1C,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAE,WAAU;8CAAoC;;;;;;8CACjD,8OAAC;oCAAE,WAAU;8CAAqC;;;;;;;;;;;;sCAGpD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,8OAAC;oDAAE,WAAU;8DAAiB,IAAI,OAAO,kBAAkB;;;;;;;;;;;;;;;;;;8CAI/D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,8OAAC;oDAAE,WAAU;8DAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOxC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,kMAAA,CAAA,OAAI;wBAAC,WAAU;wBAAwC,MAAM;;;;;;kCAC9D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAiC;;;;;;0CAC/C,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;0BAMzC,8OAAC;gBAAE,WAAU;;oBAAqB;kCAEgB,8OAAC;wBAAE,MAAK;wBAA+B,WAAU;kCAA0D;;;;;;oBAAyB;;;;;;;0BAGtL,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,4JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;;4BACX;0CACkB,8OAAC,kNAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;;;;;;;kCAGzC,8OAAC,4JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;kCACX;;;;;;;;;;;;;;;;;;AAMT;uCAEe", "debugId": null}}, {"offset": {"line": 2777, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/fwu_incubation%28react%2Blaravel%29/incubation/frontend/src/app/components/proposal/MultiStepForm.tsx"], "sourcesContent": ["\"use client\"\r\nimport { useState, useEffect } from 'react';\r\n\r\nimport { ChevronRight, ChevronLeft, Check } from \"lucide-react\";\r\nimport ProjectDetailsStep from './steps/ProjectDetailsStep';\r\nimport TeamInfoStep from './steps/TeamInfoStep';\r\nimport ProposalUploadStep from './steps/ProposalUploadStep';\r\nimport ReviewStep from './steps/ReviewStep';\r\nimport SuccessStep from './steps/SuccessStep';\r\n\r\n// Form data interface\r\nexport interface ProposalFormData {\r\n  // Project Details\r\n  projectTitle: string;\r\n  projectCategory: string;\r\n  projectSummary: string;\r\n  projectDuration: string;\r\n  projectBudget: string;\r\n  \r\n  // Team Information\r\n  teamLeadName: string;\r\n  teamLeadEmail: string;\r\n  teamLeadPhone: string;\r\n  teamLeadAffiliation: string;\r\n  teamMembers: {\r\n    name: string;\r\n    role: string;\r\n    expertise: string;\r\n  }[];\r\n  \r\n  // Proposal Upload\r\n  proposalFile: File | null;\r\n  additionalDocuments: File[];\r\n  agreementChecked: boolean;\r\n}\r\n\r\nconst MultiStepForm: React.FC = () => {\r\n  const [currentStep, setCurrentStep] = useState(1);\r\n  const [isVisible, setIsVisible] = useState(false);\r\n  const [formData, setFormData] = useState<ProposalFormData>({\r\n    // Project Details\r\n    projectTitle: '',\r\n    projectCategory: '',\r\n    projectSummary: '',\r\n    projectDuration: '',\r\n    projectBudget: '',\r\n    \r\n    // Team Information\r\n    teamLeadName: '',\r\n    teamLeadEmail: '',\r\n    teamLeadPhone: '',\r\n    teamLeadAffiliation: '',\r\n    teamMembers: [{ name: '', role: '', expertise: '' }],\r\n    \r\n    // Proposal Upload\r\n    proposalFile: null,\r\n    additionalDocuments: [],\r\n    agreementChecked: false,\r\n  });\r\n  \r\n  const [errors, setErrors] = useState<Record<string, string>>({});\r\n  const [isSubmitting, setIsSubmitting] = useState(false);\r\n  useEffect(() => {\r\n    setIsVisible(true);\r\n  }, []);\r\n\r\n  const validateStep = (step: number): boolean => {\r\n    const newErrors: Record<string, string> = {};\r\n    \r\n    if (step === 1) {\r\n      // Validate Project Details\r\n      if (!formData.projectTitle.trim()) newErrors.projectTitle = 'Project title is required';\r\n      if (!formData.projectCategory) newErrors.projectCategory = 'Please select a category';\r\n      if (!formData.projectSummary.trim()) newErrors.projectSummary = 'Project summary is required';\r\n      if (!formData.projectDuration) newErrors.projectDuration = 'Project duration is required';\r\n      if (!formData.projectBudget) newErrors.projectBudget = 'Estimated budget is required';\r\n    } else if (step === 2) {\r\n      // Validate Team Information\r\n      if (!formData.teamLeadName.trim()) newErrors.teamLeadName = 'Team lead name is required';\r\n      if (!formData.teamLeadEmail.trim()) newErrors.teamLeadEmail = 'Email is required';\r\n      else if (!/\\S+@\\S+\\.\\S+/.test(formData.teamLeadEmail)) newErrors.teamLeadEmail = 'Email is invalid';\r\n      if (!formData.teamLeadPhone.trim()) newErrors.teamLeadPhone = 'Phone number is required';\r\n      if (!formData.teamLeadAffiliation.trim()) newErrors.teamLeadAffiliation = 'Affiliation is required';\r\n      \r\n      // Validate team members\r\n      formData.teamMembers.forEach((member, index) => {\r\n        if (member.name.trim() && (!member.role.trim() || !member.expertise.trim())) {\r\n          newErrors[`teamMember${index}`] = 'All team member fields are required';\r\n        }\r\n      });\r\n    } else if (step === 3) {\r\n      // Validate Proposal Upload\r\n      if (!formData.proposalFile) newErrors.proposalFile = 'Proposal document is required';\r\n      if (!formData.agreementChecked) newErrors.agreementChecked = 'You must agree to the terms';\r\n    }\r\n    \r\n    setErrors(newErrors);\r\n    return Object.keys(newErrors).length === 0;\r\n  };\r\n\r\n  const handleNext = () => {\r\n    if (validateStep(currentStep)) {\r\n      setCurrentStep(prev => prev + 1);\r\n      window.scrollTo(0, 0);\r\n    }\r\n  };\r\n\r\n  const handlePrevious = () => {\r\n    setCurrentStep(prev => prev - 1);\r\n    window.scrollTo(0, 0);\r\n  };\r\n\r\n  const handleSubmit = async () => {\r\n    if (validateStep(currentStep)) {\r\n      setIsSubmitting(true);\r\n      \r\n      // Simulate API call\r\n      await new Promise(resolve => setTimeout(resolve, 2000));\r\n      \r\n      setIsSubmitting(false);\r\n      setCurrentStep(5); // Move to success step\r\n      window.scrollTo(0, 0);\r\n    }\r\n  };\r\n\r\n  const updateFormData = (newData: Partial<ProposalFormData>) => {\r\n    setFormData(prev => ({ ...prev, ...newData }));\r\n  };\r\n\r\n  const renderStep = () => {\r\n    switch (currentStep) {\r\n      case 1:\r\n        return <ProjectDetailsStep formData={formData} updateFormData={updateFormData} errors={errors} />;\r\n      case 2:\r\n        return <TeamInfoStep formData={formData} updateFormData={updateFormData} errors={errors} />;\r\n      case 3:\r\n        return <ProposalUploadStep formData={formData} updateFormData={updateFormData} errors={errors} />;\r\n      case 4:\r\n        return <ReviewStep formData={formData} />;\r\n      case 5:\r\n        return <SuccessStep />;\r\n      default:\r\n        return null;\r\n    }\r\n  };\r\n\r\n  const steps = [\r\n    { number: 1, name: 'Project Details' },\r\n    { number: 2, name: 'Team Information' },\r\n    { number: 3, name: 'Upload Documents' },\r\n    { number: 4, name: 'Review & Submit' },\r\n  ];\r\n\r\n  return (\r\n    <section className=\"py-16 bg-white\">\r\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\r\n        <div className={`max-w-4xl mx-auto transition-all duration-1000 transform ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>\r\n          {/* Progress Steps */}\r\n          {currentStep < 5 && (\r\n            <div className=\"mb-12\">\r\n              <div className=\"flex justify-between items-center\">\r\n                {steps.map((step) => (\r\n                  <div key={step.number} className=\"flex flex-col items-center\">\r\n                    <div \r\n                      className={`w-10 h-10 rounded-full flex items-center justify-center text-white font-bold ${\r\n                        currentStep === step.number \r\n                          ? 'bg-purple-600' \r\n                          : currentStep > step.number \r\n                            ? 'bg-green-500' \r\n                            : 'bg-gray-300'\r\n                      }`}\r\n                    >\r\n                      {currentStep > step.number ? <Check size={20} /> : step.number}\r\n                    </div>\r\n                    <span className={`text-sm mt-2 ${currentStep === step.number ? 'text-purple-600 font-medium' : 'text-gray-500'}`}>\r\n                      {step.name}\r\n                    </span>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n              <div className=\"relative mt-4\">\r\n                <div className=\"absolute top-0 left-0 right-0 h-1 bg-gray-200 rounded-full\"></div>\r\n                <div \r\n                  className=\"absolute top-0 left-0 h-1 bg-purple-600 rounded-full transition-all duration-500\"\r\n                  style={{ width: `${((currentStep - 1) / (steps.length - 1)) * 100}%` }}\r\n                ></div>\r\n              </div>\r\n            </div>\r\n          )}\r\n          \r\n          {/* Form Steps */}\r\n          <div className=\"bg-white rounded-xl shadow-lg p-6 md:p-8\">\r\n            {renderStep()}\r\n            \r\n            {/* Navigation Buttons */}\r\n            {currentStep < 5 && (\r\n              <div className=\"mt-8 flex justify-between\">\r\n                {currentStep > 1 ? (\r\n                  <button\r\n                    onClick={handlePrevious}\r\n                    className=\"px-6 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-colors flex items-center\"\r\n                  >\r\n                    <ChevronLeft className=\"mr-2\" /> Previous\r\n                  </button>\r\n                ) : (\r\n                  <div></div> // Empty div to maintain flex spacing\r\n                )}\r\n                \r\n                {currentStep < 4 ? (\r\n                  <button\r\n                    onClick={handleNext}\r\n                    className=\"px-6 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors flex items-center\"\r\n                  >\r\n                    Next <ChevronRight className=\"ml-2\" />\r\n                  </button>\r\n                ) : currentStep === 4 ? (\r\n                  <button\r\n                    onClick={handleSubmit}\r\n                    disabled={isSubmitting}\r\n                    className={`px-6 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors flex items-center ${isSubmitting ? 'opacity-70 cursor-not-allowed' : ''}`}\r\n                  >\r\n                    {isSubmitting ? (\r\n                      <>\r\n                        <svg className=\"animate-spin -ml-1 mr-3 h-5 w-5 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\r\n                          <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\r\n                          <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\r\n                        </svg>\r\n                        Submitting...\r\n                      </>\r\n                    ) : (\r\n                      <>Submit Proposal</>\r\n                    )}\r\n                  </button>\r\n                ) : null}\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\nexport default MultiStepForm;\r\n"], "names": [], "mappings": ";;;;AACA;AAEA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AARA;;;;;;;;;AAoCA,MAAM,gBAA0B;IAC9B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB;QACzD,kBAAkB;QAClB,cAAc;QACd,iBAAiB;QACjB,gBAAgB;QAChB,iBAAiB;QACjB,eAAe;QAEf,mBAAmB;QACnB,cAAc;QACd,eAAe;QACf,eAAe;QACf,qBAAqB;QACrB,aAAa;YAAC;gBAAE,MAAM;gBAAI,MAAM;gBAAI,WAAW;YAAG;SAAE;QAEpD,kBAAkB;QAClB,cAAc;QACd,qBAAqB,EAAE;QACvB,kBAAkB;IACpB;IAEA,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAC9D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,aAAa;IACf,GAAG,EAAE;IAEL,MAAM,eAAe,CAAC;QACpB,MAAM,YAAoC,CAAC;QAE3C,IAAI,SAAS,GAAG;YACd,2BAA2B;YAC3B,IAAI,CAAC,SAAS,YAAY,CAAC,IAAI,IAAI,UAAU,YAAY,GAAG;YAC5D,IAAI,CAAC,SAAS,eAAe,EAAE,UAAU,eAAe,GAAG;YAC3D,IAAI,CAAC,SAAS,cAAc,CAAC,IAAI,IAAI,UAAU,cAAc,GAAG;YAChE,IAAI,CAAC,SAAS,eAAe,EAAE,UAAU,eAAe,GAAG;YAC3D,IAAI,CAAC,SAAS,aAAa,EAAE,UAAU,aAAa,GAAG;QACzD,OAAO,IAAI,SAAS,GAAG;YACrB,4BAA4B;YAC5B,IAAI,CAAC,SAAS,YAAY,CAAC,IAAI,IAAI,UAAU,YAAY,GAAG;YAC5D,IAAI,CAAC,SAAS,aAAa,CAAC,IAAI,IAAI,UAAU,aAAa,GAAG;iBACzD,IAAI,CAAC,eAAe,IAAI,CAAC,SAAS,aAAa,GAAG,UAAU,aAAa,GAAG;YACjF,IAAI,CAAC,SAAS,aAAa,CAAC,IAAI,IAAI,UAAU,aAAa,GAAG;YAC9D,IAAI,CAAC,SAAS,mBAAmB,CAAC,IAAI,IAAI,UAAU,mBAAmB,GAAG;YAE1E,wBAAwB;YACxB,SAAS,WAAW,CAAC,OAAO,CAAC,CAAC,QAAQ;gBACpC,IAAI,OAAO,IAAI,CAAC,IAAI,MAAM,CAAC,CAAC,OAAO,IAAI,CAAC,IAAI,MAAM,CAAC,OAAO,SAAS,CAAC,IAAI,EAAE,GAAG;oBAC3E,SAAS,CAAC,CAAC,UAAU,EAAE,OAAO,CAAC,GAAG;gBACpC;YACF;QACF,OAAO,IAAI,SAAS,GAAG;YACrB,2BAA2B;YAC3B,IAAI,CAAC,SAAS,YAAY,EAAE,UAAU,YAAY,GAAG;YACrD,IAAI,CAAC,SAAS,gBAAgB,EAAE,UAAU,gBAAgB,GAAG;QAC/D;QAEA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAEA,MAAM,aAAa;QACjB,IAAI,aAAa,cAAc;YAC7B,eAAe,CAAA,OAAQ,OAAO;YAC9B,OAAO,QAAQ,CAAC,GAAG;QACrB;IACF;IAEA,MAAM,iBAAiB;QACrB,eAAe,CAAA,OAAQ,OAAO;QAC9B,OAAO,QAAQ,CAAC,GAAG;IACrB;IAEA,MAAM,eAAe;QACnB,IAAI,aAAa,cAAc;YAC7B,gBAAgB;YAEhB,oBAAoB;YACpB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,gBAAgB;YAChB,eAAe,IAAI,uBAAuB;YAC1C,OAAO,QAAQ,CAAC,GAAG;QACrB;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,GAAG,OAAO;YAAC,CAAC;IAC9C;IAEA,MAAM,aAAa;QACjB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,oKAAA,CAAA,UAAkB;oBAAC,UAAU;oBAAU,gBAAgB;oBAAgB,QAAQ;;;;;;YACzF,KAAK;gBACH,qBAAO,8OAAC,8JAAA,CAAA,UAAY;oBAAC,UAAU;oBAAU,gBAAgB;oBAAgB,QAAQ;;;;;;YACnF,KAAK;gBACH,qBAAO,8OAAC,oKAAA,CAAA,UAAkB;oBAAC,UAAU;oBAAU,gBAAgB;oBAAgB,QAAQ;;;;;;YACzF,KAAK;gBACH,qBAAO,8OAAC,4JAAA,CAAA,UAAU;oBAAC,UAAU;;;;;;YAC/B,KAAK;gBACH,qBAAO,8OAAC,6JAAA,CAAA,UAAW;;;;;YACrB;gBACE,OAAO;QACX;IACF;IAEA,MAAM,QAAQ;QACZ;YAAE,QAAQ;YAAG,MAAM;QAAkB;QACrC;YAAE,QAAQ;YAAG,MAAM;QAAmB;QACtC;YAAE,QAAQ;YAAG,MAAM;QAAmB;QACtC;YAAE,QAAQ;YAAG,MAAM;QAAkB;KACtC;IAED,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAW,CAAC,yDAAyD,EAAE,YAAY,8BAA8B,4BAA4B;;oBAE/I,cAAc,mBACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACZ,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC;wCAAsB,WAAU;;0DAC/B,8OAAC;gDACC,WAAW,CAAC,6EAA6E,EACvF,gBAAgB,KAAK,MAAM,GACvB,kBACA,cAAc,KAAK,MAAM,GACvB,iBACA,eACN;0DAED,cAAc,KAAK,MAAM,iBAAG,8OAAC,oMAAA,CAAA,QAAK;oDAAC,MAAM;;;;;2DAAS,KAAK,MAAM;;;;;;0DAEhE,8OAAC;gDAAK,WAAW,CAAC,aAAa,EAAE,gBAAgB,KAAK,MAAM,GAAG,gCAAgC,iBAAiB;0DAC7G,KAAK,IAAI;;;;;;;uCAbJ,KAAK,MAAM;;;;;;;;;;0CAkBzB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,OAAO,GAAG,AAAC,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,MAAM,GAAG,CAAC,IAAK,IAAI,CAAC,CAAC;wCAAC;;;;;;;;;;;;;;;;;;kCAO7E,8OAAC;wBAAI,WAAU;;4BACZ;4BAGA,cAAc,mBACb,8OAAC;gCAAI,WAAU;;oCACZ,cAAc,kBACb,8OAAC;wCACC,SAAS;wCACT,WAAU;;0DAEV,8OAAC,oNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;4CAAS;;;;;;6DAGlC,8OAAC;;;;6CAAW,qCAAqC;;oCAGlD,cAAc,kBACb,8OAAC;wCACC,SAAS;wCACT,WAAU;;4CACX;0DACM,8OAAC,sNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;;;;;+CAE7B,gBAAgB,kBAClB,8OAAC;wCACC,SAAS;wCACT,UAAU;wCACV,WAAW,CAAC,sGAAsG,EAAE,eAAe,kCAAkC,IAAI;kDAExK,6BACC;;8DACE,8OAAC;oDAAI,WAAU;oDAA6C,OAAM;oDAA6B,MAAK;oDAAO,SAAQ;;sEACjH,8OAAC;4DAAO,WAAU;4DAAa,IAAG;4DAAK,IAAG;4DAAK,GAAE;4DAAK,QAAO;4DAAe,aAAY;;;;;;sEACxF,8OAAC;4DAAK,WAAU;4DAAa,MAAK;4DAAe,GAAE;;;;;;;;;;;;gDAC/C;;yEAIR;sDAAE;;;;;;+CAGJ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQpB;uCAEe", "debugId": null}}, {"offset": {"line": 3167, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/fwu_incubation%28react%2Blaravel%29/incubation/frontend/src/app/submit-proposal/page.tsx"], "sourcesContent": ["\"use client\"\r\nimport ProposalHero from '../components/proposal/ProposalHero';\r\nimport MultiStepForm from '../components/proposal/MultiStepForm';\r\nimport { ArrowRight, CheckCircle, Clock,FileText } from 'lucide-react';\r\n\r\nexport default function SubmitProposalPage() {\r\n  return (\r\n    <main className=\"bg-white\">\r\n      {/* Hero Section */}\r\n      <ProposalHero />\r\n      \r\n      {/* Process Overview */}\r\n      <section className=\"py-16 bg-gray-50\">\r\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\r\n          <div className=\"max-w-4xl mx-auto text-center mb-12\">\r\n            <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">Proposal Submission Process</h2>\r\n            <p className=\"text-xl text-gray-600\">\r\n              Follow these simple steps to submit your project proposal to the Far Western University Incubation Center.\r\n            </p>\r\n          </div>\r\n          \r\n          <div className=\"grid md:grid-cols-3 gap-8 max-w-5xl mx-auto\">\r\n            {/* Step 1 */}\r\n            <div className=\"bg-white rounded-xl shadow-md p-6 text-center\">\r\n              <div className=\"w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4\">\r\n                <FileText className=\"text-purple-600\" size={28} />\r\n              </div>\r\n              <h3 className=\"text-xl font-bold text-gray-900 mb-2\">1. Complete the Form</h3>\r\n              <p className=\"text-gray-600\">\r\n                Fill out the multi-step form with details about your project and team members.\r\n              </p>\r\n            </div>\r\n            \r\n            {/* Step 2 */}\r\n            <div className=\"bg-white rounded-xl shadow-md p-6 text-center\">\r\n              <div className=\"w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4\">\r\n                <CheckCircle className=\"text-purple-600\" size={28} />\r\n              </div>\r\n              <h3 className=\"text-xl font-bold text-gray-900 mb-2\">2. Submit Proposal</h3>\r\n              <p className=\"text-gray-600\">\r\n                Upload your detailed proposal document and any supporting materials.\r\n              </p>\r\n            </div>\r\n            \r\n            {/* Step 3 */}\r\n            <div className=\"bg-white rounded-xl shadow-md p-6 text-center\">\r\n              <div className=\"w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4\">\r\n                <Clock className=\"text-purple-600\" size={28} />\r\n              </div>\r\n              <h3 className=\"text-xl font-bold text-gray-900 mb-2\">3. Review Process</h3>\r\n              <p className=\"text-gray-600\">\r\n                Our team will review your proposal and contact you within 14 business days.\r\n              </p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n      \r\n      {/* Multi-step Form */}\r\n      <MultiStepForm />\r\n      \r\n      {/* Eligibility Criteria */}\r\n      <section className=\"py-16 bg-gray-50\">\r\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\r\n          <div className=\"max-w-4xl mx-auto\">\r\n            <div className=\"bg-white rounded-xl shadow-lg overflow-hidden\">\r\n              <div className=\"bg-purple-600 px-6 py-4\">\r\n                <h2 className=\"text-2xl font-bold text-white\">Eligibility Criteria</h2>\r\n              </div>\r\n              <div className=\"p-6 md:p-8\">\r\n                <p className=\"text-gray-600 mb-6\">\r\n                  To be eligible for consideration by the Far Western University Incubation Center, your project proposal should meet the following criteria:\r\n                </p>\r\n                \r\n                <ul className=\"space-y-4\">\r\n                  <li className=\"flex\">\r\n                    <div className=\"flex-shrink-0 w-6 h-6 bg-green-100 text-green-500 rounded-full flex items-center justify-center mr-3 mt-0.5\">\r\n                      <CheckCircle size={14} />\r\n                    </div>\r\n                    <p className=\"text-gray-700\">\r\n                      <strong>Innovation:</strong> The project should demonstrate innovative approaches or solutions to existing problems.\r\n                    </p>\r\n                  </li>\r\n                  <li className=\"flex\">\r\n                    <div className=\"flex-shrink-0 w-6 h-6 bg-green-100 text-green-500 rounded-full flex items-center justify-center mr-3 mt-0.5\">\r\n                      <CheckCircle size={14} />\r\n                    </div>\r\n                    <p className=\"text-gray-700\">\r\n                      <strong>Feasibility:</strong> The project should be technically and economically feasible with a clear implementation plan.\r\n                    </p>\r\n                  </li>\r\n                  <li className=\"flex\">\r\n                    <div className=\"flex-shrink-0 w-6 h-6 bg-green-100 text-green-500 rounded-full flex items-center justify-center mr-3 mt-0.5\">\r\n                      <CheckCircle size={14} />\r\n                    </div>\r\n                    <p className=\"text-gray-700\">\r\n                      <strong>Impact:</strong> The project should have potential for positive social, economic, or environmental impact, particularly in the Far Western region of Nepal.\r\n                    </p>\r\n                  </li>\r\n                  <li className=\"flex\">\r\n                    <div className=\"flex-shrink-0 w-6 h-6 bg-green-100 text-green-500 rounded-full flex items-center justify-center mr-3 mt-0.5\">\r\n                      <CheckCircle size={14} />\r\n                    </div>\r\n                    <p className=\"text-gray-700\">\r\n                      <strong>Team Capability:</strong> The project team should have the necessary skills and commitment to execute the project successfully.\r\n                    </p>\r\n                  </li>\r\n                  <li className=\"flex\">\r\n                    <div className=\"flex-shrink-0 w-6 h-6 bg-green-100 text-green-500 rounded-full flex items-center justify-center mr-3 mt-0.5\">\r\n                      <CheckCircle size={14} />\r\n                    </div>\r\n                    <p className=\"text-gray-700\">\r\n                      <strong>Sustainability:</strong> The project should have potential for long-term sustainability beyond the initial funding period.\r\n                    </p>\r\n                  </li>\r\n                </ul>\r\n                \r\n                <div className=\"mt-8 p-4 bg-blue-50 rounded-lg border border-blue-200\">\r\n                  <p className=\"text-blue-800 text-sm\">\r\n                    <strong>Note:</strong> Priority will be given to projects that address local challenges and contribute to the development of the Far Western region of Nepal.\r\n                  </p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n      \r\n      {/* FAQ Section */}\r\n      <section className=\"py-16 bg-white\">\r\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\r\n          <div className=\"max-w-4xl mx-auto text-center mb-12\">\r\n            <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">Frequently Asked Questions</h2>\r\n            <p className=\"text-xl text-gray-600\">\r\n              Find answers to common questions about the proposal submission process.\r\n            </p>\r\n          </div>\r\n          \r\n          <div className=\"max-w-3xl mx-auto space-y-6\">\r\n            <div className=\"bg-gray-50 rounded-lg p-6\">\r\n              <h3 className=\"text-lg font-bold text-gray-900 mb-2\">What types of projects does the FWU Incubation Center support?</h3>\r\n              <p className=\"text-gray-600\">\r\n                We support a wide range of innovative projects across various sectors including technology, agriculture, healthcare, education, and environment. We particularly encourage projects that address local challenges in the Far Western region of Nepal.\r\n              </p>\r\n            </div>\r\n            \r\n            <div className=\"bg-gray-50 rounded-lg p-6\">\r\n              <h3 className=\"text-lg font-bold text-gray-900 mb-2\">How long does the review process take?</h3>\r\n              <p className=\"text-gray-600\">\r\n                Our team typically reviews proposals within 14 business days. You will receive a confirmation email immediately after submission, and we will contact you with the review outcome afterward.\r\n              </p>\r\n            </div>\r\n            \r\n            <div className=\"bg-gray-50 rounded-lg p-6\">\r\n              <h3 className=\"text-lg font-bold text-gray-900 mb-2\">What support does the incubation center provide?</h3>\r\n              <p className=\"text-gray-600\">\r\n                Selected projects receive mentorship, workspace, technical resources, networking opportunities, and potential funding support. The specific support package is tailored to each project&apos;s needs.\r\n              </p>\r\n            </div>\r\n            \r\n            <div className=\"bg-gray-50 rounded-lg p-6\">\r\n              <h3 className=\"text-lg font-bold text-gray-900 mb-2\">Can I submit multiple project proposals?</h3>\r\n              <p className=\"text-gray-600\">\r\n                Yes, you can submit multiple project proposals. However, each proposal should be submitted separately with its own complete set of information and documentation.\r\n              </p>\r\n            </div>\r\n          </div>\r\n          \r\n          <div className=\"text-center mt-10\">\r\n            <p className=\"text-gray-600 mb-4\">\r\n              Still have questions about the proposal submission process?\r\n            </p>\r\n            <a \r\n              href=\"mailto:<EMAIL>\" \r\n              className=\"inline-flex items-center px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors\"\r\n            >\r\n              Contact Us <ArrowRight className=\"ml-2\" />\r\n            </a>\r\n          </div>\r\n        </div>\r\n      </section>\r\n    </main>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAHA;;;;;AAKe,SAAS;IACtB,qBACE,8OAAC;QAAK,WAAU;;0BAEd,8OAAC,qJAAA,CAAA,UAAY;;;;;0BAGb,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAKvC,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,8MAAA,CAAA,WAAQ;gDAAC,WAAU;gDAAkB,MAAM;;;;;;;;;;;sDAE9C,8OAAC;4CAAG,WAAU;sDAAuC;;;;;;sDACrD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAM/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,2NAAA,CAAA,cAAW;gDAAC,WAAU;gDAAkB,MAAM;;;;;;;;;;;sDAEjD,8OAAC;4CAAG,WAAU;sDAAuC;;;;;;sDACrD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAM/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;gDAAkB,MAAM;;;;;;;;;;;sDAE3C,8OAAC;4CAAG,WAAU;sDAAuC;;;;;;sDACrD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASrC,8OAAC,sJAAA,CAAA,UAAa;;;;;0BAGd,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAG,WAAU;kDAAgC;;;;;;;;;;;8CAEhD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;sDAAqB;;;;;;sDAIlC,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,2NAAA,CAAA,cAAW;gEAAC,MAAM;;;;;;;;;;;sEAErB,8OAAC;4DAAE,WAAU;;8EACX,8OAAC;8EAAO;;;;;;gEAAoB;;;;;;;;;;;;;8DAGhC,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,2NAAA,CAAA,cAAW;gEAAC,MAAM;;;;;;;;;;;sEAErB,8OAAC;4DAAE,WAAU;;8EACX,8OAAC;8EAAO;;;;;;gEAAqB;;;;;;;;;;;;;8DAGjC,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,2NAAA,CAAA,cAAW;gEAAC,MAAM;;;;;;;;;;;sEAErB,8OAAC;4DAAE,WAAU;;8EACX,8OAAC;8EAAO;;;;;;gEAAgB;;;;;;;;;;;;;8DAG5B,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,2NAAA,CAAA,cAAW;gEAAC,MAAM;;;;;;;;;;;sEAErB,8OAAC;4DAAE,WAAU;;8EACX,8OAAC;8EAAO;;;;;;gEAAyB;;;;;;;;;;;;;8DAGrC,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,2NAAA,CAAA,cAAW;gEAAC,MAAM;;;;;;;;;;;sEAErB,8OAAC;4DAAE,WAAU;;8EACX,8OAAC;8EAAO;;;;;;gEAAwB;;;;;;;;;;;;;;;;;;;sDAKtC,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAE,WAAU;;kEACX,8OAAC;kEAAO;;;;;;oDAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUpC,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAKvC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAuC;;;;;;sDACrD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAK/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAuC;;;;;;sDACrD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAK/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAuC;;;;;;sDACrD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAK/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAuC;;;;;;sDACrD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;sCAMjC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAGlC,8OAAC;oCACC,MAAK;oCACL,WAAU;;wCACX;sDACY,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO/C", "debugId": null}}]}