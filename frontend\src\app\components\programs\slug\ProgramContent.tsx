"use client"
import { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import Image from 'next/image';

import {
  ArrowLeft,
  BookOpen,
  Users,
  Calendar,
  FileText,
  Clock,
  MapPin,
  Star,
  Award,
  Target,
  Sparkles,
  ChevronRight,
  Play,
  Download,
  Share2
} from 'lucide-react';
import ProgramApplication from '../ProgramApplication';
import ProgramFaculty from '../ProgramFaculty';
import ProgramCurriculum from '../ProgramCurriculum';
import ProgramDetails from '../ProgramDetails';
import { Program } from '../../../../types/program.types';

interface ProgramContentProps {
  program: Program | null
}

const ProgramContent: React.FC<ProgramContentProps> = ({ program }: ProgramContentProps) => {
  const [activeTab, setActiveTab] = useState('details');
  const [isVisible, setIsVisible] = useState(false);
  const [imageLoaded, setImageLoaded] = useState(false);
  const heroRef = useRef(null);

  useEffect(() => {
    setIsVisible(true);
  }, []);

  if (!program) {
    return (
      <main className="container mx-auto py-16 px-4 text-center">
        <div className="max-w-lg mx-auto bg-white rounded-xl shadow-lg p-8">
          <div className="text-blue-600 mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-4">Program Not Found</h1>
          <p className="text-gray-600 mb-8">
            The academic program you are looking for does not exist or has been moved to a different location.
          </p>
          <Link 
            href="/programs" 
            className="inline-flex items-center bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
          >
            <ArrowLeft className="mr-2" /> Back to Programs
          </Link>
        </div>
      </main>
    );
  }

  return (
    <main className="bg-white">
      {/* Enhanced Hero Section */}
      <section
        ref={heroRef}
        className="relative min-h-[100vh] flex items-center overflow-hidden bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900"
      >
        {/* Dynamic Background */}
        <div className="absolute inset-0">
          {/* Hero Background Image */}
          {program.heroImageUrl && (
            <Image
              src={program.heroImageUrl}
              alt={program.title}
              fill
              priority
              className="object-cover opacity-30"
              onLoad={() => setImageLoaded(true)}
            />
          )}

          {/* Gradient Overlays */}
          <div className="absolute inset-0 bg-gradient-to-br from-blue-900/95 via-indigo-900/90 to-purple-900/95"></div>
          <div className="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent"></div>

          {/* Animated Elements */}
          <div className="absolute inset-0 overflow-hidden">
            {/* Floating Particles */}
            {[...Array(30)].map((_, i) => (
              <div
                key={i}
                className={`absolute rounded-full ${
                  i % 4 === 0 ? 'bg-blue-400/30' :
                  i % 4 === 1 ? 'bg-purple-400/30' :
                  i % 4 === 2 ? 'bg-indigo-400/30' : 'bg-pink-400/30'
                }`}
                style={{
                  width: `${Math.random() * 6 + 2}px`,
                  height: `${Math.random() * 6 + 2}px`,
                  top: `${Math.random() * 100}%`,
                  left: `${Math.random() * 100}%`,
                  animation: `float-particle ${
                    8 + Math.random() * 15
                  }s infinite ease-in-out`,
                  animationDelay: `${Math.random() * 8}s`,
                }}
              ></div>
            ))}

            {/* Glowing Orbs */}
            {[...Array(5)].map((_, i) => (
              <div
                key={`orb-${i}`}
                className={`absolute rounded-full blur-2xl ${
                  i % 3 === 0 ? 'bg-blue-500/15' :
                  i % 3 === 1 ? 'bg-purple-500/15' : 'bg-indigo-500/15'
                }`}
                style={{
                  width: `${Math.random() * 200 + 100}px`,
                  height: `${Math.random() * 200 + 100}px`,
                  top: `${Math.random() * 100}%`,
                  left: `${Math.random() * 100}%`,
                  animation: `pulse-glow ${
                    12 + Math.random() * 8
                  }s infinite ease-in-out`,
                  animationDelay: `${Math.random() * 5}s`,
                }}
              ></div>
            ))}
          </div>
        </div>

        {/* Main Content */}
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-20">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Left Content */}
            <div className={`transition-all duration-1000 transform ${
              isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
            }`}>
              {/* Back Navigation */}
              <Link
                href="/programs"
                className="inline-flex items-center text-blue-200 hover:text-white mb-8 transition-all duration-300 group"
              >
                <ArrowLeft className="mr-2 group-hover:-translate-x-1 transition-transform duration-300" />
                Back to Programs
              </Link>

              {/* Program Badges */}
              <div className="mb-6 flex flex-wrap gap-3">
                <span className="px-4 py-2 rounded-full bg-gradient-to-r from-blue-600/80 to-blue-700/80 backdrop-blur-md text-blue-100 border border-blue-400/30 text-sm font-semibold">
                  {program.category.charAt(0).toUpperCase() + program.category.slice(1)}
                </span>
                <span className="px-4 py-2 rounded-full bg-gradient-to-r from-purple-600/80 to-purple-700/80 backdrop-blur-md text-purple-100 border border-purple-400/30 text-sm font-semibold">
                  {program.level.charAt(0).toUpperCase() + program.level.slice(1).replace('-', ' ')}
                </span>
                <span className="px-4 py-2 rounded-full bg-gradient-to-r from-indigo-600/80 to-indigo-700/80 backdrop-blur-md text-indigo-100 border border-indigo-400/30 text-sm font-semibold">
                  {program.format.charAt(0).toUpperCase() + program.format.slice(1)}
                </span>
              </div>

              {/* Main Heading */}
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-black mb-6 leading-tight">
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 animate-gradient-x">
                  {program.title}
                </span>
              </h1>

              {/* Description */}
              <p className="text-xl text-blue-100 mb-8 max-w-2xl leading-relaxed">
                {program.shortDescription}
              </p>

              {/* Program Details */}
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-8">
                <div className="flex items-center space-x-3 p-3 bg-white/5 backdrop-blur-sm rounded-xl border border-white/10">
                  <Clock className="text-blue-400" size={20} />
                  <div>
                    <div className="text-white font-medium text-sm">Duration</div>
                    <div className="text-blue-200 text-sm">{program.duration}</div>
                  </div>
                </div>
                <div className="flex items-center space-x-3 p-3 bg-white/5 backdrop-blur-sm rounded-xl border border-white/10">
                  <Users className="text-purple-400" size={20} />
                  <div>
                    <div className="text-white font-medium text-sm">Capacity</div>
                    <div className="text-purple-200 text-sm">{program.capacity} participants</div>
                  </div>
                </div>
                <div className="flex items-center space-x-3 p-3 bg-white/5 backdrop-blur-sm rounded-xl border border-white/10">
                  <MapPin className="text-indigo-400" size={20} />
                  <div>
                    <div className="text-white font-medium text-sm">Location</div>
                    <div className="text-indigo-200 text-sm">{program.location.venue}</div>
                  </div>
                </div>
                <div className="flex items-center space-x-3 p-3 bg-white/5 backdrop-blur-sm rounded-xl border border-white/10">
                  <Calendar className="text-pink-400" size={20} />
                  <div>
                    <div className="text-white font-medium text-sm">Schedule</div>
                    <div className="text-pink-200 text-sm">{program.schedule}</div>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex flex-col sm:flex-row gap-4">
                <Link
                  href="/apply"
                  className="group relative px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-bold rounded-2xl transition-all duration-300 hover:shadow-2xl hover:shadow-blue-500/25 hover:-translate-y-1 flex items-center justify-center overflow-hidden"
                >
                  <div className="absolute inset-0 bg-gradient-to-r from-blue-700 to-purple-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  <span className="relative z-10">Apply Now</span>
                  <ChevronRight className="relative z-10 ml-2 group-hover:translate-x-1 transition-transform duration-300" />
                </Link>

                <button className="group relative px-8 py-4 bg-white/10 hover:bg-white/20 backdrop-blur-md text-white font-bold rounded-2xl transition-all duration-300 border-2 border-white/30 hover:border-white/50 hover:shadow-2xl hover:shadow-white/10 hover:-translate-y-1 flex items-center justify-center overflow-hidden">
                  <div className="absolute inset-0 bg-white/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  <Download className="relative z-10 mr-2" size={20} />
                  <span className="relative z-10">Download Brochure</span>
                </button>
              </div>
            </div>

            {/* Right Content - Program Highlights */}
            <div className={`transition-all duration-1000 delay-300 transform ${
              isVisible ? 'opacity-100 translate-x-0' : 'opacity-0 translate-x-10'
            }`}>
              <div className="relative">
                {/* Main Card */}
                <div className="bg-white/10 backdrop-blur-md rounded-3xl p-8 border border-white/20 shadow-2xl">
                  <div className="flex items-center justify-between mb-6">
                    <h3 className="text-2xl font-bold text-white">Program Highlights</h3>
                    <Sparkles className="text-yellow-400" size={24} />
                  </div>

                  {/* Benefits List */}
                  <div className="space-y-4">
                    {program.benefits.slice(0, 4).map((benefit, index) => (
                      <div key={index} className="flex items-start space-x-3">
                        <div className="w-6 h-6 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                          <Star className="text-white" size={12} />
                        </div>
                        <span className="text-blue-100 leading-relaxed">{benefit}</span>
                      </div>
                    ))}
                  </div>

                  {/* Stats */}
                  {program.stats && (
                    <div className="mt-8 pt-6 border-t border-white/20">
                      <div className="grid grid-cols-2 gap-4">
                        {program.stats.successRate && (
                          <div className="text-center">
                            <div className="text-2xl font-bold text-green-400">{program.stats.successRate}%</div>
                            <div className="text-sm text-green-200">Success Rate</div>
                          </div>
                        )}
                        {program.stats.totalParticipants && (
                          <div className="text-center">
                            <div className="text-2xl font-bold text-blue-400">{program.stats.totalParticipants}+</div>
                            <div className="text-sm text-blue-200">Participants</div>
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>

                {/* Floating Elements */}
                <div className="absolute -top-4 -right-4 w-16 h-16 bg-gradient-to-r from-yellow-400 to-orange-400 rounded-2xl flex items-center justify-center shadow-lg">
                  <Award className="text-white" size={24} />
                </div>

                <div className="absolute -bottom-4 -left-4 w-12 h-12 bg-gradient-to-r from-green-400 to-blue-400 rounded-xl flex items-center justify-center shadow-lg">
                  <Target className="text-white" size={16} />
                </div>
              </div>
            </div>
          </div>
        </div>
        
        {/* Wave divider */}
        <div className="absolute bottom-0 left-0 right-0">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 120" className="w-full h-auto">
            <path
              fill="#ffffff"
              fillOpacity="1"
              d="M0,64L80,69.3C160,75,320,85,480,80C640,75,800,53,960,48C1120,43,1280,53,1360,58.7L1440,64L1440,120L1360,120C1280,120,1120,120,960,120C800,120,640,120,480,120C320,120,160,120,80,120L0,120Z"
            ></path>
          </svg>
        </div>
      </section>
      
      {/* Navigation Tabs */}
      <section className="bg-white sticky top-20 z-20 shadow-md">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex overflow-x-auto py-4 scrollbar-hide">
            <button
              onClick={() => setActiveTab('details')}
              className={`px-5 py-2 rounded-lg mr-2 whitespace-nowrap transition-colors ${
                activeTab === 'details' 
                  ? 'bg-blue-600 text-white' 
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              <BookOpen className="inline mr-2 mb-0.5" />
              Program Details
            </button>
            <button
              onClick={() => setActiveTab('curriculum')}
              className={`px-5 py-2 rounded-lg mr-2 whitespace-nowrap transition-colors ${
                activeTab === 'curriculum' 
                  ? 'bg-blue-600 text-white' 
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              <FileText className="inline mr-2 mb-0.5" />
              Curriculum
            </button>
            <button
              onClick={() => setActiveTab('faculty')}
              className={`px-5 py-2 rounded-lg mr-2 whitespace-nowrap transition-colors ${
                activeTab === 'faculty' 
                  ? 'bg-blue-600 text-white' 
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              <Users className="inline mr-2 mb-0.5" />
              Faculty
            </button>
            <button
              onClick={() => setActiveTab('application')}
              className={`px-5 py-2 rounded-lg whitespace-nowrap transition-colors ${
                activeTab === 'application' 
                  ? 'bg-blue-600 text-white' 
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              <Calendar className="inline mr-2 mb-0.5" />
              Application
            </button>
          </div>
        </div>
      </section>
      
      {/* Content Sections */}
      <div className={`transition-opacity duration-300 ${activeTab === 'details' ? 'opacity-100' : 'opacity-0 hidden'}`}>
        <ProgramDetails program={program} />
      </div>
      
      <div className={`transition-opacity duration-300 ${activeTab === 'curriculum' ? 'opacity-100' : 'opacity-0 hidden'}`}>
        <ProgramCurriculum program={program} />
      </div>
      
      <div className={`transition-opacity duration-300 ${activeTab === 'faculty' ? 'opacity-100' : 'opacity-0 hidden'}`}>
        <ProgramFaculty program={program} />
      </div>
      
      <div className={`transition-opacity duration-300 ${activeTab === 'application' ? 'opacity-100' : 'opacity-0 hidden'}`}>
        <ProgramApplication program={program} />
      </div>
      
      {/* CTA Section */}
      <section className="py-16 bg-gradient-to-r from-blue-900 to-indigo-900 text-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-3xl mx-auto text-center">
            <h2 className="text-3xl font-bold mb-6">Ready to Apply?</h2>
            <p className="text-xl text-indigo-100 mb-8">
              {/* Take the next step in your academic journey with Far Western University. Applications for the {program.nextIntake} intake are now open. */}
            </p>
            <div className="flex flex-col sm:flex-row justify-center gap-4">
              <Link 
                href="/submit-application" 
                className="px-6 py-3 bg-white text-blue-900 font-bold rounded-lg hover:bg-blue-50 transition-colors"
              >
                Apply Now
              </Link>
              <button 
                className="px-6 py-3 bg-blue-700 text-white font-bold rounded-lg hover:bg-blue-800 transition-colors"
              >
                Download Brochure
              </button>
            </div>
          </div>
        </div>

        {/* Wave Divider */}
        <div className="absolute bottom-0 left-0 right-0">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 120" className="w-full h-auto">
            <defs>
              <linearGradient id="waveGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                <stop offset="0%" stopColor="#ffffff" stopOpacity="1"/>
                <stop offset="50%" stopColor="#f8fafc" stopOpacity="1"/>
                <stop offset="100%" stopColor="#ffffff" stopOpacity="1"/>
              </linearGradient>
            </defs>
            <path
              fill="url(#waveGradient)"
              d="M0,64L80,69.3C160,75,320,85,480,80C640,75,800,53,960,48C1120,43,1280,53,1360,58.7L1440,64L1440,120L1360,120C1280,120,1120,120,960,120C800,120,640,120,480,120C320,120,160,120,80,120L0,120Z"
            ></path>
          </svg>
        </div>
      </section>

      {/* Enhanced Navigation Tabs */}
      <section className="bg-white sticky top-0 z-30 shadow-lg border-b border-gray-200">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex overflow-x-auto py-4 scrollbar-hide">
            {[
              { id: 'details', label: 'Program Details', icon: BookOpen },
              { id: 'curriculum', label: 'Curriculum', icon: FileText },
              { id: 'faculty', label: 'Faculty', icon: Users },
              { id: 'application', label: 'Application', icon: Calendar }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center px-6 py-3 rounded-xl mr-3 whitespace-nowrap transition-all duration-300 font-semibold ${
                  activeTab === tab.id
                    ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-lg transform scale-105'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200 hover:scale-105'
                }`}
              >
                <tab.icon className="mr-2" size={18} />
                {tab.label}
              </button>
            ))}
          </div>
        </div>
      </section>

      {/* Content Sections */}
      <div className={`transition-all duration-500 ${activeTab === 'details' ? 'opacity-100' : 'opacity-0 hidden'}`}>
        <ProgramDetails program={program} />
      </div>

      <div className={`transition-all duration-500 ${activeTab === 'curriculum' ? 'opacity-100' : 'opacity-0 hidden'}`}>
        <ProgramCurriculum program={program} />
      </div>

      <div className={`transition-all duration-500 ${activeTab === 'faculty' ? 'opacity-100' : 'opacity-0 hidden'}`}>
        <ProgramFaculty program={program} />
      </div>

      <div className={`transition-all duration-500 ${activeTab === 'application' ? 'opacity-100' : 'opacity-0 hidden'}`}>
        <ProgramApplication program={program} />
      </div>

      {/* Enhanced CTA Section */}
      <section className="py-20 bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900 text-white relative overflow-hidden">
        {/* Background Elements */}
        <div className="absolute inset-0">
          <div className="absolute top-20 right-20 w-64 h-64 bg-blue-500/10 rounded-full blur-3xl"></div>
          <div className="absolute bottom-20 left-20 w-64 h-64 bg-purple-500/10 rounded-full blur-3xl"></div>
        </div>

        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="max-w-4xl mx-auto text-center">
            <div className="inline-block mb-6">
              <div className="flex items-center justify-center space-x-3 bg-white/10 backdrop-blur-md px-6 py-3 rounded-2xl border border-white/20">
                <Sparkles className="text-yellow-400" size={20} />
                <span className="text-white font-semibold">Ready to Start?</span>
                <Star className="text-yellow-400" size={16} />
              </div>
            </div>

            <h2 className="text-4xl md:text-5xl font-bold mb-6">
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400">
                Join {program.title}
              </span>
            </h2>

            <p className="text-xl text-blue-100 mb-10 max-w-2xl mx-auto leading-relaxed">
              Take the next step in your journey. Applications are now open for upcoming cohorts.
              Don't miss this opportunity to transform your future.
            </p>

            <div className="flex flex-col sm:flex-row justify-center gap-6 mb-12">
              <Link
                href="/apply"
                className="group relative px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-bold rounded-2xl transition-all duration-300 hover:shadow-2xl hover:shadow-blue-500/25 hover:-translate-y-1 flex items-center justify-center overflow-hidden"
              >
                <div className="absolute inset-0 bg-gradient-to-r from-blue-700 to-purple-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <span className="relative z-10">Apply Now</span>
                <ChevronRight className="relative z-10 ml-2 group-hover:translate-x-1 transition-transform duration-300" />
              </Link>

              <button className="group relative px-8 py-4 bg-white/10 hover:bg-white/20 backdrop-blur-md text-white font-bold rounded-2xl transition-all duration-300 border-2 border-white/30 hover:border-white/50 hover:shadow-2xl hover:shadow-white/10 hover:-translate-y-1 flex items-center justify-center overflow-hidden">
                <div className="absolute inset-0 bg-white/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <Share2 className="relative z-10 mr-2" size={20} />
                <span className="relative z-10">Share Program</span>
              </button>
            </div>

            {/* Next Dates */}
            {program.upcomingDates && program.upcomingDates.length > 0 && (
              <div className="bg-white/5 backdrop-blur-sm rounded-2xl p-6 border border-white/10">
                <h3 className="text-xl font-bold mb-4 text-white">Upcoming Dates</h3>
                <div className="grid md:grid-cols-2 gap-4">
                  {program.upcomingDates.slice(0, 2).map((date, index) => (
                    <div key={index} className="bg-white/5 rounded-xl p-4 border border-white/10">
                      <div className="text-blue-300 font-semibold">{date.date}</div>
                      <div className="text-white font-medium">{date.title}</div>
                      {date.description && (
                        <div className="text-blue-200 text-sm mt-1">{date.description}</div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </section>

      {/* Custom CSS for animations */}
      <style jsx global>{`
        @keyframes float-particle {
          0%, 100% { transform: translate(0, 0) rotate(0deg); }
          25% { transform: translate(10px, -10px) rotate(90deg); }
          50% { transform: translate(-5px, -15px) rotate(180deg); }
          75% { transform: translate(-10px, 5px) rotate(270deg); }
        }

        @keyframes pulse-glow {
          0%, 100% { opacity: 0.3; transform: scale(1); }
          50% { opacity: 0.6; transform: scale(1.1); }
        }

        @keyframes gradient-x {
          0%, 100% { background-position: 0% 50%; }
          50% { background-position: 100% 50%; }
        }

        .animate-gradient-x {
          background-size: 200% 200%;
          animation: gradient-x 3s ease infinite;
        }

        .scrollbar-hide {
          -ms-overflow-style: none;
          scrollbar-width: none;
        }

        .scrollbar-hide::-webkit-scrollbar {
          display: none;
        }
      `}</style>
    </main>
  );
};

export default ProgramContent;
