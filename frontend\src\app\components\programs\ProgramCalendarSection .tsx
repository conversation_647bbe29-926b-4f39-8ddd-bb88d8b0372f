"use client"
import UpcomingEventCard, { UpcomingEvent } from './UpcomingEventCard';
import { Calendar, Filter, Search, Sparkles, Star, } from 'lucide-react';
import { useState, useEffect } from 'react';
import { programsData } from '../../../data/programsData';

// Convert program data to events
const generateEventsFromPrograms = (): UpcomingEvent[] => {
  const events: UpcomingEvent[] = [];

  programsData.forEach(program => {
    program.upcomingDates.forEach(date => {
      events.push({
        id: `${program.id}-${date.date}`,
        date: new Date(date.date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' }).toUpperCase(),
        fullDate: date.date,
        time: program.schedule,
        title: date.title,
        type: program.category.charAt(0).toUpperCase() + program.category.slice(1),
        location: program.location.isOnline ? 'Online' : program.location.venue,
        description: date.description || program.shortDescription,
        color: getColorByCategory(program.category),
        detailedDescription: program.longDescription,
        registrationLink: `/programs/${program.slug}`,
        capacity: date.capacity,
        status: date.status
      });
    });
  });

  return events.sort((a, b) => new Date(a.fullDate).getTime() - new Date(b.fullDate).getTime());
};

const getColorByCategory = (category: string): string => {
  const colors = {
    'bootcamp': 'bg-blue-600',
    'hackathon': 'bg-teal-600',
    'workshop': 'bg-purple-600',
    'demo-day': 'bg-amber-600',
    'mentorship': 'bg-green-600',
    'accelerator': 'bg-red-600'
  };
  return colors[category as keyof typeof colors] || 'bg-gray-600';
};

// Enhanced Dummy Data with more details (keeping some for variety)
const additionalEventsData: UpcomingEvent[] = [
  {
    id: 'ue1',
    date: 'NOV 15',
    fullDate: 'November 15, 2024',
    time: '09:00 AM - 05:00 PM',
    title: 'Design Thinking Workshop for Innovators',
    type: 'Workshop',
    location: 'FWU Incubation Hall A',
    description: 'Learn human-centered design principles to create impactful solutions.',
    color: 'bg-purple-500',
    detailedDescription: 'This full-day workshop will introduce participants to the core principles of design thinking and how to apply them to solve complex problems. Led by industry experts, you will learn techniques for empathizing with users, defining problems, ideating solutions, prototyping, and testing. By the end of the workshop, you will have a practical toolkit for approaching innovation challenges with a human-centered mindset.',
    registrationLink: '/register/design-thinking-workshop'
  },
  {
    id: 'ue2',
    date: 'NOV 28',
    fullDate: 'November 28, 2024',
    title: 'Application Deadline: Winter Cohort 2025',
    type: 'Deadline',
    description: 'Submit your startup applications for the upcoming winter incubation program.',
    color: 'bg-red-500',
    detailedDescription: 'The Winter Cohort 2025 is our flagship 12-week incubation program designed for early-stage startups ready to accelerate their growth. Selected startups will receive mentorship, workspace, seed funding opportunities, and access to our network of investors and industry partners. Applications must include your business plan, team information, current traction, and growth strategy.',
    registrationLink: '/apply/winter-cohort-2025'
  },
  {
    id: 'ue3',
    date: 'DEC 05',
    fullDate: 'December 05, 2024',
    time: '02:00 PM - 04:00 PM',
    title: 'Investor Connect: Meet & Greet',
    type: 'Networking',
    location: 'Online (Zoom)',
    description: 'An opportunity for selected startups to interact with potential investors.',
    color: 'bg-teal-500',
    detailedDescription: 'This exclusive virtual networking event brings together promising startups and potential investors in a structured yet casual format. Each startup will have the opportunity to introduce their venture in a brief pitch, followed by breakout rooms for more in-depth conversations with interested investors. This is not a formal pitching event but rather a chance to build relationships that could lead to future investment opportunities.',
    registrationLink: '/register/investor-connect'
  },
  {
    id: 'ue4',
    date: 'DEC 12',
    fullDate: 'December 12-14, 2024',
    title: 'FinTech Hackathon Challenge',
    type: 'Hackathon',
    location: 'FWU Main Auditorium',
    description: 'Develop innovative solutions for the financial technology sector and win prizes.',
    color: 'bg-blue-600',
    detailedDescription: 'Join us for an intensive 48-hour hackathon focused on developing innovative solutions for the financial technology sector. Participants will form teams to tackle real-world challenges provided by our industry partners. Cash prizes totaling $10,000 will be awarded to the top three teams, with the first-place team also receiving incubation support to develop their solution further. All skill levels are welcome, and mentors will be available throughout the event.',
    registrationLink: '/register/fintech-hackathon'
  },
  {
    id: 'ue5',
    date: 'JAN 10',
    fullDate: 'January 10, 2025',
    time: '10:00 AM - 12:00 PM',
    title: 'Funding Strategies for Early-Stage Startups',
    type: 'Workshop',
    location: 'FWU Incubation Center',
    description: 'Learn about different funding options and how to approach investors effectively.',
    color: 'bg-purple-500',
    detailedDescription: 'This workshop will cover various funding strategies available to early-stage startups, including bootstrapping, angel investment, venture capital, grants, and crowdfunding. Our expert speakers will share insights on when to pursue each option, how to prepare your startup for investment, and tactics for successful fundraising. The session will include case studies of successful funding journeys and common pitfalls to avoid.',
    registrationLink: '/register/funding-strategies-workshop'
  },
];

// Helper to sort events by fullDate (simplistic, assumes "Month Day, Year" format)
const sortEvents = (events: UpcomingEvent[]): UpcomingEvent[] => {
  return events.sort((a, b) => new Date(a.fullDate).getTime() - new Date(b.fullDate).getTime());
};

// Get unique event types for filtering
const getUniqueEventTypes = (events: UpcomingEvent[]): string[] => {
  return Array.from(new Set(events.map(event => event.type))).sort();
};

const ProgramCalendarSection = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedType, setSelectedType] = useState('');
  const [isVisible, setIsVisible] = useState(false);
  const [allEvents, setAllEvents] = useState<UpcomingEvent[]>([]);

  useEffect(() => {
    setIsVisible(true);
    // Combine program events with additional events
    const programEvents = generateEventsFromPrograms();
    const combinedEvents = [...programEvents, ...additionalEventsData];
    setAllEvents(combinedEvents);
  }, []);

  const eventTypes = getUniqueEventTypes(allEvents);

  // Filter events based on search term and selected type
  const filteredEvents = allEvents
    .filter((event: UpcomingEvent) =>
      event.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      event.description.toLowerCase().includes(searchTerm.toLowerCase())
    )
    .filter((event: UpcomingEvent) =>
      selectedType ? event.type === selectedType : true
    );

  const sortedEvents = sortEvents(filteredEvents);

  return (
    <section className="py-20 md:py-28 bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-100 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-400/10 to-purple-400/10 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-indigo-400/10 to-pink-400/10 rounded-full blur-3xl"></div>

        {/* Floating Elements */}
        {[...Array(15)].map((_, i) => (
          <div
            key={i}
            className={`absolute rounded-full ${
              i % 4 === 0 ? 'bg-blue-400/20' :
              i % 4 === 1 ? 'bg-purple-400/20' :
              i % 4 === 2 ? 'bg-indigo-400/20' : 'bg-pink-400/20'
            }`}
            style={{
              width: `${Math.random() * 4 + 2}px`,
              height: `${Math.random() * 4 + 2}px`,
              top: `${Math.random() * 100}%`,
              left: `${Math.random() * 100}%`,
              animation: `float-gentle ${
                8 + Math.random() * 12
              }s infinite ease-in-out`,
              animationDelay: `${Math.random() * 8}s`,
            }}
          ></div>
        ))}
      </div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Enhanced Section Header */}
        <div className={`text-center mb-16 transition-all duration-1000 transform ${
          isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
        }`}>
          <div className="inline-block mb-6">
            <div className="flex items-center justify-center space-x-3 bg-white/80 backdrop-blur-md px-6 py-3 rounded-2xl border border-white/50 shadow-lg">
              <Calendar className="text-blue-600" size={20} />
              <span className="text-blue-600 font-semibold tracking-wide">STAY INFORMED</span>
              <Sparkles className="text-purple-600" size={16} />
            </div>
          </div>

          <h2 className="text-4xl md:text-5xl lg:text-6xl font-black mb-6 leading-tight">
            <span className="text-gray-900">Upcoming </span>
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600">
              Events
            </span>
            <br />
            <span className="text-gray-700 text-2xl md:text-3xl font-normal">
              at FWU Incubation Center
            </span>
          </h2>

          <p className="text-xl text-gray-700 max-w-3xl mx-auto leading-relaxed">
            Stay updated with our latest programs, workshops, and networking events.
            <span className="font-semibold text-blue-600"> Never miss an opportunity</span> to grow your startup.
          </p>
        </div>

        {/* Enhanced Search and Filter Controls */}
        <div className={`mb-16 transition-all duration-1000 delay-300 transform ${
          isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
        }`}>
          <div className="bg-white/80 backdrop-blur-md rounded-3xl p-8 border border-white/50 shadow-xl">
            <div className="flex flex-col lg:flex-row gap-6">
              {/* Search Bar */}
              <div className="relative flex-grow">
                <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                  <Search className="text-blue-500" size={20} />
                </div>
                <input
                  type="text"
                  placeholder="Search events, workshops, or topics..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="block w-full pl-12 pr-4 py-4 border-2 border-gray-200 rounded-2xl focus:ring-2 focus:ring-blue-400 focus:border-blue-400 transition-all duration-300 bg-white/90 backdrop-blur-sm text-gray-900 placeholder-gray-500"
                />
              </div>

              {/* Event Type Filter */}
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                  <Filter className="text-purple-500" size={20} />
                </div>
                <select
                  value={selectedType}
                  onChange={(e) => setSelectedType(e.target.value)}
                  className="block w-full lg:w-64 pl-12 pr-10 py-4 border-2 border-gray-200 rounded-2xl focus:ring-2 focus:ring-purple-400 focus:border-purple-400 transition-all duration-300 appearance-none bg-white/90 backdrop-blur-sm text-gray-900"
                >
                  <option value="">All Event Types</option>
                  {eventTypes.map(type => (
                    <option key={type} value={type}>{type}</option>
                  ))}
                </select>
              </div>
            </div>

            {/* Active Filters Display */}
            {(selectedType || searchTerm) && (
              <div className="mt-6 flex flex-wrap gap-3">
                {selectedType && (
                  <span className="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-purple-100 text-purple-800 border border-purple-200">
                    Type: {selectedType}
                    <button
                      onClick={() => setSelectedType('')}
                      className="ml-2 text-purple-600 hover:text-purple-800 transition-colors duration-200"
                    >
                      ×
                    </button>
                  </span>
                )}
                {searchTerm && (
                  <span className="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-blue-100 text-blue-800 border border-blue-200">
                    Search: &quot;{searchTerm}&quot;
                    <button
                      onClick={() => setSearchTerm('')}
                      className="ml-2 text-blue-600 hover:text-blue-800 transition-colors duration-200"
                    >
                      ×
                    </button>
                  </span>
                )}
              </div>
            )}

            {/* Quick Stats */}
            <div className="mt-6 pt-6 border-t border-gray-200">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">{allEvents.length}</div>
                  <div className="text-sm text-gray-600">Total Events</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-600">{eventTypes.length}</div>
                  <div className="text-sm text-gray-600">Event Types</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-indigo-600">{sortedEvents.length}</div>
                  <div className="text-sm text-gray-600">Filtered Results</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-pink-600">
                    {allEvents.filter(e => e.status === 'upcoming').length}
                  </div>
                  <div className="text-sm text-gray-600">Upcoming</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Enhanced Events List */}
        {sortedEvents.length > 0 ? (
          <div className="space-y-8">
            {sortedEvents.map((event, index) => (
              <div
                key={event.id}
                className={`transition-all duration-700 transform ${
                  isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
                }`}
                style={{
                  transitionDelay: `${600 + index * 150}ms`
                }}
              >
                <UpcomingEventCard event={event} />
              </div>
            ))}
          </div>
        ) : (
          <div className={`text-center py-20 transition-all duration-1000 transform ${
            isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
          }`}>
            <div className="bg-white/80 backdrop-blur-md rounded-3xl p-12 border border-white/50 shadow-xl">
              <div className="w-24 h-24 mx-auto bg-gradient-to-r from-blue-100 to-purple-100 rounded-full flex items-center justify-center mb-6">
                <Calendar className="text-blue-500" size={32} />
              </div>
              <h3 className="text-3xl font-bold text-gray-900 mb-4">No Events Found</h3>
              <p className="text-gray-600 max-w-md mx-auto mb-6 leading-relaxed">
                {searchTerm || selectedType
                  ? "No events match your current search criteria. Try adjusting your filters or search terms."
                  : "No upcoming events scheduled at the moment. Please check back soon for exciting new programs!"}
              </p>
              {(searchTerm || selectedType) && (
                <button
                  onClick={() => {
                    setSearchTerm('');
                    setSelectedType('');
                  }}
                  className="px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-xl hover:shadow-lg transition-all duration-300 font-semibold"
                >
                  Clear All Filters
                </button>
              )}
            </div>
          </div>
        )}

        {/* Enhanced Calendar Subscription */}
        <div className={`mt-24 transition-all duration-1000 delay-700 transform ${
          isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
        }`}>
          <div className="relative bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900 rounded-3xl shadow-2xl overflow-hidden">
            {/* Background Elements */}
            <div className="absolute inset-0">
              <div className="absolute top-20 right-20 w-64 h-64 bg-blue-500/10 rounded-full blur-3xl"></div>
              <div className="absolute bottom-20 left-20 w-64 h-64 bg-purple-500/10 rounded-full blur-3xl"></div>
            </div>

            <div className="relative z-10 p-12 lg:p-16 text-center">
              <div className="inline-block mb-6">
                <div className="flex items-center justify-center space-x-3 bg-white/10 backdrop-blur-md px-6 py-3 rounded-2xl border border-white/20">
                  <Calendar className="text-blue-400" size={20} />
                  <span className="text-white font-semibold">STAY CONNECTED</span>
                  <Star className="text-yellow-400" size={16} />
                </div>
              </div>

              <h3 className="text-3xl lg:text-4xl font-bold text-white mb-6 leading-tight">
                Never Miss an
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-400"> Opportunity</span>
              </h3>

              <p className="text-blue-100 text-lg mb-8 max-w-2xl mx-auto leading-relaxed">
                Subscribe to our calendar to receive automatic updates about upcoming events, workshops,
                and programs at the FWU Incubation Center. Stay ahead of the curve!
              </p>

              <div className="flex flex-col sm:flex-row justify-center gap-4 mb-8">
                <a
                  href="/subscribe-calendar"
                  className="group relative px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-bold rounded-2xl transition-all duration-300 hover:shadow-2xl hover:shadow-blue-500/25 hover:-translate-y-1 flex items-center justify-center overflow-hidden"
                >
                  <div className="absolute inset-0 bg-gradient-to-r from-blue-700 to-purple-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  <Calendar className="relative z-10 mr-2" size={20} />
                  <span className="relative z-10">Subscribe to Calendar</span>
                </a>

                <a
                  href="/events"
                  className="group relative px-8 py-4 bg-white/10 hover:bg-white/20 backdrop-blur-md text-white font-bold rounded-2xl transition-all duration-300 border-2 border-white/30 hover:border-white/50 hover:shadow-2xl hover:shadow-white/10 hover:-translate-y-1 flex items-center justify-center overflow-hidden"
                >
                  <div className="absolute inset-0 bg-white/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  <span className="relative z-10">View All Events</span>
                </a>
              </div>

              <p className="text-blue-200 text-sm">
                Compatible with Google Calendar, Outlook, Apple Calendar, and more
              </p>
            </div>
          </div>
        </div>

        {/* Custom CSS for animations */}
        <style jsx global>{`
          @keyframes float-gentle {
            0%, 100% { transform: translate(0, 0) rotate(0deg); }
            25% { transform: translate(5px, -5px) rotate(45deg); }
            50% { transform: translate(-3px, -8px) rotate(90deg); }
            75% { transform: translate(-5px, 3px) rotate(135deg); }
          }
        `}</style>
      </div>
    </section>
  );
};

export default ProgramCalendarSection;