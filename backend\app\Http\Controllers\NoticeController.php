<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Notice;

class NoticeController extends Controller
{
    //
    public function index()
    {
        $notices = Notice::paginate(10);
        // get the added_by name from admin table
        $notices->load('admin');
        return [
            'current_page' => $notices->currentPage(),
            'data' => $notices->items(),
            'total' => $notices->total(),
            'per_page' => $notices->perPage(),
            'last_page' => $notices->lastPage(),
        ];
    }
    public function store(Request $request)
    {
        $validate=$request->validate([
            'title'=>'required',
            'description'=>'required',
            'added_by'=>'required',
        ]);
        if(!$validate){
            return redirect()->back()->with('error','Please fill all the fields');
        }
        if($request->hasFile('file')){
            $file = $request->file('file');
            $file->store('notices', 'public');
            $notice->file = $file->hashName();
        }

        $notice = new Notice();
        $notice->title = $request->title;
        $notice->description = $request->description;
        $notice->added_by = $request->added_by;
        $notice->save();
        return ['status'=>'success','message'=>'Notice created successfully'];
    }
    public function show($id)
    {   
        $notice = Notice::find($id);
        $notice->load('admin');
        return [
            'notice'=>$notice,
            'admin'=>$notice->admin,
            'file'=>asset('storage/notices/'.$notice->file)
        ];
    }
    public function update(Request $request, $id)
    {  
        $notice = Notice::find($id);
        if($request->hasFile('file')){
            $file = $request->file('file');
            $file->store('notices', 'public');
            $notice->file = $file->hashName();
        }
        $notice->title = $request->title;
        $notice->description = $request->description;
        $notice->save();
        return ['status'=>'success','message'=>'Notice updated successfully'];
        // return 'Notice update';
    }
    public function destroy($id)

    {
        //delete file from storage
        $notice = Notice::find($id);
        if($notice->file){
            $image_path = public_path('storage/notices/'.$notice->file);
            if(file_exists($image_path)){
                unlink($image_path);
            }
        }
        $notice = Notice::destroy($id);
        if($notice){
            return ['status'=>'success','message'=>'Notice deleted successfully'];
        }
        // return 'Notice destroy';
    }
}
