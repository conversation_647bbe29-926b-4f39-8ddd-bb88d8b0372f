(()=>{var e={};e.id=988,e.ids=[988],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},25605:(e,t,r)=>{Promise.resolve().then(r.bind(r,41087))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},41087:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>eP});var s=r(60687),i=r(48340),a=r(41550),l=r(97992),n=r(70334),o=r(58887),d=r(43210),c=e=>"checkbox"===e.type,u=e=>e instanceof Date,m=e=>null==e;let h=e=>"object"==typeof e;var x=e=>!m(e)&&!Array.isArray(e)&&h(e)&&!u(e),f=e=>x(e)&&e.target?c(e.target)?e.target.checked:e.target.value:e,g=e=>e.substring(0,e.search(/\.\d+(\.|$)/))||e,p=(e,t)=>e.has(g(t)),b=e=>{let t=e.constructor&&e.constructor.prototype;return x(t)&&t.hasOwnProperty("isPrototypeOf")},v="undefined"!=typeof window&&void 0!==window.HTMLElement&&"undefined"!=typeof document;function y(e){let t,r=Array.isArray(e),s="undefined"!=typeof FileList&&e instanceof FileList;if(e instanceof Date)t=new Date(e);else if(e instanceof Set)t=new Set(e);else if(!(!(v&&(e instanceof Blob||s))&&(r||x(e))))return e;else if(t=r?[]:{},r||b(e))for(let r in e)e.hasOwnProperty(r)&&(t[r]=y(e[r]));else t=e;return t}var w=e=>Array.isArray(e)?e.filter(Boolean):[],j=e=>void 0===e,N=(e,t,r)=>{if(!t||!x(e))return r;let s=w(t.split(/[,[\].]+?/)).reduce((e,t)=>m(e)?e:e[t],e);return j(s)||s===e?j(e[t])?r:e[t]:s},k=e=>"boolean"==typeof e,A=e=>/^\w*$/.test(e),_=e=>w(e.replace(/["|']|\]/g,"").split(/\.|\[/)),F=(e,t,r)=>{let s=-1,i=A(t)?[t]:_(t),a=i.length,l=a-1;for(;++s<a;){let t=i[s],a=r;if(s!==l){let r=e[t];a=x(r)||Array.isArray(r)?r:isNaN(+i[s+1])?{}:[]}if("__proto__"===t||"constructor"===t||"prototype"===t)return;e[t]=a,e=e[t]}};let V={BLUR:"blur",FOCUS_OUT:"focusout"},S={onBlur:"onBlur",onChange:"onChange",onSubmit:"onSubmit",onTouched:"onTouched",all:"all"},C={max:"max",min:"min",maxLength:"maxLength",minLength:"minLength",pattern:"pattern",required:"required",validate:"validate"},D=d.createContext(null);var M=(e,t,r,s=!0)=>{let i={defaultValues:t._defaultValues};for(let a in e)Object.defineProperty(i,a,{get:()=>(t._proxyFormState[a]!==S.all&&(t._proxyFormState[a]=!s||S.all),r&&(r[a]=!0),e[a])});return i};let L="undefined"!=typeof window?d.useLayoutEffect:d.useEffect;var E=e=>"string"==typeof e,z=(e,t,r,s,i)=>E(e)?(s&&t.watch.add(e),N(r,e,i)):Array.isArray(e)?e.map(e=>(s&&t.watch.add(e),N(r,e))):(s&&(t.watchAll=!0),r),O=(e,t,r,s,i)=>t?{...r[e],types:{...r[e]&&r[e].types?r[e].types:{},[s]:i||!0}}:{},U=e=>Array.isArray(e)?e:[e],q=()=>{let e=[];return{get observers(){return e},next:t=>{for(let r of e)r.next&&r.next(t)},subscribe:t=>(e.push(t),{unsubscribe:()=>{e=e.filter(e=>e!==t)}}),unsubscribe:()=>{e=[]}}},B=e=>m(e)||!h(e);function P(e,t){if(B(e)||B(t))return e===t;if(u(e)&&u(t))return e.getTime()===t.getTime();let r=Object.keys(e),s=Object.keys(t);if(r.length!==s.length)return!1;for(let i of r){let r=e[i];if(!s.includes(i))return!1;if("ref"!==i){let e=t[i];if(u(r)&&u(e)||x(r)&&x(e)||Array.isArray(r)&&Array.isArray(e)?!P(r,e):r!==e)return!1}}return!0}var T=e=>x(e)&&!Object.keys(e).length,$=e=>"file"===e.type,I=e=>"function"==typeof e,R=e=>{if(!v)return!1;let t=e?e.ownerDocument:0;return e instanceof(t&&t.defaultView?t.defaultView.HTMLElement:HTMLElement)},W=e=>"select-multiple"===e.type,H=e=>"radio"===e.type,G=e=>H(e)||c(e),Z=e=>R(e)&&e.isConnected;function K(e,t){let r=Array.isArray(t)?t:A(t)?[t]:_(t),s=1===r.length?e:function(e,t){let r=t.slice(0,-1).length,s=0;for(;s<r;)e=j(e)?s++:e[t[s++]];return e}(e,r),i=r.length-1,a=r[i];return s&&delete s[a],0!==i&&(x(s)&&T(s)||Array.isArray(s)&&function(e){for(let t in e)if(e.hasOwnProperty(t)&&!j(e[t]))return!1;return!0}(s))&&K(e,r.slice(0,-1)),e}var Y=e=>{for(let t in e)if(I(e[t]))return!0;return!1};function J(e,t={}){let r=Array.isArray(e);if(x(e)||r)for(let r in e)Array.isArray(e[r])||x(e[r])&&!Y(e[r])?(t[r]=Array.isArray(e[r])?[]:{},J(e[r],t[r])):m(e[r])||(t[r]=!0);return t}var X=(e,t)=>(function e(t,r,s){let i=Array.isArray(t);if(x(t)||i)for(let i in t)Array.isArray(t[i])||x(t[i])&&!Y(t[i])?j(r)||B(s[i])?s[i]=Array.isArray(t[i])?J(t[i],[]):{...J(t[i])}:e(t[i],m(r)?{}:r[i],s[i]):s[i]=!P(t[i],r[i]);return s})(e,t,J(t));let Q={value:!1,isValid:!1},ee={value:!0,isValid:!0};var et=e=>{if(Array.isArray(e)){if(e.length>1){let t=e.filter(e=>e&&e.checked&&!e.disabled).map(e=>e.value);return{value:t,isValid:!!t.length}}return e[0].checked&&!e[0].disabled?e[0].attributes&&!j(e[0].attributes.value)?j(e[0].value)||""===e[0].value?ee:{value:e[0].value,isValid:!0}:ee:Q}return Q},er=(e,{valueAsNumber:t,valueAsDate:r,setValueAs:s})=>j(e)?e:t?""===e?NaN:e?+e:e:r&&E(e)?new Date(e):s?s(e):e;let es={isValid:!1,value:null};var ei=e=>Array.isArray(e)?e.reduce((e,t)=>t&&t.checked&&!t.disabled?{isValid:!0,value:t.value}:e,es):es;function ea(e){let t=e.ref;return $(t)?t.files:H(t)?ei(e.refs).value:W(t)?[...t.selectedOptions].map(({value:e})=>e):c(t)?et(e.refs).value:er(j(t.value)?e.ref.value:t.value,e)}var el=(e,t,r,s)=>{let i={};for(let r of e){let e=N(t,r);e&&F(i,r,e._f)}return{criteriaMode:r,names:[...e],fields:i,shouldUseNativeValidation:s}},en=e=>e instanceof RegExp,eo=e=>j(e)?e:en(e)?e.source:x(e)?en(e.value)?e.value.source:e.value:e,ed=e=>({isOnSubmit:!e||e===S.onSubmit,isOnBlur:e===S.onBlur,isOnChange:e===S.onChange,isOnAll:e===S.all,isOnTouch:e===S.onTouched});let ec="AsyncFunction";var eu=e=>!!e&&!!e.validate&&!!(I(e.validate)&&e.validate.constructor.name===ec||x(e.validate)&&Object.values(e.validate).find(e=>e.constructor.name===ec)),em=e=>e.mount&&(e.required||e.min||e.max||e.maxLength||e.minLength||e.pattern||e.validate),eh=(e,t,r)=>!r&&(t.watchAll||t.watch.has(e)||[...t.watch].some(t=>e.startsWith(t)&&/^\.\w+/.test(e.slice(t.length))));let ex=(e,t,r,s)=>{for(let i of r||Object.keys(e)){let r=N(e,i);if(r){let{_f:e,...a}=r;if(e){if(e.refs&&e.refs[0]&&t(e.refs[0],i)&&!s)return!0;else if(e.ref&&t(e.ref,e.name)&&!s)return!0;else if(ex(a,t))break}else if(x(a)&&ex(a,t))break}}};function ef(e,t,r){let s=N(e,r);if(s||A(r))return{error:s,name:r};let i=r.split(".");for(;i.length;){let s=i.join("."),a=N(t,s),l=N(e,s);if(a&&!Array.isArray(a)&&r!==s)break;if(l&&l.type)return{name:s,error:l};if(l&&l.root&&l.root.type)return{name:`${s}.root`,error:l.root};i.pop()}return{name:r}}var eg=(e,t,r,s)=>{r(e);let{name:i,...a}=e;return T(a)||Object.keys(a).length>=Object.keys(t).length||Object.keys(a).find(e=>t[e]===(!s||S.all))},ep=(e,t,r)=>!e||!t||e===t||U(e).some(e=>e&&(r?e===t:e.startsWith(t)||t.startsWith(e))),eb=(e,t,r,s,i)=>!i.isOnAll&&(!r&&i.isOnTouch?!(t||e):(r?s.isOnBlur:i.isOnBlur)?!e:(r?!s.isOnChange:!i.isOnChange)||e),ev=(e,t)=>!w(N(e,t)).length&&K(e,t),ey=(e,t,r)=>{let s=U(N(e,r));return F(s,"root",t[r]),F(e,r,s),e},ew=e=>E(e);function ej(e,t,r="validate"){if(ew(e)||Array.isArray(e)&&e.every(ew)||k(e)&&!e)return{type:r,message:ew(e)?e:"",ref:t}}var eN=e=>x(e)&&!en(e)?e:{value:e,message:""},ek=async(e,t,r,s,i,a)=>{let{ref:l,refs:n,required:o,maxLength:d,minLength:u,min:h,max:f,pattern:g,validate:p,name:b,valueAsNumber:v,mount:y}=e._f,w=N(r,b);if(!y||t.has(b))return{};let A=n?n[0]:l,_=e=>{i&&A.reportValidity&&(A.setCustomValidity(k(e)?"":e||""),A.reportValidity())},F={},V=H(l),S=c(l),D=(v||$(l))&&j(l.value)&&j(w)||R(l)&&""===l.value||""===w||Array.isArray(w)&&!w.length,M=O.bind(null,b,s,F),L=(e,t,r,s=C.maxLength,i=C.minLength)=>{let a=e?t:r;F[b]={type:e?s:i,message:a,ref:l,...M(e?s:i,a)}};if(a?!Array.isArray(w)||!w.length:o&&(!(V||S)&&(D||m(w))||k(w)&&!w||S&&!et(n).isValid||V&&!ei(n).isValid)){let{value:e,message:t}=ew(o)?{value:!!o,message:o}:eN(o);if(e&&(F[b]={type:C.required,message:t,ref:A,...M(C.required,t)},!s))return _(t),F}if(!D&&(!m(h)||!m(f))){let e,t,r=eN(f),i=eN(h);if(m(w)||isNaN(w)){let s=l.valueAsDate||new Date(w),a=e=>new Date(new Date().toDateString()+" "+e),n="time"==l.type,o="week"==l.type;E(r.value)&&w&&(e=n?a(w)>a(r.value):o?w>r.value:s>new Date(r.value)),E(i.value)&&w&&(t=n?a(w)<a(i.value):o?w<i.value:s<new Date(i.value))}else{let s=l.valueAsNumber||(w?+w:w);m(r.value)||(e=s>r.value),m(i.value)||(t=s<i.value)}if((e||t)&&(L(!!e,r.message,i.message,C.max,C.min),!s))return _(F[b].message),F}if((d||u)&&!D&&(E(w)||a&&Array.isArray(w))){let e=eN(d),t=eN(u),r=!m(e.value)&&w.length>+e.value,i=!m(t.value)&&w.length<+t.value;if((r||i)&&(L(r,e.message,t.message),!s))return _(F[b].message),F}if(g&&!D&&E(w)){let{value:e,message:t}=eN(g);if(en(e)&&!w.match(e)&&(F[b]={type:C.pattern,message:t,ref:l,...M(C.pattern,t)},!s))return _(t),F}if(p){if(I(p)){let e=ej(await p(w,r),A);if(e&&(F[b]={...e,...M(C.validate,e.message)},!s))return _(e.message),F}else if(x(p)){let e={};for(let t in p){if(!T(e)&&!s)break;let i=ej(await p[t](w,r),A,t);i&&(e={...i,...M(t,i.message)},_(i.message),s&&(F[b]=e))}if(!T(e)&&(F[b]={ref:A,...e},!s))return F}}return _(!0),F};let eA={mode:S.onSubmit,reValidateMode:S.onChange,shouldFocusError:!0};var e_=r(5336),eF=r(93613);let eV=(0,r(62688).A)("send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]]),eS=()=>{let[e,t]=(0,d.useState)("idle"),{register:r,handleSubmit:i,formState:{errors:a,isSubmitting:l},reset:n}=function(e={}){let t=d.useRef(void 0),r=d.useRef(void 0),[s,i]=d.useState({isDirty:!1,isValidating:!1,isLoading:I(e.defaultValues),isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,submitCount:0,dirtyFields:{},touchedFields:{},validatingFields:{},errors:e.errors||{},disabled:e.disabled||!1,isReady:!1,defaultValues:I(e.defaultValues)?void 0:e.defaultValues});!t.current&&(t.current={...e.formControl?e.formControl:function(e={}){let t,r={...eA,...e},s={submitCount:0,isDirty:!1,isReady:!1,isLoading:I(r.defaultValues),isValidating:!1,isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,touchedFields:{},dirtyFields:{},validatingFields:{},errors:r.errors||{},disabled:r.disabled||!1},i={},a=(x(r.defaultValues)||x(r.values))&&y(r.defaultValues||r.values)||{},l=r.shouldUnregister?{}:y(a),n={action:!1,mount:!1,watch:!1},o={mount:new Set,disabled:new Set,unMount:new Set,array:new Set,watch:new Set},d=0,h={isDirty:!1,dirtyFields:!1,validatingFields:!1,touchedFields:!1,isValidating:!1,isValid:!1,errors:!1},g={...h},b={array:q(),state:q()},A=r.criteriaMode===S.all,_=e=>t=>{clearTimeout(d),d=setTimeout(e,t)},C=async e=>{if(!r.disabled&&(h.isValid||g.isValid||e)){let e=r.resolver?T((await H()).errors):await J(i,!0);e!==s.isValid&&b.state.next({isValid:e})}},D=(e,t)=>{!r.disabled&&(h.isValidating||h.validatingFields||g.isValidating||g.validatingFields)&&((e||Array.from(o.mount)).forEach(e=>{e&&(t?F(s.validatingFields,e,t):K(s.validatingFields,e))}),b.state.next({validatingFields:s.validatingFields,isValidating:!T(s.validatingFields)}))},M=(e,t)=>{F(s.errors,e,t),b.state.next({errors:s.errors})},L=(e,t,r,s)=>{let o=N(i,e);if(o){let i=N(l,e,j(r)?N(a,e):r);j(i)||s&&s.defaultChecked||t?F(l,e,t?i:ea(o._f)):et(e,i),n.mount&&C()}},O=(e,t,i,l,n)=>{let o=!1,d=!1,c={name:e};if(!r.disabled){if(!i||l){(h.isDirty||g.isDirty)&&(d=s.isDirty,s.isDirty=c.isDirty=Q(),o=d!==c.isDirty);let r=P(N(a,e),t);d=!!N(s.dirtyFields,e),r?K(s.dirtyFields,e):F(s.dirtyFields,e,!0),c.dirtyFields=s.dirtyFields,o=o||(h.dirtyFields||g.dirtyFields)&&!r!==d}if(i){let t=N(s.touchedFields,e);t||(F(s.touchedFields,e,i),c.touchedFields=s.touchedFields,o=o||(h.touchedFields||g.touchedFields)&&t!==i)}o&&n&&b.state.next(c)}return o?c:{}},B=(e,i,a,l)=>{let n=N(s.errors,e),o=(h.isValid||g.isValid)&&k(i)&&s.isValid!==i;if(r.delayError&&a?(t=_(()=>M(e,a)))(r.delayError):(clearTimeout(d),t=null,a?F(s.errors,e,a):K(s.errors,e)),(a?!P(n,a):n)||!T(l)||o){let t={...l,...o&&k(i)?{isValid:i}:{},errors:s.errors,name:e};s={...s,...t},b.state.next(t)}},H=async e=>{D(e,!0);let t=await r.resolver(l,r.context,el(e||o.mount,i,r.criteriaMode,r.shouldUseNativeValidation));return D(e),t},Y=async e=>{let{errors:t}=await H(e);if(e)for(let r of e){let e=N(t,r);e?F(s.errors,r,e):K(s.errors,r)}else s.errors=t;return t},J=async(e,t,i={valid:!0})=>{for(let a in e){let n=e[a];if(n){let{_f:e,...d}=n;if(e){let d=o.array.has(e.name),c=n._f&&eu(n._f);c&&h.validatingFields&&D([a],!0);let u=await ek(n,o.disabled,l,A,r.shouldUseNativeValidation&&!t,d);if(c&&h.validatingFields&&D([a]),u[e.name]&&(i.valid=!1,t))break;t||(N(u,e.name)?d?ey(s.errors,u,e.name):F(s.errors,e.name,u[e.name]):K(s.errors,e.name))}T(d)||await J(d,t,i)}}return i.valid},Q=(e,t)=>!r.disabled&&(e&&t&&F(l,e,t),!P(ej(),a)),ee=(e,t,r)=>z(e,o,{...n.mount?l:j(t)?a:E(e)?{[e]:t}:t},r,t),et=(e,t,r={})=>{let s=N(i,e),a=t;if(s){let r=s._f;r&&(r.disabled||F(l,e,er(t,r)),a=R(r.ref)&&m(t)?"":t,W(r.ref)?[...r.ref.options].forEach(e=>e.selected=a.includes(e.value)):r.refs?c(r.ref)?r.refs.forEach(e=>{e.defaultChecked&&e.disabled||(Array.isArray(a)?e.checked=!!a.find(t=>t===e.value):e.checked=a===e.value||!!a)}):r.refs.forEach(e=>e.checked=e.value===a):$(r.ref)?r.ref.value="":(r.ref.value=a,r.ref.type||b.state.next({name:e,values:y(l)})))}(r.shouldDirty||r.shouldTouch)&&O(e,a,r.shouldTouch,r.shouldDirty,!0),r.shouldValidate&&ew(e)},es=(e,t,r)=>{for(let s in t){if(!t.hasOwnProperty(s))return;let a=t[s],l=e+"."+s,n=N(i,l);(o.array.has(e)||x(a)||n&&!n._f)&&!u(a)?es(l,a,r):et(l,a,r)}},ei=(e,t,r={})=>{let d=N(i,e),c=o.array.has(e),u=y(t);F(l,e,u),c?(b.array.next({name:e,values:y(l)}),(h.isDirty||h.dirtyFields||g.isDirty||g.dirtyFields)&&r.shouldDirty&&b.state.next({name:e,dirtyFields:X(a,l),isDirty:Q(e,u)})):!d||d._f||m(u)?et(e,u,r):es(e,u,r),eh(e,o)&&b.state.next({...s}),b.state.next({name:n.mount?e:void 0,values:y(l)})},en=async e=>{n.mount=!0;let a=e.target,d=a.name,c=!0,m=N(i,d),x=e=>{c=Number.isNaN(e)||u(e)&&isNaN(e.getTime())||P(e,N(l,d,e))},p=ed(r.mode),v=ed(r.reValidateMode);if(m){let n,u,w=a.type?ea(m._f):f(e),j=e.type===V.BLUR||e.type===V.FOCUS_OUT,k=!em(m._f)&&!r.resolver&&!N(s.errors,d)&&!m._f.deps||eb(j,N(s.touchedFields,d),s.isSubmitted,v,p),_=eh(d,o,j);F(l,d,w),j?(m._f.onBlur&&m._f.onBlur(e),t&&t(0)):m._f.onChange&&m._f.onChange(e);let S=O(d,w,j),M=!T(S)||_;if(j||b.state.next({name:d,type:e.type,values:y(l)}),k)return(h.isValid||g.isValid)&&("onBlur"===r.mode?j&&C():j||C()),M&&b.state.next({name:d,..._?{}:S});if(!j&&_&&b.state.next({...s}),r.resolver){let{errors:e}=await H([d]);if(x(w),c){let t=ef(s.errors,i,d),r=ef(e,i,t.name||d);n=r.error,d=r.name,u=T(e)}}else D([d],!0),n=(await ek(m,o.disabled,l,A,r.shouldUseNativeValidation))[d],D([d]),x(w),c&&(n?u=!1:(h.isValid||g.isValid)&&(u=await J(i,!0)));c&&(m._f.deps&&ew(m._f.deps),B(d,u,n,S))}},ec=(e,t)=>{if(N(s.errors,t)&&e.focus)return e.focus(),1},ew=async(e,t={})=>{let a,l,n=U(e);if(r.resolver){let t=await Y(j(e)?e:n);a=T(t),l=e?!n.some(e=>N(t,e)):a}else e?((l=(await Promise.all(n.map(async e=>{let t=N(i,e);return await J(t&&t._f?{[e]:t}:t)}))).every(Boolean))||s.isValid)&&C():l=a=await J(i);return b.state.next({...!E(e)||(h.isValid||g.isValid)&&a!==s.isValid?{}:{name:e},...r.resolver||!e?{isValid:a}:{},errors:s.errors}),t.shouldFocus&&!l&&ex(i,ec,e?n:o.mount),l},ej=e=>{let t={...n.mount?l:a};return j(e)?t:E(e)?N(t,e):e.map(e=>N(t,e))},eN=(e,t)=>({invalid:!!N((t||s).errors,e),isDirty:!!N((t||s).dirtyFields,e),error:N((t||s).errors,e),isValidating:!!N(s.validatingFields,e),isTouched:!!N((t||s).touchedFields,e)}),e_=(e,t,r)=>{let a=(N(i,e,{_f:{}})._f||{}).ref,{ref:l,message:n,type:o,...d}=N(s.errors,e)||{};F(s.errors,e,{...d,...t,ref:a}),b.state.next({name:e,errors:s.errors,isValid:!1}),r&&r.shouldFocus&&a&&a.focus&&a.focus()},eF=e=>b.state.subscribe({next:t=>{ep(e.name,t.name,e.exact)&&eg(t,e.formState||h,ez,e.reRenderRoot)&&e.callback({values:{...l},...s,...t})}}).unsubscribe,eV=(e,t={})=>{for(let n of e?U(e):o.mount)o.mount.delete(n),o.array.delete(n),t.keepValue||(K(i,n),K(l,n)),t.keepError||K(s.errors,n),t.keepDirty||K(s.dirtyFields,n),t.keepTouched||K(s.touchedFields,n),t.keepIsValidating||K(s.validatingFields,n),r.shouldUnregister||t.keepDefaultValue||K(a,n);b.state.next({values:y(l)}),b.state.next({...s,...!t.keepDirty?{}:{isDirty:Q()}}),t.keepIsValid||C()},eS=({disabled:e,name:t})=>{(k(e)&&n.mount||e||o.disabled.has(t))&&(e?o.disabled.add(t):o.disabled.delete(t))},eC=(e,t={})=>{let s=N(i,e),l=k(t.disabled)||k(r.disabled);return F(i,e,{...s||{},_f:{...s&&s._f?s._f:{ref:{name:e}},name:e,mount:!0,...t}}),o.mount.add(e),s?eS({disabled:k(t.disabled)?t.disabled:r.disabled,name:e}):L(e,!0,t.value),{...l?{disabled:t.disabled||r.disabled}:{},...r.progressive?{required:!!t.required,min:eo(t.min),max:eo(t.max),minLength:eo(t.minLength),maxLength:eo(t.maxLength),pattern:eo(t.pattern)}:{},name:e,onChange:en,onBlur:en,ref:l=>{if(l){eC(e,t),s=N(i,e);let r=j(l.value)&&l.querySelectorAll&&l.querySelectorAll("input,select,textarea")[0]||l,n=G(r),o=s._f.refs||[];(n?o.find(e=>e===r):r===s._f.ref)||(F(i,e,{_f:{...s._f,...n?{refs:[...o.filter(Z),r,...Array.isArray(N(a,e))?[{}]:[]],ref:{type:r.type,name:e}}:{ref:r}}}),L(e,!1,void 0,r))}else(s=N(i,e,{}))._f&&(s._f.mount=!1),(r.shouldUnregister||t.shouldUnregister)&&!(p(o.array,e)&&n.action)&&o.unMount.add(e)}}},eD=()=>r.shouldFocusError&&ex(i,ec,o.mount),eM=(e,t)=>async a=>{let n;a&&(a.preventDefault&&a.preventDefault(),a.persist&&a.persist());let d=y(l);if(b.state.next({isSubmitting:!0}),r.resolver){let{errors:e,values:t}=await H();s.errors=e,d=t}else await J(i);if(o.disabled.size)for(let e of o.disabled)F(d,e,void 0);if(K(s.errors,"root"),T(s.errors)){b.state.next({errors:{}});try{await e(d,a)}catch(e){n=e}}else t&&await t({...s.errors},a),eD(),setTimeout(eD);if(b.state.next({isSubmitted:!0,isSubmitting:!1,isSubmitSuccessful:T(s.errors)&&!n,submitCount:s.submitCount+1,errors:s.errors}),n)throw n},eL=(e,t={})=>{let d=e?y(e):a,c=y(d),u=T(e),m=u?a:c;if(t.keepDefaultValues||(a=d),!t.keepValues){if(t.keepDirtyValues)for(let e of Array.from(new Set([...o.mount,...Object.keys(X(a,l))])))N(s.dirtyFields,e)?F(m,e,N(l,e)):ei(e,N(m,e));else{if(v&&j(e))for(let e of o.mount){let t=N(i,e);if(t&&t._f){let e=Array.isArray(t._f.refs)?t._f.refs[0]:t._f.ref;if(R(e)){let t=e.closest("form");if(t){t.reset();break}}}}for(let e of o.mount)ei(e,N(m,e))}l=y(m),b.array.next({values:{...m}}),b.state.next({values:{...m}})}o={mount:t.keepDirtyValues?o.mount:new Set,unMount:new Set,array:new Set,disabled:new Set,watch:new Set,watchAll:!1,focus:""},n.mount=!h.isValid||!!t.keepIsValid||!!t.keepDirtyValues,n.watch=!!r.shouldUnregister,b.state.next({submitCount:t.keepSubmitCount?s.submitCount:0,isDirty:!u&&(t.keepDirty?s.isDirty:!!(t.keepDefaultValues&&!P(e,a))),isSubmitted:!!t.keepIsSubmitted&&s.isSubmitted,dirtyFields:u?{}:t.keepDirtyValues?t.keepDefaultValues&&l?X(a,l):s.dirtyFields:t.keepDefaultValues&&e?X(a,e):t.keepDirty?s.dirtyFields:{},touchedFields:t.keepTouched?s.touchedFields:{},errors:t.keepErrors?s.errors:{},isSubmitSuccessful:!!t.keepIsSubmitSuccessful&&s.isSubmitSuccessful,isSubmitting:!1})},eE=(e,t)=>eL(I(e)?e(l):e,t),ez=e=>{s={...s,...e}},eO={control:{register:eC,unregister:eV,getFieldState:eN,handleSubmit:eM,setError:e_,_subscribe:eF,_runSchema:H,_focusError:eD,_getWatch:ee,_getDirty:Q,_setValid:C,_setFieldArray:(e,t=[],o,d,c=!0,u=!0)=>{if(d&&o&&!r.disabled){if(n.action=!0,u&&Array.isArray(N(i,e))){let t=o(N(i,e),d.argA,d.argB);c&&F(i,e,t)}if(u&&Array.isArray(N(s.errors,e))){let t=o(N(s.errors,e),d.argA,d.argB);c&&F(s.errors,e,t),ev(s.errors,e)}if((h.touchedFields||g.touchedFields)&&u&&Array.isArray(N(s.touchedFields,e))){let t=o(N(s.touchedFields,e),d.argA,d.argB);c&&F(s.touchedFields,e,t)}(h.dirtyFields||g.dirtyFields)&&(s.dirtyFields=X(a,l)),b.state.next({name:e,isDirty:Q(e,t),dirtyFields:s.dirtyFields,errors:s.errors,isValid:s.isValid})}else F(l,e,t)},_setDisabledField:eS,_setErrors:e=>{s.errors=e,b.state.next({errors:s.errors,isValid:!1})},_getFieldArray:e=>w(N(n.mount?l:a,e,r.shouldUnregister?N(a,e,[]):[])),_reset:eL,_resetDefaultValues:()=>I(r.defaultValues)&&r.defaultValues().then(e=>{eE(e,r.resetOptions),b.state.next({isLoading:!1})}),_removeUnmounted:()=>{for(let e of o.unMount){let t=N(i,e);t&&(t._f.refs?t._f.refs.every(e=>!Z(e)):!Z(t._f.ref))&&eV(e)}o.unMount=new Set},_disableForm:e=>{k(e)&&(b.state.next({disabled:e}),ex(i,(t,r)=>{let s=N(i,r);s&&(t.disabled=s._f.disabled||e,Array.isArray(s._f.refs)&&s._f.refs.forEach(t=>{t.disabled=s._f.disabled||e}))},0,!1))},_subjects:b,_proxyFormState:h,get _fields(){return i},get _formValues(){return l},get _state(){return n},set _state(value){n=value},get _defaultValues(){return a},get _names(){return o},set _names(value){o=value},get _formState(){return s},get _options(){return r},set _options(value){r={...r,...value}}},subscribe:e=>(n.mount=!0,g={...g,...e.formState},eF({...e,formState:g})),trigger:ew,register:eC,handleSubmit:eM,watch:(e,t)=>I(e)?b.state.subscribe({next:r=>e(ee(void 0,t),r)}):ee(e,t,!0),setValue:ei,getValues:ej,reset:eE,resetField:(e,t={})=>{N(i,e)&&(j(t.defaultValue)?ei(e,y(N(a,e))):(ei(e,t.defaultValue),F(a,e,y(t.defaultValue))),t.keepTouched||K(s.touchedFields,e),t.keepDirty||(K(s.dirtyFields,e),s.isDirty=t.defaultValue?Q(e,y(N(a,e))):Q()),!t.keepError&&(K(s.errors,e),h.isValid&&C()),b.state.next({...s}))},clearErrors:e=>{e&&U(e).forEach(e=>K(s.errors,e)),b.state.next({errors:e?s.errors:{}})},unregister:eV,setError:e_,setFocus:(e,t={})=>{let r=N(i,e),s=r&&r._f;if(s){let e=s.refs?s.refs[0]:s.ref;e.focus&&(e.focus(),t.shouldSelect&&I(e.select)&&e.select())}},getFieldState:eN};return{...eO,formControl:eO}}(e),formState:s},e.formControl&&e.defaultValues&&!I(e.defaultValues)&&e.formControl.reset(e.defaultValues,e.resetOptions));let a=t.current.control;return a._options=e,L(()=>{let e=a._subscribe({formState:a._proxyFormState,callback:()=>i({...a._formState}),reRenderRoot:!0});return i(e=>({...e,isReady:!0})),a._formState.isReady=!0,e},[a]),d.useEffect(()=>a._disableForm(e.disabled),[a,e.disabled]),d.useEffect(()=>{e.mode&&(a._options.mode=e.mode),e.reValidateMode&&(a._options.reValidateMode=e.reValidateMode)},[a,e.mode,e.reValidateMode]),d.useEffect(()=>{e.errors&&(a._setErrors(e.errors),a._focusError())},[a,e.errors]),d.useEffect(()=>{e.shouldUnregister&&a._subjects.state.next({values:a._getWatch()})},[a,e.shouldUnregister]),d.useEffect(()=>{if(a._proxyFormState.isDirty){let e=a._getDirty();e!==s.isDirty&&a._subjects.state.next({isDirty:e})}},[a,s.isDirty]),d.useEffect(()=>{e.values&&!P(e.values,r.current)?(a._reset(e.values,a._options.resetOptions),r.current=e.values,i(e=>({...e}))):a._resetDefaultValues()},[a,e.values]),d.useEffect(()=>{a._state.mount||(a._setValid(),a._state.mount=!0),a._state.watch&&(a._state.watch=!1,a._subjects.state.next({...a._formState})),a._removeUnmounted()}),t.current.formState=M(s,a),t.current}(),o=async e=>{t("submitting"),console.log("Contact Form Data:",e),await new Promise(e=>setTimeout(e,1500)),Math.random()>.2?(t("success"),n()):t("error")};return"success"===e?(0,s.jsxs)("div",{className:"bg-white p-8 md:p-10 rounded-2xl shadow-xl text-center border border-gray-100",children:[(0,s.jsx)("div",{className:"w-20 h-20 mx-auto bg-green-100 rounded-full flex items-center justify-center mb-6",children:(0,s.jsx)(e_.A,{className:"text-green-500 text-4xl"})}),(0,s.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-3",children:"Message Sent!"}),(0,s.jsx)("p",{className:"text-gray-600 mb-8 max-w-md mx-auto leading-relaxed",children:"Thank you for contacting the FWU Incubation Center. We've received your message and will get back to you as soon as possible."}),(0,s.jsxs)("div",{className:"flex flex-col items-center space-y-4",children:[(0,s.jsx)("p",{className:"text-sm text-gray-500 mb-2",children:"You will receive a confirmation email shortly."}),(0,s.jsx)("button",{onClick:()=>t("idle"),className:"bg-indigo-600 hover:bg-indigo-700 text-white font-semibold py-3 px-8 rounded-lg shadow-md transition-all duration-300 hover:scale-105 transform",children:"Send Another Message"})]})]}):(0,s.jsx)("div",{className:"bg-white p-8 md:p-10 rounded-2xl shadow-xl border border-gray-100 hover:border-indigo-100 transition-all duration-300 hover:shadow-indigo-100/20",children:(0,s.jsxs)("form",{onSubmit:i(o),className:"space-y-8",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{htmlFor:"name",className:"flex items-center text-sm font-medium text-gray-700 mb-2",children:["Full Name ",(0,s.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("input",{type:"text",id:"name",className:`w-full px-4 py-3 pl-10 border-2 ${a.name?"border-red-300 bg-red-50":"border-gray-200 focus:border-indigo-300"} rounded-lg ${a.name?"focus:ring-red-200":"focus:ring-indigo-100"} focus:ring-4 outline-none transition-all`,placeholder:"e.g., Rajesh Sharma",...r("name",{required:"Full name is required."})}),(0,s.jsx)("div",{className:"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none",children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:`h-5 w-5 ${a.name?"text-red-400":"text-indigo-400"}`,fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})})})]}),a.name&&(0,s.jsxs)("div",{className:"mt-2 flex items-start text-sm text-red-600",children:[(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 text-red-500 mt-0.5 mr-1.5 flex-shrink-0",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"})}),(0,s.jsx)("span",{children:a.name.message})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{htmlFor:"email",className:"flex items-center text-sm font-medium text-gray-700 mb-2",children:["Email Address ",(0,s.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("input",{type:"email",id:"email",className:`w-full px-4 py-3 pl-10 border-2 ${a.email?"border-red-300 bg-red-50":"border-gray-200 focus:border-indigo-300"} rounded-lg ${a.email?"focus:ring-red-200":"focus:ring-indigo-100"} focus:ring-4 outline-none transition-all`,placeholder:"<EMAIL>",...r("email",{required:"Email is required.",pattern:{value:/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,message:"Invalid email address."}})}),(0,s.jsx)("div",{className:"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none",children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:`h-5 w-5 ${a.email?"text-red-400":"text-indigo-400"}`,fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})})})]}),a.email&&(0,s.jsxs)("div",{className:"mt-2 flex items-start text-sm text-red-600",children:[(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 text-red-500 mt-0.5 mr-1.5 flex-shrink-0",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"})}),(0,s.jsx)("span",{children:a.email.message})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{htmlFor:"message",className:"flex items-center text-sm font-medium text-gray-700 mb-2",children:["Your Message ",(0,s.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),(0,s.jsx)("div",{className:"relative",children:(0,s.jsx)("textarea",{id:"message",rows:6,className:`w-full px-4 py-3 border-2 ${a.message?"border-red-300 bg-red-50":"border-gray-200 focus:border-indigo-300"} rounded-lg ${a.message?"focus:ring-red-200":"focus:ring-indigo-100"} focus:ring-4 outline-none transition-all`,placeholder:"Write your message here... Please include details about your inquiry or how we can help you.",...r("message",{required:"Message is required.",minLength:{value:10,message:"Message must be at least 10 characters."}})})}),a.message&&(0,s.jsxs)("div",{className:"mt-2 flex items-start text-sm text-red-600",children:[(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 text-red-500 mt-0.5 mr-1.5 flex-shrink-0",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"})}),(0,s.jsx)("span",{children:a.message.message})]})]}),"error"===e&&(0,s.jsxs)("div",{className:"flex items-center p-5 text-sm text-red-700 bg-red-100 rounded-xl border border-red-200",role:"alert",children:[(0,s.jsx)(eF.A,{className:"text-xl mr-3 text-red-500"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-bold",children:"Message Not Sent"}),(0,s.jsx)("p",{children:"There was an error sending your message. Please try again or contact us directly."})]})]}),(0,s.jsx)("div",{className:"text-sm text-gray-500",children:(0,s.jsxs)("p",{children:["By submitting this form, you agree to our ",(0,s.jsx)("a",{href:"#",className:"text-indigo-600 hover:underline",children:"Privacy Policy"})," and consent to being contacted regarding your inquiry."]})}),(0,s.jsx)("button",{type:"submit",disabled:l,className:"w-full flex justify-center items-center bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-4 px-6 rounded-xl shadow-md transition-all duration-300 hover:scale-105 transform disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100",children:l?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("svg",{className:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,s.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,s.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Sending Message..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(eV,{className:"mr-2"})," Send Message"]})})]})})};var eC=r(57800),eD=r(48730);let eM=({icon:e,label:t,value:r,href:i})=>(0,s.jsxs)("div",{className:"flex items-start space-x-4 p-4 rounded-xl hover:bg-indigo-50 transition-colors group",children:[(0,s.jsx)("div",{className:"flex-shrink-0 w-12 h-12 bg-indigo-100 text-indigo-600 rounded-full flex items-center justify-center group-hover:bg-indigo-200 transition-colors",children:e}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-sm font-semibold text-indigo-600 uppercase tracking-wider mb-1",children:t}),i?(0,s.jsx)("a",{href:i,className:"text-md text-gray-900 hover:text-indigo-700 transition-colors break-words font-medium",children:r}):(0,s.jsx)("p",{className:"text-md text-gray-900 break-words",children:r})]})]}),eL=({details:e})=>(0,s.jsxs)("div",{className:"bg-white p-8 md:p-10 rounded-2xl shadow-xl border border-gray-100 hover:border-indigo-100 transition-all duration-300 hover:shadow-indigo-100/20",children:[(0,s.jsxs)("div",{className:"flex items-center mb-8",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-indigo-100 rounded-full flex items-center justify-center mr-4",children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 text-indigo-600",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Contact Information"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(eM,{icon:(0,s.jsx)(l.A,{size:22}),label:"Our Address",value:e.address}),(0,s.jsx)(eM,{icon:(0,s.jsx)(i.A,{size:22}),label:"Phone Number",value:e.phone,href:`tel:${e.phone.replace(/\s+/g,"")}`}),(0,s.jsx)(eM,{icon:(0,s.jsx)(a.A,{size:22}),label:"General Inquiries",value:e.email,href:`mailto:${e.email}`}),(0,s.jsx)(eM,{icon:(0,s.jsx)(eC.A,{size:22}),label:"Incubation Center",value:e.incubationEmail,href:`mailto:${e.incubationEmail}`}),(0,s.jsxs)("div",{className:"flex items-start space-x-4 p-4 rounded-xl hover:bg-indigo-50 transition-colors group",children:[(0,s.jsx)("div",{className:"flex-shrink-0 w-12 h-12 bg-indigo-100 text-indigo-600 rounded-full flex items-center justify-center group-hover:bg-indigo-200 transition-colors",children:(0,s.jsx)(eD.A,{size:22})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-sm font-semibold text-indigo-600 uppercase tracking-wider mb-2",children:"Office Hours"}),(0,s.jsx)("div",{className:"space-y-1",children:e.officeHours.map((e,t)=>(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:`w-2 h-2 rounded-full mr-2 ${e.hours.includes("Closed")?"bg-red-400":"bg-green-400"}`}),(0,s.jsxs)("p",{className:"text-md text-gray-900",children:[(0,s.jsxs)("span",{className:"font-medium",children:[e.day,":"]})," ",e.hours]})]},t))})]})]})]}),(0,s.jsxs)("div",{className:"mt-8 pt-6 border-t border-gray-100",children:[(0,s.jsx)("h4",{className:"text-sm font-semibold text-gray-500 uppercase tracking-wider mb-4",children:"Connect With Us"}),(0,s.jsxs)("div",{className:"flex space-x-4",children:[(0,s.jsx)("a",{href:"#",className:"w-10 h-10 bg-indigo-100 text-indigo-600 rounded-full flex items-center justify-center hover:bg-indigo-200 transition-colors",children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",fill:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{d:"M22.675 0h-21.35c-.732 0-1.325.593-1.325 1.325v21.351c0 .731.593 1.324 1.325 1.324h11.495v-9.294h-3.128v-3.622h3.128v-2.671c0-3.1 1.893-4.788 4.659-4.788 1.325 0 2.463.099 2.795.143v3.24l-1.918.001c-1.504 0-1.795.715-1.795 1.763v2.313h3.587l-.467 3.622h-3.12v9.293h6.116c.73 0 1.323-.593 1.323-1.325v-21.35c0-.732-.593-1.325-1.325-1.325z"})})}),(0,s.jsx)("a",{href:"#",className:"w-10 h-10 bg-indigo-100 text-indigo-600 rounded-full flex items-center justify-center hover:bg-indigo-200 transition-colors",children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",fill:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{d:"M12 0c-6.627 0-12 5.373-12 12s5.373 12 12 12 12-5.373 12-12-5.373-12-12-12zm6.066 9.645c.183 4.04-2.83 8.544-8.164 8.544-1.622 0-3.131-.476-4.402-1.291 1.524.18 3.045-.244 4.252-1.189-1.256-.023-2.317-.854-2.684-1.995.451.086.895.061 1.298-.049-1.381-.278-2.335-1.522-2.304-2.853.388.215.83.344 1.301.359-1.279-.855-1.641-2.544-.889-3.835 1.416 1.738 3.533 2.881 5.92 3.001-.419-1.796.944-3.527 2.799-3.527.825 0 1.572.349 2.096.907.654-.128 1.27-.368 1.824-.697-.215.671-.67 1.233-1.263 1.589.581-.07 1.135-.224 1.649-.453-.384.578-.87 1.084-1.433 1.489z"})})}),(0,s.jsx)("a",{href:"#",className:"w-10 h-10 bg-indigo-100 text-indigo-600 rounded-full flex items-center justify-center hover:bg-indigo-200 transition-colors",children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",fill:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{d:"M12 0c-6.627 0-12 5.373-12 12s5.373 12 12 12 12-5.373 12-12-5.373-12-12-12zm-2 16h-2v-6h2v6zm-1-6.891c-.607 0-1.1-.496-1.1-1.109 0-.612.492-1.109 1.1-1.109s1.1.497 1.1 1.109c0 .613-.493 1.109-1.1 1.109zm8 6.891h-1.998v-2.861c0-1.881-2.002-1.722-2.002 0v2.861h-2v-6h2v1.093c.872-1.616 4-1.736 4 1.548v3.359z"})})}),(0,s.jsx)("a",{href:"#",className:"w-10 h-10 bg-indigo-100 text-indigo-600 rounded-full flex items-center justify-center hover:bg-indigo-200 transition-colors",children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",fill:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{d:"M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"})})})]})]})]}),eE=({embedUrl:e,address:t})=>(0,s.jsxs)("div",{className:"bg-white p-6 rounded-2xl shadow-xl border border-gray-100 hover:border-indigo-100 transition-all duration-300 hover:shadow-indigo-100/20 overflow-hidden",children:[(0,s.jsxs)("div",{className:"flex items-center mb-6",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-indigo-100 rounded-full flex items-center justify-center mr-4",children:(0,s.jsx)(l.A,{className:"text-indigo-600 text-xl"})}),(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Find Us"})]}),(0,s.jsxs)("div",{className:"rounded-xl overflow-hidden border-4 border-indigo-50 shadow-inner relative",children:[(0,s.jsxs)("div",{className:"aspect-w-16 aspect-h-9",children:[" ",(0,s.jsx)("iframe",{src:e,width:"100%",height:"100%",style:{border:0},allowFullScreen:!1,loading:"lazy",referrerPolicy:"no-referrer-when-downgrade",title:`Map of ${t}`,className:"grayscale hover:grayscale-0 transition-all duration-500"})]}),(0,s.jsx)("div",{className:"absolute bottom-0 left-0 right-0 bg-gradient-to-t from-indigo-900/80 to-transparent p-4 text-white",children:(0,s.jsxs)("div",{className:"flex items-start",children:[(0,s.jsx)(l.A,{className:"mt-1 mr-2 flex-shrink-0"}),(0,s.jsx)("p",{className:"text-sm",children:t})]})})]}),(0,s.jsx)("div",{className:"mt-6 text-center",children:(0,s.jsxs)("a",{href:`https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(t)}`,target:"_blank",rel:"noopener noreferrer",className:"inline-flex items-center text-indigo-600 font-semibold hover:text-indigo-800 transition-colors",children:["Get Directions",(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 ml-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"})})]})})]});var ez=r(30474),eO=r(85814),eU=r.n(eO);let eq={phone:"+977-099-520729",email:"<EMAIL>",incubationEmail:"<EMAIL>",address:"Bheemdatta Municipality-18, Mahendranagar, Kanchanpur, Nepal",officeHours:[{day:"Monday - Friday",hours:"9:00 AM - 5:00 PM"},{day:"Saturday",hours:"9:00 AM - 2:00 PM"},{day:"Sunday",hours:"Closed"}],mapEmbedUrl:"https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3499.8943315606097!2d80.18761937532953!3d28.69999997561701!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x39a1a0f8c01ebb33%3A0x7a2b35cd7920b47f!2sFar%20Western%20University!5e0!3m2!1sen!2snp!4v1715000000000!5m2!1sen!2snp"},eB={name:"Santosh Bist",phone:"9858751161",email:"<EMAIL>",photoUrl:"https://fwu.edu.np/assets/uploads/employee-photo/photo-1624353722-sms.jpg"};function eP(){return(0,s.jsxs)("main",{className:"bg-white relative overflow-hidden",children:[(0,s.jsx)("div",{className:"absolute top-0 right-0 w-[600px] h-[600px] bg-indigo-100 rounded-full opacity-30 blur-3xl -translate-x-1/3 -translate-y-1/3"}),(0,s.jsx)("div",{className:"absolute bottom-0 left-0 w-[500px] h-[500px] bg-blue-100 rounded-full opacity-30 blur-3xl translate-x-1/4 translate-y-1/4"}),(0,s.jsx)("div",{className:"absolute inset-0 opacity-5",style:{backgroundImage:"radial-gradient(#4338ca 1px, transparent 1px)",backgroundSize:"40px 40px"}}),(0,s.jsxs)("section",{className:"relative py-20 md:py-28 bg-gradient-to-r from-indigo-900 via-indigo-800 to-blue-900 text-white overflow-hidden",children:[(0,s.jsxs)("div",{className:"absolute inset-0 overflow-hidden",children:[(0,s.jsx)("div",{className:"absolute top-20 left-10 w-64 h-64 rounded-full bg-indigo-500 opacity-10 animate-float-slow"}),(0,s.jsx)("div",{className:"absolute bottom-20 right-10 w-96 h-96 rounded-full bg-blue-500 opacity-10 animate-float-reverse"}),(0,s.jsx)("div",{className:"absolute top-1/2 left-1/4 w-32 h-32 rounded-full bg-purple-500 opacity-5 animate-pulse"}),(0,s.jsx)("div",{className:"absolute inset-0 opacity-10",style:{backgroundImage:"radial-gradient(circle, rgba(255,255,255,0.1) 1px, transparent 1px)",backgroundSize:"30px 30px"}})]}),(0,s.jsx)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8 relative z-10",children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto text-center",children:[(0,s.jsx)("div",{className:"inline-block mb-6 p-2 bg-indigo-800/30 rounded-full",children:(0,s.jsx)("div",{className:"px-4 py-1 bg-indigo-700/50 rounded-full",children:(0,s.jsx)("span",{className:"text-indigo-100 font-medium",children:"FWU Incubation Center"})})}),(0,s.jsxs)("h1",{className:"text-4xl sm:text-5xl md:text-6xl font-extrabold mb-6 leading-tight",children:["Get In ",(0,s.jsx)("span",{className:"text-transparent bg-clip-text bg-gradient-to-r from-blue-300 to-indigo-300",children:"Touch"})]}),(0,s.jsx)("p",{className:"text-xl text-indigo-100 max-w-3xl mx-auto mb-10 leading-relaxed",children:"We're here to help and answer any questions you might have about the FWU Incubation Center. Connect with us to learn more about our programs and opportunities."}),(0,s.jsxs)("div",{className:"flex flex-wrap justify-center gap-4 mb-8",children:[(0,s.jsxs)("div",{className:"flex items-center bg-white/10 backdrop-blur-sm px-4 py-2 rounded-full",children:[(0,s.jsx)(i.A,{className:"text-blue-300 mr-2"}),(0,s.jsx)("span",{className:"text-white text-sm",children:eq.phone})]}),(0,s.jsxs)("div",{className:"flex items-center bg-white/10 backdrop-blur-sm px-4 py-2 rounded-full",children:[(0,s.jsx)(a.A,{className:"text-blue-300 mr-2"}),(0,s.jsx)("span",{className:"text-white text-sm",children:eq.email})]}),(0,s.jsxs)("div",{className:"flex items-center bg-white/10 backdrop-blur-sm px-4 py-2 rounded-full",children:[(0,s.jsx)(l.A,{className:"text-blue-300 mr-2"}),(0,s.jsx)("span",{className:"text-white text-sm",children:"Mahendranagar, Kanchanpur"})]})]}),(0,s.jsxs)("a",{href:"#contact-form",className:"inline-flex items-center bg-blue-600 hover:bg-blue-700 text-white font-bold py-4 px-8 rounded-lg shadow-lg transition-all duration-300 hover:scale-105 transform",children:["Send Message ",(0,s.jsx)(n.A,{className:"ml-2"})]})]})}),(0,s.jsx)("div",{className:"absolute bottom-0 left-0 right-0",children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1440 120",className:"w-full h-auto",children:(0,s.jsx)("path",{fill:"#ffffff",fillOpacity:"1",d:"M0,64L80,69.3C160,75,320,85,480,80C640,75,800,53,960,48C1120,43,1280,53,1360,58.7L1440,64L1440,120L1360,120C1280,120,1120,120,960,120C800,120,640,120,480,120C320,120,160,120,80,120L0,120Z"})})})]}),(0,s.jsx)("section",{className:"py-16 relative z-10",children:(0,s.jsx)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"grid md:grid-cols-3 gap-8",children:[(0,s.jsxs)("div",{className:"bg-white rounded-2xl shadow-xl p-8 border border-gray-100 hover:border-indigo-100 transition-all duration-300 hover:shadow-indigo-100/20 group",children:[(0,s.jsx)("div",{className:"w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mb-6 group-hover:bg-indigo-200 transition-colors",children:(0,s.jsx)(l.A,{className:"text-indigo-600 text-2xl"})}),(0,s.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-3",children:"Visit Us"}),(0,s.jsx)("p",{className:"text-gray-600 mb-4",children:eq.address}),(0,s.jsxs)("a",{href:"https://maps.app.goo.gl/Ehu1U2FZzjRUsGEB6",target:"_blank",rel:"noopener noreferrer",className:"inline-flex items-center text-indigo-600 font-semibold hover:text-indigo-800 transition-colors group",children:["Get Directions ",(0,s.jsx)(n.A,{className:"ml-2 group-hover:translate-x-1 transition-transform"})]})]}),(0,s.jsxs)("div",{className:"bg-white rounded-2xl shadow-xl p-8 border border-gray-100 hover:border-indigo-100 transition-all duration-300 hover:shadow-indigo-100/20 group",children:[(0,s.jsx)("div",{className:"w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mb-6 group-hover:bg-indigo-200 transition-colors",children:(0,s.jsx)(i.A,{className:"text-indigo-600 text-2xl"})}),(0,s.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-3",children:"Call Us"}),(0,s.jsx)("p",{className:"text-gray-600 mb-4",children:"Have questions? Call us directly at:"}),(0,s.jsx)("a",{href:`tel:${eq.phone.replace(/\s+/g,"")}`,className:"text-xl font-bold text-indigo-600 hover:text-indigo-800 transition-colors block mb-4",children:eq.phone}),(0,s.jsx)("p",{className:"text-sm text-gray-500",children:"Available during office hours"})]}),(0,s.jsxs)("div",{className:"bg-white rounded-2xl shadow-xl p-8 border border-gray-100 hover:border-indigo-100 transition-all duration-300 hover:shadow-indigo-100/20 group",children:[(0,s.jsx)("div",{className:"w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mb-6 group-hover:bg-indigo-200 transition-colors",children:(0,s.jsx)(a.A,{className:"text-indigo-600 text-2xl"})}),(0,s.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-3",children:"Email Us"}),(0,s.jsx)("p",{className:"text-gray-600 mb-4",children:"For general inquiries:"}),(0,s.jsx)("a",{href:`mailto:${eq.email}`,className:"text-indigo-600 hover:text-indigo-800 transition-colors block mb-4 font-medium",children:eq.email}),(0,s.jsx)("p",{className:"text-gray-600 mb-2",children:"For incubation center:"}),(0,s.jsx)("a",{href:`mailto:${eq.incubationEmail}`,className:"text-indigo-600 hover:text-indigo-800 transition-colors block font-medium",children:eq.incubationEmail})]})]})})}),(0,s.jsx)("section",{id:"contact-form",className:"py-16 md:py-24 relative z-10",children:(0,s.jsxs)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:[(0,s.jsxs)("div",{className:"max-w-3xl mx-auto text-center mb-16",children:[(0,s.jsx)("div",{className:"inline-block mb-4",children:(0,s.jsx)("div",{className:"flex items-center justify-center w-16 h-16 mx-auto bg-indigo-100 rounded-full mb-4",children:(0,s.jsx)(o.A,{className:"h-8 w-8 text-indigo-600"})})}),(0,s.jsx)("h2",{className:"text-3xl md:text-4xl font-bold text-gray-900 mb-4",children:"Send Us a Message"}),(0,s.jsx)("div",{className:"w-24 h-1 bg-indigo-600 mx-auto mb-6 rounded-full"}),(0,s.jsx)("p",{className:"text-xl text-gray-600 leading-relaxed",children:"Have questions about our incubation programs or want to learn more? Fill out the form below and we'll get back to you as soon as possible."})]}),(0,s.jsxs)("div",{className:"grid lg:grid-cols-2 gap-12 items-start",children:[(0,s.jsx)(eS,{}),(0,s.jsxs)("div",{className:"space-y-12",children:[(0,s.jsx)(eL,{details:eq}),(0,s.jsxs)("div",{className:"bg-white p-8 md:p-10 rounded-2xl shadow-xl border border-gray-100 hover:border-indigo-100 transition-all duration-300 hover:shadow-indigo-100/20",children:[(0,s.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Information Officer"}),(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"relative w-20 h-20 rounded-full overflow-hidden mr-6 border-2 border-indigo-100",children:(0,s.jsx)(ez.default,{src:eB.photoUrl,alt:eB.name,fill:!0,className:"object-cover"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-xl font-bold text-gray-900",children:eB.name}),(0,s.jsx)("p",{className:"text-gray-600 mb-2",children:"Information Officer"}),(0,s.jsxs)("div",{className:"flex items-center text-indigo-600 mb-1",children:[(0,s.jsx)(i.A,{className:"mr-2 text-sm"}),(0,s.jsx)("a",{href:`tel:${eB.phone}`,className:"hover:text-indigo-800 transition-colors",children:eB.phone})]}),(0,s.jsxs)("div",{className:"flex items-center text-indigo-600",children:[(0,s.jsx)(a.A,{className:"mr-2 text-sm"}),(0,s.jsx)("a",{href:`mailto:${eB.email}`,className:"hover:text-indigo-800 transition-colors",children:eB.email})]})]})]})]}),(0,s.jsx)(eE,{embedUrl:eq.mapEmbedUrl,address:eq.address})]})]})]})}),(0,s.jsxs)("section",{className:"py-16 bg-gradient-to-r from-indigo-900 to-blue-900 text-white relative overflow-hidden",children:[(0,s.jsxs)("div",{className:"absolute inset-0 overflow-hidden",children:[(0,s.jsx)("div",{className:"absolute top-0 right-0 w-64 h-64 rounded-full bg-indigo-500 opacity-10"}),(0,s.jsx)("div",{className:"absolute bottom-0 left-0 w-96 h-96 rounded-full bg-blue-500 opacity-10"})]}),(0,s.jsx)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8 relative z-10",children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto text-center",children:[(0,s.jsx)("h2",{className:"text-3xl md:text-4xl font-bold mb-6",children:"Ready to Join Our Incubation Center?"}),(0,s.jsx)("p",{className:"text-xl text-indigo-100 mb-10 leading-relaxed",children:"Apply now to transform your innovative idea into a successful business with our expert mentorship, resources, and collaborative ecosystem."}),(0,s.jsxs)("div",{className:"flex flex-wrap justify-center gap-6",children:[(0,s.jsx)(eU(),{href:"/apply",className:"bg-white text-indigo-900 hover:bg-indigo-50 font-bold py-4 px-8 rounded-lg shadow-lg transition-all duration-300 hover:scale-105 transform",children:"Apply Now"}),(0,s.jsx)(eU(),{href:"/programs",className:"bg-transparent border-2 border-white text-white hover:bg-white/10 font-bold py-4 px-8 rounded-lg shadow-lg transition-all duration-300 hover:scale-105 transform",children:"Explore Programs"})]})]})})]})]})}},47460:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>l.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var s=r(65239),i=r(48088),a=r(88170),l=r.n(a),n=r(30893),o={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);r.d(t,o);let d={children:["",{children:["fwu",{children:["contact",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,47886)),"D:\\fwu_incubation(react+laravel)\\incubation\\frontend\\src\\app\\fwu\\contact\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,45689)),"D:\\fwu_incubation(react+laravel)\\incubation\\frontend\\src\\app\\fwu\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,53862)),"D:\\fwu_incubation(react+laravel)\\incubation\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"D:\\fwu_incubation(react+laravel)\\incubation\\frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["D:\\fwu_incubation(react+laravel)\\incubation\\frontend\\src\\app\\fwu\\contact\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/fwu/contact/page",pathname:"/fwu/contact",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},47886:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\fwu_incubation(react+laravel)\\\\incubation\\\\frontend\\\\src\\\\app\\\\fwu\\\\contact\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\fwu_incubation(react+laravel)\\incubation\\frontend\\src\\app\\fwu\\contact\\page.tsx","default")},48730:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},58887:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("message-square",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70334:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},79551:e=>{"use strict";e.exports=require("url")},89157:(e,t,r)=>{Promise.resolve().then(r.bind(r,47886))},93613:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,693,585,171,305],()=>r(47460));module.exports=s})();