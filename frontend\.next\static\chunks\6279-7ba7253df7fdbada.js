"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6279],{5845:(e,t,r)=>{r.d(t,{i:()=>l});var n,o=r(12115),a=r(52712),i=(n||(n=r.t(o,2)))[" useInsertionEffect ".trim().toString()]||a.N;function l({prop:e,defaultProp:t,onChange:r=()=>{},caller:n}){let[a,l,s]=function({defaultProp:e,onChange:t}){let[r,n]=o.useState(e),a=o.useRef(r),l=o.useRef(t);return i(()=>{l.current=t},[t]),o.useEffect(()=>{a.current!==r&&(l.current?.(r),a.current=r)},[r,a]),[r,n,l]}({defaultProp:t,onChange:r}),c=void 0!==e,d=c?e:a;{let t=o.useRef(void 0!==e);o.useEffect(()=>{let e=t.current;if(e!==c){let t=c?"controlled":"uncontrolled";console.warn(`${n} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=c},[c,n])}return[d,o.useCallback(t=>{if(c){let r="function"==typeof t?t(e):t;r!==e&&s.current?.(r)}else l(t)},[c,e,l,s])]}Symbol("RADIX:SYNC_STATE")},6101:(e,t,r)=>{r.d(t,{s:()=>i,t:()=>a});var n=r(12115);function o(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function a(...e){return t=>{let r=!1,n=e.map(e=>{let n=o(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():o(e[t],null)}}}}function i(...e){return n.useCallback(a(...e),e)}},15452:(e,t,r)=>{r.d(t,{UC:()=>er,VY:()=>eo,ZL:()=>ee,bL:()=>J,bm:()=>ea,hE:()=>en,hJ:()=>et,l9:()=>Q});var n=r(12115),o=r(85185),a=r(6101),i=r(46081),l=r(61285),s=r(5845),c=r(19178),d=r(25519),u=r(34378),f=r(28905),m=r(63655),p=r(92293),g=r(93795),v=r(38168),b=r(99708),h=r(95155),y="Dialog",[w,x]=(0,i.A)(y),[k,E]=w(y),N=e=>{let{__scopeDialog:t,children:r,open:o,defaultOpen:a,onOpenChange:i,modal:c=!0}=e,d=n.useRef(null),u=n.useRef(null),[f,m]=(0,s.i)({prop:o,defaultProp:null!=a&&a,onChange:i,caller:y});return(0,h.jsx)(k,{scope:t,triggerRef:d,contentRef:u,contentId:(0,l.B)(),titleId:(0,l.B)(),descriptionId:(0,l.B)(),open:f,onOpenChange:m,onOpenToggle:n.useCallback(()=>m(e=>!e),[m]),modal:c,children:r})};N.displayName=y;var C="DialogTrigger",S=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,i=E(C,r),l=(0,a.s)(t,i.triggerRef);return(0,h.jsx)(m.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":i.open,"aria-controls":i.contentId,"data-state":K(i.open),...n,ref:l,onClick:(0,o.m)(e.onClick,i.onOpenToggle)})});S.displayName=C;var R="DialogPortal",[O,M]=w(R,{forceMount:void 0}),P=e=>{let{__scopeDialog:t,forceMount:r,children:o,container:a}=e,i=E(R,t);return(0,h.jsx)(O,{scope:t,forceMount:r,children:n.Children.map(o,e=>(0,h.jsx)(f.C,{present:r||i.open,children:(0,h.jsx)(u.Z,{asChild:!0,container:a,children:e})}))})};P.displayName=R;var j="DialogOverlay",D=n.forwardRef((e,t)=>{let r=M(j,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,a=E(j,e.__scopeDialog);return a.modal?(0,h.jsx)(f.C,{present:n||a.open,children:(0,h.jsx)(A,{...o,ref:t})}):null});D.displayName=j;var T=(0,b.TL)("DialogOverlay.RemoveScroll"),A=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=E(j,r);return(0,h.jsx)(g.A,{as:T,allowPinchZoom:!0,shards:[o.contentRef],children:(0,h.jsx)(m.sG.div,{"data-state":K(o.open),...n,ref:t,style:{pointerEvents:"auto",...n.style}})})}),L="DialogContent",z=n.forwardRef((e,t)=>{let r=M(L,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,a=E(L,e.__scopeDialog);return(0,h.jsx)(f.C,{present:n||a.open,children:a.modal?(0,h.jsx)(I,{...o,ref:t}):(0,h.jsx)(_,{...o,ref:t})})});z.displayName=L;var I=n.forwardRef((e,t)=>{let r=E(L,e.__scopeDialog),i=n.useRef(null),l=(0,a.s)(t,r.contentRef,i);return n.useEffect(()=>{let e=i.current;if(e)return(0,v.Eq)(e)},[]),(0,h.jsx)(W,{...e,ref:l,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null==(t=r.triggerRef.current)||t.focus()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;(2===t.button||r)&&e.preventDefault()}),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault())})}),_=n.forwardRef((e,t)=>{let r=E(L,e.__scopeDialog),o=n.useRef(!1),a=n.useRef(!1);return(0,h.jsx)(W,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var n,i;null==(n=e.onCloseAutoFocus)||n.call(e,t),t.defaultPrevented||(o.current||null==(i=r.triggerRef.current)||i.focus(),t.preventDefault()),o.current=!1,a.current=!1},onInteractOutside:t=>{var n,i;null==(n=e.onInteractOutside)||n.call(e,t),t.defaultPrevented||(o.current=!0,"pointerdown"===t.detail.originalEvent.type&&(a.current=!0));let l=t.target;(null==(i=r.triggerRef.current)?void 0:i.contains(l))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&a.current&&t.preventDefault()}})}),W=n.forwardRef((e,t)=>{let{__scopeDialog:r,trapFocus:o,onOpenAutoFocus:i,onCloseAutoFocus:l,...s}=e,u=E(L,r),f=n.useRef(null),m=(0,a.s)(t,f);return(0,p.Oh)(),(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(d.n,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:i,onUnmountAutoFocus:l,children:(0,h.jsx)(c.qW,{role:"dialog",id:u.contentId,"aria-describedby":u.descriptionId,"aria-labelledby":u.titleId,"data-state":K(u.open),...s,ref:m,onDismiss:()=>u.onOpenChange(!1)})}),(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(V,{titleId:u.titleId}),(0,h.jsx)(H,{contentRef:f,descriptionId:u.descriptionId})]})]})}),F="DialogTitle",$=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=E(F,r);return(0,h.jsx)(m.sG.h2,{id:o.titleId,...n,ref:t})});$.displayName=F;var B="DialogDescription",G=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=E(B,r);return(0,h.jsx)(m.sG.p,{id:o.descriptionId,...n,ref:t})});G.displayName=B;var U="DialogClose",q=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,a=E(U,r);return(0,h.jsx)(m.sG.button,{type:"button",...n,ref:t,onClick:(0,o.m)(e.onClick,()=>a.onOpenChange(!1))})});function K(e){return e?"open":"closed"}q.displayName=U;var X="DialogTitleWarning",[Y,Z]=(0,i.q)(X,{contentName:L,titleName:F,docsSlug:"dialog"}),V=e=>{let{titleId:t}=e,r=Z(X),o="`".concat(r.contentName,"` requires a `").concat(r.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(r.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(r.docsSlug);return n.useEffect(()=>{t&&(document.getElementById(t)||console.error(o))},[o,t]),null},H=e=>{let{contentRef:t,descriptionId:r}=e,o=Z("DialogDescriptionWarning"),a="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(o.contentName,"}.");return n.useEffect(()=>{var e;let n=null==(e=t.current)?void 0:e.getAttribute("aria-describedby");r&&n&&(document.getElementById(r)||console.warn(a))},[a,t,r]),null},J=N,Q=S,ee=P,et=D,er=z,en=$,eo=G,ea=q},19178:(e,t,r)=>{r.d(t,{qW:()=>f});var n,o=r(12115),a=r(85185),i=r(63655),l=r(6101),s=r(39033),c=r(95155),d="dismissableLayer.update",u=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),f=o.forwardRef((e,t)=>{var r,f;let{disableOutsidePointerEvents:g=!1,onEscapeKeyDown:v,onPointerDownOutside:b,onFocusOutside:h,onInteractOutside:y,onDismiss:w,...x}=e,k=o.useContext(u),[E,N]=o.useState(null),C=null!=(f=null==E?void 0:E.ownerDocument)?f:null==(r=globalThis)?void 0:r.document,[,S]=o.useState({}),R=(0,l.s)(t,e=>N(e)),O=Array.from(k.layers),[M]=[...k.layersWithOutsidePointerEventsDisabled].slice(-1),P=O.indexOf(M),j=E?O.indexOf(E):-1,D=k.layersWithOutsidePointerEventsDisabled.size>0,T=j>=P,A=function(e){var t;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,n=(0,s.c)(e),a=o.useRef(!1),i=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!a.current){let t=function(){p("dismissableLayer.pointerDownOutside",n,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(r.removeEventListener("click",i.current),i.current=t,r.addEventListener("click",i.current,{once:!0})):t()}else r.removeEventListener("click",i.current);a.current=!1},t=window.setTimeout(()=>{r.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),r.removeEventListener("pointerdown",e),r.removeEventListener("click",i.current)}},[r,n]),{onPointerDownCapture:()=>a.current=!0}}(e=>{let t=e.target,r=[...k.branches].some(e=>e.contains(t));T&&!r&&(null==b||b(e),null==y||y(e),e.defaultPrevented||null==w||w())},C),L=function(e){var t;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,n=(0,s.c)(e),a=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!a.current&&p("dismissableLayer.focusOutside",n,{originalEvent:e},{discrete:!1})};return r.addEventListener("focusin",e),()=>r.removeEventListener("focusin",e)},[r,n]),{onFocusCapture:()=>a.current=!0,onBlurCapture:()=>a.current=!1}}(e=>{let t=e.target;![...k.branches].some(e=>e.contains(t))&&(null==h||h(e),null==y||y(e),e.defaultPrevented||null==w||w())},C);return!function(e,t=globalThis?.document){let r=(0,s.c)(e);o.useEffect(()=>{let e=e=>{"Escape"===e.key&&r(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[r,t])}(e=>{j===k.layers.size-1&&(null==v||v(e),!e.defaultPrevented&&w&&(e.preventDefault(),w()))},C),o.useEffect(()=>{if(E)return g&&(0===k.layersWithOutsidePointerEventsDisabled.size&&(n=C.body.style.pointerEvents,C.body.style.pointerEvents="none"),k.layersWithOutsidePointerEventsDisabled.add(E)),k.layers.add(E),m(),()=>{g&&1===k.layersWithOutsidePointerEventsDisabled.size&&(C.body.style.pointerEvents=n)}},[E,C,g,k]),o.useEffect(()=>()=>{E&&(k.layers.delete(E),k.layersWithOutsidePointerEventsDisabled.delete(E),m())},[E,k]),o.useEffect(()=>{let e=()=>S({});return document.addEventListener(d,e),()=>document.removeEventListener(d,e)},[]),(0,c.jsx)(i.sG.div,{...x,ref:R,style:{pointerEvents:D?T?"auto":"none":void 0,...e.style},onFocusCapture:(0,a.m)(e.onFocusCapture,L.onFocusCapture),onBlurCapture:(0,a.m)(e.onBlurCapture,L.onBlurCapture),onPointerDownCapture:(0,a.m)(e.onPointerDownCapture,A.onPointerDownCapture)})});function m(){let e=new CustomEvent(d);document.dispatchEvent(e)}function p(e,t,r,n){let{discrete:o}=n,a=r.originalEvent.target,l=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:r});t&&a.addEventListener(e,t,{once:!0}),o?(0,i.hO)(a,l):a.dispatchEvent(l)}f.displayName="DismissableLayer",o.forwardRef((e,t)=>{let r=o.useContext(u),n=o.useRef(null),a=(0,l.s)(t,n);return o.useEffect(()=>{let e=n.current;if(e)return r.branches.add(e),()=>{r.branches.delete(e)}},[r.branches]),(0,c.jsx)(i.sG.div,{...e,ref:a})}).displayName="DismissableLayerBranch"},25519:(e,t,r)=>{r.d(t,{n:()=>u});var n=r(12115),o=r(6101),a=r(63655),i=r(39033),l=r(95155),s="focusScope.autoFocusOnMount",c="focusScope.autoFocusOnUnmount",d={bubbles:!1,cancelable:!0},u=n.forwardRef((e,t)=>{let{loop:r=!1,trapped:u=!1,onMountAutoFocus:v,onUnmountAutoFocus:b,...h}=e,[y,w]=n.useState(null),x=(0,i.c)(v),k=(0,i.c)(b),E=n.useRef(null),N=(0,o.s)(t,e=>w(e)),C=n.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;n.useEffect(()=>{if(u){let e=function(e){if(C.paused||!y)return;let t=e.target;y.contains(t)?E.current=t:p(E.current,{select:!0})},t=function(e){if(C.paused||!y)return;let t=e.relatedTarget;null!==t&&(y.contains(t)||p(E.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let r=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&p(y)});return y&&r.observe(y,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),r.disconnect()}}},[u,y,C.paused]),n.useEffect(()=>{if(y){g.add(C);let e=document.activeElement;if(!y.contains(e)){let t=new CustomEvent(s,d);y.addEventListener(s,x),y.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=document.activeElement;for(let n of e)if(p(n,{select:t}),document.activeElement!==r)return}(f(y).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&p(y))}return()=>{y.removeEventListener(s,x),setTimeout(()=>{let t=new CustomEvent(c,d);y.addEventListener(c,k),y.dispatchEvent(t),t.defaultPrevented||p(null!=e?e:document.body,{select:!0}),y.removeEventListener(c,k),g.remove(C)},0)}}},[y,x,k,C]);let S=n.useCallback(e=>{if(!r&&!u||C.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,n=document.activeElement;if(t&&n){let t=e.currentTarget,[o,a]=function(e){let t=f(e);return[m(t,e),m(t.reverse(),e)]}(t);o&&a?e.shiftKey||n!==a?e.shiftKey&&n===o&&(e.preventDefault(),r&&p(a,{select:!0})):(e.preventDefault(),r&&p(o,{select:!0})):n===t&&e.preventDefault()}},[r,u,C.paused]);return(0,l.jsx)(a.sG.div,{tabIndex:-1,...h,ref:N,onKeyDown:S})});function f(e){let t=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)t.push(r.currentNode);return t}function m(e,t){for(let r of e)if(!function(e,t){let{upTo:r}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===r||e!==r);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(r,{upTo:t}))return r}function p(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var r;let n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&(r=e)instanceof HTMLInputElement&&"select"in r&&t&&e.select()}}u.displayName="FocusScope";var g=function(){let e=[];return{add(t){let r=e[0];t!==r&&(null==r||r.pause()),(e=v(e,t)).unshift(t)},remove(t){var r;null==(r=(e=v(e,t))[0])||r.resume()}}}();function v(e,t){let r=[...e],n=r.indexOf(t);return -1!==n&&r.splice(n,1),r}},27213:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]])},28905:(e,t,r)=>{r.d(t,{C:()=>i});var n=r(12115),o=r(6101),a=r(52712),i=e=>{let{present:t,children:r}=e,i=function(e){var t,r;let[o,i]=n.useState(),s=n.useRef(null),c=n.useRef(e),d=n.useRef("none"),[u,f]=(t=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},n.useReducer((e,t)=>{let n=r[e][t];return null!=n?n:e},t));return n.useEffect(()=>{let e=l(s.current);d.current="mounted"===u?e:"none"},[u]),(0,a.N)(()=>{let t=s.current,r=c.current;if(r!==e){let n=d.current,o=l(t);e?f("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?f("UNMOUNT"):r&&n!==o?f("ANIMATION_OUT"):f("UNMOUNT"),c.current=e}},[e,f]),(0,a.N)(()=>{if(o){var e;let t,r=null!=(e=o.ownerDocument.defaultView)?e:window,n=e=>{let n=l(s.current).includes(e.animationName);if(e.target===o&&n&&(f("ANIMATION_END"),!c.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",t=r.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},a=e=>{e.target===o&&(d.current=l(s.current))};return o.addEventListener("animationstart",a),o.addEventListener("animationcancel",n),o.addEventListener("animationend",n),()=>{r.clearTimeout(t),o.removeEventListener("animationstart",a),o.removeEventListener("animationcancel",n),o.removeEventListener("animationend",n)}}f("ANIMATION_END")},[o,f]),{isPresent:["mounted","unmountSuspended"].includes(u),ref:n.useCallback(e=>{s.current=e?getComputedStyle(e):null,i(e)},[])}}(t),s="function"==typeof r?r({present:i.isPresent}):n.Children.only(r),c=(0,o.s)(i.ref,function(e){var t,r;let n=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,o=n&&"isReactWarning"in n&&n.isReactWarning;return o?e.ref:(o=(n=null==(r=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:r.get)&&"isReactWarning"in n&&n.isReactWarning)?e.props.ref:e.props.ref||e.ref}(s));return"function"==typeof r||i.isPresent?n.cloneElement(s,{ref:c}):null};function l(e){return(null==e?void 0:e.animationName)||"none"}i.displayName="Presence"},34378:(e,t,r)=>{r.d(t,{Z:()=>s});var n=r(12115),o=r(47650),a=r(63655),i=r(52712),l=r(95155),s=n.forwardRef((e,t)=>{var r,s;let{container:c,...d}=e,[u,f]=n.useState(!1);(0,i.N)(()=>f(!0),[]);let m=c||u&&(null==(s=globalThis)||null==(r=s.document)?void 0:r.body);return m?o.createPortal((0,l.jsx)(a.sG.div,{...d,ref:t}),m):null});s.displayName="Portal"},38168:(e,t,r)=>{r.d(t,{Eq:()=>d});var n=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},o=new WeakMap,a=new WeakMap,i={},l=0,s=function(e){return e&&(e.host||s(e.parentNode))},c=function(e,t,r,n){var c=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var r=s(e);return r&&t.contains(r)?r:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});i[r]||(i[r]=new WeakMap);var d=i[r],u=[],f=new Set,m=new Set(c),p=function(e){!e||f.has(e)||(f.add(e),p(e.parentNode))};c.forEach(p);var g=function(e){!e||m.has(e)||Array.prototype.forEach.call(e.children,function(e){if(f.has(e))g(e);else try{var t=e.getAttribute(n),i=null!==t&&"false"!==t,l=(o.get(e)||0)+1,s=(d.get(e)||0)+1;o.set(e,l),d.set(e,s),u.push(e),1===l&&i&&a.set(e,!0),1===s&&e.setAttribute(r,"true"),i||e.setAttribute(n,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return g(t),f.clear(),l++,function(){u.forEach(function(e){var t=o.get(e)-1,i=d.get(e)-1;o.set(e,t),d.set(e,i),t||(a.has(e)||e.removeAttribute(n),a.delete(e)),i||e.removeAttribute(r)}),--l||(o=new WeakMap,o=new WeakMap,a=new WeakMap,i={})}},d=function(e,t,r){void 0===r&&(r="data-aria-hidden");var o=Array.from(Array.isArray(e)?e:[e]),a=t||n(e);return a?(o.push.apply(o,Array.from(a.querySelectorAll("[aria-live], script"))),c(o,a,r,"aria-hidden")):function(){return null}}},39033:(e,t,r)=>{r.d(t,{c:()=>o});var n=r(12115);function o(e){let t=n.useRef(e);return n.useEffect(()=>{t.current=e}),n.useMemo(()=>(...e)=>t.current?.(...e),[])}},39688:(e,t,r)=>{r.d(t,{QP:()=>ec});let n=e=>{let t=l(e),{conflictingClassGroups:r,conflictingClassGroupModifiers:n}=e;return{getClassGroupId:e=>{let r=e.split("-");return""===r[0]&&1!==r.length&&r.shift(),o(r,t)||i(e)},getConflictingClassGroupIds:(e,t)=>{let o=r[e]||[];return t&&n[e]?[...o,...n[e]]:o}}},o=(e,t)=>{if(0===e.length)return t.classGroupId;let r=e[0],n=t.nextPart.get(r),a=n?o(e.slice(1),n):void 0;if(a)return a;if(0===t.validators.length)return;let i=e.join("-");return t.validators.find(({validator:e})=>e(i))?.classGroupId},a=/^\[(.+)\]$/,i=e=>{if(a.test(e)){let t=a.exec(e)[1],r=t?.substring(0,t.indexOf(":"));if(r)return"arbitrary.."+r}},l=e=>{let{theme:t,classGroups:r}=e,n={nextPart:new Map,validators:[]};for(let e in r)s(r[e],n,e,t);return n},s=(e,t,r,n)=>{e.forEach(e=>{if("string"==typeof e){(""===e?t:c(t,e)).classGroupId=r;return}if("function"==typeof e)return d(e)?void s(e(n),t,r,n):void t.validators.push({validator:e,classGroupId:r});Object.entries(e).forEach(([e,o])=>{s(o,c(t,e),r,n)})})},c=(e,t)=>{let r=e;return t.split("-").forEach(e=>{r.nextPart.has(e)||r.nextPart.set(e,{nextPart:new Map,validators:[]}),r=r.nextPart.get(e)}),r},d=e=>e.isThemeGetter,u=e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let t=0,r=new Map,n=new Map,o=(o,a)=>{r.set(o,a),++t>e&&(t=0,n=r,r=new Map)};return{get(e){let t=r.get(e);return void 0!==t?t:void 0!==(t=n.get(e))?(o(e,t),t):void 0},set(e,t){r.has(e)?r.set(e,t):o(e,t)}}},f=e=>{let{prefix:t,experimentalParseClassName:r}=e,n=e=>{let t,r=[],n=0,o=0,a=0;for(let i=0;i<e.length;i++){let l=e[i];if(0===n&&0===o){if(":"===l){r.push(e.slice(a,i)),a=i+1;continue}if("/"===l){t=i;continue}}"["===l?n++:"]"===l?n--:"("===l?o++:")"===l&&o--}let i=0===r.length?e:e.substring(a),l=m(i);return{modifiers:r,hasImportantModifier:l!==i,baseClassName:l,maybePostfixModifierPosition:t&&t>a?t-a:void 0}};if(t){let e=t+":",r=n;n=t=>t.startsWith(e)?r(t.substring(e.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:t,maybePostfixModifierPosition:void 0}}if(r){let e=n;n=t=>r({className:t,parseClassName:e})}return n},m=e=>e.endsWith("!")?e.substring(0,e.length-1):e.startsWith("!")?e.substring(1):e,p=e=>{let t=Object.fromEntries(e.orderSensitiveModifiers.map(e=>[e,!0]));return e=>{if(e.length<=1)return e;let r=[],n=[];return e.forEach(e=>{"["===e[0]||t[e]?(r.push(...n.sort(),e),n=[]):n.push(e)}),r.push(...n.sort()),r}},g=e=>({cache:u(e.cacheSize),parseClassName:f(e),sortModifiers:p(e),...n(e)}),v=/\s+/,b=(e,t)=>{let{parseClassName:r,getClassGroupId:n,getConflictingClassGroupIds:o,sortModifiers:a}=t,i=[],l=e.trim().split(v),s="";for(let e=l.length-1;e>=0;e-=1){let t=l[e],{isExternal:c,modifiers:d,hasImportantModifier:u,baseClassName:f,maybePostfixModifierPosition:m}=r(t);if(c){s=t+(s.length>0?" "+s:s);continue}let p=!!m,g=n(p?f.substring(0,m):f);if(!g){if(!p||!(g=n(f))){s=t+(s.length>0?" "+s:s);continue}p=!1}let v=a(d).join(":"),b=u?v+"!":v,h=b+g;if(i.includes(h))continue;i.push(h);let y=o(g,p);for(let e=0;e<y.length;++e){let t=y[e];i.push(b+t)}s=t+(s.length>0?" "+s:s)}return s};function h(){let e,t,r=0,n="";for(;r<arguments.length;)(e=arguments[r++])&&(t=y(e))&&(n&&(n+=" "),n+=t);return n}let y=e=>{let t;if("string"==typeof e)return e;let r="";for(let n=0;n<e.length;n++)e[n]&&(t=y(e[n]))&&(r&&(r+=" "),r+=t);return r},w=e=>{let t=t=>t[e]||[];return t.isThemeGetter=!0,t},x=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,k=/^\((?:(\w[\w-]*):)?(.+)\)$/i,E=/^\d+\/\d+$/,N=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,C=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,S=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,R=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,O=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,M=e=>E.test(e),P=e=>!!e&&!Number.isNaN(Number(e)),j=e=>!!e&&Number.isInteger(Number(e)),D=e=>e.endsWith("%")&&P(e.slice(0,-1)),T=e=>N.test(e),A=()=>!0,L=e=>C.test(e)&&!S.test(e),z=()=>!1,I=e=>R.test(e),_=e=>O.test(e),W=e=>!$(e)&&!X(e),F=e=>ee(e,eo,z),$=e=>x.test(e),B=e=>ee(e,ea,L),G=e=>ee(e,ei,P),U=e=>ee(e,er,z),q=e=>ee(e,en,_),K=e=>ee(e,es,I),X=e=>k.test(e),Y=e=>et(e,ea),Z=e=>et(e,el),V=e=>et(e,er),H=e=>et(e,eo),J=e=>et(e,en),Q=e=>et(e,es,!0),ee=(e,t,r)=>{let n=x.exec(e);return!!n&&(n[1]?t(n[1]):r(n[2]))},et=(e,t,r=!1)=>{let n=k.exec(e);return!!n&&(n[1]?t(n[1]):r)},er=e=>"position"===e||"percentage"===e,en=e=>"image"===e||"url"===e,eo=e=>"length"===e||"size"===e||"bg-size"===e,ea=e=>"length"===e,ei=e=>"number"===e,el=e=>"family-name"===e,es=e=>"shadow"===e;Symbol.toStringTag;let ec=function(e,...t){let r,n,o,a=function(l){return n=(r=g(t.reduce((e,t)=>t(e),e()))).cache.get,o=r.cache.set,a=i,i(l)};function i(e){let t=n(e);if(t)return t;let a=b(e,r);return o(e,a),a}return function(){return a(h.apply(null,arguments))}}(()=>{let e=w("color"),t=w("font"),r=w("text"),n=w("font-weight"),o=w("tracking"),a=w("leading"),i=w("breakpoint"),l=w("container"),s=w("spacing"),c=w("radius"),d=w("shadow"),u=w("inset-shadow"),f=w("text-shadow"),m=w("drop-shadow"),p=w("blur"),g=w("perspective"),v=w("aspect"),b=w("ease"),h=w("animate"),y=()=>["auto","avoid","all","avoid-page","page","left","right","column"],x=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],k=()=>[...x(),X,$],E=()=>["auto","hidden","clip","visible","scroll"],N=()=>["auto","contain","none"],C=()=>[X,$,s],S=()=>[M,"full","auto",...C()],R=()=>[j,"none","subgrid",X,$],O=()=>["auto",{span:["full",j,X,$]},j,X,$],L=()=>[j,"auto",X,$],z=()=>["auto","min","max","fr",X,$],I=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],_=()=>["start","end","center","stretch","center-safe","end-safe"],ee=()=>["auto",...C()],et=()=>[M,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...C()],er=()=>[e,X,$],en=()=>[...x(),V,U,{position:[X,$]}],eo=()=>["no-repeat",{repeat:["","x","y","space","round"]}],ea=()=>["auto","cover","contain",H,F,{size:[X,$]}],ei=()=>[D,Y,B],el=()=>["","none","full",c,X,$],es=()=>["",P,Y,B],ec=()=>["solid","dashed","dotted","double"],ed=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],eu=()=>[P,D,V,U],ef=()=>["","none",p,X,$],em=()=>["none",P,X,$],ep=()=>["none",P,X,$],eg=()=>[P,X,$],ev=()=>[M,"full",...C()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[T],breakpoint:[T],color:[A],container:[T],"drop-shadow":[T],ease:["in","out","in-out"],font:[W],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[T],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[T],shadow:[T],spacing:["px",P],text:[T],"text-shadow":[T],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",M,$,X,v]}],container:["container"],columns:[{columns:[P,$,X,l]}],"break-after":[{"break-after":y()}],"break-before":[{"break-before":y()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:k()}],overflow:[{overflow:E()}],"overflow-x":[{"overflow-x":E()}],"overflow-y":[{"overflow-y":E()}],overscroll:[{overscroll:N()}],"overscroll-x":[{"overscroll-x":N()}],"overscroll-y":[{"overscroll-y":N()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:S()}],"inset-x":[{"inset-x":S()}],"inset-y":[{"inset-y":S()}],start:[{start:S()}],end:[{end:S()}],top:[{top:S()}],right:[{right:S()}],bottom:[{bottom:S()}],left:[{left:S()}],visibility:["visible","invisible","collapse"],z:[{z:[j,"auto",X,$]}],basis:[{basis:[M,"full","auto",l,...C()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[P,M,"auto","initial","none",$]}],grow:[{grow:["",P,X,$]}],shrink:[{shrink:["",P,X,$]}],order:[{order:[j,"first","last","none",X,$]}],"grid-cols":[{"grid-cols":R()}],"col-start-end":[{col:O()}],"col-start":[{"col-start":L()}],"col-end":[{"col-end":L()}],"grid-rows":[{"grid-rows":R()}],"row-start-end":[{row:O()}],"row-start":[{"row-start":L()}],"row-end":[{"row-end":L()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":z()}],"auto-rows":[{"auto-rows":z()}],gap:[{gap:C()}],"gap-x":[{"gap-x":C()}],"gap-y":[{"gap-y":C()}],"justify-content":[{justify:[...I(),"normal"]}],"justify-items":[{"justify-items":[..._(),"normal"]}],"justify-self":[{"justify-self":["auto",..._()]}],"align-content":[{content:["normal",...I()]}],"align-items":[{items:[..._(),{baseline:["","last"]}]}],"align-self":[{self:["auto",..._(),{baseline:["","last"]}]}],"place-content":[{"place-content":I()}],"place-items":[{"place-items":[..._(),"baseline"]}],"place-self":[{"place-self":["auto",..._()]}],p:[{p:C()}],px:[{px:C()}],py:[{py:C()}],ps:[{ps:C()}],pe:[{pe:C()}],pt:[{pt:C()}],pr:[{pr:C()}],pb:[{pb:C()}],pl:[{pl:C()}],m:[{m:ee()}],mx:[{mx:ee()}],my:[{my:ee()}],ms:[{ms:ee()}],me:[{me:ee()}],mt:[{mt:ee()}],mr:[{mr:ee()}],mb:[{mb:ee()}],ml:[{ml:ee()}],"space-x":[{"space-x":C()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":C()}],"space-y-reverse":["space-y-reverse"],size:[{size:et()}],w:[{w:[l,"screen",...et()]}],"min-w":[{"min-w":[l,"screen","none",...et()]}],"max-w":[{"max-w":[l,"screen","none","prose",{screen:[i]},...et()]}],h:[{h:["screen","lh",...et()]}],"min-h":[{"min-h":["screen","lh","none",...et()]}],"max-h":[{"max-h":["screen","lh",...et()]}],"font-size":[{text:["base",r,Y,B]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[n,X,G]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",D,$]}],"font-family":[{font:[Z,$,t]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[o,X,$]}],"line-clamp":[{"line-clamp":[P,"none",X,G]}],leading:[{leading:[a,...C()]}],"list-image":[{"list-image":["none",X,$]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",X,$]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:er()}],"text-color":[{text:er()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...ec(),"wavy"]}],"text-decoration-thickness":[{decoration:[P,"from-font","auto",X,B]}],"text-decoration-color":[{decoration:er()}],"underline-offset":[{"underline-offset":[P,"auto",X,$]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:C()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",X,$]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",X,$]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:en()}],"bg-repeat":[{bg:eo()}],"bg-size":[{bg:ea()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},j,X,$],radial:["",X,$],conic:[j,X,$]},J,q]}],"bg-color":[{bg:er()}],"gradient-from-pos":[{from:ei()}],"gradient-via-pos":[{via:ei()}],"gradient-to-pos":[{to:ei()}],"gradient-from":[{from:er()}],"gradient-via":[{via:er()}],"gradient-to":[{to:er()}],rounded:[{rounded:el()}],"rounded-s":[{"rounded-s":el()}],"rounded-e":[{"rounded-e":el()}],"rounded-t":[{"rounded-t":el()}],"rounded-r":[{"rounded-r":el()}],"rounded-b":[{"rounded-b":el()}],"rounded-l":[{"rounded-l":el()}],"rounded-ss":[{"rounded-ss":el()}],"rounded-se":[{"rounded-se":el()}],"rounded-ee":[{"rounded-ee":el()}],"rounded-es":[{"rounded-es":el()}],"rounded-tl":[{"rounded-tl":el()}],"rounded-tr":[{"rounded-tr":el()}],"rounded-br":[{"rounded-br":el()}],"rounded-bl":[{"rounded-bl":el()}],"border-w":[{border:es()}],"border-w-x":[{"border-x":es()}],"border-w-y":[{"border-y":es()}],"border-w-s":[{"border-s":es()}],"border-w-e":[{"border-e":es()}],"border-w-t":[{"border-t":es()}],"border-w-r":[{"border-r":es()}],"border-w-b":[{"border-b":es()}],"border-w-l":[{"border-l":es()}],"divide-x":[{"divide-x":es()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":es()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...ec(),"hidden","none"]}],"divide-style":[{divide:[...ec(),"hidden","none"]}],"border-color":[{border:er()}],"border-color-x":[{"border-x":er()}],"border-color-y":[{"border-y":er()}],"border-color-s":[{"border-s":er()}],"border-color-e":[{"border-e":er()}],"border-color-t":[{"border-t":er()}],"border-color-r":[{"border-r":er()}],"border-color-b":[{"border-b":er()}],"border-color-l":[{"border-l":er()}],"divide-color":[{divide:er()}],"outline-style":[{outline:[...ec(),"none","hidden"]}],"outline-offset":[{"outline-offset":[P,X,$]}],"outline-w":[{outline:["",P,Y,B]}],"outline-color":[{outline:er()}],shadow:[{shadow:["","none",d,Q,K]}],"shadow-color":[{shadow:er()}],"inset-shadow":[{"inset-shadow":["none",u,Q,K]}],"inset-shadow-color":[{"inset-shadow":er()}],"ring-w":[{ring:es()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:er()}],"ring-offset-w":[{"ring-offset":[P,B]}],"ring-offset-color":[{"ring-offset":er()}],"inset-ring-w":[{"inset-ring":es()}],"inset-ring-color":[{"inset-ring":er()}],"text-shadow":[{"text-shadow":["none",f,Q,K]}],"text-shadow-color":[{"text-shadow":er()}],opacity:[{opacity:[P,X,$]}],"mix-blend":[{"mix-blend":[...ed(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":ed()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[P]}],"mask-image-linear-from-pos":[{"mask-linear-from":eu()}],"mask-image-linear-to-pos":[{"mask-linear-to":eu()}],"mask-image-linear-from-color":[{"mask-linear-from":er()}],"mask-image-linear-to-color":[{"mask-linear-to":er()}],"mask-image-t-from-pos":[{"mask-t-from":eu()}],"mask-image-t-to-pos":[{"mask-t-to":eu()}],"mask-image-t-from-color":[{"mask-t-from":er()}],"mask-image-t-to-color":[{"mask-t-to":er()}],"mask-image-r-from-pos":[{"mask-r-from":eu()}],"mask-image-r-to-pos":[{"mask-r-to":eu()}],"mask-image-r-from-color":[{"mask-r-from":er()}],"mask-image-r-to-color":[{"mask-r-to":er()}],"mask-image-b-from-pos":[{"mask-b-from":eu()}],"mask-image-b-to-pos":[{"mask-b-to":eu()}],"mask-image-b-from-color":[{"mask-b-from":er()}],"mask-image-b-to-color":[{"mask-b-to":er()}],"mask-image-l-from-pos":[{"mask-l-from":eu()}],"mask-image-l-to-pos":[{"mask-l-to":eu()}],"mask-image-l-from-color":[{"mask-l-from":er()}],"mask-image-l-to-color":[{"mask-l-to":er()}],"mask-image-x-from-pos":[{"mask-x-from":eu()}],"mask-image-x-to-pos":[{"mask-x-to":eu()}],"mask-image-x-from-color":[{"mask-x-from":er()}],"mask-image-x-to-color":[{"mask-x-to":er()}],"mask-image-y-from-pos":[{"mask-y-from":eu()}],"mask-image-y-to-pos":[{"mask-y-to":eu()}],"mask-image-y-from-color":[{"mask-y-from":er()}],"mask-image-y-to-color":[{"mask-y-to":er()}],"mask-image-radial":[{"mask-radial":[X,$]}],"mask-image-radial-from-pos":[{"mask-radial-from":eu()}],"mask-image-radial-to-pos":[{"mask-radial-to":eu()}],"mask-image-radial-from-color":[{"mask-radial-from":er()}],"mask-image-radial-to-color":[{"mask-radial-to":er()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":x()}],"mask-image-conic-pos":[{"mask-conic":[P]}],"mask-image-conic-from-pos":[{"mask-conic-from":eu()}],"mask-image-conic-to-pos":[{"mask-conic-to":eu()}],"mask-image-conic-from-color":[{"mask-conic-from":er()}],"mask-image-conic-to-color":[{"mask-conic-to":er()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:en()}],"mask-repeat":[{mask:eo()}],"mask-size":[{mask:ea()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",X,$]}],filter:[{filter:["","none",X,$]}],blur:[{blur:ef()}],brightness:[{brightness:[P,X,$]}],contrast:[{contrast:[P,X,$]}],"drop-shadow":[{"drop-shadow":["","none",m,Q,K]}],"drop-shadow-color":[{"drop-shadow":er()}],grayscale:[{grayscale:["",P,X,$]}],"hue-rotate":[{"hue-rotate":[P,X,$]}],invert:[{invert:["",P,X,$]}],saturate:[{saturate:[P,X,$]}],sepia:[{sepia:["",P,X,$]}],"backdrop-filter":[{"backdrop-filter":["","none",X,$]}],"backdrop-blur":[{"backdrop-blur":ef()}],"backdrop-brightness":[{"backdrop-brightness":[P,X,$]}],"backdrop-contrast":[{"backdrop-contrast":[P,X,$]}],"backdrop-grayscale":[{"backdrop-grayscale":["",P,X,$]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[P,X,$]}],"backdrop-invert":[{"backdrop-invert":["",P,X,$]}],"backdrop-opacity":[{"backdrop-opacity":[P,X,$]}],"backdrop-saturate":[{"backdrop-saturate":[P,X,$]}],"backdrop-sepia":[{"backdrop-sepia":["",P,X,$]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":C()}],"border-spacing-x":[{"border-spacing-x":C()}],"border-spacing-y":[{"border-spacing-y":C()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",X,$]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[P,"initial",X,$]}],ease:[{ease:["linear","initial",b,X,$]}],delay:[{delay:[P,X,$]}],animate:[{animate:["none",h,X,$]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[g,X,$]}],"perspective-origin":[{"perspective-origin":k()}],rotate:[{rotate:em()}],"rotate-x":[{"rotate-x":em()}],"rotate-y":[{"rotate-y":em()}],"rotate-z":[{"rotate-z":em()}],scale:[{scale:ep()}],"scale-x":[{"scale-x":ep()}],"scale-y":[{"scale-y":ep()}],"scale-z":[{"scale-z":ep()}],"scale-3d":["scale-3d"],skew:[{skew:eg()}],"skew-x":[{"skew-x":eg()}],"skew-y":[{"skew-y":eg()}],transform:[{transform:[X,$,"","none","gpu","cpu"]}],"transform-origin":[{origin:k()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:ev()}],"translate-x":[{"translate-x":ev()}],"translate-y":[{"translate-y":ev()}],"translate-z":[{"translate-z":ev()}],"translate-none":["translate-none"],accent:[{accent:er()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:er()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",X,$]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":C()}],"scroll-mx":[{"scroll-mx":C()}],"scroll-my":[{"scroll-my":C()}],"scroll-ms":[{"scroll-ms":C()}],"scroll-me":[{"scroll-me":C()}],"scroll-mt":[{"scroll-mt":C()}],"scroll-mr":[{"scroll-mr":C()}],"scroll-mb":[{"scroll-mb":C()}],"scroll-ml":[{"scroll-ml":C()}],"scroll-p":[{"scroll-p":C()}],"scroll-px":[{"scroll-px":C()}],"scroll-py":[{"scroll-py":C()}],"scroll-ps":[{"scroll-ps":C()}],"scroll-pe":[{"scroll-pe":C()}],"scroll-pt":[{"scroll-pt":C()}],"scroll-pr":[{"scroll-pr":C()}],"scroll-pb":[{"scroll-pb":C()}],"scroll-pl":[{"scroll-pl":C()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",X,$]}],fill:[{fill:["none",...er()]}],"stroke-w":[{stroke:[P,Y,B,G]}],stroke:[{stroke:["none",...er()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}})},46081:(e,t,r)=>{r.d(t,{A:()=>i,q:()=>a});var n=r(12115),o=r(95155);function a(e,t){let r=n.createContext(t),a=e=>{let{children:t,...a}=e,i=n.useMemo(()=>a,Object.values(a));return(0,o.jsx)(r.Provider,{value:i,children:t})};return a.displayName=e+"Provider",[a,function(o){let a=n.useContext(r);if(a)return a;if(void 0!==t)return t;throw Error(`\`${o}\` must be used within \`${e}\``)}]}function i(e,t=[]){let r=[],a=()=>{let t=r.map(e=>n.createContext(e));return function(r){let o=r?.[e]||t;return n.useMemo(()=>({[`__scope${e}`]:{...r,[e]:o}}),[r,o])}};return a.scopeName=e,[function(t,a){let i=n.createContext(a),l=r.length;r=[...r,a];let s=t=>{let{scope:r,children:a,...s}=t,c=r?.[e]?.[l]||i,d=n.useMemo(()=>s,Object.values(s));return(0,o.jsx)(c.Provider,{value:d,children:a})};return s.displayName=t+"Provider",[s,function(r,o){let s=o?.[e]?.[l]||i,c=n.useContext(s);if(c)return c;if(void 0!==a)return a;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=r.reduce((t,{useScope:r,scopeName:n})=>{let o=r(e)[`__scope${n}`];return{...t,...o}},{});return n.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return r.scopeName=t.scopeName,r}(a,...t)]}},52596:(e,t,r)=>{function n(){for(var e,t,r=0,n="",o=arguments.length;r<o;r++)(e=arguments[r])&&(t=function e(t){var r,n,o="";if("string"==typeof t||"number"==typeof t)o+=t;else if("object"==typeof t)if(Array.isArray(t)){var a=t.length;for(r=0;r<a;r++)t[r]&&(n=e(t[r]))&&(o&&(o+=" "),o+=n)}else for(n in t)t[n]&&(o&&(o+=" "),o+=n);return o}(e))&&(n&&(n+=" "),n+=t);return n}r.d(t,{$:()=>n,A:()=>o});let o=n},52712:(e,t,r)=>{r.d(t,{N:()=>o});var n=r(12115),o=globalThis?.document?n.useLayoutEffect:()=>{}},54416:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},61285:(e,t,r)=>{r.d(t,{B:()=>s});var n,o=r(12115),a=r(52712),i=(n||(n=r.t(o,2)))[" useId ".trim().toString()]||(()=>void 0),l=0;function s(e){let[t,r]=o.useState(i());return(0,a.N)(()=>{e||r(e=>e??String(l++))},[e]),e||(t?`radix-${t}`:"")}},63655:(e,t,r)=>{r.d(t,{hO:()=>s,sG:()=>l});var n=r(12115),o=r(47650),a=r(99708),i=r(95155),l=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,a.TL)(`Primitive.${t}`),o=n.forwardRef((e,n)=>{let{asChild:o,...a}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(o?r:t,{...a,ref:n})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{});function s(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},85185:(e,t,r)=>{r.d(t,{m:()=>n});function n(e,t,{checkForDefaultPrevented:r=!0}={}){return function(n){if(e?.(n),!1===r||!n.defaultPrevented)return t?.(n)}}},92293:(e,t,r)=>{r.d(t,{Oh:()=>a});var n=r(12115),o=0;function a(){n.useEffect(()=>{var e,t;let r=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!=(e=r[0])?e:i()),document.body.insertAdjacentElement("beforeend",null!=(t=r[1])?t:i()),o++,()=>{1===o&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),o--}},[])}function i(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},93795:(e,t,r)=>{r.d(t,{A:()=>K});var n,o,a=function(){return(a=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function i(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r}Object.create;Object.create;var l=("function"==typeof SuppressedError&&SuppressedError,r(12115)),s="right-scroll-bar-position",c="width-before-scroll-bar";function d(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var u="undefined"!=typeof window?l.useLayoutEffect:l.useEffect,f=new WeakMap;function m(e){return e}var p=function(e){void 0===e&&(e={});var t,r,n,o,i=(t=null,void 0===r&&(r=m),n=[],o=!1,{read:function(){if(o)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:null},useMedium:function(e){var t=r(e,o);return n.push(t),function(){n=n.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(o=!0;n.length;){var t=n;n=[],t.forEach(e)}n={push:function(t){return e(t)},filter:function(){return n}}},assignMedium:function(e){o=!0;var t=[];if(n.length){var r=n;n=[],r.forEach(e),t=n}var a=function(){var r=t;t=[],r.forEach(e)},i=function(){return Promise.resolve().then(a)};i(),n={push:function(e){t.push(e),i()},filter:function(e){return t=t.filter(e),n}}}});return i.options=a({async:!0,ssr:!1},e),i}(),g=function(){},v=l.forwardRef(function(e,t){var r,n,o,s,c=l.useRef(null),m=l.useState({onScrollCapture:g,onWheelCapture:g,onTouchMoveCapture:g}),v=m[0],b=m[1],h=e.forwardProps,y=e.children,w=e.className,x=e.removeScrollBar,k=e.enabled,E=e.shards,N=e.sideCar,C=e.noRelative,S=e.noIsolation,R=e.inert,O=e.allowPinchZoom,M=e.as,P=e.gapMode,j=i(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),D=(r=[c,t],n=function(e){return r.forEach(function(t){return d(t,e)})},(o=(0,l.useState)(function(){return{value:null,callback:n,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=n,s=o.facade,u(function(){var e=f.get(s);if(e){var t=new Set(e),n=new Set(r),o=s.current;t.forEach(function(e){n.has(e)||d(e,null)}),n.forEach(function(e){t.has(e)||d(e,o)})}f.set(s,r)},[r]),s),T=a(a({},j),v);return l.createElement(l.Fragment,null,k&&l.createElement(N,{sideCar:p,removeScrollBar:x,shards:E,noRelative:C,noIsolation:S,inert:R,setCallbacks:b,allowPinchZoom:!!O,lockRef:c,gapMode:P}),h?l.cloneElement(l.Children.only(y),a(a({},T),{ref:D})):l.createElement(void 0===M?"div":M,a({},T,{className:w,ref:D}),y))});v.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},v.classNames={fullWidth:c,zeroRight:s};var b=function(e){var t=e.sideCar,r=i(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var n=t.read();if(!n)throw Error("Sidecar medium not found");return l.createElement(n,a({},r))};b.isSideCarExport=!0;var h=function(){var e=0,t=null;return{add:function(n){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=o||r.nc;return t&&e.setAttribute("nonce",t),e}())){var a,i;(a=t).styleSheet?a.styleSheet.cssText=n:a.appendChild(document.createTextNode(n)),i=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(i)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},y=function(){var e=h();return function(t,r){l.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&r])}},w=function(){var e=y();return function(t){return e(t.styles,t.dynamic),null}},x={left:0,top:0,right:0,gap:0},k=function(e){return parseInt(e||"",10)||0},E=function(e){var t=window.getComputedStyle(document.body),r=t["padding"===e?"paddingLeft":"marginLeft"],n=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[k(r),k(n),k(o)]},N=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return x;var t=E(e),r=document.documentElement.clientWidth,n=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,n-r+t[2]-t[0])}},C=w(),S="data-scroll-locked",R=function(e,t,r,n){var o=e.left,a=e.top,i=e.right,l=e.gap;return void 0===r&&(r="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(n,";\n   padding-right: ").concat(l,"px ").concat(n,";\n  }\n  body[").concat(S,"] {\n    overflow: hidden ").concat(n,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(n,";"),"margin"===r&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(a,"px;\n    padding-right: ").concat(i,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(l,"px ").concat(n,";\n    "),"padding"===r&&"padding-right: ".concat(l,"px ").concat(n,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(s," {\n    right: ").concat(l,"px ").concat(n,";\n  }\n  \n  .").concat(c," {\n    margin-right: ").concat(l,"px ").concat(n,";\n  }\n  \n  .").concat(s," .").concat(s," {\n    right: 0 ").concat(n,";\n  }\n  \n  .").concat(c," .").concat(c," {\n    margin-right: 0 ").concat(n,";\n  }\n  \n  body[").concat(S,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(l,"px;\n  }\n")},O=function(){var e=parseInt(document.body.getAttribute(S)||"0",10);return isFinite(e)?e:0},M=function(){l.useEffect(function(){return document.body.setAttribute(S,(O()+1).toString()),function(){var e=O()-1;e<=0?document.body.removeAttribute(S):document.body.setAttribute(S,e.toString())}},[])},P=function(e){var t=e.noRelative,r=e.noImportant,n=e.gapMode,o=void 0===n?"margin":n;M();var a=l.useMemo(function(){return N(o)},[o]);return l.createElement(C,{styles:R(a,!t,o,r?"":"!important")})},j=!1;if("undefined"!=typeof window)try{var D=Object.defineProperty({},"passive",{get:function(){return j=!0,!0}});window.addEventListener("test",D,D),window.removeEventListener("test",D,D)}catch(e){j=!1}var T=!!j&&{passive:!1},A=function(e,t){if(!(e instanceof Element))return!1;var r=window.getComputedStyle(e);return"hidden"!==r[t]&&(r.overflowY!==r.overflowX||"TEXTAREA"===e.tagName||"visible"!==r[t])},L=function(e,t){var r=t.ownerDocument,n=t;do{if("undefined"!=typeof ShadowRoot&&n instanceof ShadowRoot&&(n=n.host),z(e,n)){var o=I(e,n);if(o[1]>o[2])return!0}n=n.parentNode}while(n&&n!==r.body);return!1},z=function(e,t){return"v"===e?A(t,"overflowY"):A(t,"overflowX")},I=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},_=function(e,t,r,n,o){var a,i=(a=window.getComputedStyle(t).direction,"h"===e&&"rtl"===a?-1:1),l=i*n,s=r.target,c=t.contains(s),d=!1,u=l>0,f=0,m=0;do{if(!s)break;var p=I(e,s),g=p[0],v=p[1]-p[2]-i*g;(g||v)&&z(e,s)&&(f+=v,m+=g);var b=s.parentNode;s=b&&b.nodeType===Node.DOCUMENT_FRAGMENT_NODE?b.host:b}while(!c&&s!==document.body||c&&(t.contains(s)||t===s));return u&&(o&&1>Math.abs(f)||!o&&l>f)?d=!0:!u&&(o&&1>Math.abs(m)||!o&&-l>m)&&(d=!0),d},W=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},F=function(e){return[e.deltaX,e.deltaY]},$=function(e){return e&&"current"in e?e.current:e},B=0,G=[];let U=(n=function(e){var t=l.useRef([]),r=l.useRef([0,0]),n=l.useRef(),o=l.useState(B++)[0],a=l.useState(w)[0],i=l.useRef(e);l.useEffect(function(){i.current=e},[e]),l.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,r){if(r||2==arguments.length)for(var n,o=0,a=t.length;o<a;o++)!n&&o in t||(n||(n=Array.prototype.slice.call(t,0,o)),n[o]=t[o]);return e.concat(n||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map($),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var s=l.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!i.current.allowPinchZoom;var o,a=W(e),l=r.current,s="deltaX"in e?e.deltaX:l[0]-a[0],c="deltaY"in e?e.deltaY:l[1]-a[1],d=e.target,u=Math.abs(s)>Math.abs(c)?"h":"v";if("touches"in e&&"h"===u&&"range"===d.type)return!1;var f=L(u,d);if(!f)return!0;if(f?o=u:(o="v"===u?"h":"v",f=L(u,d)),!f)return!1;if(!n.current&&"changedTouches"in e&&(s||c)&&(n.current=o),!o)return!0;var m=n.current||o;return _(m,t,e,"h"===m?s:c,!0)},[]),c=l.useCallback(function(e){if(G.length&&G[G.length-1]===a){var r="deltaY"in e?F(e):W(e),n=t.current.filter(function(t){var n;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(n=t.delta,n[0]===r[0]&&n[1]===r[1])})[0];if(n&&n.should){e.cancelable&&e.preventDefault();return}if(!n){var o=(i.current.shards||[]).map($).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?s(e,o[0]):!i.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),d=l.useCallback(function(e,r,n,o){var a={name:e,delta:r,target:n,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(n)};t.current.push(a),setTimeout(function(){t.current=t.current.filter(function(e){return e!==a})},1)},[]),u=l.useCallback(function(e){r.current=W(e),n.current=void 0},[]),f=l.useCallback(function(t){d(t.type,F(t),t.target,s(t,e.lockRef.current))},[]),m=l.useCallback(function(t){d(t.type,W(t),t.target,s(t,e.lockRef.current))},[]);l.useEffect(function(){return G.push(a),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:m}),document.addEventListener("wheel",c,T),document.addEventListener("touchmove",c,T),document.addEventListener("touchstart",u,T),function(){G=G.filter(function(e){return e!==a}),document.removeEventListener("wheel",c,T),document.removeEventListener("touchmove",c,T),document.removeEventListener("touchstart",u,T)}},[]);var p=e.removeScrollBar,g=e.inert;return l.createElement(l.Fragment,null,g?l.createElement(a,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,p?l.createElement(P,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},p.useMedium(n),b);var q=l.forwardRef(function(e,t){return l.createElement(v,a({},e,{ref:t,sideCar:U}))});q.classNames=v.classNames;let K=q},99708:(e,t,r)=>{r.d(t,{DX:()=>l,Dc:()=>c,TL:()=>i});var n=r(12115),o=r(6101),a=r(95155);function i(e){let t=function(e){let t=n.forwardRef((e,t)=>{let{children:r,...a}=e;if(n.isValidElement(r)){var i;let e,l,s=(i=r,(l=(e=Object.getOwnPropertyDescriptor(i.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.ref:(l=(e=Object.getOwnPropertyDescriptor(i,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.props.ref:i.props.ref||i.ref),c=function(e,t){let r={...t};for(let n in t){let o=e[n],a=t[n];/^on[A-Z]/.test(n)?o&&a?r[n]=(...e)=>{let t=a(...e);return o(...e),t}:o&&(r[n]=o):"style"===n?r[n]={...o,...a}:"className"===n&&(r[n]=[o,a].filter(Boolean).join(" "))}return{...e,...r}}(a,r.props);return r.type!==n.Fragment&&(c.ref=t?(0,o.t)(t,s):s),n.cloneElement(r,c)}return n.Children.count(r)>1?n.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=n.forwardRef((e,r)=>{let{children:o,...i}=e,l=n.Children.toArray(o),s=l.find(d);if(s){let e=s.props.children,o=l.map(t=>t!==s?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,a.jsx)(t,{...i,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,o):null})}return(0,a.jsx)(t,{...i,ref:r,children:o})});return r.displayName=`${e}.Slot`,r}var l=i("Slot"),s=Symbol("radix.slottable");function c(e){let t=({children:e})=>(0,a.jsx)(a.Fragment,{children:e});return t.displayName=`${e}.Slottable`,t.__radixId=s,t}function d(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===s}}}]);