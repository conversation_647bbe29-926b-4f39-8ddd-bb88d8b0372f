{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/fwu_incubation%28react%2Blaravel%29/incubation/frontend/src/app/components/shared/SectionTitle.tsx"], "sourcesContent": ["// components/shared/SectionTitle.tsx\r\ninterface SectionTitleProps {\r\n  title: string;\r\n  subtitle?: string;\r\n  align?: 'left' | 'center' | 'right';\r\n  accentColor?: string;\r\n}\r\n\r\nconst SectionTitle: React.FC<SectionTitleProps> = ({\r\n  title,\r\n  subtitle,\r\n  align = 'center',\r\n  accentColor = 'indigo'\r\n}) => {\r\n  const alignmentClass = {\r\n    left: 'text-left',\r\n    center: 'text-center',\r\n    right: 'text-right',\r\n  };\r\n\r\n  const accentColorClass = {\r\n    indigo: 'bg-indigo-600',\r\n    blue: 'bg-blue-600',\r\n    teal: 'bg-teal-600',\r\n    purple: 'bg-purple-600',\r\n    green: 'bg-green-600',\r\n  };\r\n\r\n  const textColorClass = {\r\n    indigo: 'text-indigo-600',\r\n    blue: 'text-blue-600',\r\n    teal: 'text-teal-600',\r\n    purple: 'text-purple-600',\r\n    green: 'text-green-600',\r\n  };\r\n\r\n  return (\r\n    <div className={`mb-12 md:mb-16 ${alignmentClass[align]}`}>\r\n      {subtitle && (\r\n        <p className={`${textColorClass[accentColor as keyof typeof textColorClass]} font-semibold text-sm md:text-base uppercase tracking-wider mb-2`}>\r\n          {subtitle}\r\n        </p>\r\n      )}\r\n      <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900\">\r\n        {title}\r\n      </h2>\r\n      <div className={`mt-4 h-1.5 w-24 ${align === 'center' ? 'mx-auto' : (align === 'right' ? 'ml-auto' : '')} ${accentColorClass[accentColor as keyof typeof accentColorClass]} rounded-full`}></div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default SectionTitle;"], "names": [], "mappings": "AAAA,qCAAqC;;;;;;AAQrC,MAAM,eAA4C,CAAC,EACjD,KAAK,EACL,QAAQ,EACR,QAAQ,QAAQ,EAChB,cAAc,QAAQ,EACvB;IACC,MAAM,iBAAiB;QACrB,MAAM;QACN,QAAQ;QACR,OAAO;IACT;IAEA,MAAM,mBAAmB;QACvB,QAAQ;QACR,MAAM;QACN,MAAM;QACN,QAAQ;QACR,OAAO;IACT;IAEA,MAAM,iBAAiB;QACrB,QAAQ;QACR,MAAM;QACN,MAAM;QACN,QAAQ;QACR,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAC,eAAe,EAAE,cAAc,CAAC,MAAM,EAAE;;YACtD,0BACC,6LAAC;gBAAE,WAAW,GAAG,cAAc,CAAC,YAA2C,CAAC,iEAAiE,CAAC;0BAC3I;;;;;;0BAGL,6LAAC;gBAAG,WAAU;0BACX;;;;;;0BAEH,6LAAC;gBAAI,WAAW,CAAC,gBAAgB,EAAE,UAAU,WAAW,YAAa,UAAU,UAAU,YAAY,GAAI,CAAC,EAAE,gBAAgB,CAAC,YAA6C,CAAC,aAAa,CAAC;;;;;;;;;;;;AAG/L;KAzCM;uCA2CS", "debugId": null}}, {"offset": {"line": 79, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/fwu_incubation%28react%2Blaravel%29/incubation/frontend/src/app/components/programs/GalleryImageCard.tsx"], "sourcesContent": ["\"use client\"\r\nimport Image from 'next/image';\r\nimport Link from 'next/link';\r\n\r\nexport interface GalleryImageCardProps {\r\n  imageUrl: string;\r\n  altText: string;\r\n  caption?: string;\r\n  eventDate?: string;\r\n  isLoaded?: boolean;\r\n}\r\n\r\nconst GalleryImageCard: React.FC<GalleryImageCardProps> = ({\r\n  imageUrl,\r\n  altText,\r\n  caption,\r\n  eventDate,\r\n  isLoaded = true\r\n}) => {\r\n  return (\r\n    <Link href=\"/gallery\" className=\"block\">\r\n      <div className=\"relative group rounded-lg overflow-hidden shadow-lg h-64 transform hover:shadow-2xl transition-all duration-300 hover:-translate-y-1 bg-gray-200\">\r\n        <div className=\"w-full h-full relative\">\r\n          {isLoaded && (\r\n            <Image\r\n              src={imageUrl}\r\n              alt={altText}\r\n              fill\r\n              className=\"object-cover transform group-hover:scale-110 transition-transform duration-500 ease-in-out\"\r\n              onError={(e) => {\r\n                // Fallback to a placeholder on error\r\n                const target = e.target as HTMLImageElement;\r\n                target.src = \"https://via.placeholder.com/800x600/e2e8f0/475569?text=FWU+Incubation\";\r\n              }}\r\n            />\r\n          )}\r\n        </div>\r\n        {(caption || eventDate) && (\r\n          <div className=\"absolute inset-0 bg-gradient-to-t from-black/70 via-black/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex flex-col justify-end p-4\">\r\n            {caption && <h4 className=\"text-white text-lg font-semibold\">{caption}</h4>}\r\n            {eventDate && <p className=\"text-gray-300 text-sm\">{eventDate}</p>}\r\n          </div>\r\n        )}\r\n      </div>\r\n    </Link>\r\n  );\r\n};\r\n\r\nexport default GalleryImageCard;"], "names": [], "mappings": ";;;;AACA;AACA;AAFA;;;;AAYA,MAAM,mBAAoD,CAAC,EACzD,QAAQ,EACR,OAAO,EACP,OAAO,EACP,SAAS,EACT,WAAW,IAAI,EAChB;IACC,qBACE,6LAAC,+JAAA,CAAA,UAAI;QAAC,MAAK;QAAW,WAAU;kBAC9B,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACZ,0BACC,6LAAC,gIAAA,CAAA,UAAK;wBACJ,KAAK;wBACL,KAAK;wBACL,IAAI;wBACJ,WAAU;wBACV,SAAS,CAAC;4BACR,qCAAqC;4BACrC,MAAM,SAAS,EAAE,MAAM;4BACvB,OAAO,GAAG,GAAG;wBACf;;;;;;;;;;;gBAIL,CAAC,WAAW,SAAS,mBACpB,6LAAC;oBAAI,WAAU;;wBACZ,yBAAW,6LAAC;4BAAG,WAAU;sCAAoC;;;;;;wBAC7D,2BAAa,6LAAC;4BAAE,WAAU;sCAAyB;;;;;;;;;;;;;;;;;;;;;;;AAMhE;KAlCM;uCAoCS", "debugId": null}}, {"offset": {"line": 168, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/fwu_incubation%28react%2Blaravel%29/incubation/frontend/src/app/components/programs/PastEventsGallerySection%20.tsx"], "sourcesContent": ["\"use client\"\r\nimport SectionTitle from '../shared/SectionTitle';\r\nimport GalleryImageCard from './GalleryImageCard';\r\nimport Link from 'next/link';\r\nimport { useEffect, useState } from 'react';\r\n\r\n// Gallery images data with Unsplash images\r\nconst pastEventsData = [\r\n  {\r\n    id: 'pe1',\r\n    imageUrl: 'https://images.unsplash.com/photo-1517245386807-bb43f82c33c4?q=80&w=800&auto=format&fit=crop',\r\n    altText: 'Incubation Center Planning Meeting',\r\n    caption: 'Incubation Center Planning Meeting',\r\n    eventDate: 'March 15, 2025',\r\n  },\r\n  {\r\n    id: 'pe2',\r\n    imageUrl: 'https://images.unsplash.com/photo-1519389950473-47ba0277781c?q=80&w=800&auto=format&fit=crop',\r\n    altText: 'Startup Mentorship Session',\r\n    caption: 'Startup Mentorship Session',\r\n    eventDate: 'March 20, 2025',\r\n  },\r\n  {\r\n    id: 'pe3',\r\n    imageUrl: 'https://images.unsplash.com/photo-1531482615713-2afd69097998?q=80&w=800&auto=format&fit=crop',\r\n    altText: 'Entrepreneurship Skills Development',\r\n    caption: 'Entrepreneurship Skills Development',\r\n    eventDate: 'January 25, 2025',\r\n  },\r\n  {\r\n    id: 'pe4',\r\n    imageUrl: 'https://images.unsplash.com/photo-1540575467063-178a50c2df87?q=80&w=800&auto=format&fit=crop',\r\n    altText: 'International Conference on Innovation',\r\n    caption: 'International Conference on Innovation',\r\n    eventDate: 'February 15, 2025',\r\n  },\r\n  {\r\n    id: 'pe5',\r\n    imageUrl: 'https://images.unsplash.com/photo-1504384308090-c894fdcc538d?q=80&w=800&auto=format&fit=crop',\r\n    altText: 'Innovation Hackathon',\r\n    caption: 'Innovation Hackathon',\r\n    eventDate: 'April 12-13, 2025',\r\n  },\r\n  {\r\n    id: 'pe6',\r\n    imageUrl: 'https://images.unsplash.com/photo-1560439514-4e9645039924?q=80&w=800&auto=format&fit=crop',\r\n    altText: 'MOU Signing with Industry Partners',\r\n    caption: 'MOU Signing with Industry Partners',\r\n    eventDate: 'December 10, 2024',\r\n  },\r\n];\r\n\r\nconst PastEventsGallerySection = () => {\r\n  const [isLoaded, setIsLoaded] = useState(false);\r\n\r\n  // Simulate image loading\r\n  useEffect(() => {\r\n    setIsLoaded(true);\r\n  }, []);\r\n\r\n  return (\r\n    <section className=\"py-16 md:py-24 bg-gradient-to-b from-brand-light to-white\">\r\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\r\n        <SectionTitle title=\"FWU Incubation Center Gallery\" subtitle=\"Moments & Memories\" />\r\n\r\n        <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8 mt-12\">\r\n          {pastEventsData.map((event, index) => (\r\n            <div\r\n              key={event.id}\r\n              className=\"opacity-0 animate-fadeIn\"\r\n              style={{\r\n                animationDelay: `${index * 150}ms`,\r\n                animationFillMode: 'forwards'\r\n              }}\r\n            >\r\n              <GalleryImageCard\r\n                imageUrl={event.imageUrl}\r\n                altText={event.altText}\r\n                caption={event.caption}\r\n                eventDate={event.eventDate}\r\n                isLoaded={isLoaded}\r\n              />\r\n            </div>\r\n          ))}\r\n        </div>\r\n\r\n        <div className=\"text-center mt-12 opacity-0 animate-fadeIn animation-delay-1000\" style={{ animationFillMode: 'forwards' }}>\r\n          <Link\r\n            href=\"/gallery\"\r\n            className=\"inline-block bg-brand-primary hover:bg-brand-primary-dark border border-blue-400 font-semibold py-3 px-8 rounded-lg shadow-md transition-all duration-300 hover:scale-105 transform\"\r\n          >\r\n            View Full Gallery\r\n          </Link>\r\n          <p className=\"text-gray-600 mt-2\">\r\n            Explore more photos from our events, workshops, and partnerships\r\n          </p>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\nexport default PastEventsGallerySection;"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;;;AAJA;;;;;AAMA,2CAA2C;AAC3C,MAAM,iBAAiB;IACrB;QACE,IAAI;QACJ,UAAU;QACV,SAAS;QACT,SAAS;QACT,WAAW;IACb;IACA;QACE,IAAI;QACJ,UAAU;QACV,SAAS;QACT,SAAS;QACT,WAAW;IACb;IACA;QACE,IAAI;QACJ,UAAU;QACV,SAAS;QACT,SAAS;QACT,WAAW;IACb;IACA;QACE,IAAI;QACJ,UAAU;QACV,SAAS;QACT,SAAS;QACT,WAAW;IACb;IACA;QACE,IAAI;QACJ,UAAU;QACV,SAAS;QACT,SAAS;QACT,WAAW;IACb;IACA;QACE,IAAI;QACJ,UAAU;QACV,SAAS;QACT,SAAS;QACT,WAAW;IACb;CACD;AAED,MAAM,2BAA2B;;IAC/B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,yBAAyB;IACzB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8CAAE;YACR,YAAY;QACd;6CAAG,EAAE;IAEL,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,sJAAA,CAAA,UAAY;oBAAC,OAAM;oBAAgC,UAAS;;;;;;8BAE7D,6LAAC;oBAAI,WAAU;8BACZ,eAAe,GAAG,CAAC,CAAC,OAAO,sBAC1B,6LAAC;4BAEC,WAAU;4BACV,OAAO;gCACL,gBAAgB,GAAG,QAAQ,IAAI,EAAE,CAAC;gCAClC,mBAAmB;4BACrB;sCAEA,cAAA,6LAAC,4JAAA,CAAA,UAAgB;gCACf,UAAU,MAAM,QAAQ;gCACxB,SAAS,MAAM,OAAO;gCACtB,SAAS,MAAM,OAAO;gCACtB,WAAW,MAAM,SAAS;gCAC1B,UAAU;;;;;;2BAZP,MAAM,EAAE;;;;;;;;;;8BAkBnB,6LAAC;oBAAI,WAAU;oBAAkE,OAAO;wBAAE,mBAAmB;oBAAW;;sCACtH,6LAAC,+JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;sCACX;;;;;;sCAGD,6LAAC;4BAAE,WAAU;sCAAqB;;;;;;;;;;;;;;;;;;;;;;;AAO5C;GAhDM;KAAA;uCAkDS", "debugId": null}}, {"offset": {"line": 334, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/fwu_incubation%28react%2Blaravel%29/incubation/frontend/src/app/components/programs/UpcomingEventCard.tsx"], "sourcesContent": ["\"use client\"\r\nimport { Calendar, Clock, MapPin, ChevronRight } from 'lucide-react';\r\nimport { useState } from 'react';\r\n\r\nexport interface UpcomingEvent {\r\n  id: string;\r\n  date: string; // e.g., \"Oct 25\"\r\n  fullDate: string; // e.g., \"October 25, 2024\"\r\n  time?: string; // e.g., \"10:00 AM - 04:00 PM\"\r\n  title: string;\r\n  type: string; // e.g., \"Workshop\", \"Deadline\", \"Networking\"\r\n  location?: string; // e.g., \"Online\" or \"FWU Auditorium\"\r\n  description: string;\r\n  color?: string; // Tailwind color class e.g. 'bg-blue-500'\r\n  detailedDescription?: string; // More details for expanded view\r\n  registrationLink?: string; // Link to register for the event\r\n}\r\n\r\ninterface UpcomingEventCardProps {\r\n  event: UpcomingEvent;\r\n}\r\n\r\nconst UpcomingEventCard: React.FC<UpcomingEventCardProps> = ({ event }) => {\r\n  const [isExpanded, setIsExpanded] = useState(false);\r\n  const typeColor = event.color || 'bg-brand-accent';\r\n\r\n  const toggleExpand = () => {\r\n    setIsExpanded(!isExpanded);\r\n  };\r\n\r\n  return (\r\n    <div className=\"bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden flex flex-col group opacity-0 animate-fadeIn hover:shadow-xl transition-shadow duration-300\">\r\n      <div className=\"flex flex-col md:flex-row\">\r\n        {/* Date Column */}\r\n        <div\r\n          className={`p-8 md:w-1/4 flex flex-col items-center justify-center text-white ${typeColor} transition-transform duration-300 relative overflow-hidden`}\r\n        >\r\n          {/* Background pattern - using CSS pattern instead of image */}\r\n          <div className=\"absolute inset-0 opacity-20\"\r\n               style={{\r\n                 backgroundImage: 'radial-gradient(#ffffff 1px, transparent 1px)',\r\n                 backgroundSize: '10px 10px'\r\n               }}>\r\n          </div>\r\n\r\n          <div className=\"relative\">\r\n            <div className=\"text-5xl font-bold mb-1\">{event.date.split(' ')[1]}</div>\r\n            <div className=\"text-lg uppercase font-medium\">{event.date.split(' ')[0]}</div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Content Column */}\r\n        <div className=\"p-8 flex-grow\">\r\n          <div className=\"flex justify-between items-start mb-4\">\r\n            <span className={`inline-block px-4 py-1 text-xs font-semibold text-white ${typeColor} rounded-full`}>\r\n              {event.type}\r\n            </span>\r\n            <button\r\n              onClick={toggleExpand}\r\n              className=\"w-8 h-8 flex items-center justify-center bg-gray-100 hover:bg-gray-200 text-gray-600 rounded-full focus:outline-none transition-all duration-200 hover:scale-110 active:scale-95\"\r\n              aria-label={isExpanded ? \"Collapse details\" : \"Expand details\"}\r\n            >\r\n              <div className={`transform transition-transform duration-300 ${isExpanded ? 'rotate-90' : 'rotate-0'}`}>\r\n                <ChevronRight size={18} />\r\n              </div>\r\n            </button>\r\n          </div>\r\n\r\n          <h3 className=\"text-xl font-bold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors\">\r\n            {event.title}\r\n          </h3>\r\n\r\n          <p className=\"text-gray-600 mb-5 leading-relaxed\">{event.description}</p>\r\n\r\n          <div className=\"flex flex-wrap gap-4 text-sm text-gray-600\">\r\n            <div className=\"flex items-center bg-gray-50 px-3 py-1 rounded-full\">\r\n              <Calendar className=\"mr-2 text-blue-500\" /> {event.fullDate}\r\n            </div>\r\n            {event.time && (\r\n              <div className=\"flex items-center bg-gray-50 px-3 py-1 rounded-full\">\r\n                <Clock className=\"mr-2 text-blue-500\" /> {event.time}\r\n              </div>\r\n            )}\r\n            {event.location && (\r\n              <div className=\"flex items-center bg-gray-50 px-3 py-1 rounded-full\">\r\n                <MapPin className=\"mr-2 text-blue-500\" /> {event.location}\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Expandable Details Section */}\r\n      <div\r\n        className={`px-8 pb-8 pt-0 border-t border-gray-100 mt-2 overflow-hidden transition-all duration-500 ease-in-out ${\r\n          isExpanded ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'\r\n        }`}\r\n      >\r\n        <div className=\"pt-6 text-gray-700 leading-relaxed\">\r\n          <p>{event.detailedDescription || \"More details about this event will be announced soon. Stay tuned for updates!\"}</p>\r\n        </div>\r\n\r\n        {event.registrationLink && (\r\n          <div className=\"mt-6 flex\">\r\n            <a\r\n              href={event.registrationLink}\r\n              className=\"inline-flex items-center bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-6 rounded-lg transition-all duration-300 hover:scale-105 active:scale-95\"\r\n            >\r\n              <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\" />\r\n              </svg>\r\n              Register Now\r\n            </a>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default UpcomingEventCard;"], "names": [], "mappings": ";;;;AACA;AAAA;AAAA;AAAA;AACA;;;AAFA;;;AAsBA,MAAM,oBAAsD,CAAC,EAAE,KAAK,EAAE;;IACpE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,YAAY,MAAM,KAAK,IAAI;IAEjC,MAAM,eAAe;QACnB,cAAc,CAAC;IACjB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBACC,WAAW,CAAC,kEAAkE,EAAE,UAAU,2DAA2D,CAAC;;0CAGtJ,6LAAC;gCAAI,WAAU;gCACV,OAAO;oCACL,iBAAiB;oCACjB,gBAAgB;gCAClB;;;;;;0CAGL,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAA2B,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;;;;;;kDAClE,6LAAC;wCAAI,WAAU;kDAAiC,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;;;;;;;;;;;;;;;;;;kCAK5E,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAW,CAAC,wDAAwD,EAAE,UAAU,aAAa,CAAC;kDACjG,MAAM,IAAI;;;;;;kDAEb,6LAAC;wCACC,SAAS;wCACT,WAAU;wCACV,cAAY,aAAa,qBAAqB;kDAE9C,cAAA,6LAAC;4CAAI,WAAW,CAAC,4CAA4C,EAAE,aAAa,cAAc,YAAY;sDACpG,cAAA,6LAAC,yNAAA,CAAA,eAAY;gDAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;0CAK1B,6LAAC;gCAAG,WAAU;0CACX,MAAM,KAAK;;;;;;0CAGd,6LAAC;gCAAE,WAAU;0CAAsC,MAAM,WAAW;;;;;;0CAEpE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAuB;4CAAE,MAAM,QAAQ;;;;;;;oCAE5D,MAAM,IAAI,kBACT,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAAuB;4CAAE,MAAM,IAAI;;;;;;;oCAGvD,MAAM,QAAQ,kBACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,6MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAuB;4CAAE,MAAM,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;0BAQnE,6LAAC;gBACC,WAAW,CAAC,qGAAqG,EAC/G,aAAa,yBAAyB,qBACtC;;kCAEF,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;sCAAG,MAAM,mBAAmB,IAAI;;;;;;;;;;;oBAGlC,MAAM,gBAAgB,kBACrB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,MAAM,MAAM,gBAAgB;4BAC5B,WAAU;;8CAEV,6LAAC;oCAAI,OAAM;oCAA6B,WAAU;oCAAe,MAAK;oCAAO,SAAQ;oCAAY,QAAO;8CACtG,cAAA,6LAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;gCACjE;;;;;;;;;;;;;;;;;;;;;;;;AAQpB;GAhGM;KAAA;uCAkGS", "debugId": null}}, {"offset": {"line": 623, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/fwu_incubation%28react%2Blaravel%29/incubation/frontend/src/app/components/programs/ProgramCalendarSection%20.tsx"], "sourcesContent": ["\"use client\"\r\nimport UpcomingEventCard, { UpcomingEvent } from './UpcomingEventCard';\r\nimport { Calendar, Filter, Search } from 'lucide-react';\r\nimport { useState } from 'react';\r\n\r\n// Enhanced Dummy Data with more details\r\nconst upcomingEventsData: UpcomingEvent[] = [\r\n  {\r\n    id: 'ue1',\r\n    date: 'NOV 15',\r\n    fullDate: 'November 15, 2024',\r\n    time: '09:00 AM - 05:00 PM',\r\n    title: 'Design Thinking Workshop for Innovators',\r\n    type: 'Workshop',\r\n    location: 'FWU Incubation Hall A',\r\n    description: 'Learn human-centered design principles to create impactful solutions.',\r\n    color: 'bg-purple-500',\r\n    detailedDescription: 'This full-day workshop will introduce participants to the core principles of design thinking and how to apply them to solve complex problems. Led by industry experts, you will learn techniques for empathizing with users, defining problems, ideating solutions, prototyping, and testing. By the end of the workshop, you will have a practical toolkit for approaching innovation challenges with a human-centered mindset.',\r\n    registrationLink: '/register/design-thinking-workshop'\r\n  },\r\n  {\r\n    id: 'ue2',\r\n    date: 'NOV 28',\r\n    fullDate: 'November 28, 2024',\r\n    title: 'Application Deadline: Winter Cohort 2025',\r\n    type: 'Deadline',\r\n    description: 'Submit your startup applications for the upcoming winter incubation program.',\r\n    color: 'bg-red-500',\r\n    detailedDescription: 'The Winter Cohort 2025 is our flagship 12-week incubation program designed for early-stage startups ready to accelerate their growth. Selected startups will receive mentorship, workspace, seed funding opportunities, and access to our network of investors and industry partners. Applications must include your business plan, team information, current traction, and growth strategy.',\r\n    registrationLink: '/apply/winter-cohort-2025'\r\n  },\r\n  {\r\n    id: 'ue3',\r\n    date: 'DEC 05',\r\n    fullDate: 'December 05, 2024',\r\n    time: '02:00 PM - 04:00 PM',\r\n    title: 'Investor Connect: Meet & Greet',\r\n    type: 'Networking',\r\n    location: 'Online (Zoom)',\r\n    description: 'An opportunity for selected startups to interact with potential investors.',\r\n    color: 'bg-teal-500',\r\n    detailedDescription: 'This exclusive virtual networking event brings together promising startups and potential investors in a structured yet casual format. Each startup will have the opportunity to introduce their venture in a brief pitch, followed by breakout rooms for more in-depth conversations with interested investors. This is not a formal pitching event but rather a chance to build relationships that could lead to future investment opportunities.',\r\n    registrationLink: '/register/investor-connect'\r\n  },\r\n  {\r\n    id: 'ue4',\r\n    date: 'DEC 12',\r\n    fullDate: 'December 12-14, 2024',\r\n    title: 'FinTech Hackathon Challenge',\r\n    type: 'Hackathon',\r\n    location: 'FWU Main Auditorium',\r\n    description: 'Develop innovative solutions for the financial technology sector and win prizes.',\r\n    color: 'bg-blue-600',\r\n    detailedDescription: 'Join us for an intensive 48-hour hackathon focused on developing innovative solutions for the financial technology sector. Participants will form teams to tackle real-world challenges provided by our industry partners. Cash prizes totaling $10,000 will be awarded to the top three teams, with the first-place team also receiving incubation support to develop their solution further. All skill levels are welcome, and mentors will be available throughout the event.',\r\n    registrationLink: '/register/fintech-hackathon'\r\n  },\r\n  {\r\n    id: 'ue5',\r\n    date: 'JAN 10',\r\n    fullDate: 'January 10, 2025',\r\n    time: '10:00 AM - 12:00 PM',\r\n    title: 'Funding Strategies for Early-Stage Startups',\r\n    type: 'Workshop',\r\n    location: 'FWU Incubation Center',\r\n    description: 'Learn about different funding options and how to approach investors effectively.',\r\n    color: 'bg-purple-500',\r\n    detailedDescription: 'This workshop will cover various funding strategies available to early-stage startups, including bootstrapping, angel investment, venture capital, grants, and crowdfunding. Our expert speakers will share insights on when to pursue each option, how to prepare your startup for investment, and tactics for successful fundraising. The session will include case studies of successful funding journeys and common pitfalls to avoid.',\r\n    registrationLink: '/register/funding-strategies-workshop'\r\n  },\r\n];\r\n\r\n// Helper to sort events by fullDate (simplistic, assumes \"Month Day, Year\" format)\r\nconst sortEvents = (events: UpcomingEvent[]): UpcomingEvent[] => {\r\n  return events.sort((a, b) => new Date(a.fullDate).getTime() - new Date(b.fullDate).getTime());\r\n};\r\n\r\n// Get unique event types for filtering\r\nconst getUniqueEventTypes = (events: UpcomingEvent[]): string[] => {\r\n  return Array.from(new Set(events.map(event => event.type))).sort();\r\n};\r\n\r\nconst ProgramCalendarSection = () => {\r\n  const [searchTerm, setSearchTerm] = useState('');\r\n  const [selectedType, setSelectedType] = useState('');\r\n\r\n  const eventTypes = getUniqueEventTypes(upcomingEventsData);\r\n\r\n  // Filter events based on search term and selected type\r\n  const filteredEvents = upcomingEventsData\r\n    .filter(event =>\r\n      event.title.toLowerCase().includes(searchTerm.toLowerCase()) ||\r\n      event.description.toLowerCase().includes(searchTerm.toLowerCase())\r\n    )\r\n    .filter(event =>\r\n      selectedType ? event.type === selectedType : true\r\n    );\r\n\r\n  const sortedEvents = sortEvents(filteredEvents);\r\n\r\n  return (\r\n    <section className=\"py-20 md:py-28 bg-white\">\r\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\r\n        {/* Section header with decorative elements */}\r\n        <div className=\"relative mb-16\">\r\n          <div className=\"absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-24 h-1 bg-blue-200 rounded-full\"></div>\r\n          <div className=\"text-center\">\r\n            <p className=\"text-blue-600 font-semibold mb-2\">Stay Informed</p>\r\n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900\">Upcoming Events at FWU Incubation Center</h2>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Search and Filter Controls */}\r\n        <div className=\"mb-12 mt-8 bg-white p-6 rounded-xl shadow-lg border border-gray-100\">\r\n          <div className=\"flex flex-col md:flex-row gap-6\">\r\n            <div className=\"relative flex-grow\">\r\n              <div className=\"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none\">\r\n                <Search className=\"text-blue-500\" />\r\n              </div>\r\n              <input\r\n                type=\"text\"\r\n                placeholder=\"Search events...\"\r\n                value={searchTerm}\r\n                onChange={(e) => setSearchTerm(e.target.value)}\r\n                className=\"block w-full pl-12 pr-4 py-3 border-2 border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-400 focus:border-blue-400 transition-colors\"\r\n              />\r\n            </div>\r\n\r\n            <div className=\"relative w-full md:w-auto\">\r\n              <div className=\"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none\">\r\n                <Filter className=\"text-blue-500\" />\r\n              </div>\r\n              <select\r\n                value={selectedType}\r\n                onChange={(e) => setSelectedType(e.target.value)}\r\n                className=\"block w-full md:w-56 pl-12 pr-10 py-3 border-2 border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-400 focus:border-blue-400 transition-colors appearance-none bg-no-repeat bg-right\"\r\n                style={{ backgroundImage: \"url(\\\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e\\\")\", backgroundSize: \"1.5em 1.5em\", backgroundPosition: \"right 0.75rem center\" }}\r\n              >\r\n                <option value=\"\">All Event Types</option>\r\n                {eventTypes.map(type => (\r\n                  <option key={type} value={type}>{type}</option>\r\n                ))}\r\n              </select>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Events List */}\r\n        {sortedEvents.length > 0 ? (\r\n          <div className=\"space-y-10\">\r\n            {sortedEvents.map((event, index) => (\r\n              <div\r\n                key={event.id}\r\n                className=\"opacity-0 animate-fadeIn\"\r\n                style={{ animationDelay: `${index * 150}ms`, animationFillMode: 'forwards' }}\r\n              >\r\n                <UpcomingEventCard event={event} />\r\n              </div>\r\n            ))}\r\n          </div>\r\n        ) : (\r\n          <div className=\"text-center py-20 bg-white rounded-xl shadow-lg border border-gray-100 opacity-0 animate-fadeIn\">\r\n            <div className=\"w-20 h-20 mx-auto bg-blue-50 rounded-full flex items-center justify-center mb-6\">\r\n              <Calendar className=\"text-blue-500 text-3xl\" />\r\n            </div>\r\n            <h3 className=\"text-2xl font-bold text-gray-900 mb-3\">No Events Found</h3>\r\n            <p className=\"text-gray-600 max-w-md mx-auto\">\r\n              {searchTerm || selectedType\r\n                ? \"No events match your current search criteria. Try adjusting your filters.\"\r\n                : \"No upcoming events scheduled at the moment. Please check back soon!\"}\r\n            </p>\r\n          </div>\r\n        )}\r\n\r\n        {/* Calendar Subscription */}\r\n        <div className=\"mt-20 opacity-0 animate-fadeIn animation-delay-500\" style={{ animationFillMode: 'forwards' }}>\r\n          <div className=\"bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl shadow-lg p-10 text-center\">\r\n            <div className=\"w-16 h-16 mx-auto bg-white rounded-full flex items-center justify-center mb-6 shadow-md\">\r\n              <Calendar className=\"text-blue-600 text-2xl\" />\r\n            </div>\r\n\r\n            <h3 className=\"text-2xl font-bold text-gray-900 mb-3\">Stay Updated with Our Events</h3>\r\n            <p className=\"text-gray-700 max-w-2xl mx-auto mb-8\">\r\n              Subscribe to our calendar to receive automatic updates about upcoming events, workshops,\r\n              and programs at the FWU Incubation Center.\r\n            </p>\r\n\r\n            <a\r\n              href=\"/subscribe-calendar\" // Link to iCal feed or subscription page\r\n              className=\"inline-flex items-center bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-8 rounded-lg shadow-md transition-all duration-300 hover:scale-105 transform\"\r\n            >\r\n              <Calendar className=\"mr-2\" />\r\n              Subscribe to Calendar\r\n            </a>\r\n            <p className=\"text-sm text-gray-600 mt-4\">\r\n              Never miss an event! Add our calendar to your preferred calendar app.\r\n            </p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\nexport default ProgramCalendarSection;"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AAAA;AACA;;;AAHA;;;;AAKA,wCAAwC;AACxC,MAAM,qBAAsC;IAC1C;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,MAAM;QACN,OAAO;QACP,MAAM;QACN,UAAU;QACV,aAAa;QACb,OAAO;QACP,qBAAqB;QACrB,kBAAkB;IACpB;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,OAAO;QACP,MAAM;QACN,aAAa;QACb,OAAO;QACP,qBAAqB;QACrB,kBAAkB;IACpB;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,MAAM;QACN,OAAO;QACP,MAAM;QACN,UAAU;QACV,aAAa;QACb,OAAO;QACP,qBAAqB;QACrB,kBAAkB;IACpB;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,OAAO;QACP,MAAM;QACN,UAAU;QACV,aAAa;QACb,OAAO;QACP,qBAAqB;QACrB,kBAAkB;IACpB;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,MAAM;QACN,OAAO;QACP,MAAM;QACN,UAAU;QACV,aAAa;QACb,OAAO;QACP,qBAAqB;QACrB,kBAAkB;IACpB;CACD;AAED,mFAAmF;AACnF,MAAM,aAAa,CAAC;IAClB,OAAO,OAAO,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,QAAQ,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,QAAQ,EAAE,OAAO;AAC5F;AAEA,uCAAuC;AACvC,MAAM,sBAAsB,CAAC;IAC3B,OAAO,MAAM,IAAI,CAAC,IAAI,IAAI,OAAO,GAAG,CAAC,CAAA,QAAS,MAAM,IAAI,IAAI,IAAI;AAClE;AAEA,MAAM,yBAAyB;;IAC7B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,aAAa,oBAAoB;IAEvC,uDAAuD;IACvD,MAAM,iBAAiB,mBACpB,MAAM,CAAC,CAAA,QACN,MAAM,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACzD,MAAM,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,KAEhE,MAAM,CAAC,CAAA,QACN,eAAe,MAAM,IAAI,KAAK,eAAe;IAGjD,MAAM,eAAe,WAAW;IAEhC,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;8CAAmC;;;;;;8CAChD,6LAAC;oCAAG,WAAU;8CAA+C;;;;;;;;;;;;;;;;;;8BAKjE,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;;;;;;kDAEpB,6LAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wCAC7C,WAAU;;;;;;;;;;;;0CAId,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;;;;;;kDAEpB,6LAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;wCAC/C,WAAU;wCACV,OAAO;4CAAE,iBAAiB;4CAAuO,gBAAgB;4CAAe,oBAAoB;wCAAuB;;0DAE3U,6LAAC;gDAAO,OAAM;0DAAG;;;;;;4CAChB,WAAW,GAAG,CAAC,CAAA,qBACd,6LAAC;oDAAkB,OAAO;8DAAO;mDAApB;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAQtB,aAAa,MAAM,GAAG,kBACrB,6LAAC;oBAAI,WAAU;8BACZ,aAAa,GAAG,CAAC,CAAC,OAAO,sBACxB,6LAAC;4BAEC,WAAU;4BACV,OAAO;gCAAE,gBAAgB,GAAG,QAAQ,IAAI,EAAE,CAAC;gCAAE,mBAAmB;4BAAW;sCAE3E,cAAA,6LAAC,6JAAA,CAAA,UAAiB;gCAAC,OAAO;;;;;;2BAJrB,MAAM,EAAE;;;;;;;;;yCASnB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,6MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;;;;;;sCAEtB,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,6LAAC;4BAAE,WAAU;sCACV,cAAc,eACX,8EACA;;;;;;;;;;;;8BAMV,6LAAC;oBAAI,WAAU;oBAAqD,OAAO;wBAAE,mBAAmB;oBAAW;8BACzG,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,6MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;0CAGtB,6LAAC;gCAAG,WAAU;0CAAwC;;;;;;0CACtD,6LAAC;gCAAE,WAAU;0CAAuC;;;;;;0CAKpD,6LAAC;gCACC,MAAK,sBAAsB,yCAAyC;;gCACpE,WAAU;;kDAEV,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAS;;;;;;;0CAG/B,6LAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQtD;GAxHM;KAAA;uCA0HS", "debugId": null}}, {"offset": {"line": 1035, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/fwu_incubation%28react%2Blaravel%29/incubation/frontend/src/app/programs/page.tsx"], "sourcesContent": ["\"use client\"\r\nimport { useState, useEffect, useRef } from 'react';\r\nimport PastEventsGallerySection from \"../components/programs/PastEventsGallerySection \";\r\nimport ProgramCalendarSection from \"../components/programs/ProgramCalendarSection \";\r\nimport ProgramTypesSection from \"../components/programs/ProgramTypesSection\";\r\nimport {\r\n  ArrowDown,\r\n  Sparkles,\r\n  Target,\r\n  Users,\r\n  Award,\r\n\r\n  Rocket,\r\n  Star,\r\n  ChevronRight,\r\n  Play\r\n} from 'lucide-react';\r\nimport Image from 'next/image';\r\nimport Link from 'next/link';\r\n\r\nexport default function ProgramsPage() {\r\n  const [isVisible, setIsVisible] = useState(false);\r\n  const [activeFeature, setActiveFeature] = useState(0);\r\n  const heroRef = useRef(null);\r\n\r\n  useEffect(() => {\r\n    setIsVisible(true);\r\n\r\n    // Auto-rotate features\r\n    const interval = setInterval(() => {\r\n      setActiveFeature((prev) => (prev + 1) % 4);\r\n    }, 3000);\r\n\r\n    return () => clearInterval(interval);\r\n  }, []);\r\n\r\n  // Function to scroll to the next section smoothly\r\n  const scrollToNextSection = () => {\r\n    const programsSection = document.getElementById('program-types');\r\n    if (programsSection) {\r\n      programsSection.scrollIntoView({ behavior: 'smooth' });\r\n    }\r\n  };\r\n\r\n  return (\r\n    <>\r\n      {/* Enhanced Hero Section with Modern Design */}\r\n      <section\r\n        ref={heroRef}\r\n        className=\"relative min-h-[100vh] flex items-center overflow-hidden bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900\"\r\n      >\r\n        {/* Dynamic Background Elements */}\r\n        <div className=\"absolute inset-0\">\r\n          {/* Hero Background Image */}\r\n          <Image\r\n            src=\"https://images.unsplash.com/photo-1522202176988-66273c2fd55f?q=80&w=2000&auto=format&fit=crop\"\r\n            alt=\"FWU Incubation Programs\"\r\n            fill\r\n            priority\r\n            className=\"object-cover opacity-20\"\r\n          />\r\n\r\n          {/* Gradient Overlays */}\r\n          <div className=\"absolute inset-0 bg-gradient-to-br from-blue-900/95 via-indigo-900/90 to-purple-900/95\"></div>\r\n          <div className=\"absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent\"></div>\r\n\r\n          {/* Animated Mesh Pattern */}\r\n          <div\r\n            className=\"absolute inset-0 opacity-10\"\r\n            style={{\r\n              backgroundImage: `\r\n                linear-gradient(rgba(59, 130, 246, 0.3) 1px, transparent 1px),\r\n                linear-gradient(90deg, rgba(59, 130, 246, 0.3) 1px, transparent 1px)\r\n              `,\r\n              backgroundSize: '60px 60px',\r\n              animation: 'mesh-drift 25s linear infinite'\r\n            }}\r\n          ></div>\r\n        </div>\r\n\r\n        {/* Floating Elements */}\r\n        <div className=\"absolute inset-0 overflow-hidden\">\r\n          {/* Animated Particles */}\r\n          {[...Array(40)].map((_, i) => (\r\n            <div\r\n              key={i}\r\n              className={`absolute rounded-full ${\r\n                i % 5 === 0 ? 'bg-blue-400/30' :\r\n                i % 5 === 1 ? 'bg-purple-400/30' :\r\n                i % 5 === 2 ? 'bg-indigo-400/30' :\r\n                i % 5 === 3 ? 'bg-pink-400/30' : 'bg-cyan-400/30'\r\n              }`}\r\n              style={{\r\n                width: `${Math.random() * 8 + 3}px`,\r\n                height: `${Math.random() * 8 + 3}px`,\r\n                top: `${Math.random() * 100}%`,\r\n                left: `${Math.random() * 100}%`,\r\n                animation: `float-drift ${\r\n                  10 + Math.random() * 20\r\n                }s infinite ease-in-out`,\r\n                animationDelay: `${Math.random() * 10}s`,\r\n              }}\r\n            ></div>\r\n          ))}\r\n\r\n          {/* Glowing Orbs */}\r\n          {[...Array(6)].map((_, i) => (\r\n            <div\r\n              key={`orb-${i}`}\r\n              className={`absolute rounded-full blur-2xl ${\r\n                i % 3 === 0 ? 'bg-blue-500/15' :\r\n                i % 3 === 1 ? 'bg-purple-500/15' : 'bg-indigo-500/15'\r\n              }`}\r\n              style={{\r\n                width: `${Math.random() * 300 + 150}px`,\r\n                height: `${Math.random() * 300 + 150}px`,\r\n                top: `${Math.random() * 100}%`,\r\n                left: `${Math.random() * 100}%`,\r\n                animation: `pulse-glow ${\r\n                  15 + Math.random() * 10\r\n                }s infinite ease-in-out`,\r\n                animationDelay: `${Math.random() * 8}s`,\r\n              }}\r\n            ></div>\r\n          ))}\r\n        </div>\r\n\r\n        {/* Geometric Decorations */}\r\n        <div className=\"absolute inset-0\">\r\n          <div className=\"absolute top-32 right-32 w-40 h-40 border-2 border-white/10 rounded-full animate-spin-slow\"></div>\r\n          <div className=\"absolute bottom-40 left-32 w-32 h-32 border border-purple-400/20 rotate-45 animate-pulse\"></div>\r\n          <div className=\"absolute top-1/2 right-1/4 w-20 h-20 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-xl rotate-12 animate-float\"></div>\r\n        </div>\r\n\r\n        {/* Main Content */}\r\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 relative z-20\">\r\n          <div className=\"grid lg:grid-cols-2 gap-12 items-center\">\r\n            {/* Left Content */}\r\n            <div className={`text-center lg:text-left transition-all duration-1000 transform ${\r\n              isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'\r\n            }`}>\r\n              {/* Enhanced Badge */}\r\n              <div className=\"inline-block mb-8\">\r\n                <div className=\"flex items-center justify-center lg:justify-start space-x-3 bg-gradient-to-r from-white/20 to-white/10 backdrop-blur-md px-6 py-3 rounded-2xl border border-white/30 shadow-2xl\">\r\n                  <Sparkles className=\"text-yellow-300\" size={20} />\r\n                  <span className=\"text-white font-semibold text-sm tracking-wider uppercase\">\r\n                    Innovation Hub\r\n                  </span>\r\n                  <Star className=\"text-yellow-300\" size={16} />\r\n                </div>\r\n              </div>\r\n\r\n              {/* Main Heading */}\r\n              <h1 className=\"text-5xl sm:text-6xl md:text-7xl lg:text-8xl font-black mb-8 leading-tight\">\r\n                <span className=\"block text-white mb-2\">FWU</span>\r\n                <span className=\"block text-transparent bg-clip-text bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 animate-gradient-x\">\r\n                  Incubation\r\n                </span>\r\n                <span className=\"block text-transparent bg-clip-text bg-gradient-to-r from-purple-400 via-pink-400 to-blue-400 animate-gradient-x-reverse\">\r\n                  Programs\r\n                </span>\r\n              </h1>\r\n\r\n              {/* Description */}\r\n              <p className=\"text-xl sm:text-2xl text-blue-100 max-w-2xl mx-auto lg:mx-0 mb-6 leading-relaxed\">\r\n                Empowering <span className=\"font-semibold text-white\">innovation</span> and\r\n                <span className=\"text-purple-300 font-semibold\"> entrepreneurship</span> at Far Western University\r\n              </p>\r\n\r\n              <p className=\"text-lg text-blue-200 max-w-2xl mx-auto lg:mx-0 mb-10 leading-relaxed\">\r\n                Discover our range of specialized programs designed to support entrepreneurs at every stage of their journey,\r\n                from ideation to market launch. Join our vibrant community of innovators and change-makers.\r\n              </p>\r\n\r\n              {/* Action Buttons */}\r\n              <div className=\"flex flex-col sm:flex-row items-center justify-center lg:justify-start gap-6 mb-12\">\r\n                <button\r\n                  onClick={scrollToNextSection}\r\n                  className=\"group relative px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-bold rounded-2xl transition-all duration-300 hover:shadow-2xl hover:shadow-blue-500/25 hover:-translate-y-1 flex items-center overflow-hidden\"\r\n                >\r\n                  <div className=\"absolute inset-0 bg-gradient-to-r from-blue-700 to-purple-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\r\n                  <span className=\"relative z-10\">Explore Programs</span>\r\n                  <ArrowDown className=\"relative z-10 ml-2 group-hover:translate-y-1 transition-transform duration-300\" />\r\n                </button>\r\n\r\n                <Link\r\n                  href=\"/apply\"\r\n                  className=\"group relative px-8 py-4 bg-white/10 hover:bg-white/20 backdrop-blur-md text-white font-bold rounded-2xl transition-all duration-300 border-2 border-white/30 hover:border-white/50 hover:shadow-2xl hover:shadow-white/10 hover:-translate-y-1 flex items-center overflow-hidden\"\r\n                >\r\n                  <div className=\"absolute inset-0 bg-white/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\r\n                  <span className=\"relative z-10\">Apply Now</span>\r\n                  <ChevronRight className=\"relative z-10 ml-2 group-hover:translate-x-1 transition-transform duration-300\" />\r\n                </Link>\r\n              </div>\r\n\r\n              {/* Feature Highlights */}\r\n              <div className=\"grid grid-cols-2 gap-4 max-w-md mx-auto lg:mx-0\">\r\n                {[\r\n                  { icon: Target, text: \"Expert Mentorship\", color: \"text-blue-400\" },\r\n                  { icon: Users, text: \"Vibrant Community\", color: \"text-purple-400\" },\r\n                  { icon: Rocket, text: \"Launch Support\", color: \"text-pink-400\" },\r\n                  { icon: Award, text: \"Industry Recognition\", color: \"text-yellow-400\" }\r\n                ].map((item, index) => (\r\n                  <div\r\n                    key={index}\r\n                    className={`flex items-center space-x-3 p-3 bg-white/5 backdrop-blur-sm rounded-xl border border-white/10 hover:bg-white/10 transition-all duration-300 group ${\r\n                      activeFeature === index ? 'bg-white/15 border-white/30' : ''\r\n                    }`}\r\n                  >\r\n                    <item.icon className={`${item.color} group-hover:scale-110 transition-transform duration-300`} size={20} />\r\n                    <span className=\"text-white font-medium text-sm\">{item.text}</span>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            </div>\r\n\r\n            {/* Right Content - Video/Image */}\r\n            <div className={`relative transition-all duration-1000 delay-300 transform ${\r\n              isVisible ? 'opacity-100 translate-x-0' : 'opacity-0 translate-x-10'\r\n            }`}>\r\n              <div className=\"relative rounded-3xl overflow-hidden shadow-2xl group\">\r\n                {/* Main Image */}\r\n                <div className=\"relative h-[500px] lg:h-[600px] w-full\">\r\n                  <Image\r\n                    src=\"https://images.unsplash.com/photo-1521737604893-d14cc237f11d?q=80&w=1000&auto=format&fit=crop\"\r\n                    alt=\"FWU Incubation Programs\"\r\n                    fill\r\n                    className=\"object-cover transform group-hover:scale-105 transition-transform duration-700\"\r\n                  />\r\n                  <div className=\"absolute inset-0 bg-gradient-to-t from-indigo-900/70 via-transparent to-transparent\"></div>\r\n\r\n                  {/* Play Button Overlay */}\r\n                  <div className=\"absolute inset-0 flex items-center justify-center\">\r\n                    <button className=\"w-20 h-20 bg-white/20 backdrop-blur-md rounded-full flex items-center justify-center border-2 border-white/30 hover:bg-white/30 transition-all duration-300 group-hover:scale-110\">\r\n                      <Play className=\"text-white ml-1\" size={32} />\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Floating Stats Cards */}\r\n                <div className=\"absolute -bottom-6 -left-6 bg-white/90 backdrop-blur-md p-4 rounded-2xl shadow-xl border border-white/50\">\r\n                  <div className=\"text-2xl font-bold text-indigo-600 mb-1\">20+</div>\r\n                  <div className=\"text-sm text-gray-600\">Active Programs</div>\r\n                </div>\r\n\r\n                <div className=\"absolute -top-6 -right-6 bg-white/90 backdrop-blur-md p-4 rounded-2xl shadow-xl border border-white/50\">\r\n                  <div className=\"text-2xl font-bold text-purple-600 mb-1\">500+</div>\r\n                  <div className=\"text-sm text-gray-600\">Participants</div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Enhanced Stats Section */}\r\n        <div className=\"absolute bottom-20 left-1/2 transform -translate-x-1/2 w-full max-w-4xl px-4\">\r\n          <div className={`grid grid-cols-2 md:grid-cols-4 gap-4 transition-all duration-1000 delay-500 transform ${\r\n            isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'\r\n          }`}>\r\n            {[\r\n              { number: \"20+\", label: \"Active Programs\", color: \"from-blue-500 to-blue-600\" },\r\n              { number: \"500+\", label: \"Participants\", color: \"from-purple-500 to-purple-600\" },\r\n              { number: \"50+\", label: \"Expert Mentors\", color: \"from-indigo-500 to-indigo-600\" },\r\n              { number: \"95%\", label: \"Success Rate\", color: \"from-pink-500 to-pink-600\" }\r\n            ].map((stat, index) => (\r\n              <div\r\n                key={index}\r\n                className=\"bg-white/10 backdrop-blur-md p-4 rounded-2xl border border-white/20 hover:bg-white/20 transition-all duration-300 group\"\r\n              >\r\n                <div className={`text-2xl md:text-3xl font-bold bg-gradient-to-r ${stat.color} bg-clip-text text-transparent mb-1 group-hover:scale-110 transition-transform duration-300`}>\r\n                  {stat.number}\r\n                </div>\r\n                <div className=\"text-white/80 text-sm font-medium\">{stat.label}</div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Scroll Indicator */}\r\n        <div className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2\">\r\n          <button\r\n            onClick={scrollToNextSection}\r\n            className=\"w-12 h-12 bg-white/10 backdrop-blur-md rounded-full flex items-center justify-center border border-white/20 hover:bg-white/20 transition-all duration-300 group animate-bounce\"\r\n            aria-label=\"Scroll down to programs\"\r\n          >\r\n            <ArrowDown className=\"text-white group-hover:translate-y-1 transition-transform duration-300\" size={24} />\r\n          </button>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Program Types Section with ID for scroll targeting */}\r\n      <div id=\"program-types\">\r\n        {/* <ProgramTypesSection /> */}\r\n      </div>\r\n\r\n      {/* Calendar Section */}\r\n      <ProgramCalendarSection />\r\n\r\n      {/* Past Events Gallery */}\r\n      <PastEventsGallerySection />\r\n\r\n      {/* Custom CSS for animations */}\r\n      <style jsx global>{`\r\n        @keyframes mesh-drift {\r\n          0% { transform: translate(0, 0); }\r\n          100% { transform: translate(-60px, -60px); }\r\n        }\r\n\r\n        @keyframes float-drift {\r\n          0%, 100% { transform: translate(0, 0) rotate(0deg); }\r\n          25% { transform: translate(10px, -10px) rotate(90deg); }\r\n          50% { transform: translate(-5px, -15px) rotate(180deg); }\r\n          75% { transform: translate(-10px, 5px) rotate(270deg); }\r\n        }\r\n\r\n        @keyframes pulse-glow {\r\n          0%, 100% { opacity: 0.3; transform: scale(1); }\r\n          50% { opacity: 0.6; transform: scale(1.1); }\r\n        }\r\n\r\n        @keyframes gradient-x {\r\n          0%, 100% { background-position: 0% 50%; }\r\n          50% { background-position: 100% 50%; }\r\n        }\r\n\r\n        @keyframes gradient-x-reverse {\r\n          0%, 100% { background-position: 100% 50%; }\r\n          50% { background-position: 0% 50%; }\r\n        }\r\n\r\n        .animate-gradient-x {\r\n          background-size: 200% 200%;\r\n          animation: gradient-x 3s ease infinite;\r\n        }\r\n\r\n        .animate-gradient-x-reverse {\r\n          background-size: 200% 200%;\r\n          animation: gradient-x-reverse 3s ease infinite;\r\n        }\r\n\r\n        .animate-float {\r\n          animation: float 6s ease-in-out infinite;\r\n        }\r\n\r\n        .animate-spin-slow {\r\n          animation: spin 20s linear infinite;\r\n        }\r\n\r\n        @keyframes float {\r\n          0%, 100% { transform: translateY(0px) rotate(0deg); }\r\n          50% { transform: translateY(-20px) rotate(180deg); }\r\n        }\r\n      `}</style>\r\n    </>\r\n  );\r\n}"], "names": [], "mappings": ";;;;;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AACA;;;AAlBA;;;;;;;;AAoBe,SAAS;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,aAAa;YAEb,uBAAuB;YACvB,MAAM,WAAW;mDAAY;oBAC3B;2DAAiB,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI;;gBAC1C;kDAAG;YAEH;0CAAO,IAAM,cAAc;;QAC7B;iCAAG,EAAE;IAEL,kDAAkD;IAClD,MAAM,sBAAsB;QAC1B,MAAM,kBAAkB,SAAS,cAAc,CAAC;QAChD,IAAI,iBAAiB;YACnB,gBAAgB,cAAc,CAAC;gBAAE,UAAU;YAAS;QACtD;IACF;IAEA,qBACE;;0BAEE,6LAAC;gBACC,KAAK;0DACK;;kCAGV,6LAAC;kEAAc;;0CAEb,6LAAC,gIAAA,CAAA,UAAK;gCACJ,KAAI;gCACJ,KAAI;gCACJ,IAAI;gCACJ,QAAQ;gCACR,WAAU;;;;;;0CAIZ,6LAAC;0EAAc;;;;;;0CACf,6LAAC;0EAAc;;;;;;0CAGf,6LAAC;gCAEC,OAAO;oCACL,iBAAiB,CAAC;;;cAGlB,CAAC;oCACD,gBAAgB;oCAChB,WAAW;gCACb;0EARU;;;;;;;;;;;;kCAad,6LAAC;kEAAc;;4BAEZ;mCAAI,MAAM;6BAAI,CAAC,GAAG,CAAC,CAAC,GAAG,kBACtB,6LAAC;oCAQC,OAAO;wCACL,OAAO,GAAG,KAAK,MAAM,KAAK,IAAI,EAAE,EAAE,CAAC;wCACnC,QAAQ,GAAG,KAAK,MAAM,KAAK,IAAI,EAAE,EAAE,CAAC;wCACpC,KAAK,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;wCAC9B,MAAM,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;wCAC/B,WAAW,CAAC,YAAY,EACtB,KAAK,KAAK,MAAM,KAAK,GACtB,sBAAsB,CAAC;wCACxB,gBAAgB,GAAG,KAAK,MAAM,KAAK,GAAG,CAAC,CAAC;oCAC1C;8EAfW,CAAC,sBAAsB,EAChC,IAAI,MAAM,IAAI,mBACd,IAAI,MAAM,IAAI,qBACd,IAAI,MAAM,IAAI,qBACd,IAAI,MAAM,IAAI,mBAAmB,kBACjC;mCANG;;;;;4BAqBR;mCAAI,MAAM;6BAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC;oCAMC,OAAO;wCACL,OAAO,GAAG,KAAK,MAAM,KAAK,MAAM,IAAI,EAAE,CAAC;wCACvC,QAAQ,GAAG,KAAK,MAAM,KAAK,MAAM,IAAI,EAAE,CAAC;wCACxC,KAAK,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;wCAC9B,MAAM,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;wCAC/B,WAAW,CAAC,WAAW,EACrB,KAAK,KAAK,MAAM,KAAK,GACtB,sBAAsB,CAAC;wCACxB,gBAAgB,GAAG,KAAK,MAAM,KAAK,EAAE,CAAC,CAAC;oCACzC;8EAbW,CAAC,+BAA+B,EACzC,IAAI,MAAM,IAAI,mBACd,IAAI,MAAM,IAAI,qBAAqB,oBACnC;mCAJG,CAAC,IAAI,EAAE,GAAG;;;;;;;;;;;kCAoBrB,6LAAC;kEAAc;;0CACb,6LAAC;0EAAc;;;;;;0CACf,6LAAC;0EAAc;;;;;;0CACf,6LAAC;0EAAc;;;;;;;;;;;;kCAIjB,6LAAC;kEAAc;kCACb,cAAA,6LAAC;sEAAc;;8CAEb,6LAAC;8EAAe,CAAC,gEAAgE,EAC/E,YAAY,8BAA8B,4BAC1C;;sDAEA,6LAAC;sFAAc;sDACb,cAAA,6LAAC;0FAAc;;kEACb,6LAAC,6MAAA,CAAA,WAAQ;wDAAC,WAAU;wDAAkB,MAAM;;;;;;kEAC5C,6LAAC;kGAAe;kEAA4D;;;;;;kEAG5E,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;wDAAkB,MAAM;;;;;;;;;;;;;;;;;sDAK5C,6LAAC;sFAAa;;8DACZ,6LAAC;8FAAe;8DAAwB;;;;;;8DACxC,6LAAC;8FAAe;8DAAmH;;;;;;8DAGnI,6LAAC;8FAAe;8DAA2H;;;;;;;;;;;;sDAM7I,6LAAC;sFAAY;;gDAAmF;8DACnF,6LAAC;8FAAe;8DAA2B;;;;;;gDAAiB;8DACvE,6LAAC;8FAAe;8DAAgC;;;;;;gDAAwB;;;;;;;sDAG1E,6LAAC;sFAAY;sDAAwE;;;;;;sDAMrF,6LAAC;sFAAc;;8DACb,6LAAC;oDACC,SAAS;8FACC;;sEAEV,6LAAC;sGAAc;;;;;;sEACf,6LAAC;sGAAe;sEAAgB;;;;;;sEAChC,6LAAC,mNAAA,CAAA,YAAS;4DAAC,WAAU;;;;;;;;;;;;8DAGvB,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;;sEAEV,6LAAC;sGAAc;;;;;;sEACf,6LAAC;sGAAe;sEAAgB;;;;;;sEAChC,6LAAC,yNAAA,CAAA,eAAY;4DAAC,WAAU;;;;;;;;;;;;;;;;;;sDAK5B,6LAAC;sFAAc;sDACZ;gDACC;oDAAE,MAAM,yMAAA,CAAA,SAAM;oDAAE,MAAM;oDAAqB,OAAO;gDAAgB;gDAClE;oDAAE,MAAM,uMAAA,CAAA,QAAK;oDAAE,MAAM;oDAAqB,OAAO;gDAAkB;gDACnE;oDAAE,MAAM,yMAAA,CAAA,SAAM;oDAAE,MAAM;oDAAkB,OAAO;gDAAgB;gDAC/D;oDAAE,MAAM,uMAAA,CAAA,QAAK;oDAAE,MAAM;oDAAwB,OAAO;gDAAkB;6CACvE,CAAC,GAAG,CAAC,CAAC,MAAM,sBACX,6LAAC;8FAEY,CAAC,kJAAkJ,EAC5J,kBAAkB,QAAQ,gCAAgC,IAC1D;;sEAEF,6LAAC,KAAK,IAAI;4DAAC,WAAW,GAAG,KAAK,KAAK,CAAC,wDAAwD,CAAC;4DAAE,MAAM;;;;;;sEACrG,6LAAC;sGAAe;sEAAkC,KAAK,IAAI;;;;;;;mDANtD;;;;;;;;;;;;;;;;8CAab,6LAAC;8EAAe,CAAC,0DAA0D,EACzE,YAAY,8BAA8B,4BAC1C;8CACA,cAAA,6LAAC;kFAAc;;0DAEb,6LAAC;0FAAc;;kEACb,6LAAC,gIAAA,CAAA,UAAK;wDACJ,KAAI;wDACJ,KAAI;wDACJ,IAAI;wDACJ,WAAU;;;;;;kEAEZ,6LAAC;kGAAc;;;;;;kEAGf,6LAAC;kGAAc;kEACb,cAAA,6LAAC;sGAAiB;sEAChB,cAAA,6LAAC,qMAAA,CAAA,OAAI;gEAAC,WAAU;gEAAkB,MAAM;;;;;;;;;;;;;;;;;;;;;;0DAM9C,6LAAC;0FAAc;;kEACb,6LAAC;kGAAc;kEAA0C;;;;;;kEACzD,6LAAC;kGAAc;kEAAwB;;;;;;;;;;;;0DAGzC,6LAAC;0FAAc;;kEACb,6LAAC;kGAAc;kEAA0C;;;;;;kEACzD,6LAAC;kGAAc;kEAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQjD,6LAAC;kEAAc;kCACb,cAAA,6LAAC;sEAAe,CAAC,uFAAuF,EACtG,YAAY,8BAA8B,4BAC1C;sCACC;gCACC;oCAAE,QAAQ;oCAAO,OAAO;oCAAmB,OAAO;gCAA4B;gCAC9E;oCAAE,QAAQ;oCAAQ,OAAO;oCAAgB,OAAO;gCAAgC;gCAChF;oCAAE,QAAQ;oCAAO,OAAO;oCAAkB,OAAO;gCAAgC;gCACjF;oCAAE,QAAQ;oCAAO,OAAO;oCAAgB,OAAO;gCAA4B;6BAC5E,CAAC,GAAG,CAAC,CAAC,MAAM,sBACX,6LAAC;8EAEW;;sDAEV,6LAAC;sFAAe,CAAC,gDAAgD,EAAE,KAAK,KAAK,CAAC,2FAA2F,CAAC;sDACvK,KAAK,MAAM;;;;;;sDAEd,6LAAC;sFAAc;sDAAqC,KAAK,KAAK;;;;;;;mCANzD;;;;;;;;;;;;;;;kCAab,6LAAC;kEAAc;kCACb,cAAA,6LAAC;4BACC,SAAS;4BAET,cAAW;sEADD;sCAGV,cAAA,6LAAC,mNAAA,CAAA,YAAS;gCAAC,WAAU;gCAAyE,MAAM;;;;;;;;;;;;;;;;;;;;;;0BAM1G,6LAAC;gBAAI,IAAG;;;;;;;0BAKR,6LAAC,oKAAA,CAAA,UAAsB;;;;;0BAGvB,6LAAC,sKAAA,CAAA,UAAwB;;;;;;;;;;;AAwD/B;GA/UwB;KAAA", "debugId": null}}]}