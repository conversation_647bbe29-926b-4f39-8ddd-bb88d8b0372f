
"use client"
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, MapPin, Star, Calendar, Award } from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';
import { Program } from '../../../types/program.types';

export interface ProgramTypeCardProps {
  program: Program;
  variant?: 'default' | 'featured' | 'compact';
  showImage?: boolean;
  showStats?: boolean;
  className?: string;
}

const ProgramTypeCard: React.FC<ProgramTypeCardProps> = ({
  program,
  // variant = 'default',
  showImage = true,
  showStats = true,
  className = ''
}) => {
  const getAccentColor = (category: string) => {
    const colors = {
      'bootcamp': 'from-blue-500 to-indigo-600',
      'hackathon': 'from-teal-500 to-cyan-600',
      'workshop': 'from-purple-500 to-pink-600',
      'demo-day': 'from-amber-500 to-orange-600',
      'mentorship': 'from-green-500 to-emerald-600',
      'accelerator': 'from-red-500 to-rose-600'
    };
    return colors[category as keyof typeof colors] || 'from-gray-500 to-gray-600';
  };

  const getStatusColor = (status: string) => {
    const colors = {
      'active': 'bg-green-100 text-green-800',
      'coming-soon': 'bg-yellow-100 text-yellow-800',
      'inactive': 'bg-gray-100 text-gray-800',
      'archived': 'bg-red-100 text-red-800'
    };
    return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  return (
    <div className={`group relative bg-white rounded-3xl border border-gray-100 shadow-xl hover:shadow-2xl transition-all duration-500 overflow-hidden hover:-translate-y-2 ${className}`}>
      {/* Gradient Accent */}
      <div className={`absolute top-0 left-0 right-0 h-1 bg-gradient-to-r ${getAccentColor(program.category)} transform origin-left scale-x-0 group-hover:scale-x-100 transition-transform duration-700`}></div>

      {/* Featured Badge */}
      {program.featured && (
        <div className="absolute top-4 right-4 z-10">
          <div className="bg-gradient-to-r from-yellow-400 to-orange-400 text-white px-3 py-1 rounded-full text-xs font-bold flex items-center shadow-lg">
            <Star className="mr-1" size={12} />
            Featured
          </div>
        </div>
      )}

      {/* Status Badge */}
      <div className="absolute top-4 left-4 z-10">
        <span className={`px-3 py-1 rounded-full text-xs font-semibold ${getStatusColor(program.status)}`}>
          {program.status.replace('-', ' ').toUpperCase()}
        </span>
      </div>

      {/* Hero Image */}
      {showImage && program.heroImageUrl && (
        <div className="relative h-48 overflow-hidden">
          <Image
            src={program.heroImageUrl}
            alt={program.title}
            fill
            className="object-cover group-hover:scale-110 transition-transform duration-700"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent"></div>

          {/* Category Badge on Image */}
          <div className="absolute bottom-4 left-4">
            <span className={`px-3 py-1 rounded-full text-xs font-bold text-white bg-gradient-to-r ${getAccentColor(program.category)} shadow-lg`}>
              {program.category.charAt(0).toUpperCase() + program.category.slice(1).replace('-', ' ')}
            </span>
          </div>
        </div>
      )}

      {/* Content */}
      <div className="p-8">
        {/* Icon and Title */}
        <div className="flex items-start justify-between mb-6">
          <div className="flex items-center space-x-4">
            {program.icon && (
              <div className={`w-16 h-16 rounded-2xl bg-gradient-to-r ${getAccentColor(program.category)} flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300`}>
                <div className="text-white text-2xl">
                  {program.icon}
                </div>
              </div>
            )}
          </div>
        </div>

        <h3 className="text-2xl font-bold text-gray-900 mb-4 group-hover:text-transparent group-hover:bg-clip-text group-hover:bg-gradient-to-r group-hover:from-blue-600 group-hover:to-purple-600 transition-all duration-300">
          {program.title}
        </h3>

        <p className="text-gray-600 mb-6 leading-relaxed line-clamp-3">
          {program.shortDescription}
        </p>

        {/* Program Details */}
        <div className="grid grid-cols-2 gap-4 mb-6">
          <div className="flex items-center space-x-2 text-sm text-gray-500">
            <Clock size={16} />
            <span>{program.duration}</span>
          </div>
          <div className="flex items-center space-x-2 text-sm text-gray-500">
            <Users size={16} />
            <span>{program.capacity} spots</span>
          </div>
          <div className="flex items-center space-x-2 text-sm text-gray-500">
            <MapPin size={16} />
            <span>{program.format}</span>
          </div>
          <div className="flex items-center space-x-2 text-sm text-gray-500">
            <Calendar size={16} />
            <span>{program.upcomingDates.length} dates</span>
          </div>
        </div>

        {/* Benefits Preview */}
        <div className="mb-6">
          <h4 className="text-sm font-semibold text-gray-700 mb-3">Key Benefits:</h4>
          <div className="space-y-2">
            {program.benefits.slice(0, 2).map((benefit, index) => (
              <div key={index} className="flex items-start space-x-2">
                <div className="w-4 h-4 bg-gradient-to-r from-green-400 to-blue-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <Star className="text-white" size={8} />
                </div>
                <span className="text-sm text-gray-600 leading-relaxed">{benefit}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Stats */}
        {showStats && program.stats && (
          <div className="grid grid-cols-2 gap-4 mb-6 p-4 bg-gray-50 rounded-xl">
            {program.stats.successRate && (
              <div className="text-center">
                <div className="text-lg font-bold text-green-600">{program.stats.successRate}%</div>
                <div className="text-xs text-gray-500">Success Rate</div>
              </div>
            )}
            {program.stats.totalParticipants && (
              <div className="text-center">
                <div className="text-lg font-bold text-blue-600">{program.stats.totalParticipants}+</div>
                <div className="text-xs text-gray-500">Participants</div>
              </div>
            )}
          </div>
        )}

        {/* Tags */}
        <div className="flex flex-wrap gap-2 mb-6">
          {program.tags.slice(0, 3).map((tag, index) => (
            <span
              key={index}
              className="px-2 py-1 bg-blue-50 text-blue-600 text-xs font-medium rounded-lg"
            >
              {tag}
            </span>
          ))}
          {program.tags.length > 3 && (
            <span className="px-2 py-1 bg-gray-50 text-gray-500 text-xs font-medium rounded-lg">
              +{program.tags.length - 3} more
            </span>
          )}
        </div>

        {/* Action Button */}
        <Link
          href={`/programs/${program.slug}`}
          className={`group/btn relative w-full flex items-center justify-center px-6 py-4 bg-gradient-to-r ${getAccentColor(program.category)} text-white font-bold rounded-2xl transition-all duration-300 hover:shadow-lg hover:shadow-blue-500/25 overflow-hidden`}
        >
          <div className="absolute inset-0 bg-white/20 opacity-0 group-hover/btn:opacity-100 transition-opacity duration-300"></div>
          <span className="relative z-10 mr-2">Learn More</span>
          <ArrowRight className="relative z-10 group-hover/btn:translate-x-1 transition-transform duration-300" size={20} />
        </Link>

        {/* Next Date */}
        {program.upcomingDates.length > 0 && (
          <div className="mt-4 p-3 bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl border border-blue-100">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-sm font-semibold text-gray-700">Next Session</div>
                <div className="text-xs text-gray-500">{program.upcomingDates[0].date}</div>
              </div>
              <div className="flex items-center space-x-1">
                <Award className="text-blue-500" size={16} />
                <span className="text-xs text-blue-600 font-medium">
                  {program.upcomingDates[0].capacity} spots
                </span>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ProgramTypeCard;