"use client"
import React from 'react'
import { 
  Plus, 
  Bell,
  TrendingUp,
  Users,
  FileText,
  Camera,
  UserCheck,
  ClipboardList,
  // Renamed to avoid conflict with Recharts PieChart component
  Award,
  Rocket,
  LucideIcon // Import LucideIcon type for icon props
} from 'lucide-react'
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Pie, Cell } from 'recharts' // Removed LineChart, Line, Area, AreaChart as they were not used, and corrected Pie import.
import UniversityDashboardHeader from './_components/UniversityDashboardHeader';

// --- Type Definitions for Mock Data ---

interface Stats {
  totalApplications: number;
  activeStartups: number;
  totalApplicants: number;
  committeeMembers: number;
  news: number;
  notices: number;
  galleryItems: number;
  successfulGraduates: number;
}

interface MonthlyApplicationData {
  month: string;
  applications: number;
  startups: number;
}

interface CategoryDistributionData {
  name: string;
  value: number;
  color: string;
}

interface RecentActivity {
  id: number;
  type: 'application' | 'startup' | 'news' | 'committee'; // Use a union type for specific activity types
  title: string;
  time: string;
  status: 'pending' | 'success' | 'published' | 'scheduled'; // Use a union type for specific statuses
}

interface DashboardDataType {
  stats: Stats;
  monthlyApplications: MonthlyApplicationData[];
  categoryDistribution: CategoryDistributionData[];
  recentActivities: RecentActivity[];
}

// Mock data for dashboard
const dashboardData: DashboardDataType = {
  stats: {
    totalApplications: 145,
    activeStartups: 23,
    totalApplicants: 89,
    committeeMembers: 12,
    news: 34,
    notices: 18,
    galleryItems: 156,
    successfulGraduates: 8
  },
  monthlyApplications: [
    { month: 'Jan', applications: 12, startups: 2 },
    { month: 'Feb', applications: 18, startups: 3 },
    { month: 'Mar', applications: 15, startups: 4 },
    { month: 'Apr', applications: 22, startups: 5 },
    { month: 'May', applications: 28, startups: 3 },
    { month: 'Jun', applications: 35, startups: 6 }
  ],
  categoryDistribution: [
    { name: 'Technology', value: 35, color: '#3B82F6' },
    { name: 'Agriculture', value: 25, color: '#10B981' },
    { name: 'Healthcare', value: 20, color: '#F59E0B' },
    { name: 'Education', value: 12, color: '#EF4444' },
    { name: 'Others', value: 8, color: '#8B5CF6' }
  ],
  recentActivities: [
    { id: 1, type: 'application', title: 'New application from TechNova Solutions', time: '2 hours ago', status: 'pending' },
    { id: 2, type: 'startup', title: 'AgroInnovate graduated successfully', time: '5 hours ago', status: 'success' },
    { id: 3, type: 'news', title: 'Workshop on Digital Marketing published', time: '1 day ago', status: 'published' },
    { id: 4, type: 'committee', title: 'Monthly committee meeting scheduled', time: '2 days ago', status: 'scheduled' }
  ]
}

// --- Component Props Types ---

interface StatsCardProps {
  icon: LucideIcon; // Use LucideIcon type for icon components
  title: string;
  value: number | string; // Value can be number or string
  change?: number; // Optional prop
  color: string;
  description?: string; // Optional prop
}

// Header Component (no props, so simple React.FC)

// Stats Card Component
const StatsCard: React.FC<StatsCardProps> = ({ icon: Icon, title, value, change, color, description }) => (
  <div className="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 group cursor-pointer">
    <div className="flex items-start justify-between">
      <div className="flex-1">
        <div className="flex items-center space-x-3 mb-3">
          <div className={`p-3 rounded-xl ${color} group-hover:scale-110 transition-transform`}>
            <Icon className="w-6 h-6 text-white" />
          </div>
          <div>
            <p className="text-gray-600 text-sm font-medium">{title}</p>
            <h3 className="text-3xl font-bold text-gray-900">{value}</h3>
          </div>
        </div>
        {description && (
          <p className="text-gray-500 text-sm">{description}</p>
        )}
        {change !== undefined && ( // Check if change is provided
          <p className={`text-sm mt-2 flex items-center ${change > 0 ? 'text-green-600' : 'text-red-600'}`}>
            <TrendingUp className="w-4 h-4 mr-1" />
            {change > 0 ? '+' : ''}{change}% from last month
          </p>
        )}
      </div>
    </div>
  </div>
)

// Chart Component
const ApplicationsChart: React.FC = () => (
  <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
    <div className="flex items-center justify-between mb-6">
      <div>
        <h3 className="text-xl font-bold text-gray-900">Applications & Startups</h3>
        <p className="text-gray-600 text-sm">Monthly overview</p>
      </div>
      <div className="flex items-center space-x-4">
        <div className="flex items-center space-x-2">
          <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
          <span className="text-sm text-gray-600">Applications</span>
        </div>
        <div className="flex items-center space-x-2">
          <div className="w-3 h-3 bg-green-500 rounded-full"></div>
          <span className="text-sm text-gray-600">Startups</span>
        </div>
      </div>
    </div>
    <ResponsiveContainer width="100%" height={300}>
      <BarChart data={dashboardData.monthlyApplications}>
        <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
        <XAxis dataKey="month" stroke="#6b7280" />
        <YAxis stroke="#6b7280" />
        <Tooltip 
          contentStyle={{ 
            backgroundColor: 'white', 
            border: '1px solid #e5e7eb', 
            borderRadius: '12px',
            boxShadow: '0 10px 25px rgba(0,0,0,0.1)'
          }} 
        />
        <Bar dataKey="applications" fill="#3B82F6" radius={[4, 4, 0, 0]} />
        <Bar dataKey="startups" fill="#10B981" radius={[4, 4, 0, 0]} />
      </BarChart>
    </ResponsiveContainer>
  </div>
)

// Pie Chart Component
const CategoryChart: React.FC = () => (
  <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
    <div className="flex items-center justify-between mb-6">
      <div>
        <h3 className="text-xl font-bold text-gray-900">Startup Categories</h3>
        <p className="text-gray-600 text-sm">Distribution by sector</p>
      </div>
    </div>
    <ResponsiveContainer width="100%" height={300}>
      <>
        <Pie
          data={dashboardData.categoryDistribution}
          cx="50%"
          cy="50%"
          innerRadius={60}
          outerRadius={120}
          paddingAngle={5}
          dataKey="value"
        >
          {dashboardData.categoryDistribution.map((entry: CategoryDistributionData, index: number) => (
            <Cell key={`cell-${index}`} fill={entry.color} />
          ))}
        </Pie>
        <Tooltip 
          contentStyle={{ 
            backgroundColor: 'white', 
            border: '1px solid #e5e7eb', 
            borderRadius: '12px',
            boxShadow: '0 10px 25px rgba(0,0,0,0.1)'
          }} 
        />
      </>
    </ResponsiveContainer>
    <div className="grid grid-cols-2 gap-4 mt-4">
      {dashboardData.categoryDistribution.map((item: CategoryDistributionData, index: number) => (
        <div key={index} className="flex items-center space-x-2">
          <div className={`w-3 h-3 rounded-full`} style={{ backgroundColor: item.color }}></div>
          <span className="text-sm text-gray-600">{item.name}</span>
          <span className="text-sm font-semibold text-gray-900">{item.value}%</span>
        </div>
      ))}
    </div>
  </div>
)

// Recent Activities Component
const RecentActivities: React.FC = () => (
  <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
    <div className="flex items-center justify-between mb-6">
      <div>
        <h3 className="text-xl font-bold text-gray-900">Recent Activities</h3>
        <p className="text-gray-600 text-sm">Latest updates</p>
      </div>
      <button className="text-blue-600 hover:text-blue-700 text-sm font-medium">View All</button>
    </div>
    <div className="space-y-4">
      {dashboardData.recentActivities.map((activity: RecentActivity) => (
        <div key={activity.id} className="flex items-start space-x-4 p-4 rounded-xl hover:bg-gray-50 transition-colors">
          <div className={`p-2 rounded-lg ${
            activity.type === 'application' ? 'bg-blue-100' :
            activity.type === 'startup' ? 'bg-green-100' :
            activity.type === 'news' ? 'bg-purple-100' : 'bg-orange-100'
          }`}>
            {activity.type === 'application' && <ClipboardList className="w-5 h-5 text-blue-600" />}
            {activity.type === 'startup' && <Rocket className="w-5 h-5 text-green-600" />}
            {activity.type === 'news' && <FileText className="w-5 h-5 text-purple-600" />}
            {activity.type === 'committee' && <Users className="w-5 h-5 text-orange-600" />}
          </div>
          <div className="flex-1">
            <p className="text-sm font-medium text-gray-900">{activity.title}</p>
            <p className="text-xs text-gray-500 mt-1">{activity.time}</p>
          </div>
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${
            activity.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
            activity.status === 'success' ? 'bg-green-100 text-green-800' :
            activity.status === 'published' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'
          }`}>
            {activity.status}
          </span>
        </div>
      ))}
    </div>
  </div>
)

// Quick Actions Component
const QuickActions: React.FC = () => {
  interface Action {
    icon: LucideIcon;
    label: string;
    color: string;
  }

  const actions: Action[] = [
    { icon: Plus, label: 'Add News', color: 'bg-blue-500 hover:bg-blue-600' },
    { icon: Bell, label: 'Post Notice', color: 'bg-green-500 hover:bg-green-600' },
    { icon: UserCheck, label: 'Review Applications', color: 'bg-purple-500 hover:bg-purple-600' },
    { icon: Camera, label: 'Upload Gallery', color: 'bg-orange-500 hover:bg-orange-600' },
    { icon: Users, label: 'Manage Committee', color: 'bg-red-500 hover:bg-red-600' },
    { icon: Award, label: 'Graduate Startup', color: 'bg-indigo-500 hover:bg-indigo-600' }
  ]

  return (
    <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
      <div className="mb-6">
        <h3 className="text-xl font-bold text-gray-900">Quick Actions</h3>
        <p className="text-gray-600 text-sm">Common tasks</p>
      </div>
      <div className="grid grid-cols-2 gap-4">
        {actions.map((action: Action, index: number) => (
          <button
            key={index}
            className={`flex items-center space-x-3 p-4 rounded-xl text-white transition-all hover:scale-105 ${action.color}`}
          >
            <action.icon className="w-5 h-5" />
            <span className="text-sm font-medium">{action.label}</span>
          </button>
        ))}
      </div>
    </div>
  )
}

// Main Dashboard Component
const FWUIncubationDashboard: React.FC = () => {

  return (
    <div className="min-h-screen bg-gray-50">
         <UniversityDashboardHeader
        pageTitle="Incubation Center - Official Notices"
        pageSubtitle="Stay updated with the latest announcements, programs, and opportunities from our innovation hub. Access important documents and guidelines for startups and entrepreneurs."
        breadcrumb={["Home", "Incubation Center", "Notices"]}
        showStats={true}
        page="dashboard"

      />
      <div className=" mx-auto px-6 py-8">
        {/* Welcome Section - Enhanced with a mock mission/vision based on university type */}
       

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <StatsCard
            icon={ClipboardList}
            title="Total Applications"
            value={dashboardData.stats.totalApplications}
            change={15}
            color="bg-gradient-to-br from-blue-500 to-blue-600"
            description="Startup applications received"
          />
          <StatsCard
            icon={Rocket}
            title="Active Startups"
            value={dashboardData.stats.activeStartups}
            change={8}
            color="bg-gradient-to-br from-green-500 to-green-600"
            description="Currently incubating"
          />
          <StatsCard
            icon={Users}
            title="Total Applicants"
            value={dashboardData.stats.totalApplicants}
            change={12}
            color="bg-gradient-to-br from-purple-500 to-purple-600"
            description="Entrepreneurs registered"
          />
          <StatsCard
            icon={Award}
            title="Successful Graduates"
            value={dashboardData.stats.successfulGraduates}
            change={25}
            color="bg-gradient-to-br from-orange-500 to-orange-600"
            description="Completed incubation"
          />
        </div>

        {/* Secondary Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <StatsCard
            icon={FileText}
            title="News Articles"
            value={dashboardData.stats.news}
            change={5}
            color="bg-gradient-to-br from-indigo-500 to-indigo-600"
          />
          <StatsCard
            icon={Bell}
            title="Active Notices"
            value={dashboardData.stats.notices}
            change={-2}
            color="bg-gradient-to-br from-red-500 to-red-600"
          />
          <StatsCard
            icon={UserCheck}
            title="Committee Members"
            value={dashboardData.stats.committeeMembers}
            change={0}
            color="bg-gradient-to-br from-teal-500 to-teal-600"
          />
          <StatsCard
            icon={Camera}
            title="Gallery Items"
            value={dashboardData.stats.galleryItems}
            change={18}
            color="bg-gradient-to-br from-pink-500 to-pink-600"
          />
        </div>

        {/* Charts and Activities */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
          <div className="lg:col-span-2">
            <ApplicationsChart />
          </div>
          <div>
            <CategoryChart />
          </div>
        </div>

        {/* Bottom Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <RecentActivities />
          <QuickActions />
        </div>
      </div>
    </div>
  )
}

export default FWUIncubationDashboard