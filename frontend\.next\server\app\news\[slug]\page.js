(()=>{var e={};e.id=120,e.ids=[120],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21183:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>b});var r=s(60687),a=s(95225),n=s(43210),i=s(28559),l=s(37360),o=s(40228),c=s(48730);let d=(0,s(62688).A)("share-2",[["circle",{cx:"18",cy:"5",r:"3",key:"gq8acd"}],["circle",{cx:"6",cy:"12",r:"3",key:"w7nqdw"}],["circle",{cx:"18",cy:"19",r:"3",key:"1xt0gg"}],["line",{x1:"8.59",x2:"15.42",y1:"13.51",y2:"17.49",key:"47mynk"}],["line",{x1:"15.41",x2:"8.59",y1:"6.51",y2:"10.49",key:"1n3mei"}]]);var x=s(30474),u=s(85814),m=s.n(u),p=s(70334);let h=({currentArticleId:e,allNews:t})=>{let[s,a]=(0,n.useState)(!1);(0,n.useEffect)(()=>{a(!0)},[]);let i=t.filter(t=>t.id!==e).slice(0,3);if(0===i.length)return null;let l=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"});return(0,r.jsx)("section",{className:"py-16 bg-gray-50",children:(0,r.jsxs)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8 max-w-5xl",children:[(0,r.jsxs)("div",{className:`mb-10 transition-all duration-700 transform ${s?"opacity-100 translate-y-0":"opacity-0 translate-y-10"}`,children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Related Articles"}),(0,r.jsx)("div",{className:"w-20 h-1 bg-indigo-600 mb-4 rounded-full"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Explore more news and updates from the FWU Incubation Center"})]}),(0,r.jsx)("div",{className:"grid md:grid-cols-3 gap-6",children:i.map((e,t)=>(0,r.jsxs)("div",{className:`bg-white rounded-xl shadow-md overflow-hidden group hover:shadow-lg transition-all duration-500 transform ${s?"opacity-100 translate-y-0":"opacity-0 translate-y-10"}`,style:{transitionDelay:`${(t+1)*100}ms`},children:[(0,r.jsxs)("div",{className:"relative h-48 overflow-hidden",children:[e.imageUrl?(0,r.jsx)(x.default,{src:e.imageUrl,alt:e.title,fill:!0,className:"object-cover transition-transform duration-700 group-hover:scale-105"}):(0,r.jsx)("div",{className:"w-full h-full bg-gradient-to-r from-indigo-500 to-purple-500"}),(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"}),(0,r.jsx)("div",{className:"absolute bottom-0 left-0 right-0 p-4",children:(0,r.jsxs)("div",{className:"flex items-center text-white/90 text-xs",children:[(0,r.jsx)(o.A,{className:"mr-1.5"}),(0,r.jsx)("span",{children:l(e.date)})]})})]}),(0,r.jsxs)("div",{className:"p-5",children:[(0,r.jsx)("h3",{className:"text-lg font-bold text-gray-900 mb-2 line-clamp-2 group-hover:text-indigo-600 transition-colors",children:(0,r.jsx)(m(),{href:`/news/${e.slug}`,className:"hover:underline",children:e.title})}),(0,r.jsx)("p",{className:"text-gray-600 text-sm mb-4 line-clamp-2",children:e.summary}),(0,r.jsxs)(m(),{href:`/news/${e.slug}`,className:"inline-flex items-center text-indigo-600 hover:text-indigo-800 text-sm font-medium transition-colors",children:["Read Article ",(0,r.jsx)(p.A,{className:"ml-1 group-hover:translate-x-1 transition-transform"})]})]})]},e.id))})]})})};function g({article:e}){let[t,s]=(0,n.useState)(!1);if(!e)return(0,r.jsx)("main",{className:"container mx-auto py-16 px-4 text-center",children:(0,r.jsxs)("div",{className:"max-w-lg mx-auto bg-white rounded-xl shadow-lg p-8",children:[(0,r.jsx)("div",{className:"text-indigo-600 mb-4",children:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-16 w-16 mx-auto",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Article Not Found"}),(0,r.jsx)("p",{className:"text-gray-600 mb-8",children:"The news article you are looking for does not exist or has been moved to a different location."}),(0,r.jsxs)(m(),{href:"/news",className:"inline-flex items-center bg-indigo-600 text-white px-6 py-3 rounded-lg hover:bg-indigo-700 transition-colors",children:[(0,r.jsx)(i.A,{className:"mr-2"})," Back to News"]})]})});let u=new Date(e.date).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"}),p=(()=>{let t=[];return t.push(e.summary),t.push("Far Western University Incubation Center is committed to fostering innovation and entrepreneurship among students and faculty. Through our programs, we provide mentorship, resources, and networking opportunities to help turn ideas into successful ventures.","Our approach combines academic research with practical business development, creating a unique environment where theoretical knowledge meets real-world application. This synergy is essential for developing sustainable and impactful startups.","The incubation process at FWU involves multiple stages, from idea validation to market entry. Each startup receives customized support based on their specific needs and industry focus, ensuring they have the best chance of success in today's competitive landscape.","Collaboration is at the heart of our philosophy. We actively partner with industry leaders, government agencies, and other academic institutions to create a robust ecosystem that supports innovation across various sectors."),t})();return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("section",{className:"relative bg-gradient-to-r from-indigo-900 to-blue-900 text-white py-16 md:py-24 overflow-hidden",children:[(0,r.jsxs)("div",{className:"absolute inset-0 overflow-hidden",children:[(0,r.jsx)("div",{className:"absolute top-20 left-10 w-64 h-64 rounded-full bg-indigo-500 opacity-10 animate-float-slow"}),(0,r.jsx)("div",{className:"absolute bottom-20 right-10 w-96 h-96 rounded-full bg-blue-500 opacity-10 animate-float-reverse"}),(0,r.jsx)("div",{className:"absolute inset-0 opacity-10",style:{backgroundImage:"radial-gradient(circle, rgba(255,255,255,0.1) 1px, transparent 1px)",backgroundSize:"30px 30px"}})]}),(0,r.jsx)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8 relative z-10",children:(0,r.jsxs)("div",{className:`max-w-4xl mx-auto transition-all duration-1000 transform ${t?"opacity-100 translate-y-0":"opacity-0 translate-y-10"}`,children:[(0,r.jsxs)(m(),{href:"/news",className:"inline-flex items-center text-indigo-200 hover:text-white mb-6 transition-colors",children:[(0,r.jsx)(i.A,{className:"mr-2"})," Back to News"]}),(0,r.jsx)("div",{className:"mb-4",children:(0,r.jsxs)("span",{className:`text-sm font-semibold px-3 py-1 rounded-full border inline-block ${"News"===e.category?"bg-blue-500/20 text-blue-100 border-blue-400/30":"Notice"===e.category?"bg-yellow-500/20 text-yellow-100 border-yellow-400/30":"Event Recap"===e.category?"bg-green-500/20 text-green-100 border-green-400/30":"bg-purple-500/20 text-purple-100 border-purple-400/30"}`,children:[(0,r.jsx)(l.A,{className:"inline mr-1.5 mb-0.5"})," ",e.category]})}),(0,r.jsx)("h1",{className:"text-3xl md:text-5xl font-extrabold mb-6 leading-tight",children:e.title}),(0,r.jsxs)("div",{className:"flex flex-wrap items-center gap-x-6 gap-y-2 text-indigo-100",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(o.A,{className:"mr-2"}),(0,r.jsxs)("span",{children:["Published on ",u]})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(c.A,{className:"mr-2"}),(0,r.jsx)("span",{children:"5 min read"})]})]})]})}),(0,r.jsx)("div",{className:"absolute bottom-0 left-0 right-0",children:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1440 120",className:"w-full h-auto",children:(0,r.jsx)("path",{fill:"#ffffff",fillOpacity:"1",d:"M0,64L80,69.3C160,75,320,85,480,80C640,75,800,53,960,48C1120,43,1280,53,1360,58.7L1440,64L1440,120L1360,120C1280,120,1120,120,960,120C800,120,640,120,480,120C320,120,160,120,80,120L0,120Z"})})})]}),(0,r.jsx)("main",{className:"bg-white py-12 md:py-20",children:(0,r.jsx)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsx)("div",{className:"max-w-4xl mx-auto",children:(0,r.jsxs)("article",{className:`transition-all duration-1000 delay-300 transform ${t?"opacity-100 translate-y-0":"opacity-0 translate-y-10"}`,children:[e.imageUrl&&(0,r.jsx)("div",{className:"relative w-full h-72 md:h-[500px] rounded-xl overflow-hidden shadow-xl mb-10",children:(0,r.jsx)(x.default,{src:e.imageUrl,alt:e.title,fill:!0,className:"object-cover",priority:!0})}),(0,r.jsx)("div",{className:"flex justify-end mb-8",children:(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("span",{className:"text-gray-500 text-sm",children:"Share:"}),(0,r.jsx)("button",{className:"p-2 bg-blue-500 text-white rounded-full hover:bg-blue-600 transition-colors",children:(0,r.jsx)("svg",{className:"w-4 h-4",fill:"currentColor",viewBox:"0 0 24 24","aria-hidden":"true",children:(0,r.jsx)("path",{d:"M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84"})})}),(0,r.jsx)("button",{className:"p-2 bg-blue-700 text-white rounded-full hover:bg-blue-800 transition-colors",children:(0,r.jsx)("svg",{className:"w-4 h-4",fill:"currentColor",viewBox:"0 0 24 24","aria-hidden":"true",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z",clipRule:"evenodd"})})}),(0,r.jsx)("button",{className:"p-2 bg-indigo-600 text-white rounded-full hover:bg-indigo-700 transition-colors",children:(0,r.jsx)(d,{className:"w-4 h-4"})})]})}),(0,r.jsxs)("div",{className:"prose prose-lg max-w-none text-gray-700 leading-relaxed",children:[p.map((e,s)=>(0,r.jsx)("p",{className:`mb-6 transition-all duration-1000 transform ${t?"opacity-100 translate-y-0":"opacity-0 translate-y-10"}`,style:{transitionDelay:`${(s+4)*100}ms`},children:e},s)),(0,r.jsx)("blockquote",{className:"border-l-4 border-indigo-500 pl-4 italic my-8 text-gray-600",children:"“Innovation is the ability to see change as an opportunity - not a threat. The Far Western University Incubation Center is committed to nurturing this mindset among our students and faculty.”"}),(0,r.jsx)("h3",{className:"text-xl font-bold text-gray-900 mt-8 mb-4",children:"Key Benefits of the FWU Incubation Center:"}),(0,r.jsxs)("ul",{className:"list-disc pl-6 mb-8",children:[(0,r.jsx)("li",{children:"Access to mentorship from industry experts and successful entrepreneurs"}),(0,r.jsx)("li",{children:"Dedicated workspace and resources for startup development"}),(0,r.jsx)("li",{children:"Networking opportunities with potential investors and partners"}),(0,r.jsx)("li",{children:"Workshops and training sessions on business development"}),(0,r.jsx)("li",{children:"Potential funding opportunities through our partner network"})]})]}),(0,r.jsx)("div",{className:"mt-12 pt-8 border-t border-gray-200",children:(0,r.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,r.jsx)("span",{className:"text-gray-700 font-medium",children:"Tags:"}),(0,r.jsx)("span",{className:"px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm hover:bg-gray-200 transition-colors cursor-pointer",children:"Incubation"}),(0,r.jsx)("span",{className:"px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm hover:bg-gray-200 transition-colors cursor-pointer",children:"Entrepreneurship"}),(0,r.jsx)("span",{className:"px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm hover:bg-gray-200 transition-colors cursor-pointer",children:"Innovation"}),(0,r.jsx)("span",{className:"px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm hover:bg-gray-200 transition-colors cursor-pointer",children:"FWU"})]})}),(0,r.jsx)("div",{className:"mt-12 p-6 bg-indigo-50 rounded-xl",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0 mr-4",children:(0,r.jsx)("div",{className:"w-12 h-12 rounded-full bg-indigo-600 flex items-center justify-center text-white text-xl font-bold",children:"FW"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"text-lg font-bold text-gray-900",children:"FWU Incubation Center"}),(0,r.jsx)("p",{className:"text-gray-600",children:"The official news and updates from the Far Western University Incubation Center."})]})]})})]})})})}),(0,r.jsx)(h,{currentArticleId:e.id,allNews:a.allNewsData}),(0,r.jsx)("section",{className:"py-16 bg-gradient-to-r from-indigo-900 to-blue-900 text-white",children:(0,r.jsx)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"max-w-3xl mx-auto text-center",children:[(0,r.jsx)("h2",{className:"text-3xl font-bold mb-6",children:"Stay Updated with FWU Incubation Center"}),(0,r.jsx)("p",{className:"text-xl text-indigo-100 mb-8",children:"Subscribe to our newsletter to receive the latest news, events, and opportunities directly in your inbox."}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,r.jsx)("input",{type:"email",placeholder:"Your email address",className:"px-6 py-3 rounded-lg bg-white/10 backdrop-blur-sm border border-white/20 text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-white/50 w-full sm:w-auto"}),(0,r.jsx)("button",{className:"px-6 py-3 bg-white text-indigo-900 font-bold rounded-lg hover:bg-indigo-100 transition-colors",children:"Subscribe"})]})]})})})]})}function b({params:e}){let t=a.allNewsData.find(t=>t.slug===e.slug)||null;return(0,r.jsx)(g,{article:t})}},28559:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},36791:(e,t,s)=>{Promise.resolve().then(s.bind(s,96286))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},71632:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>x,pages:()=>d,routeModule:()=>u,tree:()=>c});var r=s(65239),a=s(48088),n=s(88170),i=s.n(n),l=s(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(t,o);let c={children:["",{children:["news",{children:["[slug]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,96286)),"D:\\fwu_incubation(react+laravel)\\incubation\\frontend\\src\\app\\news\\[slug]\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,53862)),"D:\\fwu_incubation(react+laravel)\\incubation\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["D:\\fwu_incubation(react+laravel)\\incubation\\frontend\\src\\app\\news\\[slug]\\page.tsx"],x={require:s,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/news/[slug]/page",pathname:"/news/[slug]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},73583:(e,t,s)=>{Promise.resolve().then(s.bind(s,21183))},79551:e=>{"use strict";e.exports=require("url")},96286:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\fwu_incubation(react+laravel)\\\\incubation\\\\frontend\\\\src\\\\app\\\\news\\\\[slug]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\fwu_incubation(react+laravel)\\incubation\\frontend\\src\\app\\news\\[slug]\\page.tsx","default")}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[447,172,658,54,832],()=>s(71632));module.exports=r})();