"use client"
import React, { useState } from 'react'
import {
  Tag,
  Users,
  FileText,
  TrendingUp // Added TrendingUp as it's used in StatsCard
} from 'lucide-react'
import NewsTable from './_components/NewsTable'
import Controls from './_components/Controls'
import Pagination from './_components/PaginationProps'
import { useQuery } from '@tanstack/react-query'
import axios from 'axios'
import LoadingDemo from '../_components/LoadingComponents'
import UniversityDashboardHeader from '../_components/UniversityDashboardHeader'

// --- Type Definitions ---

interface Admin {
  id: number
  name: string
  email: string
  profile_image: string
  role: string,
  email_verified: number
  created_at: string
  updated_at: string
}

export interface NewsItem{
  news_id: number
  title: string
  description: string
  news_photo?: string|null|File,
  category: string
  added_by: number
  created_at: string
  updated_at: string
  admin: Admin
}

 export interface MockNewsDataType {
  current_page: number
  data: NewsItem[]
  total: number
  per_page: number
  last_page: number
}

// --- Components ---

interface StatsCardProps {
  icon: React.ElementType // Type for Lucide icon components
  title: string
  value: string | number
  change?: number
  color: string
}

// Stats Card Component
const StatsCard: React.FC<StatsCardProps> = ({ icon: Icon, title, value, change, color }) => (
  <div className="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 group">
    <div className="flex items-center justify-between">
      <div>
        <p className="text-gray-600 text-sm font-medium">{title}</p>
        <h3 className="text-2xl font-bold text-gray-900 mt-1">{value}</h3>
        {change !== undefined && ( // Check if change is provided
          <p className={`text-sm mt-2 flex items-center ${change > 0 ? 'text-green-600' : 'text-red-600'}`}>
            <TrendingUp className="w-4 h-4 mr-1" />
            {change > 0 ? '+' : ''}{change}% from last month
          </p>
        )}
      </div>
      <div className={`p-3 rounded-xl ${color} group-hover:scale-110 transition-transform`}>
        <Icon className="w-6 h-6 text-white" />
      </div>
    </div>
  </div>
)

// Main Dashboard Component
const AdminNewsDashboard: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState<string>('')
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [currentPage, setCurrentPage] = useState<number>(1)
// When refetchInterval changes to true, data will be refetched every 600ms
// When false, automatic refetching is disabled
  const { data: queryNews ,isLoading ,error } = useQuery({
  queryKey: ['news'],
  queryFn: async () => {
    const res = await axios.get('http://localhost:8000/api/news');
    return res.data as MockNewsDataType;
  },
  select: (data) => {
    return data; // Make sure to return data if you're selecting
  },
  // (Optional) You can still use this to control refetch on focus
  refetchOnWindowFocus: false,
});

if(isLoading){
  return <LoadingDemo type="spinner" />
}

if(error){
  return <LoadingDemo type="error"/>
}



const mockNewsData: MockNewsDataType|undefined = queryNews;
  // Filter and search logic
 const filteredNews= mockNewsData?.data.filter((item: NewsItem) => {
      const matchesSearch = item.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.description.toLowerCase().includes(searchTerm.toLowerCase())
      const matchesCategory = selectedCategory === 'all' || item.category === selectedCategory
      return matchesSearch && matchesCategory
    })
  

const categories: string[] = [
  'event',
  'announcement',
  'research',
  'startup',
  'seminar',
  'funding',
  'achievement',
  'notice',
  'workshop',
];


  return (
    <div className="min-h-screen bg-gray-50">
       <UniversityDashboardHeader
        pageTitle="Incubation Center - Official News"
        pageSubtitle=" Stay updated with the latest announcements, programs, and opportunities from our innovation hub. Access important documents and guidelines for startups and entrepreneurs."
        breadcrumb={["Home", "Incubation Center", "News"]}
        showStats={true}
      />
      <div className="max-w-7xl mx-auto px-6 py-8">

        {/* Stats Cards */}

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <StatsCard
            icon={FileText}
            title="Total News"
            value={mockNewsData?.total ?? 0}
            change={12}
            color="bg-gradient-to-br from-blue-500 to-blue-600"
          />
          <StatsCard
            icon={Users}
            title="Active Authors"
            value="3"
            change={5}
            color="bg-gradient-to-br from-green-500 to-green-600"
          />
          <StatsCard
            icon={Tag}
            title="Categories"
            value="4"
            change={0}
            color="bg-gradient-to-br from-purple-500 to-purple-600"
          />
          <StatsCard
            icon={TrendingUp}
            title="This Month"
            value="8"
            change={25}
            color="bg-gradient-to-br from-orange-500 to-orange-600"
          />
        </div>


        {/* Controls */}
         <Controls  searchTerm={searchTerm} setSearchTerm={setSearchTerm} selectedCategory={selectedCategory} setSelectedCategory={setSelectedCategory} categories={categories} />
        {/* News Table */}
        <div className="mb-8">
          <NewsTable
            news={filteredNews ?? []}
          />
        </div>

        {/* Pagination */}
        <Pagination
          mockNewsData={mockNewsData ?? { current_page: 1, data: [], total: 0, per_page: 10, last_page: 1 }}
          currentPage={currentPage}
          totalPages={mockNewsData?.last_page ?? 1}
          onPageChange={setCurrentPage}
        />
        
      </div>
    </div>
  )
}

export default AdminNewsDashboard