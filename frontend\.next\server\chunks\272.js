"use strict";exports.id=272,exports.ids=[272],exports.modules={1359:(e,t,r)=>{r.d(t,{Oh:()=>i});var n=r(43210),o=0;function i(){n.useEffect(()=>{let e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??a()),document.body.insertAdjacentElement("beforeend",e[1]??a()),o++,()=>{1===o&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),o--}},[])}function a(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},8693:(e,t,r)=>{r.d(t,{Ht:()=>s,jE:()=>a});var n=r(43210),o=r(60687),i=n.createContext(void 0),a=e=>{let t=n.useContext(i);if(e)return e;if(!t)throw Error("No QueryClient set, use QueryClientProvider to set one");return t},s=({client:e,children:t})=>(n.useEffect(()=>(e.mount(),()=>{e.unmount()}),[e]),(0,o.jsx)(i.Provider,{value:e,children:t}))},8730:(e,t,r)=>{r.d(t,{DX:()=>s,Dc:()=>u,TL:()=>a});var n=r(43210),o=r(98599),i=r(60687);function a(e){let t=function(e){let t=n.forwardRef((e,t)=>{let{children:r,...i}=e;if(n.isValidElement(r)){var a;let e,s,l=(a=r,(s=(e=Object.getOwnPropertyDescriptor(a.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.ref:(s=(e=Object.getOwnPropertyDescriptor(a,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.props.ref:a.props.ref||a.ref),u=function(e,t){let r={...t};for(let n in t){let o=e[n],i=t[n];/^on[A-Z]/.test(n)?o&&i?r[n]=(...e)=>{let t=i(...e);return o(...e),t}:o&&(r[n]=o):"style"===n?r[n]={...o,...i}:"className"===n&&(r[n]=[o,i].filter(Boolean).join(" "))}return{...e,...r}}(i,r.props);return r.type!==n.Fragment&&(u.ref=t?(0,o.t)(t,l):l),n.cloneElement(r,u)}return n.Children.count(r)>1?n.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=n.forwardRef((e,r)=>{let{children:o,...a}=e,s=n.Children.toArray(o),l=s.find(c);if(l){let e=l.props.children,o=s.map(t=>t!==l?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,i.jsx)(t,{...a,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,o):null})}return(0,i.jsx)(t,{...a,ref:r,children:o})});return r.displayName=`${e}.Slot`,r}var s=a("Slot"),l=Symbol("radix.slottable");function u(e){let t=({children:e})=>(0,i.jsx)(i.Fragment,{children:e});return t.displayName=`${e}.Slottable`,t.__radixId=l,t}function c(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===l}},9005:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]])},10022:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},11273:(e,t,r)=>{r.d(t,{A:()=>a,q:()=>i});var n=r(43210),o=r(60687);function i(e,t){let r=n.createContext(t),i=e=>{let{children:t,...i}=e,a=n.useMemo(()=>i,Object.values(i));return(0,o.jsx)(r.Provider,{value:a,children:t})};return i.displayName=e+"Provider",[i,function(o){let i=n.useContext(r);if(i)return i;if(void 0!==t)return t;throw Error(`\`${o}\` must be used within \`${e}\``)}]}function a(e,t=[]){let r=[],i=()=>{let t=r.map(e=>n.createContext(e));return function(r){let o=r?.[e]||t;return n.useMemo(()=>({[`__scope${e}`]:{...r,[e]:o}}),[r,o])}};return i.scopeName=e,[function(t,i){let a=n.createContext(i),s=r.length;r=[...r,i];let l=t=>{let{scope:r,children:i,...l}=t,u=r?.[e]?.[s]||a,c=n.useMemo(()=>l,Object.values(l));return(0,o.jsx)(u.Provider,{value:c,children:i})};return l.displayName=t+"Provider",[l,function(r,o){let l=o?.[e]?.[s]||a,u=n.useContext(l);if(u)return u;if(void 0!==i)return i;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=r.reduce((t,{useScope:r,scopeName:n})=>{let o=r(e)[`__scope${n}`];return{...t,...o}},{});return n.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return r.scopeName=t.scopeName,r}(i,...t)]}},13495:(e,t,r)=>{r.d(t,{c:()=>o});var n=r(43210);function o(e){let t=n.useRef(e);return n.useEffect(()=>{t.current=e}),n.useMemo(()=>(...e)=>t.current?.(...e),[])}},14163:(e,t,r)=>{r.d(t,{hO:()=>l,sG:()=>s});var n=r(43210),o=r(51215),i=r(8730),a=r(60687),s=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,i.TL)(`Primitive.${t}`),o=n.forwardRef((e,n)=>{let{asChild:o,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,a.jsx)(o?r:t,{...i,ref:n})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{});function l(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},20158:(e,t,r)=>{r.d(t,{i3:()=>Z,UC:()=>V,ZL:()=>H,Kq:()=>B,bL:()=>U,l9:()=>$});var n=r(43210),o=r(70569),i=r(98599),a=r(11273),s=r(31355),l=r(96963),u=r(38674),c=r(25028),d=r(46059),f=r(14163),p=r(8730),h=r(65551),m=r(60687),g=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),y=n.forwardRef((e,t)=>(0,m.jsx)(f.sG.span,{...e,ref:t,style:{...g,...e.style}}));y.displayName="VisuallyHidden";var[v,b]=(0,a.A)("Tooltip",[u.Bk]),w=(0,u.Bk)(),x="TooltipProvider",k="tooltip.open",[C,E]=v(x),R=e=>{let{__scopeTooltip:t,delayDuration:r=700,skipDelayDuration:o=300,disableHoverableContent:i=!1,children:a}=e,s=n.useRef(!0),l=n.useRef(!1),u=n.useRef(0);return n.useEffect(()=>{let e=u.current;return()=>window.clearTimeout(e)},[]),(0,m.jsx)(C,{scope:t,isOpenDelayedRef:s,delayDuration:r,onOpen:n.useCallback(()=>{window.clearTimeout(u.current),s.current=!1},[]),onClose:n.useCallback(()=>{window.clearTimeout(u.current),u.current=window.setTimeout(()=>s.current=!0,o)},[o]),isPointerInTransitRef:l,onPointerInTransitChange:n.useCallback(e=>{l.current=e},[]),disableHoverableContent:i,children:a})};R.displayName=x;var O="Tooltip",[M,S]=v(O),j=e=>{let{__scopeTooltip:t,children:r,open:o,defaultOpen:i,onOpenChange:a,disableHoverableContent:s,delayDuration:c}=e,d=E(O,e.__scopeTooltip),f=w(t),[p,g]=n.useState(null),y=(0,l.B)(),v=n.useRef(0),b=s??d.disableHoverableContent,x=c??d.delayDuration,C=n.useRef(!1),[R,S]=(0,h.i)({prop:o,defaultProp:i??!1,onChange:e=>{e?(d.onOpen(),document.dispatchEvent(new CustomEvent(k))):d.onClose(),a?.(e)},caller:O}),j=n.useMemo(()=>R?C.current?"delayed-open":"instant-open":"closed",[R]),A=n.useCallback(()=>{window.clearTimeout(v.current),v.current=0,C.current=!1,S(!0)},[S]),P=n.useCallback(()=>{window.clearTimeout(v.current),v.current=0,S(!1)},[S]),T=n.useCallback(()=>{window.clearTimeout(v.current),v.current=window.setTimeout(()=>{C.current=!0,S(!0),v.current=0},x)},[x,S]);return n.useEffect(()=>()=>{v.current&&(window.clearTimeout(v.current),v.current=0)},[]),(0,m.jsx)(u.bL,{...f,children:(0,m.jsx)(M,{scope:t,contentId:y,open:R,stateAttribute:j,trigger:p,onTriggerChange:g,onTriggerEnter:n.useCallback(()=>{d.isOpenDelayedRef.current?T():A()},[d.isOpenDelayedRef,T,A]),onTriggerLeave:n.useCallback(()=>{b?P():(window.clearTimeout(v.current),v.current=0)},[P,b]),onOpen:A,onClose:P,disableHoverableContent:b,children:r})})};j.displayName=O;var A="TooltipTrigger",P=n.forwardRef((e,t)=>{let{__scopeTooltip:r,...a}=e,s=S(A,r),l=E(A,r),c=w(r),d=n.useRef(null),p=(0,i.s)(t,d,s.onTriggerChange),h=n.useRef(!1),g=n.useRef(!1),y=n.useCallback(()=>h.current=!1,[]);return n.useEffect(()=>()=>document.removeEventListener("pointerup",y),[y]),(0,m.jsx)(u.Mz,{asChild:!0,...c,children:(0,m.jsx)(f.sG.button,{"aria-describedby":s.open?s.contentId:void 0,"data-state":s.stateAttribute,...a,ref:p,onPointerMove:(0,o.m)(e.onPointerMove,e=>{"touch"!==e.pointerType&&(g.current||l.isPointerInTransitRef.current||(s.onTriggerEnter(),g.current=!0))}),onPointerLeave:(0,o.m)(e.onPointerLeave,()=>{s.onTriggerLeave(),g.current=!1}),onPointerDown:(0,o.m)(e.onPointerDown,()=>{s.open&&s.onClose(),h.current=!0,document.addEventListener("pointerup",y,{once:!0})}),onFocus:(0,o.m)(e.onFocus,()=>{h.current||s.onOpen()}),onBlur:(0,o.m)(e.onBlur,s.onClose),onClick:(0,o.m)(e.onClick,s.onClose)})})});P.displayName=A;var T="TooltipPortal",[D,N]=v(T,{forceMount:void 0}),L=e=>{let{__scopeTooltip:t,forceMount:r,children:n,container:o}=e,i=S(T,t);return(0,m.jsx)(D,{scope:t,forceMount:r,children:(0,m.jsx)(d.C,{present:r||i.open,children:(0,m.jsx)(c.Z,{asChild:!0,container:o,children:n})})})};L.displayName=T;var F="TooltipContent",I=n.forwardRef((e,t)=>{let r=N(F,e.__scopeTooltip),{forceMount:n=r.forceMount,side:o="top",...i}=e,a=S(F,e.__scopeTooltip);return(0,m.jsx)(d.C,{present:n||a.open,children:a.disableHoverableContent?(0,m.jsx)(K,{side:o,...i,ref:t}):(0,m.jsx)(_,{side:o,...i,ref:t})})}),_=n.forwardRef((e,t)=>{let r=S(F,e.__scopeTooltip),o=E(F,e.__scopeTooltip),a=n.useRef(null),s=(0,i.s)(t,a),[l,u]=n.useState(null),{trigger:c,onClose:d}=r,f=a.current,{onPointerInTransitChange:p}=o,h=n.useCallback(()=>{u(null),p(!1)},[p]),g=n.useCallback((e,t)=>{let r=e.currentTarget,n={x:e.clientX,y:e.clientY},o=function(e,t){let r=Math.abs(t.top-e.y),n=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),i=Math.abs(t.left-e.x);switch(Math.min(r,n,o,i)){case i:return"left";case o:return"right";case r:return"top";case n:return"bottom";default:throw Error("unreachable")}}(n,r.getBoundingClientRect());u(function(e){let t=e.slice();return t.sort((e,t)=>e.x<t.x?-1:e.x>t.x?1:e.y<t.y?-1:1*!!(e.y>t.y)),function(e){if(e.length<=1)return e.slice();let t=[];for(let r=0;r<e.length;r++){let n=e[r];for(;t.length>=2;){let e=t[t.length-1],r=t[t.length-2];if((e.x-r.x)*(n.y-r.y)>=(e.y-r.y)*(n.x-r.x))t.pop();else break}t.push(n)}t.pop();let r=[];for(let t=e.length-1;t>=0;t--){let n=e[t];for(;r.length>=2;){let e=r[r.length-1],t=r[r.length-2];if((e.x-t.x)*(n.y-t.y)>=(e.y-t.y)*(n.x-t.x))r.pop();else break}r.push(n)}return(r.pop(),1===t.length&&1===r.length&&t[0].x===r[0].x&&t[0].y===r[0].y)?t:t.concat(r)}(t)}([...function(e,t,r=5){let n=[];switch(t){case"top":n.push({x:e.x-r,y:e.y+r},{x:e.x+r,y:e.y+r});break;case"bottom":n.push({x:e.x-r,y:e.y-r},{x:e.x+r,y:e.y-r});break;case"left":n.push({x:e.x+r,y:e.y-r},{x:e.x+r,y:e.y+r});break;case"right":n.push({x:e.x-r,y:e.y-r},{x:e.x-r,y:e.y+r})}return n}(n,o),...function(e){let{top:t,right:r,bottom:n,left:o}=e;return[{x:o,y:t},{x:r,y:t},{x:r,y:n},{x:o,y:n}]}(t.getBoundingClientRect())])),p(!0)},[p]);return n.useEffect(()=>()=>h(),[h]),n.useEffect(()=>{if(c&&f){let e=e=>g(e,f),t=e=>g(e,c);return c.addEventListener("pointerleave",e),f.addEventListener("pointerleave",t),()=>{c.removeEventListener("pointerleave",e),f.removeEventListener("pointerleave",t)}}},[c,f,g,h]),n.useEffect(()=>{if(l){let e=e=>{let t=e.target,r={x:e.clientX,y:e.clientY},n=c?.contains(t)||f?.contains(t),o=!function(e,t){let{x:r,y:n}=e,o=!1;for(let e=0,i=t.length-1;e<t.length;i=e++){let a=t[e],s=t[i],l=a.x,u=a.y,c=s.x,d=s.y;u>n!=d>n&&r<(c-l)*(n-u)/(d-u)+l&&(o=!o)}return o}(r,l);n?h():o&&(h(),d())};return document.addEventListener("pointermove",e),()=>document.removeEventListener("pointermove",e)}},[c,f,l,d,h]),(0,m.jsx)(K,{...e,ref:s})}),[q,z]=v(O,{isInside:!1}),G=(0,p.Dc)("TooltipContent"),K=n.forwardRef((e,t)=>{let{__scopeTooltip:r,children:o,"aria-label":i,onEscapeKeyDown:a,onPointerDownOutside:l,...c}=e,d=S(F,r),f=w(r),{onClose:p}=d;return n.useEffect(()=>(document.addEventListener(k,p),()=>document.removeEventListener(k,p)),[p]),n.useEffect(()=>{if(d.trigger){let e=e=>{let t=e.target;t?.contains(d.trigger)&&p()};return window.addEventListener("scroll",e,{capture:!0}),()=>window.removeEventListener("scroll",e,{capture:!0})}},[d.trigger,p]),(0,m.jsx)(s.qW,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:a,onPointerDownOutside:l,onFocusOutside:e=>e.preventDefault(),onDismiss:p,children:(0,m.jsxs)(u.UC,{"data-state":d.stateAttribute,...f,...c,ref:t,style:{...c.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[(0,m.jsx)(G,{children:o}),(0,m.jsx)(q,{scope:r,isInside:!0,children:(0,m.jsx)(y,{id:d.contentId,role:"tooltip",children:i||o})})]})})});I.displayName=F;var W="TooltipArrow",Q=n.forwardRef((e,t)=>{let{__scopeTooltip:r,...n}=e,o=w(r);return z(W,r).isInside?null:(0,m.jsx)(u.i3,{...o,...n,ref:t})});Q.displayName=W;var B=R,U=j,$=P,H=L,V=I,Z=Q},22115:(e,t,r)=>{r.d(t,{t:()=>i});var n=r(35536),o=r(31212),i=new class extends n.Q{#e=!0;#t;#r;constructor(){super(),this.#r=e=>{if(!o.S$&&window.addEventListener){let t=()=>e(!0),r=()=>e(!1);return window.addEventListener("online",t,!1),window.addEventListener("offline",r,!1),()=>{window.removeEventListener("online",t),window.removeEventListener("offline",r)}}}}onSubscribe(){this.#t||this.setEventListener(this.#r)}onUnsubscribe(){this.hasListeners()||(this.#t?.(),this.#t=void 0)}setEventListener(e){this.#r=e,this.#t?.(),this.#t=e(this.setOnline.bind(this))}setOnline(e){this.#e!==e&&(this.#e=e,this.listeners.forEach(t=>{t(e)}))}isOnline(){return this.#e}}},24224:(e,t,r)=>{r.d(t,{F:()=>a});var n=r(49384);let o=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,i=n.$,a=(e,t)=>r=>{var n;if((null==t?void 0:t.variants)==null)return i(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:a,defaultVariants:s}=t,l=Object.keys(a).map(e=>{let t=null==r?void 0:r[e],n=null==s?void 0:s[e];if(null===t)return null;let i=o(t)||o(n);return a[e][i]}),u=r&&Object.entries(r).reduce((e,t)=>{let[r,n]=t;return void 0===n||(e[r]=n),e},{});return i(e,l,null==t||null==(n=t.compoundVariants)?void 0:n.reduce((e,t)=>{let{class:r,className:n,...o}=t;return Object.entries(o).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...s,...u}[t]):({...s,...u})[t]===r})?[...e,r,n]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},25028:(e,t,r)=>{r.d(t,{Z:()=>l});var n=r(43210),o=r(51215),i=r(14163),a=r(66156),s=r(60687),l=n.forwardRef((e,t)=>{let{container:r,...l}=e,[u,c]=n.useState(!1);(0,a.N)(()=>c(!0),[]);let d=r||u&&globalThis?.document?.body;return d?o.createPortal((0,s.jsx)(i.sG.div,{...l,ref:t}),d):null});l.displayName="Portal"},25217:(e,t,r)=>{r.d(t,{E:()=>y});var n=r(31212),o=r(61489),i=r(33465),a=r(35536),s=class extends a.Q{constructor(e={}){super(),this.config=e,this.#n=new Map}#n;build(e,t,r){let i=t.queryKey,a=t.queryHash??(0,n.F$)(i,t),s=this.get(a);return s||(s=new o.X({client:e,queryKey:i,queryHash:a,options:e.defaultQueryOptions(t),state:r,defaultOptions:e.getQueryDefaults(i)}),this.add(s)),s}add(e){this.#n.has(e.queryHash)||(this.#n.set(e.queryHash,e),this.notify({type:"added",query:e}))}remove(e){let t=this.#n.get(e.queryHash);t&&(e.destroy(),t===e&&this.#n.delete(e.queryHash),this.notify({type:"removed",query:e}))}clear(){i.jG.batch(()=>{this.getAll().forEach(e=>{this.remove(e)})})}get(e){return this.#n.get(e)}getAll(){return[...this.#n.values()]}find(e){let t={exact:!0,...e};return this.getAll().find(e=>(0,n.MK)(t,e))}findAll(e={}){let t=this.getAll();return Object.keys(e).length>0?t.filter(t=>(0,n.MK)(e,t)):t}notify(e){i.jG.batch(()=>{this.listeners.forEach(t=>{t(e)})})}onFocus(){i.jG.batch(()=>{this.getAll().forEach(e=>{e.onFocus()})})}onOnline(){i.jG.batch(()=>{this.getAll().forEach(e=>{e.onOnline()})})}},l=r(62536),u=r(29604),c=class extends l.k{#o;#i;#a;constructor(e){super(),this.mutationId=e.mutationId,this.#i=e.mutationCache,this.#o=[],this.state=e.state||{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0},this.setOptions(e.options),this.scheduleGc()}setOptions(e){this.options=e,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(e){this.#o.includes(e)||(this.#o.push(e),this.clearGcTimeout(),this.#i.notify({type:"observerAdded",mutation:this,observer:e}))}removeObserver(e){this.#o=this.#o.filter(t=>t!==e),this.scheduleGc(),this.#i.notify({type:"observerRemoved",mutation:this,observer:e})}optionalRemove(){this.#o.length||("pending"===this.state.status?this.scheduleGc():this.#i.remove(this))}continue(){return this.#a?.continue()??this.execute(this.state.variables)}async execute(e){let t=()=>{this.#s({type:"continue"})};this.#a=(0,u.II)({fn:()=>this.options.mutationFn?this.options.mutationFn(e):Promise.reject(Error("No mutationFn found")),onFail:(e,t)=>{this.#s({type:"failed",failureCount:e,error:t})},onPause:()=>{this.#s({type:"pause"})},onContinue:t,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>this.#i.canRun(this)});let r="pending"===this.state.status,n=!this.#a.canStart();try{if(r)t();else{this.#s({type:"pending",variables:e,isPaused:n}),await this.#i.config.onMutate?.(e,this);let t=await this.options.onMutate?.(e);t!==this.state.context&&this.#s({type:"pending",context:t,variables:e,isPaused:n})}let o=await this.#a.start();return await this.#i.config.onSuccess?.(o,e,this.state.context,this),await this.options.onSuccess?.(o,e,this.state.context),await this.#i.config.onSettled?.(o,null,this.state.variables,this.state.context,this),await this.options.onSettled?.(o,null,e,this.state.context),this.#s({type:"success",data:o}),o}catch(t){try{throw await this.#i.config.onError?.(t,e,this.state.context,this),await this.options.onError?.(t,e,this.state.context),await this.#i.config.onSettled?.(void 0,t,this.state.variables,this.state.context,this),await this.options.onSettled?.(void 0,t,e,this.state.context),t}finally{this.#s({type:"error",error:t})}}finally{this.#i.runNext(this)}}#s(e){this.state=(t=>{switch(e.type){case"failed":return{...t,failureCount:e.failureCount,failureReason:e.error};case"pause":return{...t,isPaused:!0};case"continue":return{...t,isPaused:!1};case"pending":return{...t,context:e.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:e.isPaused,status:"pending",variables:e.variables,submittedAt:Date.now()};case"success":return{...t,data:e.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...t,data:void 0,error:e.error,failureCount:t.failureCount+1,failureReason:e.error,isPaused:!1,status:"error"}}})(this.state),i.jG.batch(()=>{this.#o.forEach(t=>{t.onMutationUpdate(e)}),this.#i.notify({mutation:this,type:"updated",action:e})})}},d=class extends a.Q{constructor(e={}){super(),this.config=e,this.#l=new Set,this.#u=new Map,this.#c=0}#l;#u;#c;build(e,t,r){let n=new c({mutationCache:this,mutationId:++this.#c,options:e.defaultMutationOptions(t),state:r});return this.add(n),n}add(e){this.#l.add(e);let t=f(e);if("string"==typeof t){let r=this.#u.get(t);r?r.push(e):this.#u.set(t,[e])}this.notify({type:"added",mutation:e})}remove(e){if(this.#l.delete(e)){let t=f(e);if("string"==typeof t){let r=this.#u.get(t);if(r)if(r.length>1){let t=r.indexOf(e);-1!==t&&r.splice(t,1)}else r[0]===e&&this.#u.delete(t)}}this.notify({type:"removed",mutation:e})}canRun(e){let t=f(e);if("string"!=typeof t)return!0;{let r=this.#u.get(t),n=r?.find(e=>"pending"===e.state.status);return!n||n===e}}runNext(e){let t=f(e);if("string"!=typeof t)return Promise.resolve();{let r=this.#u.get(t)?.find(t=>t!==e&&t.state.isPaused);return r?.continue()??Promise.resolve()}}clear(){i.jG.batch(()=>{this.#l.forEach(e=>{this.notify({type:"removed",mutation:e})}),this.#l.clear(),this.#u.clear()})}getAll(){return Array.from(this.#l)}find(e){let t={exact:!0,...e};return this.getAll().find(e=>(0,n.nJ)(t,e))}findAll(e={}){return this.getAll().filter(t=>(0,n.nJ)(e,t))}notify(e){i.jG.batch(()=>{this.listeners.forEach(t=>{t(e)})})}resumePausedMutations(){let e=this.getAll().filter(e=>e.state.isPaused);return i.jG.batch(()=>Promise.all(e.map(e=>e.continue().catch(n.lQ))))}};function f(e){return e.options.scope?.id}var p=r(39850),h=r(22115);function m(e){return{onFetch:(t,r)=>{let o=t.options,i=t.fetchOptions?.meta?.fetchMore?.direction,a=t.state.data?.pages||[],s=t.state.data?.pageParams||[],l={pages:[],pageParams:[]},u=0,c=async()=>{let r=!1,c=e=>{Object.defineProperty(e,"signal",{enumerable:!0,get:()=>(t.signal.aborted?r=!0:t.signal.addEventListener("abort",()=>{r=!0}),t.signal)})},d=(0,n.ZM)(t.options,t.fetchOptions),f=async(e,o,i)=>{if(r)return Promise.reject();if(null==o&&e.pages.length)return Promise.resolve(e);let a=(()=>{let e={client:t.client,queryKey:t.queryKey,pageParam:o,direction:i?"backward":"forward",meta:t.options.meta};return c(e),e})(),s=await d(a),{maxPages:l}=t.options,u=i?n.ZZ:n.y9;return{pages:u(e.pages,s,l),pageParams:u(e.pageParams,o,l)}};if(i&&a.length){let e="backward"===i,t={pages:a,pageParams:s},r=(e?function(e,{pages:t,pageParams:r}){return t.length>0?e.getPreviousPageParam?.(t[0],t,r[0],r):void 0}:g)(o,t);l=await f(t,r,e)}else{let t=e??a.length;do{let e=0===u?s[0]??o.initialPageParam:g(o,l);if(u>0&&null==e)break;l=await f(l,e),u++}while(u<t)}return l};t.options.persister?t.fetchFn=()=>t.options.persister?.(c,{client:t.client,queryKey:t.queryKey,meta:t.options.meta,signal:t.signal},r):t.fetchFn=c}}}function g(e,{pages:t,pageParams:r}){let n=t.length-1;return t.length>0?e.getNextPageParam(t[n],t,r[n],r):void 0}var y=class{#d;#i;#f;#p;#h;#m;#g;#y;constructor(e={}){this.#d=e.queryCache||new s,this.#i=e.mutationCache||new d,this.#f=e.defaultOptions||{},this.#p=new Map,this.#h=new Map,this.#m=0}mount(){this.#m++,1===this.#m&&(this.#g=p.m.subscribe(async e=>{e&&(await this.resumePausedMutations(),this.#d.onFocus())}),this.#y=h.t.subscribe(async e=>{e&&(await this.resumePausedMutations(),this.#d.onOnline())}))}unmount(){this.#m--,0===this.#m&&(this.#g?.(),this.#g=void 0,this.#y?.(),this.#y=void 0)}isFetching(e){return this.#d.findAll({...e,fetchStatus:"fetching"}).length}isMutating(e){return this.#i.findAll({...e,status:"pending"}).length}getQueryData(e){let t=this.defaultQueryOptions({queryKey:e});return this.#d.get(t.queryHash)?.state.data}ensureQueryData(e){let t=this.defaultQueryOptions(e),r=this.#d.build(this,t),o=r.state.data;return void 0===o?this.fetchQuery(e):(e.revalidateIfStale&&r.isStaleByTime((0,n.d2)(t.staleTime,r))&&this.prefetchQuery(t),Promise.resolve(o))}getQueriesData(e){return this.#d.findAll(e).map(({queryKey:e,state:t})=>[e,t.data])}setQueryData(e,t,r){let o=this.defaultQueryOptions({queryKey:e}),i=this.#d.get(o.queryHash),a=i?.state.data,s=(0,n.Zw)(t,a);if(void 0!==s)return this.#d.build(this,o).setData(s,{...r,manual:!0})}setQueriesData(e,t,r){return i.jG.batch(()=>this.#d.findAll(e).map(({queryKey:e})=>[e,this.setQueryData(e,t,r)]))}getQueryState(e){let t=this.defaultQueryOptions({queryKey:e});return this.#d.get(t.queryHash)?.state}removeQueries(e){let t=this.#d;i.jG.batch(()=>{t.findAll(e).forEach(e=>{t.remove(e)})})}resetQueries(e,t){let r=this.#d;return i.jG.batch(()=>(r.findAll(e).forEach(e=>{e.reset()}),this.refetchQueries({type:"active",...e},t)))}cancelQueries(e,t={}){let r={revert:!0,...t};return Promise.all(i.jG.batch(()=>this.#d.findAll(e).map(e=>e.cancel(r)))).then(n.lQ).catch(n.lQ)}invalidateQueries(e,t={}){return i.jG.batch(()=>(this.#d.findAll(e).forEach(e=>{e.invalidate()}),e?.refetchType==="none")?Promise.resolve():this.refetchQueries({...e,type:e?.refetchType??e?.type??"active"},t))}refetchQueries(e,t={}){let r={...t,cancelRefetch:t.cancelRefetch??!0};return Promise.all(i.jG.batch(()=>this.#d.findAll(e).filter(e=>!e.isDisabled()&&!e.isStatic()).map(e=>{let t=e.fetch(void 0,r);return r.throwOnError||(t=t.catch(n.lQ)),"paused"===e.state.fetchStatus?Promise.resolve():t}))).then(n.lQ)}fetchQuery(e){let t=this.defaultQueryOptions(e);void 0===t.retry&&(t.retry=!1);let r=this.#d.build(this,t);return r.isStaleByTime((0,n.d2)(t.staleTime,r))?r.fetch(t):Promise.resolve(r.state.data)}prefetchQuery(e){return this.fetchQuery(e).then(n.lQ).catch(n.lQ)}fetchInfiniteQuery(e){return e.behavior=m(e.pages),this.fetchQuery(e)}prefetchInfiniteQuery(e){return this.fetchInfiniteQuery(e).then(n.lQ).catch(n.lQ)}ensureInfiniteQueryData(e){return e.behavior=m(e.pages),this.ensureQueryData(e)}resumePausedMutations(){return h.t.isOnline()?this.#i.resumePausedMutations():Promise.resolve()}getQueryCache(){return this.#d}getMutationCache(){return this.#i}getDefaultOptions(){return this.#f}setDefaultOptions(e){this.#f=e}setQueryDefaults(e,t){this.#p.set((0,n.EN)(e),{queryKey:e,defaultOptions:t})}getQueryDefaults(e){let t=[...this.#p.values()],r={};return t.forEach(t=>{(0,n.Cp)(e,t.queryKey)&&Object.assign(r,t.defaultOptions)}),r}setMutationDefaults(e,t){this.#h.set((0,n.EN)(e),{mutationKey:e,defaultOptions:t})}getMutationDefaults(e){let t=[...this.#h.values()],r={};return t.forEach(t=>{(0,n.Cp)(e,t.mutationKey)&&Object.assign(r,t.defaultOptions)}),r}defaultQueryOptions(e){if(e._defaulted)return e;let t={...this.#f.queries,...this.getQueryDefaults(e.queryKey),...e,_defaulted:!0};return t.queryHash||(t.queryHash=(0,n.F$)(t.queryKey,t)),void 0===t.refetchOnReconnect&&(t.refetchOnReconnect="always"!==t.networkMode),void 0===t.throwOnError&&(t.throwOnError=!!t.suspense),!t.networkMode&&t.persister&&(t.networkMode="offlineFirst"),t.queryFn===n.hT&&(t.enabled=!1),t}defaultMutationOptions(e){return e?._defaulted?e:{...this.#f.mutations,...e?.mutationKey&&this.getMutationDefaults(e.mutationKey),...e,_defaulted:!0}}clear(){this.#d.clear(),this.#i.clear()}}},26134:(e,t,r)=>{r.d(t,{UC:()=>er,VY:()=>eo,ZL:()=>ee,bL:()=>Y,bm:()=>ei,hE:()=>en,hJ:()=>et,l9:()=>J});var n=r(43210),o=r(70569),i=r(98599),a=r(11273),s=r(96963),l=r(65551),u=r(31355),c=r(32547),d=r(25028),f=r(46059),p=r(14163),h=r(1359),m=r(42247),g=r(63376),y=r(8730),v=r(60687),b="Dialog",[w,x]=(0,a.A)(b),[k,C]=w(b),E=e=>{let{__scopeDialog:t,children:r,open:o,defaultOpen:i,onOpenChange:a,modal:u=!0}=e,c=n.useRef(null),d=n.useRef(null),[f,p]=(0,l.i)({prop:o,defaultProp:i??!1,onChange:a,caller:b});return(0,v.jsx)(k,{scope:t,triggerRef:c,contentRef:d,contentId:(0,s.B)(),titleId:(0,s.B)(),descriptionId:(0,s.B)(),open:f,onOpenChange:p,onOpenToggle:n.useCallback(()=>p(e=>!e),[p]),modal:u,children:r})};E.displayName=b;var R="DialogTrigger",O=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,a=C(R,r),s=(0,i.s)(t,a.triggerRef);return(0,v.jsx)(p.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":a.open,"aria-controls":a.contentId,"data-state":U(a.open),...n,ref:s,onClick:(0,o.m)(e.onClick,a.onOpenToggle)})});O.displayName=R;var M="DialogPortal",[S,j]=w(M,{forceMount:void 0}),A=e=>{let{__scopeDialog:t,forceMount:r,children:o,container:i}=e,a=C(M,t);return(0,v.jsx)(S,{scope:t,forceMount:r,children:n.Children.map(o,e=>(0,v.jsx)(f.C,{present:r||a.open,children:(0,v.jsx)(d.Z,{asChild:!0,container:i,children:e})}))})};A.displayName=M;var P="DialogOverlay",T=n.forwardRef((e,t)=>{let r=j(P,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,i=C(P,e.__scopeDialog);return i.modal?(0,v.jsx)(f.C,{present:n||i.open,children:(0,v.jsx)(N,{...o,ref:t})}):null});T.displayName=P;var D=(0,y.TL)("DialogOverlay.RemoveScroll"),N=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=C(P,r);return(0,v.jsx)(m.A,{as:D,allowPinchZoom:!0,shards:[o.contentRef],children:(0,v.jsx)(p.sG.div,{"data-state":U(o.open),...n,ref:t,style:{pointerEvents:"auto",...n.style}})})}),L="DialogContent",F=n.forwardRef((e,t)=>{let r=j(L,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,i=C(L,e.__scopeDialog);return(0,v.jsx)(f.C,{present:n||i.open,children:i.modal?(0,v.jsx)(I,{...o,ref:t}):(0,v.jsx)(_,{...o,ref:t})})});F.displayName=L;var I=n.forwardRef((e,t)=>{let r=C(L,e.__scopeDialog),a=n.useRef(null),s=(0,i.s)(t,r.contentRef,a);return n.useEffect(()=>{let e=a.current;if(e)return(0,g.Eq)(e)},[]),(0,v.jsx)(q,{...e,ref:s,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{e.preventDefault(),r.triggerRef.current?.focus()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;(2===t.button||r)&&e.preventDefault()}),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault())})}),_=n.forwardRef((e,t)=>{let r=C(L,e.__scopeDialog),o=n.useRef(!1),i=n.useRef(!1);return(0,v.jsx)(q,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(o.current||r.triggerRef.current?.focus(),t.preventDefault()),o.current=!1,i.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(o.current=!0,"pointerdown"===t.detail.originalEvent.type&&(i.current=!0));let n=t.target;r.triggerRef.current?.contains(n)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&i.current&&t.preventDefault()}})}),q=n.forwardRef((e,t)=>{let{__scopeDialog:r,trapFocus:o,onOpenAutoFocus:a,onCloseAutoFocus:s,...l}=e,d=C(L,r),f=n.useRef(null),p=(0,i.s)(t,f);return(0,h.Oh)(),(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(c.n,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:a,onUnmountAutoFocus:s,children:(0,v.jsx)(u.qW,{role:"dialog",id:d.contentId,"aria-describedby":d.descriptionId,"aria-labelledby":d.titleId,"data-state":U(d.open),...l,ref:p,onDismiss:()=>d.onOpenChange(!1)})}),(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(Z,{titleId:d.titleId}),(0,v.jsx)(X,{contentRef:f,descriptionId:d.descriptionId})]})]})}),z="DialogTitle",G=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=C(z,r);return(0,v.jsx)(p.sG.h2,{id:o.titleId,...n,ref:t})});G.displayName=z;var K="DialogDescription",W=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=C(K,r);return(0,v.jsx)(p.sG.p,{id:o.descriptionId,...n,ref:t})});W.displayName=K;var Q="DialogClose",B=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,i=C(Q,r);return(0,v.jsx)(p.sG.button,{type:"button",...n,ref:t,onClick:(0,o.m)(e.onClick,()=>i.onOpenChange(!1))})});function U(e){return e?"open":"closed"}B.displayName=Q;var $="DialogTitleWarning",[H,V]=(0,a.q)($,{contentName:L,titleName:z,docsSlug:"dialog"}),Z=({titleId:e})=>{let t=V($),r=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return n.useEffect(()=>{e&&(document.getElementById(e)||console.error(r))},[r,e]),null},X=({contentRef:e,descriptionId:t})=>{let r=V("DialogDescriptionWarning"),o=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${r.contentName}}.`;return n.useEffect(()=>{let r=e.current?.getAttribute("aria-describedby");t&&r&&(document.getElementById(t)||console.warn(o))},[o,e,t]),null},Y=E,J=O,ee=A,et=T,er=F,en=G,eo=W,ei=B},29604:(e,t,r)=>{r.d(t,{II:()=>d,v_:()=>l,wm:()=>c});var n=r(39850),o=r(22115),i=r(73458),a=r(31212);function s(e){return Math.min(1e3*2**e,3e4)}function l(e){return(e??"online")!=="online"||o.t.isOnline()}var u=class extends Error{constructor(e){super("CancelledError"),this.revert=e?.revert,this.silent=e?.silent}};function c(e){return e instanceof u}function d(e){let t,r=!1,c=0,d=!1,f=(0,i.T)(),p=()=>n.m.isFocused()&&("always"===e.networkMode||o.t.isOnline())&&e.canRun(),h=()=>l(e.networkMode)&&e.canRun(),m=r=>{d||(d=!0,e.onSuccess?.(r),t?.(),f.resolve(r))},g=r=>{d||(d=!0,e.onError?.(r),t?.(),f.reject(r))},y=()=>new Promise(r=>{t=e=>{(d||p())&&r(e)},e.onPause?.()}).then(()=>{t=void 0,d||e.onContinue?.()}),v=()=>{let t;if(d)return;let n=0===c?e.initialPromise:void 0;try{t=n??e.fn()}catch(e){t=Promise.reject(e)}Promise.resolve(t).then(m).catch(t=>{if(d)return;let n=e.retry??3*!a.S$,o=e.retryDelay??s,i="function"==typeof o?o(c,t):o,l=!0===n||"number"==typeof n&&c<n||"function"==typeof n&&n(c,t);if(r||!l)return void g(t);c++,e.onFail?.(c,t),(0,a.yy)(i).then(()=>p()?void 0:y()).then(()=>{r?g(t):v()})})};return{promise:f,cancel:t=>{d||(g(new u(t)),e.abort?.())},continue:()=>(t?.(),f),cancelRetry:()=>{r=!0},continueRetry:()=>{r=!1},canStart:h,start:()=>(h()?v():y().then(v),f)}}},31212:(e,t,r)=>{r.d(t,{Cp:()=>h,EN:()=>p,Eh:()=>u,F$:()=>f,GU:()=>R,MK:()=>c,S$:()=>n,ZM:()=>E,ZZ:()=>k,Zw:()=>i,d2:()=>l,f8:()=>m,gn:()=>a,hT:()=>C,j3:()=>s,lQ:()=>o,nJ:()=>d,pl:()=>w,y9:()=>x,yy:()=>b});var n="undefined"==typeof window||"Deno"in globalThis;function o(){}function i(e,t){return"function"==typeof e?e(t):e}function a(e){return"number"==typeof e&&e>=0&&e!==1/0}function s(e,t){return Math.max(e+(t||0)-Date.now(),0)}function l(e,t){return"function"==typeof e?e(t):e}function u(e,t){return"function"==typeof e?e(t):e}function c(e,t){let{type:r="all",exact:n,fetchStatus:o,predicate:i,queryKey:a,stale:s}=e;if(a){if(n){if(t.queryHash!==f(a,t.options))return!1}else if(!h(t.queryKey,a))return!1}if("all"!==r){let e=t.isActive();if("active"===r&&!e||"inactive"===r&&e)return!1}return("boolean"!=typeof s||t.isStale()===s)&&(!o||o===t.state.fetchStatus)&&(!i||!!i(t))}function d(e,t){let{exact:r,status:n,predicate:o,mutationKey:i}=e;if(i){if(!t.options.mutationKey)return!1;if(r){if(p(t.options.mutationKey)!==p(i))return!1}else if(!h(t.options.mutationKey,i))return!1}return(!n||t.state.status===n)&&(!o||!!o(t))}function f(e,t){return(t?.queryKeyHashFn||p)(e)}function p(e){return JSON.stringify(e,(e,t)=>y(t)?Object.keys(t).sort().reduce((e,r)=>(e[r]=t[r],e),{}):t)}function h(e,t){return e===t||typeof e==typeof t&&!!e&&!!t&&"object"==typeof e&&"object"==typeof t&&Object.keys(t).every(r=>h(e[r],t[r]))}function m(e,t){if(!t||Object.keys(e).length!==Object.keys(t).length)return!1;for(let r in e)if(e[r]!==t[r])return!1;return!0}function g(e){return Array.isArray(e)&&e.length===Object.keys(e).length}function y(e){if(!v(e))return!1;let t=e.constructor;if(void 0===t)return!0;let r=t.prototype;return!!v(r)&&!!r.hasOwnProperty("isPrototypeOf")&&Object.getPrototypeOf(e)===Object.prototype}function v(e){return"[object Object]"===Object.prototype.toString.call(e)}function b(e){return new Promise(t=>{setTimeout(t,e)})}function w(e,t,r){return"function"==typeof r.structuralSharing?r.structuralSharing(e,t):!1!==r.structuralSharing?function e(t,r){if(t===r)return t;let n=g(t)&&g(r);if(n||y(t)&&y(r)){let o=n?t:Object.keys(t),i=o.length,a=n?r:Object.keys(r),s=a.length,l=n?[]:{},u=new Set(o),c=0;for(let o=0;o<s;o++){let i=n?o:a[o];(!n&&u.has(i)||n)&&void 0===t[i]&&void 0===r[i]?(l[i]=void 0,c++):(l[i]=e(t[i],r[i]),l[i]===t[i]&&void 0!==t[i]&&c++)}return i===s&&c===i?t:l}return r}(e,t):t}function x(e,t,r=0){let n=[...e,t];return r&&n.length>r?n.slice(1):n}function k(e,t,r=0){let n=[t,...e];return r&&n.length>r?n.slice(0,-1):n}var C=Symbol();function E(e,t){return!e.queryFn&&t?.initialPromise?()=>t.initialPromise:e.queryFn&&e.queryFn!==C?e.queryFn:()=>Promise.reject(Error(`Missing queryFn: '${e.queryHash}'`))}function R(e,t){return"function"==typeof e?e(...t):!!e}},31355:(e,t,r)=>{r.d(t,{qW:()=>f});var n,o=r(43210),i=r(70569),a=r(14163),s=r(98599),l=r(13495),u=r(60687),c="dismissableLayer.update",d=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),f=o.forwardRef((e,t)=>{let{disableOutsidePointerEvents:r=!1,onEscapeKeyDown:f,onPointerDownOutside:m,onFocusOutside:g,onInteractOutside:y,onDismiss:v,...b}=e,w=o.useContext(d),[x,k]=o.useState(null),C=x?.ownerDocument??globalThis?.document,[,E]=o.useState({}),R=(0,s.s)(t,e=>k(e)),O=Array.from(w.layers),[M]=[...w.layersWithOutsidePointerEventsDisabled].slice(-1),S=O.indexOf(M),j=x?O.indexOf(x):-1,A=w.layersWithOutsidePointerEventsDisabled.size>0,P=j>=S,T=function(e,t=globalThis?.document){let r=(0,l.c)(e),n=o.useRef(!1),i=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!n.current){let n=function(){h("dismissableLayer.pointerDownOutside",r,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(t.removeEventListener("click",i.current),i.current=n,t.addEventListener("click",i.current,{once:!0})):n()}else t.removeEventListener("click",i.current);n.current=!1},o=window.setTimeout(()=>{t.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(o),t.removeEventListener("pointerdown",e),t.removeEventListener("click",i.current)}},[t,r]),{onPointerDownCapture:()=>n.current=!0}}(e=>{let t=e.target,r=[...w.branches].some(e=>e.contains(t));P&&!r&&(m?.(e),y?.(e),e.defaultPrevented||v?.())},C),D=function(e,t=globalThis?.document){let r=(0,l.c)(e),n=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!n.current&&h("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return t.addEventListener("focusin",e),()=>t.removeEventListener("focusin",e)},[t,r]),{onFocusCapture:()=>n.current=!0,onBlurCapture:()=>n.current=!1}}(e=>{let t=e.target;![...w.branches].some(e=>e.contains(t))&&(g?.(e),y?.(e),e.defaultPrevented||v?.())},C);return!function(e,t=globalThis?.document){let r=(0,l.c)(e);o.useEffect(()=>{let e=e=>{"Escape"===e.key&&r(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[r,t])}(e=>{j===w.layers.size-1&&(f?.(e),!e.defaultPrevented&&v&&(e.preventDefault(),v()))},C),o.useEffect(()=>{if(x)return r&&(0===w.layersWithOutsidePointerEventsDisabled.size&&(n=C.body.style.pointerEvents,C.body.style.pointerEvents="none"),w.layersWithOutsidePointerEventsDisabled.add(x)),w.layers.add(x),p(),()=>{r&&1===w.layersWithOutsidePointerEventsDisabled.size&&(C.body.style.pointerEvents=n)}},[x,C,r,w]),o.useEffect(()=>()=>{x&&(w.layers.delete(x),w.layersWithOutsidePointerEventsDisabled.delete(x),p())},[x,w]),o.useEffect(()=>{let e=()=>E({});return document.addEventListener(c,e),()=>document.removeEventListener(c,e)},[]),(0,u.jsx)(a.sG.div,{...b,ref:R,style:{pointerEvents:A?P?"auto":"none":void 0,...e.style},onFocusCapture:(0,i.m)(e.onFocusCapture,D.onFocusCapture),onBlurCapture:(0,i.m)(e.onBlurCapture,D.onBlurCapture),onPointerDownCapture:(0,i.m)(e.onPointerDownCapture,T.onPointerDownCapture)})});function p(){let e=new CustomEvent(c);document.dispatchEvent(e)}function h(e,t,r,{discrete:n}){let o=r.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:r});t&&o.addEventListener(e,t,{once:!0}),n?(0,a.hO)(o,i):o.dispatchEvent(i)}f.displayName="DismissableLayer",o.forwardRef((e,t)=>{let r=o.useContext(d),n=o.useRef(null),i=(0,s.s)(t,n);return o.useEffect(()=>{let e=n.current;if(e)return r.branches.add(e),()=>{r.branches.delete(e)}},[r.branches]),(0,u.jsx)(a.sG.div,{...e,ref:i})}).displayName="DismissableLayerBranch"},32547:(e,t,r)=>{r.d(t,{n:()=>d});var n=r(43210),o=r(98599),i=r(14163),a=r(13495),s=r(60687),l="focusScope.autoFocusOnMount",u="focusScope.autoFocusOnUnmount",c={bubbles:!1,cancelable:!0},d=n.forwardRef((e,t)=>{let{loop:r=!1,trapped:d=!1,onMountAutoFocus:g,onUnmountAutoFocus:y,...v}=e,[b,w]=n.useState(null),x=(0,a.c)(g),k=(0,a.c)(y),C=n.useRef(null),E=(0,o.s)(t,e=>w(e)),R=n.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;n.useEffect(()=>{if(d){let e=function(e){if(R.paused||!b)return;let t=e.target;b.contains(t)?C.current=t:h(C.current,{select:!0})},t=function(e){if(R.paused||!b)return;let t=e.relatedTarget;null!==t&&(b.contains(t)||h(C.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let r=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&h(b)});return b&&r.observe(b,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),r.disconnect()}}},[d,b,R.paused]),n.useEffect(()=>{if(b){m.add(R);let e=document.activeElement;if(!b.contains(e)){let t=new CustomEvent(l,c);b.addEventListener(l,x),b.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){let r=document.activeElement;for(let n of e)if(h(n,{select:t}),document.activeElement!==r)return}(f(b).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&h(b))}return()=>{b.removeEventListener(l,x),setTimeout(()=>{let t=new CustomEvent(u,c);b.addEventListener(u,k),b.dispatchEvent(t),t.defaultPrevented||h(e??document.body,{select:!0}),b.removeEventListener(u,k),m.remove(R)},0)}}},[b,x,k,R]);let O=n.useCallback(e=>{if(!r&&!d||R.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,n=document.activeElement;if(t&&n){let t=e.currentTarget,[o,i]=function(e){let t=f(e);return[p(t,e),p(t.reverse(),e)]}(t);o&&i?e.shiftKey||n!==i?e.shiftKey&&n===o&&(e.preventDefault(),r&&h(i,{select:!0})):(e.preventDefault(),r&&h(o,{select:!0})):n===t&&e.preventDefault()}},[r,d,R.paused]);return(0,s.jsx)(i.sG.div,{tabIndex:-1,...v,ref:E,onKeyDown:O})});function f(e){let t=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)t.push(r.currentNode);return t}function p(e,t){for(let r of e)if(!function(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===t||e!==t);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(r,{upTo:t}))return r}function h(e,{select:t=!1}={}){if(e&&e.focus){var r;let n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&(r=e)instanceof HTMLInputElement&&"select"in r&&t&&e.select()}}d.displayName="FocusScope";var m=function(){let e=[];return{add(t){let r=e[0];t!==r&&r?.pause(),(e=g(e,t)).unshift(t)},remove(t){e=g(e,t),e[0]?.resume()}}}();function g(e,t){let r=[...e],n=r.indexOf(t);return -1!==n&&r.splice(n,1),r}},33465:(e,t,r)=>{r.d(t,{jG:()=>o});var n=e=>setTimeout(e,0),o=function(){let e=[],t=0,r=e=>{e()},o=e=>{e()},i=n,a=n=>{t?e.push(n):i(()=>{r(n)})},s=()=>{let t=e;e=[],t.length&&i(()=>{o(()=>{t.forEach(e=>{r(e)})})})};return{batch:e=>{let r;t++;try{r=e()}finally{--t||s()}return r},batchCalls:e=>(...t)=>{a(()=>{e(...t)})},schedule:a,setNotifyFunction:e=>{r=e},setBatchNotifyFunction:e=>{o=e},setScheduler:e=>{i=e}}}()},35536:(e,t,r)=>{r.d(t,{Q:()=>n});var n=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(e){return this.listeners.add(e),this.onSubscribe(),()=>{this.listeners.delete(e),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}}},38674:(e,t,r)=>{r.d(t,{Mz:()=>eV,i3:()=>eX,UC:()=>eZ,bL:()=>eH,Bk:()=>eT});var n=r(43210);let o=["top","right","bottom","left"],i=Math.min,a=Math.max,s=Math.round,l=Math.floor,u=e=>({x:e,y:e}),c={left:"right",right:"left",bottom:"top",top:"bottom"},d={start:"end",end:"start"};function f(e,t){return"function"==typeof e?e(t):e}function p(e){return e.split("-")[0]}function h(e){return e.split("-")[1]}function m(e){return"x"===e?"y":"x"}function g(e){return"y"===e?"height":"width"}function y(e){return["top","bottom"].includes(p(e))?"y":"x"}function v(e){return e.replace(/start|end/g,e=>d[e])}function b(e){return e.replace(/left|right|bottom|top/g,e=>c[e])}function w(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function x(e){let{x:t,y:r,width:n,height:o}=e;return{width:n,height:o,top:r,left:t,right:t+n,bottom:r+o,x:t,y:r}}function k(e,t,r){let n,{reference:o,floating:i}=e,a=y(t),s=m(y(t)),l=g(s),u=p(t),c="y"===a,d=o.x+o.width/2-i.width/2,f=o.y+o.height/2-i.height/2,v=o[l]/2-i[l]/2;switch(u){case"top":n={x:d,y:o.y-i.height};break;case"bottom":n={x:d,y:o.y+o.height};break;case"right":n={x:o.x+o.width,y:f};break;case"left":n={x:o.x-i.width,y:f};break;default:n={x:o.x,y:o.y}}switch(h(t)){case"start":n[s]-=v*(r&&c?-1:1);break;case"end":n[s]+=v*(r&&c?-1:1)}return n}let C=async(e,t,r)=>{let{placement:n="bottom",strategy:o="absolute",middleware:i=[],platform:a}=r,s=i.filter(Boolean),l=await (null==a.isRTL?void 0:a.isRTL(t)),u=await a.getElementRects({reference:e,floating:t,strategy:o}),{x:c,y:d}=k(u,n,l),f=n,p={},h=0;for(let r=0;r<s.length;r++){let{name:i,fn:m}=s[r],{x:g,y:y,data:v,reset:b}=await m({x:c,y:d,initialPlacement:n,placement:f,strategy:o,middlewareData:p,rects:u,platform:a,elements:{reference:e,floating:t}});c=null!=g?g:c,d=null!=y?y:d,p={...p,[i]:{...p[i],...v}},b&&h<=50&&(h++,"object"==typeof b&&(b.placement&&(f=b.placement),b.rects&&(u=!0===b.rects?await a.getElementRects({reference:e,floating:t,strategy:o}):b.rects),{x:c,y:d}=k(u,f,l)),r=-1)}return{x:c,y:d,placement:f,strategy:o,middlewareData:p}};async function E(e,t){var r;void 0===t&&(t={});let{x:n,y:o,platform:i,rects:a,elements:s,strategy:l}=e,{boundary:u="clippingAncestors",rootBoundary:c="viewport",elementContext:d="floating",altBoundary:p=!1,padding:h=0}=f(t,e),m=w(h),g=s[p?"floating"===d?"reference":"floating":d],y=x(await i.getClippingRect({element:null==(r=await (null==i.isElement?void 0:i.isElement(g)))||r?g:g.contextElement||await (null==i.getDocumentElement?void 0:i.getDocumentElement(s.floating)),boundary:u,rootBoundary:c,strategy:l})),v="floating"===d?{x:n,y:o,width:a.floating.width,height:a.floating.height}:a.reference,b=await (null==i.getOffsetParent?void 0:i.getOffsetParent(s.floating)),k=await (null==i.isElement?void 0:i.isElement(b))&&await (null==i.getScale?void 0:i.getScale(b))||{x:1,y:1},C=x(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:s,rect:v,offsetParent:b,strategy:l}):v);return{top:(y.top-C.top+m.top)/k.y,bottom:(C.bottom-y.bottom+m.bottom)/k.y,left:(y.left-C.left+m.left)/k.x,right:(C.right-y.right+m.right)/k.x}}function R(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function O(e){return o.some(t=>e[t]>=0)}async function M(e,t){let{placement:r,platform:n,elements:o}=e,i=await (null==n.isRTL?void 0:n.isRTL(o.floating)),a=p(r),s=h(r),l="y"===y(r),u=["left","top"].includes(a)?-1:1,c=i&&l?-1:1,d=f(t,e),{mainAxis:m,crossAxis:g,alignmentAxis:v}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return s&&"number"==typeof v&&(g="end"===s?-1*v:v),l?{x:g*c,y:m*u}:{x:m*u,y:g*c}}function S(){return"undefined"!=typeof window}function j(e){return T(e)?(e.nodeName||"").toLowerCase():"#document"}function A(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function P(e){var t;return null==(t=(T(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function T(e){return!!S()&&(e instanceof Node||e instanceof A(e).Node)}function D(e){return!!S()&&(e instanceof Element||e instanceof A(e).Element)}function N(e){return!!S()&&(e instanceof HTMLElement||e instanceof A(e).HTMLElement)}function L(e){return!!S()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof A(e).ShadowRoot)}function F(e){let{overflow:t,overflowX:r,overflowY:n,display:o}=G(e);return/auto|scroll|overlay|hidden|clip/.test(t+n+r)&&!["inline","contents"].includes(o)}function I(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function _(e){let t=q(),r=D(e)?G(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!r[e]&&"none"!==r[e])||!!r.containerType&&"normal"!==r.containerType||!t&&!!r.backdropFilter&&"none"!==r.backdropFilter||!t&&!!r.filter&&"none"!==r.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(r.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(r.contain||"").includes(e))}function q(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function z(e){return["html","body","#document"].includes(j(e))}function G(e){return A(e).getComputedStyle(e)}function K(e){return D(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function W(e){if("html"===j(e))return e;let t=e.assignedSlot||e.parentNode||L(e)&&e.host||P(e);return L(t)?t.host:t}function Q(e,t,r){var n;void 0===t&&(t=[]),void 0===r&&(r=!0);let o=function e(t){let r=W(t);return z(r)?t.ownerDocument?t.ownerDocument.body:t.body:N(r)&&F(r)?r:e(r)}(e),i=o===(null==(n=e.ownerDocument)?void 0:n.body),a=A(o);if(i){let e=B(a);return t.concat(a,a.visualViewport||[],F(o)?o:[],e&&r?Q(e):[])}return t.concat(o,Q(o,[],r))}function B(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function U(e){let t=G(e),r=parseFloat(t.width)||0,n=parseFloat(t.height)||0,o=N(e),i=o?e.offsetWidth:r,a=o?e.offsetHeight:n,l=s(r)!==i||s(n)!==a;return l&&(r=i,n=a),{width:r,height:n,$:l}}function $(e){return D(e)?e:e.contextElement}function H(e){let t=$(e);if(!N(t))return u(1);let r=t.getBoundingClientRect(),{width:n,height:o,$:i}=U(t),a=(i?s(r.width):r.width)/n,l=(i?s(r.height):r.height)/o;return a&&Number.isFinite(a)||(a=1),l&&Number.isFinite(l)||(l=1),{x:a,y:l}}let V=u(0);function Z(e){let t=A(e);return q()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:V}function X(e,t,r,n){var o;void 0===t&&(t=!1),void 0===r&&(r=!1);let i=e.getBoundingClientRect(),a=$(e),s=u(1);t&&(n?D(n)&&(s=H(n)):s=H(e));let l=(void 0===(o=r)&&(o=!1),n&&(!o||n===A(a))&&o)?Z(a):u(0),c=(i.left+l.x)/s.x,d=(i.top+l.y)/s.y,f=i.width/s.x,p=i.height/s.y;if(a){let e=A(a),t=n&&D(n)?A(n):n,r=e,o=B(r);for(;o&&n&&t!==r;){let e=H(o),t=o.getBoundingClientRect(),n=G(o),i=t.left+(o.clientLeft+parseFloat(n.paddingLeft))*e.x,a=t.top+(o.clientTop+parseFloat(n.paddingTop))*e.y;c*=e.x,d*=e.y,f*=e.x,p*=e.y,c+=i,d+=a,o=B(r=A(o))}}return x({width:f,height:p,x:c,y:d})}function Y(e,t){let r=K(e).scrollLeft;return t?t.left+r:X(P(e)).left+r}function J(e,t,r){void 0===r&&(r=!1);let n=e.getBoundingClientRect();return{x:n.left+t.scrollLeft-(r?0:Y(e,n)),y:n.top+t.scrollTop}}function ee(e,t,r){let n;if("viewport"===t)n=function(e,t){let r=A(e),n=P(e),o=r.visualViewport,i=n.clientWidth,a=n.clientHeight,s=0,l=0;if(o){i=o.width,a=o.height;let e=q();(!e||e&&"fixed"===t)&&(s=o.offsetLeft,l=o.offsetTop)}return{width:i,height:a,x:s,y:l}}(e,r);else if("document"===t)n=function(e){let t=P(e),r=K(e),n=e.ownerDocument.body,o=a(t.scrollWidth,t.clientWidth,n.scrollWidth,n.clientWidth),i=a(t.scrollHeight,t.clientHeight,n.scrollHeight,n.clientHeight),s=-r.scrollLeft+Y(e),l=-r.scrollTop;return"rtl"===G(n).direction&&(s+=a(t.clientWidth,n.clientWidth)-o),{width:o,height:i,x:s,y:l}}(P(e));else if(D(t))n=function(e,t){let r=X(e,!0,"fixed"===t),n=r.top+e.clientTop,o=r.left+e.clientLeft,i=N(e)?H(e):u(1),a=e.clientWidth*i.x,s=e.clientHeight*i.y;return{width:a,height:s,x:o*i.x,y:n*i.y}}(t,r);else{let r=Z(e);n={x:t.x-r.x,y:t.y-r.y,width:t.width,height:t.height}}return x(n)}function et(e){return"static"===G(e).position}function er(e,t){if(!N(e)||"fixed"===G(e).position)return null;if(t)return t(e);let r=e.offsetParent;return P(e)===r&&(r=r.ownerDocument.body),r}function en(e,t){let r=A(e);if(I(e))return r;if(!N(e)){let t=W(e);for(;t&&!z(t);){if(D(t)&&!et(t))return t;t=W(t)}return r}let n=er(e,t);for(;n&&["table","td","th"].includes(j(n))&&et(n);)n=er(n,t);return n&&z(n)&&et(n)&&!_(n)?r:n||function(e){let t=W(e);for(;N(t)&&!z(t);){if(_(t))return t;if(I(t))break;t=W(t)}return null}(e)||r}let eo=async function(e){let t=this.getOffsetParent||en,r=this.getDimensions,n=await r(e.floating);return{reference:function(e,t,r){let n=N(t),o=P(t),i="fixed"===r,a=X(e,!0,i,t),s={scrollLeft:0,scrollTop:0},l=u(0);if(n||!n&&!i)if(("body"!==j(t)||F(o))&&(s=K(t)),n){let e=X(t,!0,i,t);l.x=e.x+t.clientLeft,l.y=e.y+t.clientTop}else o&&(l.x=Y(o));i&&!n&&o&&(l.x=Y(o));let c=!o||n||i?u(0):J(o,s);return{x:a.left+s.scrollLeft-l.x-c.x,y:a.top+s.scrollTop-l.y-c.y,width:a.width,height:a.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:n.width,height:n.height}}},ei={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:r,offsetParent:n,strategy:o}=e,i="fixed"===o,a=P(n),s=!!t&&I(t.floating);if(n===a||s&&i)return r;let l={scrollLeft:0,scrollTop:0},c=u(1),d=u(0),f=N(n);if((f||!f&&!i)&&(("body"!==j(n)||F(a))&&(l=K(n)),N(n))){let e=X(n);c=H(n),d.x=e.x+n.clientLeft,d.y=e.y+n.clientTop}let p=!a||f||i?u(0):J(a,l,!0);return{width:r.width*c.x,height:r.height*c.y,x:r.x*c.x-l.scrollLeft*c.x+d.x+p.x,y:r.y*c.y-l.scrollTop*c.y+d.y+p.y}},getDocumentElement:P,getClippingRect:function(e){let{element:t,boundary:r,rootBoundary:n,strategy:o}=e,s=[..."clippingAncestors"===r?I(t)?[]:function(e,t){let r=t.get(e);if(r)return r;let n=Q(e,[],!1).filter(e=>D(e)&&"body"!==j(e)),o=null,i="fixed"===G(e).position,a=i?W(e):e;for(;D(a)&&!z(a);){let t=G(a),r=_(a);r||"fixed"!==t.position||(o=null),(i?!r&&!o:!r&&"static"===t.position&&!!o&&["absolute","fixed"].includes(o.position)||F(a)&&!r&&function e(t,r){let n=W(t);return!(n===r||!D(n)||z(n))&&("fixed"===G(n).position||e(n,r))}(e,a))?n=n.filter(e=>e!==a):o=t,a=W(a)}return t.set(e,n),n}(t,this._c):[].concat(r),n],l=s[0],u=s.reduce((e,r)=>{let n=ee(t,r,o);return e.top=a(n.top,e.top),e.right=i(n.right,e.right),e.bottom=i(n.bottom,e.bottom),e.left=a(n.left,e.left),e},ee(t,l,o));return{width:u.right-u.left,height:u.bottom-u.top,x:u.left,y:u.top}},getOffsetParent:en,getElementRects:eo,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:r}=U(e);return{width:t,height:r}},getScale:H,isElement:D,isRTL:function(e){return"rtl"===G(e).direction}};function ea(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let es=e=>({name:"arrow",options:e,async fn(t){let{x:r,y:n,placement:o,rects:s,platform:l,elements:u,middlewareData:c}=t,{element:d,padding:p=0}=f(e,t)||{};if(null==d)return{};let v=w(p),b={x:r,y:n},x=m(y(o)),k=g(x),C=await l.getDimensions(d),E="y"===x,R=E?"clientHeight":"clientWidth",O=s.reference[k]+s.reference[x]-b[x]-s.floating[k],M=b[x]-s.reference[x],S=await (null==l.getOffsetParent?void 0:l.getOffsetParent(d)),j=S?S[R]:0;j&&await (null==l.isElement?void 0:l.isElement(S))||(j=u.floating[R]||s.floating[k]);let A=j/2-C[k]/2-1,P=i(v[E?"top":"left"],A),T=i(v[E?"bottom":"right"],A),D=j-C[k]-T,N=j/2-C[k]/2+(O/2-M/2),L=a(P,i(N,D)),F=!c.arrow&&null!=h(o)&&N!==L&&s.reference[k]/2-(N<P?P:T)-C[k]/2<0,I=F?N<P?N-P:N-D:0;return{[x]:b[x]+I,data:{[x]:L,centerOffset:N-L-I,...F&&{alignmentOffset:I}},reset:F}}}),el=(e,t,r)=>{let n=new Map,o={platform:ei,...r},i={...o.platform,_c:n};return C(e,t,{...o,platform:i})};var eu=r(51215),ec="undefined"!=typeof document?n.useLayoutEffect:function(){};function ed(e,t){let r,n,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((r=e.length)!==t.length)return!1;for(n=r;0!=n--;)if(!ed(e[n],t[n]))return!1;return!0}if((r=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(n=r;0!=n--;)if(!({}).hasOwnProperty.call(t,o[n]))return!1;for(n=r;0!=n--;){let r=o[n];if(("_owner"!==r||!e.$$typeof)&&!ed(e[r],t[r]))return!1}return!0}return e!=e&&t!=t}function ef(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function ep(e,t){let r=ef(e);return Math.round(t*r)/r}function eh(e){let t=n.useRef(e);return ec(()=>{t.current=e}),t}let em=e=>({name:"arrow",options:e,fn(t){let{element:r,padding:n}="function"==typeof e?e(t):e;return r&&({}).hasOwnProperty.call(r,"current")?null!=r.current?es({element:r.current,padding:n}).fn(t):{}:r?es({element:r,padding:n}).fn(t):{}}}),eg=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var r,n;let{x:o,y:i,placement:a,middlewareData:s}=t,l=await M(t,e);return a===(null==(r=s.offset)?void 0:r.placement)&&null!=(n=s.arrow)&&n.alignmentOffset?{}:{x:o+l.x,y:i+l.y,data:{...l,placement:a}}}}}(e),options:[e,t]}),ey=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:r,y:n,placement:o}=t,{mainAxis:s=!0,crossAxis:l=!1,limiter:u={fn:e=>{let{x:t,y:r}=e;return{x:t,y:r}}},...c}=f(e,t),d={x:r,y:n},h=await E(t,c),g=y(p(o)),v=m(g),b=d[v],w=d[g];if(s){let e="y"===v?"top":"left",t="y"===v?"bottom":"right",r=b+h[e],n=b-h[t];b=a(r,i(b,n))}if(l){let e="y"===g?"top":"left",t="y"===g?"bottom":"right",r=w+h[e],n=w-h[t];w=a(r,i(w,n))}let x=u.fn({...t,[v]:b,[g]:w});return{...x,data:{x:x.x-r,y:x.y-n,enabled:{[v]:s,[g]:l}}}}}}(e),options:[e,t]}),ev=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:r,y:n,placement:o,rects:i,middlewareData:a}=t,{offset:s=0,mainAxis:l=!0,crossAxis:u=!0}=f(e,t),c={x:r,y:n},d=y(o),h=m(d),g=c[h],v=c[d],b=f(s,t),w="number"==typeof b?{mainAxis:b,crossAxis:0}:{mainAxis:0,crossAxis:0,...b};if(l){let e="y"===h?"height":"width",t=i.reference[h]-i.floating[e]+w.mainAxis,r=i.reference[h]+i.reference[e]-w.mainAxis;g<t?g=t:g>r&&(g=r)}if(u){var x,k;let e="y"===h?"width":"height",t=["top","left"].includes(p(o)),r=i.reference[d]-i.floating[e]+(t&&(null==(x=a.offset)?void 0:x[d])||0)+(t?0:w.crossAxis),n=i.reference[d]+i.reference[e]+(t?0:(null==(k=a.offset)?void 0:k[d])||0)-(t?w.crossAxis:0);v<r?v=r:v>n&&(v=n)}return{[h]:g,[d]:v}}}}(e),options:[e,t]}),eb=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var r,n,o,i,a;let{placement:s,middlewareData:l,rects:u,initialPlacement:c,platform:d,elements:w}=t,{mainAxis:x=!0,crossAxis:k=!0,fallbackPlacements:C,fallbackStrategy:R="bestFit",fallbackAxisSideDirection:O="none",flipAlignment:M=!0,...S}=f(e,t);if(null!=(r=l.arrow)&&r.alignmentOffset)return{};let j=p(s),A=y(c),P=p(c)===c,T=await (null==d.isRTL?void 0:d.isRTL(w.floating)),D=C||(P||!M?[b(c)]:function(e){let t=b(e);return[v(e),t,v(t)]}(c)),N="none"!==O;!C&&N&&D.push(...function(e,t,r,n){let o=h(e),i=function(e,t,r){let n=["left","right"],o=["right","left"];switch(e){case"top":case"bottom":if(r)return t?o:n;return t?n:o;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(p(e),"start"===r,n);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(v)))),i}(c,M,O,T));let L=[c,...D],F=await E(t,S),I=[],_=(null==(n=l.flip)?void 0:n.overflows)||[];if(x&&I.push(F[j]),k){let e=function(e,t,r){void 0===r&&(r=!1);let n=h(e),o=m(y(e)),i=g(o),a="x"===o?n===(r?"end":"start")?"right":"left":"start"===n?"bottom":"top";return t.reference[i]>t.floating[i]&&(a=b(a)),[a,b(a)]}(s,u,T);I.push(F[e[0]],F[e[1]])}if(_=[..._,{placement:s,overflows:I}],!I.every(e=>e<=0)){let e=((null==(o=l.flip)?void 0:o.index)||0)+1,t=L[e];if(t&&("alignment"!==k||A===y(t)||_.every(e=>e.overflows[0]>0&&y(e.placement)===A)))return{data:{index:e,overflows:_},reset:{placement:t}};let r=null==(i=_.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!r)switch(R){case"bestFit":{let e=null==(a=_.filter(e=>{if(N){let t=y(e.placement);return t===A||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:a[0];e&&(r=e);break}case"initialPlacement":r=c}if(s!==r)return{reset:{placement:r}}}return{}}}}(e),options:[e,t]}),ew=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var r,n;let o,s,{placement:l,rects:u,platform:c,elements:d}=t,{apply:m=()=>{},...g}=f(e,t),v=await E(t,g),b=p(l),w=h(l),x="y"===y(l),{width:k,height:C}=u.floating;"top"===b||"bottom"===b?(o=b,s=w===(await (null==c.isRTL?void 0:c.isRTL(d.floating))?"start":"end")?"left":"right"):(s=b,o="end"===w?"top":"bottom");let R=C-v.top-v.bottom,O=k-v.left-v.right,M=i(C-v[o],R),S=i(k-v[s],O),j=!t.middlewareData.shift,A=M,P=S;if(null!=(r=t.middlewareData.shift)&&r.enabled.x&&(P=O),null!=(n=t.middlewareData.shift)&&n.enabled.y&&(A=R),j&&!w){let e=a(v.left,0),t=a(v.right,0),r=a(v.top,0),n=a(v.bottom,0);x?P=k-2*(0!==e||0!==t?e+t:a(v.left,v.right)):A=C-2*(0!==r||0!==n?r+n:a(v.top,v.bottom))}await m({...t,availableWidth:P,availableHeight:A});let T=await c.getDimensions(d.floating);return k!==T.width||C!==T.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),ex=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:r}=t,{strategy:n="referenceHidden",...o}=f(e,t);switch(n){case"referenceHidden":{let e=R(await E(t,{...o,elementContext:"reference"}),r.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:O(e)}}}case"escaped":{let e=R(await E(t,{...o,altBoundary:!0}),r.floating);return{data:{escapedOffsets:e,escaped:O(e)}}}default:return{}}}}}(e),options:[e,t]}),ek=(e,t)=>({...em(e),options:[e,t]});var eC=r(14163),eE=r(60687),eR=n.forwardRef((e,t)=>{let{children:r,width:n=10,height:o=5,...i}=e;return(0,eE.jsx)(eC.sG.svg,{...i,ref:t,width:n,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?r:(0,eE.jsx)("polygon",{points:"0,0 30,0 15,10"})})});eR.displayName="Arrow";var eO=r(98599),eM=r(11273),eS=r(13495),ej=r(66156),eA="Popper",[eP,eT]=(0,eM.A)(eA),[eD,eN]=eP(eA),eL=e=>{let{__scopePopper:t,children:r}=e,[o,i]=n.useState(null);return(0,eE.jsx)(eD,{scope:t,anchor:o,onAnchorChange:i,children:r})};eL.displayName=eA;var eF="PopperAnchor",eI=n.forwardRef((e,t)=>{let{__scopePopper:r,virtualRef:o,...i}=e,a=eN(eF,r),s=n.useRef(null),l=(0,eO.s)(t,s);return n.useEffect(()=>{a.onAnchorChange(o?.current||s.current)}),o?null:(0,eE.jsx)(eC.sG.div,{...i,ref:l})});eI.displayName=eF;var e_="PopperContent",[eq,ez]=eP(e_),eG=n.forwardRef((e,t)=>{let{__scopePopper:r,side:o="bottom",sideOffset:s=0,align:u="center",alignOffset:c=0,arrowPadding:d=0,avoidCollisions:f=!0,collisionBoundary:p=[],collisionPadding:h=0,sticky:m="partial",hideWhenDetached:g=!1,updatePositionStrategy:y="optimized",onPlaced:v,...b}=e,w=eN(e_,r),[x,k]=n.useState(null),C=(0,eO.s)(t,e=>k(e)),[E,R]=n.useState(null),O=function(e){let[t,r]=n.useState(void 0);return(0,ej.N)(()=>{if(e){r({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let n,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;n=t.inlineSize,o=t.blockSize}else n=e.offsetWidth,o=e.offsetHeight;r({width:n,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}r(void 0)},[e]),t}(E),M=O?.width??0,S=O?.height??0,j="number"==typeof h?h:{top:0,right:0,bottom:0,left:0,...h},A=Array.isArray(p)?p:[p],T=A.length>0,D={padding:j,boundary:A.filter(eB),altBoundary:T},{refs:N,floatingStyles:L,placement:F,isPositioned:I,middlewareData:_}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:r="absolute",middleware:o=[],platform:i,elements:{reference:a,floating:s}={},transform:l=!0,whileElementsMounted:u,open:c}=e,[d,f]=n.useState({x:0,y:0,strategy:r,placement:t,middlewareData:{},isPositioned:!1}),[p,h]=n.useState(o);ed(p,o)||h(o);let[m,g]=n.useState(null),[y,v]=n.useState(null),b=n.useCallback(e=>{e!==C.current&&(C.current=e,g(e))},[]),w=n.useCallback(e=>{e!==E.current&&(E.current=e,v(e))},[]),x=a||m,k=s||y,C=n.useRef(null),E=n.useRef(null),R=n.useRef(d),O=null!=u,M=eh(u),S=eh(i),j=eh(c),A=n.useCallback(()=>{if(!C.current||!E.current)return;let e={placement:t,strategy:r,middleware:p};S.current&&(e.platform=S.current),el(C.current,E.current,e).then(e=>{let t={...e,isPositioned:!1!==j.current};P.current&&!ed(R.current,t)&&(R.current=t,eu.flushSync(()=>{f(t)}))})},[p,t,r,S,j]);ec(()=>{!1===c&&R.current.isPositioned&&(R.current.isPositioned=!1,f(e=>({...e,isPositioned:!1})))},[c]);let P=n.useRef(!1);ec(()=>(P.current=!0,()=>{P.current=!1}),[]),ec(()=>{if(x&&(C.current=x),k&&(E.current=k),x&&k){if(M.current)return M.current(x,k,A);A()}},[x,k,A,M,O]);let T=n.useMemo(()=>({reference:C,floating:E,setReference:b,setFloating:w}),[b,w]),D=n.useMemo(()=>({reference:x,floating:k}),[x,k]),N=n.useMemo(()=>{let e={position:r,left:0,top:0};if(!D.floating)return e;let t=ep(D.floating,d.x),n=ep(D.floating,d.y);return l?{...e,transform:"translate("+t+"px, "+n+"px)",...ef(D.floating)>=1.5&&{willChange:"transform"}}:{position:r,left:t,top:n}},[r,l,D.floating,d.x,d.y]);return n.useMemo(()=>({...d,update:A,refs:T,elements:D,floatingStyles:N}),[d,A,T,D,N])}({strategy:"fixed",placement:o+("center"!==u?"-"+u:""),whileElementsMounted:(...e)=>(function(e,t,r,n){let o;void 0===n&&(n={});let{ancestorScroll:s=!0,ancestorResize:u=!0,elementResize:c="function"==typeof ResizeObserver,layoutShift:d="function"==typeof IntersectionObserver,animationFrame:f=!1}=n,p=$(e),h=s||u?[...p?Q(p):[],...Q(t)]:[];h.forEach(e=>{s&&e.addEventListener("scroll",r,{passive:!0}),u&&e.addEventListener("resize",r)});let m=p&&d?function(e,t){let r,n=null,o=P(e);function s(){var e;clearTimeout(r),null==(e=n)||e.disconnect(),n=null}return!function u(c,d){void 0===c&&(c=!1),void 0===d&&(d=1),s();let f=e.getBoundingClientRect(),{left:p,top:h,width:m,height:g}=f;if(c||t(),!m||!g)return;let y=l(h),v=l(o.clientWidth-(p+m)),b={rootMargin:-y+"px "+-v+"px "+-l(o.clientHeight-(h+g))+"px "+-l(p)+"px",threshold:a(0,i(1,d))||1},w=!0;function x(t){let n=t[0].intersectionRatio;if(n!==d){if(!w)return u();n?u(!1,n):r=setTimeout(()=>{u(!1,1e-7)},1e3)}1!==n||ea(f,e.getBoundingClientRect())||u(),w=!1}try{n=new IntersectionObserver(x,{...b,root:o.ownerDocument})}catch(e){n=new IntersectionObserver(x,b)}n.observe(e)}(!0),s}(p,r):null,g=-1,y=null;c&&(y=new ResizeObserver(e=>{let[n]=e;n&&n.target===p&&y&&(y.unobserve(t),cancelAnimationFrame(g),g=requestAnimationFrame(()=>{var e;null==(e=y)||e.observe(t)})),r()}),p&&!f&&y.observe(p),y.observe(t));let v=f?X(e):null;return f&&function t(){let n=X(e);v&&!ea(v,n)&&r(),v=n,o=requestAnimationFrame(t)}(),r(),()=>{var e;h.forEach(e=>{s&&e.removeEventListener("scroll",r),u&&e.removeEventListener("resize",r)}),null==m||m(),null==(e=y)||e.disconnect(),y=null,f&&cancelAnimationFrame(o)}})(...e,{animationFrame:"always"===y}),elements:{reference:w.anchor},middleware:[eg({mainAxis:s+S,alignmentAxis:c}),f&&ey({mainAxis:!0,crossAxis:!1,limiter:"partial"===m?ev():void 0,...D}),f&&eb({...D}),ew({...D,apply:({elements:e,rects:t,availableWidth:r,availableHeight:n})=>{let{width:o,height:i}=t.reference,a=e.floating.style;a.setProperty("--radix-popper-available-width",`${r}px`),a.setProperty("--radix-popper-available-height",`${n}px`),a.setProperty("--radix-popper-anchor-width",`${o}px`),a.setProperty("--radix-popper-anchor-height",`${i}px`)}}),E&&ek({element:E,padding:d}),eU({arrowWidth:M,arrowHeight:S}),g&&ex({strategy:"referenceHidden",...D})]}),[q,z]=e$(F),G=(0,eS.c)(v);(0,ej.N)(()=>{I&&G?.()},[I,G]);let K=_.arrow?.x,W=_.arrow?.y,B=_.arrow?.centerOffset!==0,[U,H]=n.useState();return(0,ej.N)(()=>{x&&H(window.getComputedStyle(x).zIndex)},[x]),(0,eE.jsx)("div",{ref:N.setFloating,"data-radix-popper-content-wrapper":"",style:{...L,transform:I?L.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:U,"--radix-popper-transform-origin":[_.transformOrigin?.x,_.transformOrigin?.y].join(" "),..._.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,eE.jsx)(eq,{scope:r,placedSide:q,onArrowChange:R,arrowX:K,arrowY:W,shouldHideArrow:B,children:(0,eE.jsx)(eC.sG.div,{"data-side":q,"data-align":z,...b,ref:C,style:{...b.style,animation:I?void 0:"none"}})})})});eG.displayName=e_;var eK="PopperArrow",eW={top:"bottom",right:"left",bottom:"top",left:"right"},eQ=n.forwardRef(function(e,t){let{__scopePopper:r,...n}=e,o=ez(eK,r),i=eW[o.placedSide];return(0,eE.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,eE.jsx)(eR,{...n,ref:t,style:{...n.style,display:"block"}})})});function eB(e){return null!==e}eQ.displayName=eK;var eU=e=>({name:"transformOrigin",options:e,fn(t){let{placement:r,rects:n,middlewareData:o}=t,i=o.arrow?.centerOffset!==0,a=i?0:e.arrowWidth,s=i?0:e.arrowHeight,[l,u]=e$(r),c={start:"0%",center:"50%",end:"100%"}[u],d=(o.arrow?.x??0)+a/2,f=(o.arrow?.y??0)+s/2,p="",h="";return"bottom"===l?(p=i?c:`${d}px`,h=`${-s}px`):"top"===l?(p=i?c:`${d}px`,h=`${n.floating.height+s}px`):"right"===l?(p=`${-s}px`,h=i?c:`${f}px`):"left"===l&&(p=`${n.floating.width+s}px`,h=i?c:`${f}px`),{data:{x:p,y:h}}}});function e$(e){let[t,r="center"]=e.split("-");return[t,r]}var eH=eL,eV=eI,eZ=eG,eX=eQ},39850:(e,t,r)=>{r.d(t,{m:()=>i});var n=r(35536),o=r(31212),i=new class extends n.Q{#v;#t;#r;constructor(){super(),this.#r=e=>{if(!o.S$&&window.addEventListener){let t=()=>e();return window.addEventListener("visibilitychange",t,!1),()=>{window.removeEventListener("visibilitychange",t)}}}}onSubscribe(){this.#t||this.setEventListener(this.#r)}onUnsubscribe(){this.hasListeners()||(this.#t?.(),this.#t=void 0)}setEventListener(e){this.#r=e,this.#t?.(),this.#t=e(e=>{"boolean"==typeof e?this.setFocused(e):this.onFocus()})}setFocused(e){this.#v!==e&&(this.#v=e,this.onFocus())}onFocus(){let e=this.isFocused();this.listeners.forEach(t=>{t(e)})}isFocused(){return"boolean"==typeof this.#v?this.#v:globalThis.document?.visibilityState!=="hidden"}}},42247:(e,t,r)=>{r.d(t,{A:()=>U});var n,o,i=function(){return(i=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function a(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r}Object.create;Object.create;var s=("function"==typeof SuppressedError&&SuppressedError,r(43210)),l="right-scroll-bar-position",u="width-before-scroll-bar";function c(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var d="undefined"!=typeof window?s.useLayoutEffect:s.useEffect,f=new WeakMap;function p(e){return e}var h=function(e){void 0===e&&(e={});var t,r,n,o,a=(t=null,void 0===r&&(r=p),n=[],o=!1,{read:function(){if(o)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:null},useMedium:function(e){var t=r(e,o);return n.push(t),function(){n=n.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(o=!0;n.length;){var t=n;n=[],t.forEach(e)}n={push:function(t){return e(t)},filter:function(){return n}}},assignMedium:function(e){o=!0;var t=[];if(n.length){var r=n;n=[],r.forEach(e),t=n}var i=function(){var r=t;t=[],r.forEach(e)},a=function(){return Promise.resolve().then(i)};a(),n={push:function(e){t.push(e),a()},filter:function(e){return t=t.filter(e),n}}}});return a.options=i({async:!0,ssr:!1},e),a}(),m=function(){},g=s.forwardRef(function(e,t){var r,n,o,l,u=s.useRef(null),p=s.useState({onScrollCapture:m,onWheelCapture:m,onTouchMoveCapture:m}),g=p[0],y=p[1],v=e.forwardProps,b=e.children,w=e.className,x=e.removeScrollBar,k=e.enabled,C=e.shards,E=e.sideCar,R=e.noRelative,O=e.noIsolation,M=e.inert,S=e.allowPinchZoom,j=e.as,A=e.gapMode,P=a(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),T=(r=[u,t],n=function(e){return r.forEach(function(t){return c(t,e)})},(o=(0,s.useState)(function(){return{value:null,callback:n,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=n,l=o.facade,d(function(){var e=f.get(l);if(e){var t=new Set(e),n=new Set(r),o=l.current;t.forEach(function(e){n.has(e)||c(e,null)}),n.forEach(function(e){t.has(e)||c(e,o)})}f.set(l,r)},[r]),l),D=i(i({},P),g);return s.createElement(s.Fragment,null,k&&s.createElement(E,{sideCar:h,removeScrollBar:x,shards:C,noRelative:R,noIsolation:O,inert:M,setCallbacks:y,allowPinchZoom:!!S,lockRef:u,gapMode:A}),v?s.cloneElement(s.Children.only(b),i(i({},D),{ref:T})):s.createElement(void 0===j?"div":j,i({},D,{className:w,ref:T}),b))});g.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},g.classNames={fullWidth:u,zeroRight:l};var y=function(e){var t=e.sideCar,r=a(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var n=t.read();if(!n)throw Error("Sidecar medium not found");return s.createElement(n,i({},r))};y.isSideCarExport=!0;var v=function(){var e=0,t=null;return{add:function(n){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=o||r.nc;return t&&e.setAttribute("nonce",t),e}())){var i,a;(i=t).styleSheet?i.styleSheet.cssText=n:i.appendChild(document.createTextNode(n)),a=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(a)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},b=function(){var e=v();return function(t,r){s.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&r])}},w=function(){var e=b();return function(t){return e(t.styles,t.dynamic),null}},x={left:0,top:0,right:0,gap:0},k=function(e){return parseInt(e||"",10)||0},C=function(e){var t=window.getComputedStyle(document.body),r=t["padding"===e?"paddingLeft":"marginLeft"],n=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[k(r),k(n),k(o)]},E=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return x;var t=C(e),r=document.documentElement.clientWidth,n=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,n-r+t[2]-t[0])}},R=w(),O="data-scroll-locked",M=function(e,t,r,n){var o=e.left,i=e.top,a=e.right,s=e.gap;return void 0===r&&(r="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(n,";\n   padding-right: ").concat(s,"px ").concat(n,";\n  }\n  body[").concat(O,"] {\n    overflow: hidden ").concat(n,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(n,";"),"margin"===r&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(i,"px;\n    padding-right: ").concat(a,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(s,"px ").concat(n,";\n    "),"padding"===r&&"padding-right: ".concat(s,"px ").concat(n,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(l," {\n    right: ").concat(s,"px ").concat(n,";\n  }\n  \n  .").concat(u," {\n    margin-right: ").concat(s,"px ").concat(n,";\n  }\n  \n  .").concat(l," .").concat(l," {\n    right: 0 ").concat(n,";\n  }\n  \n  .").concat(u," .").concat(u," {\n    margin-right: 0 ").concat(n,";\n  }\n  \n  body[").concat(O,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(s,"px;\n  }\n")},S=function(){var e=parseInt(document.body.getAttribute(O)||"0",10);return isFinite(e)?e:0},j=function(){s.useEffect(function(){return document.body.setAttribute(O,(S()+1).toString()),function(){var e=S()-1;e<=0?document.body.removeAttribute(O):document.body.setAttribute(O,e.toString())}},[])},A=function(e){var t=e.noRelative,r=e.noImportant,n=e.gapMode,o=void 0===n?"margin":n;j();var i=s.useMemo(function(){return E(o)},[o]);return s.createElement(R,{styles:M(i,!t,o,r?"":"!important")})},P=!1;if("undefined"!=typeof window)try{var T=Object.defineProperty({},"passive",{get:function(){return P=!0,!0}});window.addEventListener("test",T,T),window.removeEventListener("test",T,T)}catch(e){P=!1}var D=!!P&&{passive:!1},N=function(e,t){if(!(e instanceof Element))return!1;var r=window.getComputedStyle(e);return"hidden"!==r[t]&&(r.overflowY!==r.overflowX||"TEXTAREA"===e.tagName||"visible"!==r[t])},L=function(e,t){var r=t.ownerDocument,n=t;do{if("undefined"!=typeof ShadowRoot&&n instanceof ShadowRoot&&(n=n.host),F(e,n)){var o=I(e,n);if(o[1]>o[2])return!0}n=n.parentNode}while(n&&n!==r.body);return!1},F=function(e,t){return"v"===e?N(t,"overflowY"):N(t,"overflowX")},I=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},_=function(e,t,r,n,o){var i,a=(i=window.getComputedStyle(t).direction,"h"===e&&"rtl"===i?-1:1),s=a*n,l=r.target,u=t.contains(l),c=!1,d=s>0,f=0,p=0;do{if(!l)break;var h=I(e,l),m=h[0],g=h[1]-h[2]-a*m;(m||g)&&F(e,l)&&(f+=g,p+=m);var y=l.parentNode;l=y&&y.nodeType===Node.DOCUMENT_FRAGMENT_NODE?y.host:y}while(!u&&l!==document.body||u&&(t.contains(l)||t===l));return d&&(o&&1>Math.abs(f)||!o&&s>f)?c=!0:!d&&(o&&1>Math.abs(p)||!o&&-s>p)&&(c=!0),c},q=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},z=function(e){return[e.deltaX,e.deltaY]},G=function(e){return e&&"current"in e?e.current:e},K=0,W=[];let Q=(n=function(e){var t=s.useRef([]),r=s.useRef([0,0]),n=s.useRef(),o=s.useState(K++)[0],i=s.useState(w)[0],a=s.useRef(e);s.useEffect(function(){a.current=e},[e]),s.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,r){if(r||2==arguments.length)for(var n,o=0,i=t.length;o<i;o++)!n&&o in t||(n||(n=Array.prototype.slice.call(t,0,o)),n[o]=t[o]);return e.concat(n||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(G),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var l=s.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!a.current.allowPinchZoom;var o,i=q(e),s=r.current,l="deltaX"in e?e.deltaX:s[0]-i[0],u="deltaY"in e?e.deltaY:s[1]-i[1],c=e.target,d=Math.abs(l)>Math.abs(u)?"h":"v";if("touches"in e&&"h"===d&&"range"===c.type)return!1;var f=L(d,c);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=L(d,c)),!f)return!1;if(!n.current&&"changedTouches"in e&&(l||u)&&(n.current=o),!o)return!0;var p=n.current||o;return _(p,t,e,"h"===p?l:u,!0)},[]),u=s.useCallback(function(e){if(W.length&&W[W.length-1]===i){var r="deltaY"in e?z(e):q(e),n=t.current.filter(function(t){var n;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(n=t.delta,n[0]===r[0]&&n[1]===r[1])})[0];if(n&&n.should){e.cancelable&&e.preventDefault();return}if(!n){var o=(a.current.shards||[]).map(G).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?l(e,o[0]):!a.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),c=s.useCallback(function(e,r,n,o){var i={name:e,delta:r,target:n,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(n)};t.current.push(i),setTimeout(function(){t.current=t.current.filter(function(e){return e!==i})},1)},[]),d=s.useCallback(function(e){r.current=q(e),n.current=void 0},[]),f=s.useCallback(function(t){c(t.type,z(t),t.target,l(t,e.lockRef.current))},[]),p=s.useCallback(function(t){c(t.type,q(t),t.target,l(t,e.lockRef.current))},[]);s.useEffect(function(){return W.push(i),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:p}),document.addEventListener("wheel",u,D),document.addEventListener("touchmove",u,D),document.addEventListener("touchstart",d,D),function(){W=W.filter(function(e){return e!==i}),document.removeEventListener("wheel",u,D),document.removeEventListener("touchmove",u,D),document.removeEventListener("touchstart",d,D)}},[]);var h=e.removeScrollBar,m=e.inert;return s.createElement(s.Fragment,null,m?s.createElement(i,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,h?s.createElement(A,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},h.useMedium(n),y);var B=s.forwardRef(function(e,t){return s.createElement(g,i({},e,{ref:t,sideCar:Q}))});B.classNames=g.classNames;let U=B},45547:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("newspaper",[["path",{d:"M15 18h-5",key:"95g1m2"}],["path",{d:"M18 14h-8",key:"sponae"}],["path",{d:"M4 22h16a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2H8a2 2 0 0 0-2 2v16a2 2 0 0 1-4 0v-9a2 2 0 0 1 2-2h2",key:"39pd36"}],["rect",{width:"8",height:"4",x:"10",y:"6",rx:"1",key:"aywv1n"}]])},46059:(e,t,r)=>{r.d(t,{C:()=>a});var n=r(43210),o=r(98599),i=r(66156),a=e=>{let{present:t,children:r}=e,a=function(e){var t,r;let[o,a]=n.useState(),l=n.useRef(null),u=n.useRef(e),c=n.useRef("none"),[d,f]=(t=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},n.useReducer((e,t)=>r[e][t]??e,t));return n.useEffect(()=>{let e=s(l.current);c.current="mounted"===d?e:"none"},[d]),(0,i.N)(()=>{let t=l.current,r=u.current;if(r!==e){let n=c.current,o=s(t);e?f("MOUNT"):"none"===o||t?.display==="none"?f("UNMOUNT"):r&&n!==o?f("ANIMATION_OUT"):f("UNMOUNT"),u.current=e}},[e,f]),(0,i.N)(()=>{if(o){let e,t=o.ownerDocument.defaultView??window,r=r=>{let n=s(l.current).includes(r.animationName);if(r.target===o&&n&&(f("ANIMATION_END"),!u.current)){let r=o.style.animationFillMode;o.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=r)})}},n=e=>{e.target===o&&(c.current=s(l.current))};return o.addEventListener("animationstart",n),o.addEventListener("animationcancel",r),o.addEventListener("animationend",r),()=>{t.clearTimeout(e),o.removeEventListener("animationstart",n),o.removeEventListener("animationcancel",r),o.removeEventListener("animationend",r)}}f("ANIMATION_END")},[o,f]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:n.useCallback(e=>{l.current=e?getComputedStyle(e):null,a(e)},[])}}(t),l="function"==typeof r?r({present:a.isPresent}):n.Children.only(r),u=(0,o.s)(a.ref,function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(l));return"function"==typeof r||a.isPresent?n.cloneElement(l,{ref:u}):null};function s(e){return e?.animationName||"none"}a.displayName="Presence"},49384:(e,t,r)=>{function n(){for(var e,t,r=0,n="",o=arguments.length;r<o;r++)(e=arguments[r])&&(t=function e(t){var r,n,o="";if("string"==typeof t||"number"==typeof t)o+=t;else if("object"==typeof t)if(Array.isArray(t)){var i=t.length;for(r=0;r<i;r++)t[r]&&(n=e(t[r]))&&(o&&(o+=" "),o+=n)}else for(n in t)t[n]&&(o&&(o+=" "),o+=n);return o}(e))&&(n&&(n+=" "),n+=t);return n}r.d(t,{$:()=>n,A:()=>o});let o=n},51214:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("panel-left",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}]])},61489:(e,t,r)=>{r.d(t,{X:()=>s,k:()=>l});var n=r(31212),o=r(33465),i=r(29604),a=r(62536),s=class extends a.k{#b;#w;#x;#k;#a;#f;#C;constructor(e){super(),this.#C=!1,this.#f=e.defaultOptions,this.setOptions(e.options),this.observers=[],this.#k=e.client,this.#x=this.#k.getQueryCache(),this.queryKey=e.queryKey,this.queryHash=e.queryHash,this.#b=function(e){let t="function"==typeof e.initialData?e.initialData():e.initialData,r=void 0!==t,n=r?"function"==typeof e.initialDataUpdatedAt?e.initialDataUpdatedAt():e.initialDataUpdatedAt:0;return{data:t,dataUpdateCount:0,dataUpdatedAt:r?n??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:r?"success":"pending",fetchStatus:"idle"}}(this.options),this.state=e.state??this.#b,this.scheduleGc()}get meta(){return this.options.meta}get promise(){return this.#a?.promise}setOptions(e){this.options={...this.#f,...e},this.updateGcTime(this.options.gcTime)}optionalRemove(){this.observers.length||"idle"!==this.state.fetchStatus||this.#x.remove(this)}setData(e,t){let r=(0,n.pl)(this.state.data,e,this.options);return this.#s({data:r,type:"success",dataUpdatedAt:t?.updatedAt,manual:t?.manual}),r}setState(e,t){this.#s({type:"setState",state:e,setStateOptions:t})}cancel(e){let t=this.#a?.promise;return this.#a?.cancel(e),t?t.then(n.lQ).catch(n.lQ):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(this.#b)}isActive(){return this.observers.some(e=>!1!==(0,n.Eh)(e.options.enabled,this))}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===n.hT||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStatic(){return this.getObserversCount()>0&&this.observers.some(e=>"static"===(0,n.d2)(e.options.staleTime,this))}isStale(){return this.getObserversCount()>0?this.observers.some(e=>e.getCurrentResult().isStale):void 0===this.state.data||this.state.isInvalidated}isStaleByTime(e=0){return void 0===this.state.data||"static"!==e&&(!!this.state.isInvalidated||!(0,n.j3)(this.state.dataUpdatedAt,e))}onFocus(){let e=this.observers.find(e=>e.shouldFetchOnWindowFocus());e?.refetch({cancelRefetch:!1}),this.#a?.continue()}onOnline(){let e=this.observers.find(e=>e.shouldFetchOnReconnect());e?.refetch({cancelRefetch:!1}),this.#a?.continue()}addObserver(e){this.observers.includes(e)||(this.observers.push(e),this.clearGcTimeout(),this.#x.notify({type:"observerAdded",query:this,observer:e}))}removeObserver(e){this.observers.includes(e)&&(this.observers=this.observers.filter(t=>t!==e),this.observers.length||(this.#a&&(this.#C?this.#a.cancel({revert:!0}):this.#a.cancelRetry()),this.scheduleGc()),this.#x.notify({type:"observerRemoved",query:this,observer:e}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||this.#s({type:"invalidate"})}fetch(e,t){if("idle"!==this.state.fetchStatus){if(void 0!==this.state.data&&t?.cancelRefetch)this.cancel({silent:!0});else if(this.#a)return this.#a.continueRetry(),this.#a.promise}if(e&&this.setOptions(e),!this.options.queryFn){let e=this.observers.find(e=>e.options.queryFn);e&&this.setOptions(e.options)}let r=new AbortController,o=e=>{Object.defineProperty(e,"signal",{enumerable:!0,get:()=>(this.#C=!0,r.signal)})},a=()=>{let e=(0,n.ZM)(this.options,t),r=(()=>{let e={client:this.#k,queryKey:this.queryKey,meta:this.meta};return o(e),e})();return(this.#C=!1,this.options.persister)?this.options.persister(e,r,this):e(r)},s=(()=>{let e={fetchOptions:t,options:this.options,queryKey:this.queryKey,client:this.#k,state:this.state,fetchFn:a};return o(e),e})();this.options.behavior?.onFetch(s,this),this.#w=this.state,("idle"===this.state.fetchStatus||this.state.fetchMeta!==s.fetchOptions?.meta)&&this.#s({type:"fetch",meta:s.fetchOptions?.meta});let l=e=>{(0,i.wm)(e)&&e.silent||this.#s({type:"error",error:e}),(0,i.wm)(e)||(this.#x.config.onError?.(e,this),this.#x.config.onSettled?.(this.state.data,e,this)),this.scheduleGc()};return this.#a=(0,i.II)({initialPromise:t?.initialPromise,fn:s.fetchFn,abort:r.abort.bind(r),onSuccess:e=>{if(void 0===e)return void l(Error(`${this.queryHash} data is undefined`));try{this.setData(e)}catch(e){l(e);return}this.#x.config.onSuccess?.(e,this),this.#x.config.onSettled?.(e,this.state.error,this),this.scheduleGc()},onError:l,onFail:(e,t)=>{this.#s({type:"failed",failureCount:e,error:t})},onPause:()=>{this.#s({type:"pause"})},onContinue:()=>{this.#s({type:"continue"})},retry:s.options.retry,retryDelay:s.options.retryDelay,networkMode:s.options.networkMode,canRun:()=>!0}),this.#a.start()}#s(e){this.state=(t=>{switch(e.type){case"failed":return{...t,fetchFailureCount:e.failureCount,fetchFailureReason:e.error};case"pause":return{...t,fetchStatus:"paused"};case"continue":return{...t,fetchStatus:"fetching"};case"fetch":return{...t,...l(t.data,this.options),fetchMeta:e.meta??null};case"success":return{...t,data:e.data,dataUpdateCount:t.dataUpdateCount+1,dataUpdatedAt:e.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!e.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":let r=e.error;if((0,i.wm)(r)&&r.revert&&this.#w)return{...this.#w,fetchStatus:"idle"};return{...t,error:r,errorUpdateCount:t.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:t.fetchFailureCount+1,fetchFailureReason:r,fetchStatus:"idle",status:"error"};case"invalidate":return{...t,isInvalidated:!0};case"setState":return{...t,...e.state}}})(this.state),o.jG.batch(()=>{this.observers.forEach(e=>{e.onQueryUpdate()}),this.#x.notify({query:this,type:"updated",action:e})})}};function l(e,t){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:(0,i.v_)(t.networkMode)?"fetching":"paused",...void 0===e&&{error:null,status:"pending"}}}},62536:(e,t,r)=>{r.d(t,{k:()=>o});var n=r(31212),o=class{#E;destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),(0,n.gn)(this.gcTime)&&(this.#E=setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(e){this.gcTime=Math.max(this.gcTime||0,e??(n.S$?1/0:3e5))}clearGcTimeout(){this.#E&&(clearTimeout(this.#E),this.#E=void 0)}}},63376:(e,t,r)=>{r.d(t,{Eq:()=>c});var n=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},o=new WeakMap,i=new WeakMap,a={},s=0,l=function(e){return e&&(e.host||l(e.parentNode))},u=function(e,t,r,n){var u=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var r=l(e);return r&&t.contains(r)?r:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});a[r]||(a[r]=new WeakMap);var c=a[r],d=[],f=new Set,p=new Set(u),h=function(e){!e||f.has(e)||(f.add(e),h(e.parentNode))};u.forEach(h);var m=function(e){!e||p.has(e)||Array.prototype.forEach.call(e.children,function(e){if(f.has(e))m(e);else try{var t=e.getAttribute(n),a=null!==t&&"false"!==t,s=(o.get(e)||0)+1,l=(c.get(e)||0)+1;o.set(e,s),c.set(e,l),d.push(e),1===s&&a&&i.set(e,!0),1===l&&e.setAttribute(r,"true"),a||e.setAttribute(n,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return m(t),f.clear(),s++,function(){d.forEach(function(e){var t=o.get(e)-1,a=c.get(e)-1;o.set(e,t),c.set(e,a),t||(i.has(e)||e.removeAttribute(n),i.delete(e)),a||e.removeAttribute(r)}),--s||(o=new WeakMap,o=new WeakMap,i=new WeakMap,a={})}},c=function(e,t,r){void 0===r&&(r="data-aria-hidden");var o=Array.from(Array.isArray(e)?e:[e]),i=t||n(e);return i?(o.push.apply(o,Array.from(i.querySelectorAll("[aria-live], script"))),u(o,i,r,"aria-hidden")):function(){return null}}},65551:(e,t,r)=>{r.d(t,{i:()=>s});var n,o=r(43210),i=r(66156),a=(n||(n=r.t(o,2)))[" useInsertionEffect ".trim().toString()]||i.N;function s({prop:e,defaultProp:t,onChange:r=()=>{},caller:n}){let[i,s,l]=function({defaultProp:e,onChange:t}){let[r,n]=o.useState(e),i=o.useRef(r),s=o.useRef(t);return a(()=>{s.current=t},[t]),o.useEffect(()=>{i.current!==r&&(s.current?.(r),i.current=r)},[r,i]),[r,n,s]}({defaultProp:t,onChange:r}),u=void 0!==e,c=u?e:i;{let t=o.useRef(void 0!==e);o.useEffect(()=>{let e=t.current;if(e!==u){let t=u?"controlled":"uncontrolled";console.warn(`${n} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=u},[u,n])}return[c,o.useCallback(t=>{if(u){let r="function"==typeof t?t(e):t;r!==e&&l.current?.(r)}else s(t)},[u,e,s,l])]}Symbol("RADIX:SYNC_STATE")},66156:(e,t,r)=>{r.d(t,{N:()=>o});var n=r(43210),o=globalThis?.document?n.useLayoutEffect:()=>{}},70569:(e,t,r)=>{r.d(t,{m:()=>n});function n(e,t,{checkForDefaultPrevented:r=!0}={}){return function(n){if(e?.(n),!1===r||!n.defaultPrevented)return t?.(n)}}},73458:(e,t,r)=>{function n(){let e,t,r=new Promise((r,n)=>{e=r,t=n});function n(e){Object.assign(r,e),delete r.resolve,delete r.reject}return r.status="pending",r.catch(()=>{}),r.resolve=t=>{n({status:"fulfilled",value:t}),e(t)},r.reject=e=>{n({status:"rejected",reason:e}),t(e)},r}r.d(t,{T:()=>n})},82348:(e,t,r)=>{r.d(t,{QP:()=>eu});let n=e=>{let t=s(e),{conflictingClassGroups:r,conflictingClassGroupModifiers:n}=e;return{getClassGroupId:e=>{let r=e.split("-");return""===r[0]&&1!==r.length&&r.shift(),o(r,t)||a(e)},getConflictingClassGroupIds:(e,t)=>{let o=r[e]||[];return t&&n[e]?[...o,...n[e]]:o}}},o=(e,t)=>{if(0===e.length)return t.classGroupId;let r=e[0],n=t.nextPart.get(r),i=n?o(e.slice(1),n):void 0;if(i)return i;if(0===t.validators.length)return;let a=e.join("-");return t.validators.find(({validator:e})=>e(a))?.classGroupId},i=/^\[(.+)\]$/,a=e=>{if(i.test(e)){let t=i.exec(e)[1],r=t?.substring(0,t.indexOf(":"));if(r)return"arbitrary.."+r}},s=e=>{let{theme:t,classGroups:r}=e,n={nextPart:new Map,validators:[]};for(let e in r)l(r[e],n,e,t);return n},l=(e,t,r,n)=>{e.forEach(e=>{if("string"==typeof e){(""===e?t:u(t,e)).classGroupId=r;return}if("function"==typeof e)return c(e)?void l(e(n),t,r,n):void t.validators.push({validator:e,classGroupId:r});Object.entries(e).forEach(([e,o])=>{l(o,u(t,e),r,n)})})},u=(e,t)=>{let r=e;return t.split("-").forEach(e=>{r.nextPart.has(e)||r.nextPart.set(e,{nextPart:new Map,validators:[]}),r=r.nextPart.get(e)}),r},c=e=>e.isThemeGetter,d=e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let t=0,r=new Map,n=new Map,o=(o,i)=>{r.set(o,i),++t>e&&(t=0,n=r,r=new Map)};return{get(e){let t=r.get(e);return void 0!==t?t:void 0!==(t=n.get(e))?(o(e,t),t):void 0},set(e,t){r.has(e)?r.set(e,t):o(e,t)}}},f=e=>{let{prefix:t,experimentalParseClassName:r}=e,n=e=>{let t,r=[],n=0,o=0,i=0;for(let a=0;a<e.length;a++){let s=e[a];if(0===n&&0===o){if(":"===s){r.push(e.slice(i,a)),i=a+1;continue}if("/"===s){t=a;continue}}"["===s?n++:"]"===s?n--:"("===s?o++:")"===s&&o--}let a=0===r.length?e:e.substring(i),s=p(a);return{modifiers:r,hasImportantModifier:s!==a,baseClassName:s,maybePostfixModifierPosition:t&&t>i?t-i:void 0}};if(t){let e=t+":",r=n;n=t=>t.startsWith(e)?r(t.substring(e.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:t,maybePostfixModifierPosition:void 0}}if(r){let e=n;n=t=>r({className:t,parseClassName:e})}return n},p=e=>e.endsWith("!")?e.substring(0,e.length-1):e.startsWith("!")?e.substring(1):e,h=e=>{let t=Object.fromEntries(e.orderSensitiveModifiers.map(e=>[e,!0]));return e=>{if(e.length<=1)return e;let r=[],n=[];return e.forEach(e=>{"["===e[0]||t[e]?(r.push(...n.sort(),e),n=[]):n.push(e)}),r.push(...n.sort()),r}},m=e=>({cache:d(e.cacheSize),parseClassName:f(e),sortModifiers:h(e),...n(e)}),g=/\s+/,y=(e,t)=>{let{parseClassName:r,getClassGroupId:n,getConflictingClassGroupIds:o,sortModifiers:i}=t,a=[],s=e.trim().split(g),l="";for(let e=s.length-1;e>=0;e-=1){let t=s[e],{isExternal:u,modifiers:c,hasImportantModifier:d,baseClassName:f,maybePostfixModifierPosition:p}=r(t);if(u){l=t+(l.length>0?" "+l:l);continue}let h=!!p,m=n(h?f.substring(0,p):f);if(!m){if(!h||!(m=n(f))){l=t+(l.length>0?" "+l:l);continue}h=!1}let g=i(c).join(":"),y=d?g+"!":g,v=y+m;if(a.includes(v))continue;a.push(v);let b=o(m,h);for(let e=0;e<b.length;++e){let t=b[e];a.push(y+t)}l=t+(l.length>0?" "+l:l)}return l};function v(){let e,t,r=0,n="";for(;r<arguments.length;)(e=arguments[r++])&&(t=b(e))&&(n&&(n+=" "),n+=t);return n}let b=e=>{let t;if("string"==typeof e)return e;let r="";for(let n=0;n<e.length;n++)e[n]&&(t=b(e[n]))&&(r&&(r+=" "),r+=t);return r},w=e=>{let t=t=>t[e]||[];return t.isThemeGetter=!0,t},x=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,k=/^\((?:(\w[\w-]*):)?(.+)\)$/i,C=/^\d+\/\d+$/,E=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,R=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,O=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,M=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,S=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,j=e=>C.test(e),A=e=>!!e&&!Number.isNaN(Number(e)),P=e=>!!e&&Number.isInteger(Number(e)),T=e=>e.endsWith("%")&&A(e.slice(0,-1)),D=e=>E.test(e),N=()=>!0,L=e=>R.test(e)&&!O.test(e),F=()=>!1,I=e=>M.test(e),_=e=>S.test(e),q=e=>!G(e)&&!$(e),z=e=>ee(e,eo,F),G=e=>x.test(e),K=e=>ee(e,ei,L),W=e=>ee(e,ea,A),Q=e=>ee(e,er,F),B=e=>ee(e,en,_),U=e=>ee(e,el,I),$=e=>k.test(e),H=e=>et(e,ei),V=e=>et(e,es),Z=e=>et(e,er),X=e=>et(e,eo),Y=e=>et(e,en),J=e=>et(e,el,!0),ee=(e,t,r)=>{let n=x.exec(e);return!!n&&(n[1]?t(n[1]):r(n[2]))},et=(e,t,r=!1)=>{let n=k.exec(e);return!!n&&(n[1]?t(n[1]):r)},er=e=>"position"===e||"percentage"===e,en=e=>"image"===e||"url"===e,eo=e=>"length"===e||"size"===e||"bg-size"===e,ei=e=>"length"===e,ea=e=>"number"===e,es=e=>"family-name"===e,el=e=>"shadow"===e;Symbol.toStringTag;let eu=function(e,...t){let r,n,o,i=function(s){return n=(r=m(t.reduce((e,t)=>t(e),e()))).cache.get,o=r.cache.set,i=a,a(s)};function a(e){let t=n(e);if(t)return t;let i=y(e,r);return o(e,i),i}return function(){return i(v.apply(null,arguments))}}(()=>{let e=w("color"),t=w("font"),r=w("text"),n=w("font-weight"),o=w("tracking"),i=w("leading"),a=w("breakpoint"),s=w("container"),l=w("spacing"),u=w("radius"),c=w("shadow"),d=w("inset-shadow"),f=w("text-shadow"),p=w("drop-shadow"),h=w("blur"),m=w("perspective"),g=w("aspect"),y=w("ease"),v=w("animate"),b=()=>["auto","avoid","all","avoid-page","page","left","right","column"],x=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],k=()=>[...x(),$,G],C=()=>["auto","hidden","clip","visible","scroll"],E=()=>["auto","contain","none"],R=()=>[$,G,l],O=()=>[j,"full","auto",...R()],M=()=>[P,"none","subgrid",$,G],S=()=>["auto",{span:["full",P,$,G]},P,$,G],L=()=>[P,"auto",$,G],F=()=>["auto","min","max","fr",$,G],I=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],_=()=>["start","end","center","stretch","center-safe","end-safe"],ee=()=>["auto",...R()],et=()=>[j,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...R()],er=()=>[e,$,G],en=()=>[...x(),Z,Q,{position:[$,G]}],eo=()=>["no-repeat",{repeat:["","x","y","space","round"]}],ei=()=>["auto","cover","contain",X,z,{size:[$,G]}],ea=()=>[T,H,K],es=()=>["","none","full",u,$,G],el=()=>["",A,H,K],eu=()=>["solid","dashed","dotted","double"],ec=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],ed=()=>[A,T,Z,Q],ef=()=>["","none",h,$,G],ep=()=>["none",A,$,G],eh=()=>["none",A,$,G],em=()=>[A,$,G],eg=()=>[j,"full",...R()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[D],breakpoint:[D],color:[N],container:[D],"drop-shadow":[D],ease:["in","out","in-out"],font:[q],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[D],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[D],shadow:[D],spacing:["px",A],text:[D],"text-shadow":[D],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",j,G,$,g]}],container:["container"],columns:[{columns:[A,G,$,s]}],"break-after":[{"break-after":b()}],"break-before":[{"break-before":b()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:k()}],overflow:[{overflow:C()}],"overflow-x":[{"overflow-x":C()}],"overflow-y":[{"overflow-y":C()}],overscroll:[{overscroll:E()}],"overscroll-x":[{"overscroll-x":E()}],"overscroll-y":[{"overscroll-y":E()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:O()}],"inset-x":[{"inset-x":O()}],"inset-y":[{"inset-y":O()}],start:[{start:O()}],end:[{end:O()}],top:[{top:O()}],right:[{right:O()}],bottom:[{bottom:O()}],left:[{left:O()}],visibility:["visible","invisible","collapse"],z:[{z:[P,"auto",$,G]}],basis:[{basis:[j,"full","auto",s,...R()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[A,j,"auto","initial","none",G]}],grow:[{grow:["",A,$,G]}],shrink:[{shrink:["",A,$,G]}],order:[{order:[P,"first","last","none",$,G]}],"grid-cols":[{"grid-cols":M()}],"col-start-end":[{col:S()}],"col-start":[{"col-start":L()}],"col-end":[{"col-end":L()}],"grid-rows":[{"grid-rows":M()}],"row-start-end":[{row:S()}],"row-start":[{"row-start":L()}],"row-end":[{"row-end":L()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":F()}],"auto-rows":[{"auto-rows":F()}],gap:[{gap:R()}],"gap-x":[{"gap-x":R()}],"gap-y":[{"gap-y":R()}],"justify-content":[{justify:[...I(),"normal"]}],"justify-items":[{"justify-items":[..._(),"normal"]}],"justify-self":[{"justify-self":["auto",..._()]}],"align-content":[{content:["normal",...I()]}],"align-items":[{items:[..._(),{baseline:["","last"]}]}],"align-self":[{self:["auto",..._(),{baseline:["","last"]}]}],"place-content":[{"place-content":I()}],"place-items":[{"place-items":[..._(),"baseline"]}],"place-self":[{"place-self":["auto",..._()]}],p:[{p:R()}],px:[{px:R()}],py:[{py:R()}],ps:[{ps:R()}],pe:[{pe:R()}],pt:[{pt:R()}],pr:[{pr:R()}],pb:[{pb:R()}],pl:[{pl:R()}],m:[{m:ee()}],mx:[{mx:ee()}],my:[{my:ee()}],ms:[{ms:ee()}],me:[{me:ee()}],mt:[{mt:ee()}],mr:[{mr:ee()}],mb:[{mb:ee()}],ml:[{ml:ee()}],"space-x":[{"space-x":R()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":R()}],"space-y-reverse":["space-y-reverse"],size:[{size:et()}],w:[{w:[s,"screen",...et()]}],"min-w":[{"min-w":[s,"screen","none",...et()]}],"max-w":[{"max-w":[s,"screen","none","prose",{screen:[a]},...et()]}],h:[{h:["screen","lh",...et()]}],"min-h":[{"min-h":["screen","lh","none",...et()]}],"max-h":[{"max-h":["screen","lh",...et()]}],"font-size":[{text:["base",r,H,K]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[n,$,W]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",T,G]}],"font-family":[{font:[V,G,t]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[o,$,G]}],"line-clamp":[{"line-clamp":[A,"none",$,W]}],leading:[{leading:[i,...R()]}],"list-image":[{"list-image":["none",$,G]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",$,G]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:er()}],"text-color":[{text:er()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...eu(),"wavy"]}],"text-decoration-thickness":[{decoration:[A,"from-font","auto",$,K]}],"text-decoration-color":[{decoration:er()}],"underline-offset":[{"underline-offset":[A,"auto",$,G]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:R()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",$,G]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",$,G]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:en()}],"bg-repeat":[{bg:eo()}],"bg-size":[{bg:ei()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},P,$,G],radial:["",$,G],conic:[P,$,G]},Y,B]}],"bg-color":[{bg:er()}],"gradient-from-pos":[{from:ea()}],"gradient-via-pos":[{via:ea()}],"gradient-to-pos":[{to:ea()}],"gradient-from":[{from:er()}],"gradient-via":[{via:er()}],"gradient-to":[{to:er()}],rounded:[{rounded:es()}],"rounded-s":[{"rounded-s":es()}],"rounded-e":[{"rounded-e":es()}],"rounded-t":[{"rounded-t":es()}],"rounded-r":[{"rounded-r":es()}],"rounded-b":[{"rounded-b":es()}],"rounded-l":[{"rounded-l":es()}],"rounded-ss":[{"rounded-ss":es()}],"rounded-se":[{"rounded-se":es()}],"rounded-ee":[{"rounded-ee":es()}],"rounded-es":[{"rounded-es":es()}],"rounded-tl":[{"rounded-tl":es()}],"rounded-tr":[{"rounded-tr":es()}],"rounded-br":[{"rounded-br":es()}],"rounded-bl":[{"rounded-bl":es()}],"border-w":[{border:el()}],"border-w-x":[{"border-x":el()}],"border-w-y":[{"border-y":el()}],"border-w-s":[{"border-s":el()}],"border-w-e":[{"border-e":el()}],"border-w-t":[{"border-t":el()}],"border-w-r":[{"border-r":el()}],"border-w-b":[{"border-b":el()}],"border-w-l":[{"border-l":el()}],"divide-x":[{"divide-x":el()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":el()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...eu(),"hidden","none"]}],"divide-style":[{divide:[...eu(),"hidden","none"]}],"border-color":[{border:er()}],"border-color-x":[{"border-x":er()}],"border-color-y":[{"border-y":er()}],"border-color-s":[{"border-s":er()}],"border-color-e":[{"border-e":er()}],"border-color-t":[{"border-t":er()}],"border-color-r":[{"border-r":er()}],"border-color-b":[{"border-b":er()}],"border-color-l":[{"border-l":er()}],"divide-color":[{divide:er()}],"outline-style":[{outline:[...eu(),"none","hidden"]}],"outline-offset":[{"outline-offset":[A,$,G]}],"outline-w":[{outline:["",A,H,K]}],"outline-color":[{outline:er()}],shadow:[{shadow:["","none",c,J,U]}],"shadow-color":[{shadow:er()}],"inset-shadow":[{"inset-shadow":["none",d,J,U]}],"inset-shadow-color":[{"inset-shadow":er()}],"ring-w":[{ring:el()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:er()}],"ring-offset-w":[{"ring-offset":[A,K]}],"ring-offset-color":[{"ring-offset":er()}],"inset-ring-w":[{"inset-ring":el()}],"inset-ring-color":[{"inset-ring":er()}],"text-shadow":[{"text-shadow":["none",f,J,U]}],"text-shadow-color":[{"text-shadow":er()}],opacity:[{opacity:[A,$,G]}],"mix-blend":[{"mix-blend":[...ec(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":ec()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[A]}],"mask-image-linear-from-pos":[{"mask-linear-from":ed()}],"mask-image-linear-to-pos":[{"mask-linear-to":ed()}],"mask-image-linear-from-color":[{"mask-linear-from":er()}],"mask-image-linear-to-color":[{"mask-linear-to":er()}],"mask-image-t-from-pos":[{"mask-t-from":ed()}],"mask-image-t-to-pos":[{"mask-t-to":ed()}],"mask-image-t-from-color":[{"mask-t-from":er()}],"mask-image-t-to-color":[{"mask-t-to":er()}],"mask-image-r-from-pos":[{"mask-r-from":ed()}],"mask-image-r-to-pos":[{"mask-r-to":ed()}],"mask-image-r-from-color":[{"mask-r-from":er()}],"mask-image-r-to-color":[{"mask-r-to":er()}],"mask-image-b-from-pos":[{"mask-b-from":ed()}],"mask-image-b-to-pos":[{"mask-b-to":ed()}],"mask-image-b-from-color":[{"mask-b-from":er()}],"mask-image-b-to-color":[{"mask-b-to":er()}],"mask-image-l-from-pos":[{"mask-l-from":ed()}],"mask-image-l-to-pos":[{"mask-l-to":ed()}],"mask-image-l-from-color":[{"mask-l-from":er()}],"mask-image-l-to-color":[{"mask-l-to":er()}],"mask-image-x-from-pos":[{"mask-x-from":ed()}],"mask-image-x-to-pos":[{"mask-x-to":ed()}],"mask-image-x-from-color":[{"mask-x-from":er()}],"mask-image-x-to-color":[{"mask-x-to":er()}],"mask-image-y-from-pos":[{"mask-y-from":ed()}],"mask-image-y-to-pos":[{"mask-y-to":ed()}],"mask-image-y-from-color":[{"mask-y-from":er()}],"mask-image-y-to-color":[{"mask-y-to":er()}],"mask-image-radial":[{"mask-radial":[$,G]}],"mask-image-radial-from-pos":[{"mask-radial-from":ed()}],"mask-image-radial-to-pos":[{"mask-radial-to":ed()}],"mask-image-radial-from-color":[{"mask-radial-from":er()}],"mask-image-radial-to-color":[{"mask-radial-to":er()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":x()}],"mask-image-conic-pos":[{"mask-conic":[A]}],"mask-image-conic-from-pos":[{"mask-conic-from":ed()}],"mask-image-conic-to-pos":[{"mask-conic-to":ed()}],"mask-image-conic-from-color":[{"mask-conic-from":er()}],"mask-image-conic-to-color":[{"mask-conic-to":er()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:en()}],"mask-repeat":[{mask:eo()}],"mask-size":[{mask:ei()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",$,G]}],filter:[{filter:["","none",$,G]}],blur:[{blur:ef()}],brightness:[{brightness:[A,$,G]}],contrast:[{contrast:[A,$,G]}],"drop-shadow":[{"drop-shadow":["","none",p,J,U]}],"drop-shadow-color":[{"drop-shadow":er()}],grayscale:[{grayscale:["",A,$,G]}],"hue-rotate":[{"hue-rotate":[A,$,G]}],invert:[{invert:["",A,$,G]}],saturate:[{saturate:[A,$,G]}],sepia:[{sepia:["",A,$,G]}],"backdrop-filter":[{"backdrop-filter":["","none",$,G]}],"backdrop-blur":[{"backdrop-blur":ef()}],"backdrop-brightness":[{"backdrop-brightness":[A,$,G]}],"backdrop-contrast":[{"backdrop-contrast":[A,$,G]}],"backdrop-grayscale":[{"backdrop-grayscale":["",A,$,G]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[A,$,G]}],"backdrop-invert":[{"backdrop-invert":["",A,$,G]}],"backdrop-opacity":[{"backdrop-opacity":[A,$,G]}],"backdrop-saturate":[{"backdrop-saturate":[A,$,G]}],"backdrop-sepia":[{"backdrop-sepia":["",A,$,G]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":R()}],"border-spacing-x":[{"border-spacing-x":R()}],"border-spacing-y":[{"border-spacing-y":R()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",$,G]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[A,"initial",$,G]}],ease:[{ease:["linear","initial",y,$,G]}],delay:[{delay:[A,$,G]}],animate:[{animate:["none",v,$,G]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[m,$,G]}],"perspective-origin":[{"perspective-origin":k()}],rotate:[{rotate:ep()}],"rotate-x":[{"rotate-x":ep()}],"rotate-y":[{"rotate-y":ep()}],"rotate-z":[{"rotate-z":ep()}],scale:[{scale:eh()}],"scale-x":[{"scale-x":eh()}],"scale-y":[{"scale-y":eh()}],"scale-z":[{"scale-z":eh()}],"scale-3d":["scale-3d"],skew:[{skew:em()}],"skew-x":[{"skew-x":em()}],"skew-y":[{"skew-y":em()}],transform:[{transform:[$,G,"","none","gpu","cpu"]}],"transform-origin":[{origin:k()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:eg()}],"translate-x":[{"translate-x":eg()}],"translate-y":[{"translate-y":eg()}],"translate-z":[{"translate-z":eg()}],"translate-none":["translate-none"],accent:[{accent:er()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:er()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",$,G]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":R()}],"scroll-mx":[{"scroll-mx":R()}],"scroll-my":[{"scroll-my":R()}],"scroll-ms":[{"scroll-ms":R()}],"scroll-me":[{"scroll-me":R()}],"scroll-mt":[{"scroll-mt":R()}],"scroll-mr":[{"scroll-mr":R()}],"scroll-mb":[{"scroll-mb":R()}],"scroll-ml":[{"scroll-ml":R()}],"scroll-p":[{"scroll-p":R()}],"scroll-px":[{"scroll-px":R()}],"scroll-py":[{"scroll-py":R()}],"scroll-ps":[{"scroll-ps":R()}],"scroll-pe":[{"scroll-pe":R()}],"scroll-pt":[{"scroll-pt":R()}],"scroll-pr":[{"scroll-pr":R()}],"scroll-pb":[{"scroll-pb":R()}],"scroll-pl":[{"scroll-pl":R()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",$,G]}],fill:[{fill:["none",...er()]}],"stroke-w":[{stroke:[A,H,K,W]}],stroke:[{stroke:["none",...er()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}})},83002:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("image-plus",[["path",{d:"M16 5h6",key:"1vod17"}],["path",{d:"M19 2v6",key:"4bpg5p"}],["path",{d:"M21 11.5V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h7.5",key:"1ue2ih"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}]])},92930:(e,t,r)=>{r.d(t,{UC:()=>tn,q7:()=>ti,JU:()=>to,ZL:()=>tr,bL:()=>te,wv:()=>ta,l9:()=>tt});var n=r(43210),o=r(70569),i=r(98599),a=r(11273),s=r(65551),l=r(14163),u=r(8730),c=r(60687);function d(e){let t=e+"CollectionProvider",[r,o]=(0,a.A)(t),[s,l]=r(t,{collectionRef:{current:null},itemMap:new Map}),d=e=>{let{scope:t,children:r}=e,o=n.useRef(null),i=n.useRef(new Map).current;return(0,c.jsx)(s,{scope:t,itemMap:i,collectionRef:o,children:r})};d.displayName=t;let f=e+"CollectionSlot",p=(0,u.TL)(f),h=n.forwardRef((e,t)=>{let{scope:r,children:n}=e,o=l(f,r),a=(0,i.s)(t,o.collectionRef);return(0,c.jsx)(p,{ref:a,children:n})});h.displayName=f;let m=e+"CollectionItemSlot",g="data-radix-collection-item",y=(0,u.TL)(m),v=n.forwardRef((e,t)=>{let{scope:r,children:o,...a}=e,s=n.useRef(null),u=(0,i.s)(t,s),d=l(m,r);return n.useEffect(()=>(d.itemMap.set(s,{ref:s,...a}),()=>void d.itemMap.delete(s))),(0,c.jsx)(y,{...{[g]:""},ref:u,children:o})});return v.displayName=m,[{Provider:d,Slot:h,ItemSlot:v},function(t){let r=l(e+"CollectionConsumer",t);return n.useCallback(()=>{let e=r.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll(`[${g}]`));return Array.from(r.itemMap.values()).sort((e,r)=>t.indexOf(e.ref.current)-t.indexOf(r.ref.current))},[r.collectionRef,r.itemMap])},o]}var f=new WeakMap;function p(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let r=function(e,t){let r=e.length,n=h(t),o=n>=0?n:r+n;return o<0||o>=r?-1:o}(e,t);return -1===r?void 0:e[r]}function h(e){return e!=e||0===e?0:Math.trunc(e)}var m=n.createContext(void 0);function g(e){let t=n.useContext(m);return e||t||"ltr"}var y=r(31355),v=r(1359),b=r(32547),w=r(96963),x=r(38674),k=r(25028),C=r(46059),E=r(13495),R="rovingFocusGroup.onEntryFocus",O={bubbles:!1,cancelable:!0},M="RovingFocusGroup",[S,j,A]=d(M),[P,T]=(0,a.A)(M,[A]),[D,N]=P(M),L=n.forwardRef((e,t)=>(0,c.jsx)(S.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,c.jsx)(S.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,c.jsx)(F,{...e,ref:t})})}));L.displayName=M;var F=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,orientation:a,loop:u=!1,dir:d,currentTabStopId:f,defaultCurrentTabStopId:p,onCurrentTabStopIdChange:h,onEntryFocus:m,preventScrollOnEntryFocus:y=!1,...v}=e,b=n.useRef(null),w=(0,i.s)(t,b),x=g(d),[k,C]=(0,s.i)({prop:f,defaultProp:p??null,onChange:h,caller:M}),[S,A]=n.useState(!1),P=(0,E.c)(m),T=j(r),N=n.useRef(!1),[L,F]=n.useState(0);return n.useEffect(()=>{let e=b.current;if(e)return e.addEventListener(R,P),()=>e.removeEventListener(R,P)},[P]),(0,c.jsx)(D,{scope:r,orientation:a,dir:x,loop:u,currentTabStopId:k,onItemFocus:n.useCallback(e=>C(e),[C]),onItemShiftTab:n.useCallback(()=>A(!0),[]),onFocusableItemAdd:n.useCallback(()=>F(e=>e+1),[]),onFocusableItemRemove:n.useCallback(()=>F(e=>e-1),[]),children:(0,c.jsx)(l.sG.div,{tabIndex:S||0===L?-1:0,"data-orientation":a,...v,ref:w,style:{outline:"none",...e.style},onMouseDown:(0,o.m)(e.onMouseDown,()=>{N.current=!0}),onFocus:(0,o.m)(e.onFocus,e=>{let t=!N.current;if(e.target===e.currentTarget&&t&&!S){let t=new CustomEvent(R,O);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=T().filter(e=>e.focusable);z([e.find(e=>e.active),e.find(e=>e.id===k),...e].filter(Boolean).map(e=>e.ref.current),y)}}N.current=!1}),onBlur:(0,o.m)(e.onBlur,()=>A(!1))})})}),I="RovingFocusGroupItem",_=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,focusable:i=!0,active:a=!1,tabStopId:s,children:u,...d}=e,f=(0,w.B)(),p=s||f,h=N(I,r),m=h.currentTabStopId===p,g=j(r),{onFocusableItemAdd:y,onFocusableItemRemove:v,currentTabStopId:b}=h;return n.useEffect(()=>{if(i)return y(),()=>v()},[i,y,v]),(0,c.jsx)(S.ItemSlot,{scope:r,id:p,focusable:i,active:a,children:(0,c.jsx)(l.sG.span,{tabIndex:m?0:-1,"data-orientation":h.orientation,...d,ref:t,onMouseDown:(0,o.m)(e.onMouseDown,e=>{i?h.onItemFocus(p):e.preventDefault()}),onFocus:(0,o.m)(e.onFocus,()=>h.onItemFocus(p)),onKeyDown:(0,o.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void h.onItemShiftTab();if(e.target!==e.currentTarget)return;let t=function(e,t,r){var n;let o=(n=e.key,"rtl"!==r?n:"ArrowLeft"===n?"ArrowRight":"ArrowRight"===n?"ArrowLeft":n);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return q[o]}(e,h.orientation,h.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let r=g().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)r.reverse();else if("prev"===t||"next"===t){"prev"===t&&r.reverse();let n=r.indexOf(e.currentTarget);r=h.loop?function(e,t){return e.map((r,n)=>e[(t+n)%e.length])}(r,n+1):r.slice(n+1)}setTimeout(()=>z(r))}}),children:"function"==typeof u?u({isCurrentTabStop:m,hasTabStop:null!=b}):u})})});_.displayName=I;var q={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function z(e,t=!1){let r=document.activeElement;for(let n of e)if(n===r||(n.focus({preventScroll:t}),document.activeElement!==r))return}var G=r(63376),K=r(42247),W=["Enter"," "],Q=["ArrowUp","PageDown","End"],B=["ArrowDown","PageUp","Home",...Q],U={ltr:[...W,"ArrowRight"],rtl:[...W,"ArrowLeft"]},$={ltr:["ArrowLeft"],rtl:["ArrowRight"]},H="Menu",[V,Z,X]=d(H),[Y,J]=(0,a.A)(H,[X,x.Bk,T]),ee=(0,x.Bk)(),et=T(),[er,en]=Y(H),[eo,ei]=Y(H),ea=e=>{let{__scopeMenu:t,open:r=!1,children:o,dir:i,onOpenChange:a,modal:s=!0}=e,l=ee(t),[u,d]=n.useState(null),f=n.useRef(!1),p=(0,E.c)(a),h=g(i);return n.useEffect(()=>{let e=()=>{f.current=!0,document.addEventListener("pointerdown",t,{capture:!0,once:!0}),document.addEventListener("pointermove",t,{capture:!0,once:!0})},t=()=>f.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",t,{capture:!0}),document.removeEventListener("pointermove",t,{capture:!0})}},[]),(0,c.jsx)(x.bL,{...l,children:(0,c.jsx)(er,{scope:t,open:r,onOpenChange:p,content:u,onContentChange:d,children:(0,c.jsx)(eo,{scope:t,onClose:n.useCallback(()=>p(!1),[p]),isUsingKeyboardRef:f,dir:h,modal:s,children:o})})})};ea.displayName=H;var es=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e,o=ee(r);return(0,c.jsx)(x.Mz,{...o,...n,ref:t})});es.displayName="MenuAnchor";var el="MenuPortal",[eu,ec]=Y(el,{forceMount:void 0}),ed=e=>{let{__scopeMenu:t,forceMount:r,children:n,container:o}=e,i=en(el,t);return(0,c.jsx)(eu,{scope:t,forceMount:r,children:(0,c.jsx)(C.C,{present:r||i.open,children:(0,c.jsx)(k.Z,{asChild:!0,container:o,children:n})})})};ed.displayName=el;var ef="MenuContent",[ep,eh]=Y(ef),em=n.forwardRef((e,t)=>{let r=ec(ef,e.__scopeMenu),{forceMount:n=r.forceMount,...o}=e,i=en(ef,e.__scopeMenu),a=ei(ef,e.__scopeMenu);return(0,c.jsx)(V.Provider,{scope:e.__scopeMenu,children:(0,c.jsx)(C.C,{present:n||i.open,children:(0,c.jsx)(V.Slot,{scope:e.__scopeMenu,children:a.modal?(0,c.jsx)(eg,{...o,ref:t}):(0,c.jsx)(ey,{...o,ref:t})})})})}),eg=n.forwardRef((e,t)=>{let r=en(ef,e.__scopeMenu),a=n.useRef(null),s=(0,i.s)(t,a);return n.useEffect(()=>{let e=a.current;if(e)return(0,G.Eq)(e)},[]),(0,c.jsx)(eb,{...e,ref:s,trapFocus:r.open,disableOutsidePointerEvents:r.open,disableOutsideScroll:!0,onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>r.onOpenChange(!1)})}),ey=n.forwardRef((e,t)=>{let r=en(ef,e.__scopeMenu);return(0,c.jsx)(eb,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>r.onOpenChange(!1)})}),ev=(0,u.TL)("MenuContent.ScrollLock"),eb=n.forwardRef((e,t)=>{let{__scopeMenu:r,loop:a=!1,trapFocus:s,onOpenAutoFocus:l,onCloseAutoFocus:u,disableOutsidePointerEvents:d,onEntryFocus:f,onEscapeKeyDown:p,onPointerDownOutside:h,onFocusOutside:m,onInteractOutside:g,onDismiss:w,disableOutsideScroll:k,...C}=e,E=en(ef,r),R=ei(ef,r),O=ee(r),M=et(r),S=Z(r),[j,A]=n.useState(null),P=n.useRef(null),T=(0,i.s)(t,P,E.onContentChange),D=n.useRef(0),N=n.useRef(""),F=n.useRef(0),I=n.useRef(null),_=n.useRef("right"),q=n.useRef(0),z=k?K.A:n.Fragment,G=e=>{let t=N.current+e,r=S().filter(e=>!e.disabled),n=document.activeElement,o=r.find(e=>e.ref.current===n)?.textValue,i=function(e,t,r){var n;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,i=r?e.indexOf(r):-1,a=(n=Math.max(i,0),e.map((t,r)=>e[(n+r)%e.length]));1===o.length&&(a=a.filter(e=>e!==r));let s=a.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return s!==r?s:void 0}(r.map(e=>e.textValue),t,o),a=r.find(e=>e.textValue===i)?.ref.current;!function e(t){N.current=t,window.clearTimeout(D.current),""!==t&&(D.current=window.setTimeout(()=>e(""),1e3))}(t),a&&setTimeout(()=>a.focus())};n.useEffect(()=>()=>window.clearTimeout(D.current),[]),(0,v.Oh)();let W=n.useCallback(e=>_.current===I.current?.side&&function(e,t){return!!t&&function(e,t){let{x:r,y:n}=e,o=!1;for(let e=0,i=t.length-1;e<t.length;i=e++){let a=t[e],s=t[i],l=a.x,u=a.y,c=s.x,d=s.y;u>n!=d>n&&r<(c-l)*(n-u)/(d-u)+l&&(o=!o)}return o}({x:e.clientX,y:e.clientY},t)}(e,I.current?.area),[]);return(0,c.jsx)(ep,{scope:r,searchRef:N,onItemEnter:n.useCallback(e=>{W(e)&&e.preventDefault()},[W]),onItemLeave:n.useCallback(e=>{W(e)||(P.current?.focus(),A(null))},[W]),onTriggerLeave:n.useCallback(e=>{W(e)&&e.preventDefault()},[W]),pointerGraceTimerRef:F,onPointerGraceIntentChange:n.useCallback(e=>{I.current=e},[]),children:(0,c.jsx)(z,{...k?{as:ev,allowPinchZoom:!0}:void 0,children:(0,c.jsx)(b.n,{asChild:!0,trapped:s,onMountAutoFocus:(0,o.m)(l,e=>{e.preventDefault(),P.current?.focus({preventScroll:!0})}),onUnmountAutoFocus:u,children:(0,c.jsx)(y.qW,{asChild:!0,disableOutsidePointerEvents:d,onEscapeKeyDown:p,onPointerDownOutside:h,onFocusOutside:m,onInteractOutside:g,onDismiss:w,children:(0,c.jsx)(L,{asChild:!0,...M,dir:R.dir,orientation:"vertical",loop:a,currentTabStopId:j,onCurrentTabStopIdChange:A,onEntryFocus:(0,o.m)(f,e=>{R.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,c.jsx)(x.UC,{role:"menu","aria-orientation":"vertical","data-state":eB(E.open),"data-radix-menu-content":"",dir:R.dir,...O,...C,ref:T,style:{outline:"none",...C.style},onKeyDown:(0,o.m)(C.onKeyDown,e=>{let t=e.target.closest("[data-radix-menu-content]")===e.currentTarget,r=e.ctrlKey||e.altKey||e.metaKey,n=1===e.key.length;t&&("Tab"===e.key&&e.preventDefault(),!r&&n&&G(e.key));let o=P.current;if(e.target!==o||!B.includes(e.key))return;e.preventDefault();let i=S().filter(e=>!e.disabled).map(e=>e.ref.current);Q.includes(e.key)&&i.reverse(),function(e){let t=document.activeElement;for(let r of e)if(r===t||(r.focus(),document.activeElement!==t))return}(i)}),onBlur:(0,o.m)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(D.current),N.current="")}),onPointerMove:(0,o.m)(e.onPointerMove,eH(e=>{let t=e.target,r=q.current!==e.clientX;e.currentTarget.contains(t)&&r&&(_.current=e.clientX>q.current?"right":"left",q.current=e.clientX)}))})})})})})})});em.displayName=ef;var ew=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e;return(0,c.jsx)(l.sG.div,{role:"group",...n,ref:t})});ew.displayName="MenuGroup";var ex=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e;return(0,c.jsx)(l.sG.div,{...n,ref:t})});ex.displayName="MenuLabel";var ek="MenuItem",eC="menu.itemSelect",eE=n.forwardRef((e,t)=>{let{disabled:r=!1,onSelect:a,...s}=e,u=n.useRef(null),d=ei(ek,e.__scopeMenu),f=eh(ek,e.__scopeMenu),p=(0,i.s)(t,u),h=n.useRef(!1);return(0,c.jsx)(eR,{...s,ref:p,disabled:r,onClick:(0,o.m)(e.onClick,()=>{let e=u.current;if(!r&&e){let t=new CustomEvent(eC,{bubbles:!0,cancelable:!0});e.addEventListener(eC,e=>a?.(e),{once:!0}),(0,l.hO)(e,t),t.defaultPrevented?h.current=!1:d.onClose()}}),onPointerDown:t=>{e.onPointerDown?.(t),h.current=!0},onPointerUp:(0,o.m)(e.onPointerUp,e=>{h.current||e.currentTarget?.click()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let t=""!==f.searchRef.current;r||t&&" "===e.key||W.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});eE.displayName=ek;var eR=n.forwardRef((e,t)=>{let{__scopeMenu:r,disabled:a=!1,textValue:s,...u}=e,d=eh(ek,r),f=et(r),p=n.useRef(null),h=(0,i.s)(t,p),[m,g]=n.useState(!1),[y,v]=n.useState("");return n.useEffect(()=>{let e=p.current;e&&v((e.textContent??"").trim())},[u.children]),(0,c.jsx)(V.ItemSlot,{scope:r,disabled:a,textValue:s??y,children:(0,c.jsx)(_,{asChild:!0,...f,focusable:!a,children:(0,c.jsx)(l.sG.div,{role:"menuitem","data-highlighted":m?"":void 0,"aria-disabled":a||void 0,"data-disabled":a?"":void 0,...u,ref:h,onPointerMove:(0,o.m)(e.onPointerMove,eH(e=>{a?d.onItemLeave(e):(d.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,o.m)(e.onPointerLeave,eH(e=>d.onItemLeave(e))),onFocus:(0,o.m)(e.onFocus,()=>g(!0)),onBlur:(0,o.m)(e.onBlur,()=>g(!1))})})})}),eO=n.forwardRef((e,t)=>{let{checked:r=!1,onCheckedChange:n,...i}=e;return(0,c.jsx)(eN,{scope:e.__scopeMenu,checked:r,children:(0,c.jsx)(eE,{role:"menuitemcheckbox","aria-checked":eU(r)?"mixed":r,...i,ref:t,"data-state":e$(r),onSelect:(0,o.m)(i.onSelect,()=>n?.(!!eU(r)||!r),{checkForDefaultPrevented:!1})})})});eO.displayName="MenuCheckboxItem";var eM="MenuRadioGroup",[eS,ej]=Y(eM,{value:void 0,onValueChange:()=>{}}),eA=n.forwardRef((e,t)=>{let{value:r,onValueChange:n,...o}=e,i=(0,E.c)(n);return(0,c.jsx)(eS,{scope:e.__scopeMenu,value:r,onValueChange:i,children:(0,c.jsx)(ew,{...o,ref:t})})});eA.displayName=eM;var eP="MenuRadioItem",eT=n.forwardRef((e,t)=>{let{value:r,...n}=e,i=ej(eP,e.__scopeMenu),a=r===i.value;return(0,c.jsx)(eN,{scope:e.__scopeMenu,checked:a,children:(0,c.jsx)(eE,{role:"menuitemradio","aria-checked":a,...n,ref:t,"data-state":e$(a),onSelect:(0,o.m)(n.onSelect,()=>i.onValueChange?.(r),{checkForDefaultPrevented:!1})})})});eT.displayName=eP;var eD="MenuItemIndicator",[eN,eL]=Y(eD,{checked:!1}),eF=n.forwardRef((e,t)=>{let{__scopeMenu:r,forceMount:n,...o}=e,i=eL(eD,r);return(0,c.jsx)(C.C,{present:n||eU(i.checked)||!0===i.checked,children:(0,c.jsx)(l.sG.span,{...o,ref:t,"data-state":e$(i.checked)})})});eF.displayName=eD;var eI=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e;return(0,c.jsx)(l.sG.div,{role:"separator","aria-orientation":"horizontal",...n,ref:t})});eI.displayName="MenuSeparator";var e_=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e,o=ee(r);return(0,c.jsx)(x.i3,{...o,...n,ref:t})});e_.displayName="MenuArrow";var[eq,ez]=Y("MenuSub"),eG="MenuSubTrigger",eK=n.forwardRef((e,t)=>{let r=en(eG,e.__scopeMenu),a=ei(eG,e.__scopeMenu),s=ez(eG,e.__scopeMenu),l=eh(eG,e.__scopeMenu),u=n.useRef(null),{pointerGraceTimerRef:d,onPointerGraceIntentChange:f}=l,p={__scopeMenu:e.__scopeMenu},h=n.useCallback(()=>{u.current&&window.clearTimeout(u.current),u.current=null},[]);return n.useEffect(()=>h,[h]),n.useEffect(()=>{let e=d.current;return()=>{window.clearTimeout(e),f(null)}},[d,f]),(0,c.jsx)(es,{asChild:!0,...p,children:(0,c.jsx)(eR,{id:s.triggerId,"aria-haspopup":"menu","aria-expanded":r.open,"aria-controls":s.contentId,"data-state":eB(r.open),...e,ref:(0,i.t)(t,s.onTriggerChange),onClick:t=>{e.onClick?.(t),e.disabled||t.defaultPrevented||(t.currentTarget.focus(),r.open||r.onOpenChange(!0))},onPointerMove:(0,o.m)(e.onPointerMove,eH(t=>{l.onItemEnter(t),!t.defaultPrevented&&(e.disabled||r.open||u.current||(l.onPointerGraceIntentChange(null),u.current=window.setTimeout(()=>{r.onOpenChange(!0),h()},100)))})),onPointerLeave:(0,o.m)(e.onPointerLeave,eH(e=>{h();let t=r.content?.getBoundingClientRect();if(t){let n=r.content?.dataset.side,o="right"===n,i=t[o?"left":"right"],a=t[o?"right":"left"];l.onPointerGraceIntentChange({area:[{x:e.clientX+(o?-5:5),y:e.clientY},{x:i,y:t.top},{x:a,y:t.top},{x:a,y:t.bottom},{x:i,y:t.bottom}],side:n}),window.clearTimeout(d.current),d.current=window.setTimeout(()=>l.onPointerGraceIntentChange(null),300)}else{if(l.onTriggerLeave(e),e.defaultPrevented)return;l.onPointerGraceIntentChange(null)}})),onKeyDown:(0,o.m)(e.onKeyDown,t=>{let n=""!==l.searchRef.current;e.disabled||n&&" "===t.key||U[a.dir].includes(t.key)&&(r.onOpenChange(!0),r.content?.focus(),t.preventDefault())})})})});eK.displayName=eG;var eW="MenuSubContent",eQ=n.forwardRef((e,t)=>{let r=ec(ef,e.__scopeMenu),{forceMount:a=r.forceMount,...s}=e,l=en(ef,e.__scopeMenu),u=ei(ef,e.__scopeMenu),d=ez(eW,e.__scopeMenu),f=n.useRef(null),p=(0,i.s)(t,f);return(0,c.jsx)(V.Provider,{scope:e.__scopeMenu,children:(0,c.jsx)(C.C,{present:a||l.open,children:(0,c.jsx)(V.Slot,{scope:e.__scopeMenu,children:(0,c.jsx)(eb,{id:d.contentId,"aria-labelledby":d.triggerId,...s,ref:p,align:"start",side:"rtl"===u.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{u.isUsingKeyboardRef.current&&f.current?.focus(),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>{e.target!==d.trigger&&l.onOpenChange(!1)}),onEscapeKeyDown:(0,o.m)(e.onEscapeKeyDown,e=>{u.onClose(),e.preventDefault()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let t=e.currentTarget.contains(e.target),r=$[u.dir].includes(e.key);t&&r&&(l.onOpenChange(!1),d.trigger?.focus(),e.preventDefault())})})})})})});function eB(e){return e?"open":"closed"}function eU(e){return"indeterminate"===e}function e$(e){return eU(e)?"indeterminate":e?"checked":"unchecked"}function eH(e){return t=>"mouse"===t.pointerType?e(t):void 0}eQ.displayName=eW;var eV="DropdownMenu",[eZ,eX]=(0,a.A)(eV,[J]),eY=J(),[eJ,e0]=eZ(eV),e1=e=>{let{__scopeDropdownMenu:t,children:r,dir:o,open:i,defaultOpen:a,onOpenChange:l,modal:u=!0}=e,d=eY(t),f=n.useRef(null),[p,h]=(0,s.i)({prop:i,defaultProp:a??!1,onChange:l,caller:eV});return(0,c.jsx)(eJ,{scope:t,triggerId:(0,w.B)(),triggerRef:f,contentId:(0,w.B)(),open:p,onOpenChange:h,onOpenToggle:n.useCallback(()=>h(e=>!e),[h]),modal:u,children:(0,c.jsx)(ea,{...d,open:p,onOpenChange:h,dir:o,modal:u,children:r})})};e1.displayName=eV;var e2="DropdownMenuTrigger",e3=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,disabled:n=!1,...a}=e,s=e0(e2,r),u=eY(r);return(0,c.jsx)(es,{asChild:!0,...u,children:(0,c.jsx)(l.sG.button,{type:"button",id:s.triggerId,"aria-haspopup":"menu","aria-expanded":s.open,"aria-controls":s.open?s.contentId:void 0,"data-state":s.open?"open":"closed","data-disabled":n?"":void 0,disabled:n,...a,ref:(0,i.t)(t,s.triggerRef),onPointerDown:(0,o.m)(e.onPointerDown,e=>{!n&&0===e.button&&!1===e.ctrlKey&&(s.onOpenToggle(),s.open||e.preventDefault())}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{!n&&(["Enter"," "].includes(e.key)&&s.onOpenToggle(),"ArrowDown"===e.key&&s.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});e3.displayName=e2;var e6=e=>{let{__scopeDropdownMenu:t,...r}=e,n=eY(t);return(0,c.jsx)(ed,{...n,...r})};e6.displayName="DropdownMenuPortal";var e5="DropdownMenuContent",e4=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...i}=e,a=e0(e5,r),s=eY(r),l=n.useRef(!1);return(0,c.jsx)(em,{id:a.contentId,"aria-labelledby":a.triggerId,...s,...i,ref:t,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{l.current||a.triggerRef.current?.focus(),l.current=!1,e.preventDefault()}),onInteractOutside:(0,o.m)(e.onInteractOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey,n=2===t.button||r;(!a.modal||n)&&(l.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});e4.displayName=e5,n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eY(r);return(0,c.jsx)(ew,{...o,...n,ref:t})}).displayName="DropdownMenuGroup";var e9=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eY(r);return(0,c.jsx)(ex,{...o,...n,ref:t})});e9.displayName="DropdownMenuLabel";var e8=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eY(r);return(0,c.jsx)(eE,{...o,...n,ref:t})});e8.displayName="DropdownMenuItem",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eY(r);return(0,c.jsx)(eO,{...o,...n,ref:t})}).displayName="DropdownMenuCheckboxItem",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eY(r);return(0,c.jsx)(eA,{...o,...n,ref:t})}).displayName="DropdownMenuRadioGroup",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eY(r);return(0,c.jsx)(eT,{...o,...n,ref:t})}).displayName="DropdownMenuRadioItem",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eY(r);return(0,c.jsx)(eF,{...o,...n,ref:t})}).displayName="DropdownMenuItemIndicator";var e7=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eY(r);return(0,c.jsx)(eI,{...o,...n,ref:t})});e7.displayName="DropdownMenuSeparator",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eY(r);return(0,c.jsx)(e_,{...o,...n,ref:t})}).displayName="DropdownMenuArrow",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eY(r);return(0,c.jsx)(eK,{...o,...n,ref:t})}).displayName="DropdownMenuSubTrigger",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eY(r);return(0,c.jsx)(eQ,{...o,...n,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})}).displayName="DropdownMenuSubContent";var te=e1,tt=e3,tr=e6,tn=e4,to=e9,ti=e8,ta=e7},93508:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("user-check",[["path",{d:"m16 11 2 2 4-4",key:"9rsbq5"}],["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},93613:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},96963:(e,t,r)=>{r.d(t,{B:()=>l});var n,o=r(43210),i=r(66156),a=(n||(n=r.t(o,2)))[" useId ".trim().toString()]||(()=>void 0),s=0;function l(e){let[t,r]=o.useState(a());return(0,i.N)(()=>{e||r(e=>e??String(s++))},[e]),e||(t?`radix-${t}`:"")}},97051:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("bell",[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]])},98599:(e,t,r)=>{r.d(t,{s:()=>a,t:()=>i});var n=r(43210);function o(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function i(...e){return t=>{let r=!1,n=e.map(e=>{let n=o(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():o(e[t],null)}}}}function a(...e){return n.useCallback(i(...e),e)}}};