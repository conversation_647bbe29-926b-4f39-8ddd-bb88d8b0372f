(()=>{var e={};e.id=977,e.ids=[977],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13117:(e,s,t)=>{Promise.resolve().then(t.bind(t,71548))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26002:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>m,tree:()=>d});var r=t(65239),a=t(48088),i=t(88170),n=t.n(i),l=t(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);t.d(s,o);let d={children:["",{children:["contact",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,43839)),"D:\\fwu_incubation(react+laravel)\\incubation\\frontend\\src\\app\\contact\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,53862)),"D:\\fwu_incubation(react+laravel)\\incubation\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["D:\\fwu_incubation(react+laravel)\\incubation\\frontend\\src\\app\\contact\\page.tsx"],x={require:t,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/contact/page",pathname:"/contact",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},43839:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\fwu_incubation(react+laravel)\\\\incubation\\\\frontend\\\\src\\\\app\\\\contact\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\fwu_incubation(react+laravel)\\incubation\\frontend\\src\\app\\contact\\page.tsx","default")},48730:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},58887:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("message-square",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},69149:(e,s,t)=>{Promise.resolve().then(t.bind(t,43839))},70440:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});var r=t(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},71548:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>A});var r=t(60687),a=t(48340),i=t(41550),n=t(97992),l=t(70334),o=t(58887),d=t(43210),c=t(27605),x=t(5336),m=t(93613);let h=(0,t(62688).A)("send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]]),u=()=>{let[e,s]=(0,d.useState)("idle"),{register:t,handleSubmit:a,formState:{errors:i,isSubmitting:n},reset:l}=(0,c.mN)(),o=async e=>{s("submitting"),console.log("Contact Form Data:",e),await new Promise(e=>setTimeout(e,1500)),Math.random()>.2?(s("success"),l()):s("error")};return"success"===e?(0,r.jsxs)("div",{className:"bg-white p-8 md:p-10 rounded-2xl shadow-xl text-center border border-gray-100",children:[(0,r.jsx)("div",{className:"w-20 h-20 mx-auto bg-green-100 rounded-full flex items-center justify-center mb-6",children:(0,r.jsx)(x.A,{className:"text-green-500 text-4xl"})}),(0,r.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-3",children:"Message Sent!"}),(0,r.jsx)("p",{className:"text-gray-600 mb-8 max-w-md mx-auto leading-relaxed",children:"Thank you for contacting the FWU Incubation Center. We've received your message and will get back to you as soon as possible."}),(0,r.jsxs)("div",{className:"flex flex-col items-center space-y-4",children:[(0,r.jsx)("p",{className:"text-sm text-gray-500 mb-2",children:"You will receive a confirmation email shortly."}),(0,r.jsx)("button",{onClick:()=>s("idle"),className:"bg-indigo-600 hover:bg-indigo-700 text-white font-semibold py-3 px-8 rounded-lg shadow-md transition-all duration-300 hover:scale-105 transform",children:"Send Another Message"})]})]}):(0,r.jsx)("div",{className:"bg-white p-8 md:p-10 rounded-2xl shadow-xl border border-gray-100 hover:border-indigo-100 transition-all duration-300 hover:shadow-indigo-100/20",children:(0,r.jsxs)("form",{onSubmit:a(o),className:"space-y-8",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"name",className:"flex items-center text-sm font-medium text-gray-700 mb-2",children:["Full Name ",(0,r.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("input",{type:"text",id:"name",className:`w-full px-4 py-3 pl-10 border-2 ${i.name?"border-red-300 bg-red-50":"border-gray-200 focus:border-indigo-300"} rounded-lg ${i.name?"focus:ring-red-200":"focus:ring-indigo-100"} focus:ring-4 outline-none transition-all`,placeholder:"e.g., Rajesh Sharma",...t("name",{required:"Full name is required."})}),(0,r.jsx)("div",{className:"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none",children:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:`h-5 w-5 ${i.name?"text-red-400":"text-indigo-400"}`,fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})})})]}),i.name&&(0,r.jsxs)("div",{className:"mt-2 flex items-start text-sm text-red-600",children:[(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 text-red-500 mt-0.5 mr-1.5 flex-shrink-0",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"})}),(0,r.jsx)("span",{children:i.name.message})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"email",className:"flex items-center text-sm font-medium text-gray-700 mb-2",children:["Email Address ",(0,r.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("input",{type:"email",id:"email",className:`w-full px-4 py-3 pl-10 border-2 ${i.email?"border-red-300 bg-red-50":"border-gray-200 focus:border-indigo-300"} rounded-lg ${i.email?"focus:ring-red-200":"focus:ring-indigo-100"} focus:ring-4 outline-none transition-all`,placeholder:"<EMAIL>",...t("email",{required:"Email is required.",pattern:{value:/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,message:"Invalid email address."}})}),(0,r.jsx)("div",{className:"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none",children:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:`h-5 w-5 ${i.email?"text-red-400":"text-indigo-400"}`,fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})})})]}),i.email&&(0,r.jsxs)("div",{className:"mt-2 flex items-start text-sm text-red-600",children:[(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 text-red-500 mt-0.5 mr-1.5 flex-shrink-0",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"})}),(0,r.jsx)("span",{children:i.email.message})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"message",className:"flex items-center text-sm font-medium text-gray-700 mb-2",children:["Your Message ",(0,r.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),(0,r.jsx)("div",{className:"relative",children:(0,r.jsx)("textarea",{id:"message",rows:6,className:`w-full px-4 py-3 border-2 ${i.message?"border-red-300 bg-red-50":"border-gray-200 focus:border-indigo-300"} rounded-lg ${i.message?"focus:ring-red-200":"focus:ring-indigo-100"} focus:ring-4 outline-none transition-all`,placeholder:"Write your message here... Please include details about your inquiry or how we can help you.",...t("message",{required:"Message is required.",minLength:{value:10,message:"Message must be at least 10 characters."}})})}),i.message&&(0,r.jsxs)("div",{className:"mt-2 flex items-start text-sm text-red-600",children:[(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 text-red-500 mt-0.5 mr-1.5 flex-shrink-0",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"})}),(0,r.jsx)("span",{children:i.message.message})]})]}),"error"===e&&(0,r.jsxs)("div",{className:"flex items-center p-5 text-sm text-red-700 bg-red-100 rounded-xl border border-red-200",role:"alert",children:[(0,r.jsx)(m.A,{className:"text-xl mr-3 text-red-500"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-bold",children:"Message Not Sent"}),(0,r.jsx)("p",{children:"There was an error sending your message. Please try again or contact us directly."})]})]}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:(0,r.jsxs)("p",{children:["By submitting this form, you agree to our ",(0,r.jsx)("a",{href:"#",className:"text-indigo-600 hover:underline",children:"Privacy Policy"})," and consent to being contacted regarding your inquiry."]})}),(0,r.jsx)("button",{type:"submit",disabled:n,className:"w-full flex justify-center items-center bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-4 px-6 rounded-xl shadow-md transition-all duration-300 hover:scale-105 transform disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100",children:n?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("svg",{className:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,r.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,r.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Sending Message..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(h,{className:"mr-2"})," Send Message"]})})]})})};var g=t(57800),p=t(48730);let b=({icon:e,label:s,value:t,href:a})=>(0,r.jsxs)("div",{className:"flex items-start space-x-4 p-4 rounded-xl hover:bg-indigo-50 transition-colors group",children:[(0,r.jsx)("div",{className:"flex-shrink-0 w-12 h-12 bg-indigo-100 text-indigo-600 rounded-full flex items-center justify-center group-hover:bg-indigo-200 transition-colors",children:e}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"text-sm font-semibold text-indigo-600 uppercase tracking-wider mb-1",children:s}),a?(0,r.jsx)("a",{href:a,className:"text-md text-gray-900 hover:text-indigo-700 transition-colors break-words font-medium",children:t}):(0,r.jsx)("p",{className:"text-md text-gray-900 break-words",children:t})]})]}),f=({details:e})=>(0,r.jsxs)("div",{className:"bg-white p-8 md:p-10 rounded-2xl shadow-xl border border-gray-100 hover:border-indigo-100 transition-all duration-300 hover:shadow-indigo-100/20",children:[(0,r.jsxs)("div",{className:"flex items-center mb-8",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-indigo-100 rounded-full flex items-center justify-center mr-4",children:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 text-indigo-600",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Contact Information"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(b,{icon:(0,r.jsx)(n.A,{size:22}),label:"Our Address",value:e.address}),(0,r.jsx)(b,{icon:(0,r.jsx)(a.A,{size:22}),label:"Phone Number",value:e.phone,href:`tel:${e.phone.replace(/\s+/g,"")}`}),(0,r.jsx)(b,{icon:(0,r.jsx)(i.A,{size:22}),label:"General Inquiries",value:e.email,href:`mailto:${e.email}`}),(0,r.jsx)(b,{icon:(0,r.jsx)(g.A,{size:22}),label:"Incubation Center",value:e.incubationEmail,href:`mailto:${e.incubationEmail}`}),(0,r.jsxs)("div",{className:"flex items-start space-x-4 p-4 rounded-xl hover:bg-indigo-50 transition-colors group",children:[(0,r.jsx)("div",{className:"flex-shrink-0 w-12 h-12 bg-indigo-100 text-indigo-600 rounded-full flex items-center justify-center group-hover:bg-indigo-200 transition-colors",children:(0,r.jsx)(p.A,{size:22})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"text-sm font-semibold text-indigo-600 uppercase tracking-wider mb-2",children:"Office Hours"}),(0,r.jsx)("div",{className:"space-y-1",children:e.officeHours.map((e,s)=>(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:`w-2 h-2 rounded-full mr-2 ${e.hours.includes("Closed")?"bg-red-400":"bg-green-400"}`}),(0,r.jsxs)("p",{className:"text-md text-gray-900",children:[(0,r.jsxs)("span",{className:"font-medium",children:[e.day,":"]})," ",e.hours]})]},s))})]})]})]}),(0,r.jsxs)("div",{className:"mt-8 pt-6 border-t border-gray-100",children:[(0,r.jsx)("h4",{className:"text-sm font-semibold text-gray-500 uppercase tracking-wider mb-4",children:"Connect With Us"}),(0,r.jsxs)("div",{className:"flex space-x-4",children:[(0,r.jsx)("a",{href:"#",className:"w-10 h-10 bg-indigo-100 text-indigo-600 rounded-full flex items-center justify-center hover:bg-indigo-200 transition-colors",children:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",fill:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{d:"M22.675 0h-21.35c-.732 0-1.325.593-1.325 1.325v21.351c0 .731.593 1.324 1.325 1.324h11.495v-9.294h-3.128v-3.622h3.128v-2.671c0-3.1 1.893-4.788 4.659-4.788 1.325 0 2.463.099 2.795.143v3.24l-1.918.001c-1.504 0-1.795.715-1.795 1.763v2.313h3.587l-.467 3.622h-3.12v9.293h6.116c.73 0 1.323-.593 1.323-1.325v-21.35c0-.732-.593-1.325-1.325-1.325z"})})}),(0,r.jsx)("a",{href:"#",className:"w-10 h-10 bg-indigo-100 text-indigo-600 rounded-full flex items-center justify-center hover:bg-indigo-200 transition-colors",children:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",fill:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{d:"M12 0c-6.627 0-12 5.373-12 12s5.373 12 12 12 12-5.373 12-12-5.373-12-12-12zm6.066 9.645c.183 4.04-2.83 8.544-8.164 8.544-1.622 0-3.131-.476-4.402-1.291 1.524.18 3.045-.244 4.252-1.189-1.256-.023-2.317-.854-2.684-1.995.451.086.895.061 1.298-.049-1.381-.278-2.335-1.522-2.304-2.853.388.215.83.344 1.301.359-1.279-.855-1.641-2.544-.889-3.835 1.416 1.738 3.533 2.881 5.92 3.001-.419-1.796.944-3.527 2.799-3.527.825 0 1.572.349 2.096.907.654-.128 1.27-.368 1.824-.697-.215.671-.67 1.233-1.263 1.589.581-.07 1.135-.224 1.649-.453-.384.578-.87 1.084-1.433 1.489z"})})}),(0,r.jsx)("a",{href:"#",className:"w-10 h-10 bg-indigo-100 text-indigo-600 rounded-full flex items-center justify-center hover:bg-indigo-200 transition-colors",children:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",fill:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{d:"M12 0c-6.627 0-12 5.373-12 12s5.373 12 12 12 12-5.373 12-12-5.373-12-12-12zm-2 16h-2v-6h2v6zm-1-6.891c-.607 0-1.1-.496-1.1-1.109 0-.612.492-1.109 1.1-1.109s1.1.497 1.1 1.109c0 .613-.493 1.109-1.1 1.109zm8 6.891h-1.998v-2.861c0-1.881-2.002-1.722-2.002 0v2.861h-2v-6h2v1.093c.872-1.616 4-1.736 4 1.548v3.359z"})})}),(0,r.jsx)("a",{href:"#",className:"w-10 h-10 bg-indigo-100 text-indigo-600 rounded-full flex items-center justify-center hover:bg-indigo-200 transition-colors",children:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",fill:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{d:"M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"})})})]})]})]}),v=({embedUrl:e,address:s})=>(0,r.jsxs)("div",{className:"bg-white p-6 rounded-2xl shadow-xl border border-gray-100 hover:border-indigo-100 transition-all duration-300 hover:shadow-indigo-100/20 overflow-hidden",children:[(0,r.jsxs)("div",{className:"flex items-center mb-6",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-indigo-100 rounded-full flex items-center justify-center mr-4",children:(0,r.jsx)(n.A,{className:"text-indigo-600 text-xl"})}),(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Find Us"})]}),(0,r.jsxs)("div",{className:"rounded-xl overflow-hidden border-4 border-indigo-50 shadow-inner relative",children:[(0,r.jsxs)("div",{className:"aspect-w-16 aspect-h-9",children:[" ",(0,r.jsx)("iframe",{src:e,width:"100%",height:"100%",style:{border:0},allowFullScreen:!1,loading:"lazy",referrerPolicy:"no-referrer-when-downgrade",title:`Map of ${s}`,className:"grayscale hover:grayscale-0 transition-all duration-500"})]}),(0,r.jsx)("div",{className:"absolute bottom-0 left-0 right-0 bg-gradient-to-t from-indigo-900/80 to-transparent p-4 text-white",children:(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)(n.A,{className:"mt-1 mr-2 flex-shrink-0"}),(0,r.jsx)("p",{className:"text-sm",children:s})]})})]}),(0,r.jsx)("div",{className:"mt-6 text-center",children:(0,r.jsxs)("a",{href:`https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(s)}`,target:"_blank",rel:"noopener noreferrer",className:"inline-flex items-center text-indigo-600 font-semibold hover:text-indigo-800 transition-colors",children:["Get Directions",(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 ml-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"})})]})})]});var j=t(30474),w=t(85814),N=t.n(w);let y={phone:"+977-099-520729",email:"<EMAIL>",incubationEmail:"<EMAIL>",address:"Bheemdatta Municipality-18, Mahendranagar, Kanchanpur, Nepal",officeHours:[{day:"Monday - Friday",hours:"9:00 AM - 5:00 PM"},{day:"Saturday",hours:"9:00 AM - 2:00 PM"},{day:"Sunday",hours:"Closed"}],mapEmbedUrl:"https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3499.8943315606097!2d80.18761937532953!3d28.69999997561701!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x39a1a0f8c01ebb33%3A0x7a2b35cd7920b47f!2sFar%20Western%20University!5e0!3m2!1sen!2snp!4v1715000000000!5m2!1sen!2snp"},k={name:"Santosh Bist",phone:"9858751161",email:"<EMAIL>",photoUrl:"https://fwu.edu.np/assets/uploads/employee-photo/photo-1624353722-sms.jpg"};function A(){return(0,r.jsxs)("main",{className:"bg-white relative overflow-hidden",children:[(0,r.jsx)("div",{className:"absolute top-0 right-0 w-[600px] h-[600px] bg-indigo-100 rounded-full opacity-30 blur-3xl -translate-x-1/3 -translate-y-1/3"}),(0,r.jsx)("div",{className:"absolute bottom-0 left-0 w-[500px] h-[500px] bg-blue-100 rounded-full opacity-30 blur-3xl translate-x-1/4 translate-y-1/4"}),(0,r.jsx)("div",{className:"absolute inset-0 opacity-5",style:{backgroundImage:"radial-gradient(#4338ca 1px, transparent 1px)",backgroundSize:"40px 40px"}}),(0,r.jsxs)("section",{className:"relative py-20 md:py-28 bg-gradient-to-r from-indigo-900 via-indigo-800 to-blue-900 text-white overflow-hidden",children:[(0,r.jsxs)("div",{className:"absolute inset-0 overflow-hidden",children:[(0,r.jsx)("div",{className:"absolute top-20 left-10 w-64 h-64 rounded-full bg-indigo-500 opacity-10 animate-float-slow"}),(0,r.jsx)("div",{className:"absolute bottom-20 right-10 w-96 h-96 rounded-full bg-blue-500 opacity-10 animate-float-reverse"}),(0,r.jsx)("div",{className:"absolute top-1/2 left-1/4 w-32 h-32 rounded-full bg-purple-500 opacity-5 animate-pulse"}),(0,r.jsx)("div",{className:"absolute inset-0 opacity-10",style:{backgroundImage:"radial-gradient(circle, rgba(255,255,255,0.1) 1px, transparent 1px)",backgroundSize:"30px 30px"}})]}),(0,r.jsx)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8 relative z-10",children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto text-center",children:[(0,r.jsx)("div",{className:"inline-block mb-6 p-2 bg-indigo-800/30 rounded-full",children:(0,r.jsx)("div",{className:"px-4 py-1 bg-indigo-700/50 rounded-full",children:(0,r.jsx)("span",{className:"text-indigo-100 font-medium",children:"FWU Incubation Center"})})}),(0,r.jsxs)("h1",{className:"text-4xl sm:text-5xl md:text-6xl font-extrabold mb-6 leading-tight",children:["Get In ",(0,r.jsx)("span",{className:"text-transparent bg-clip-text bg-gradient-to-r from-blue-300 to-indigo-300",children:"Touch"})]}),(0,r.jsx)("p",{className:"text-xl text-indigo-100 max-w-3xl mx-auto mb-10 leading-relaxed",children:"We're here to help and answer any questions you might have about the FWU Incubation Center. Connect with us to learn more about our programs and opportunities."}),(0,r.jsxs)("div",{className:"flex flex-wrap justify-center gap-4 mb-8",children:[(0,r.jsxs)("div",{className:"flex items-center bg-white/10 backdrop-blur-sm px-4 py-2 rounded-full",children:[(0,r.jsx)(a.A,{className:"text-blue-300 mr-2"}),(0,r.jsx)("span",{className:"text-white text-sm",children:y.phone})]}),(0,r.jsxs)("div",{className:"flex items-center bg-white/10 backdrop-blur-sm px-4 py-2 rounded-full",children:[(0,r.jsx)(i.A,{className:"text-blue-300 mr-2"}),(0,r.jsx)("span",{className:"text-white text-sm",children:y.email})]}),(0,r.jsxs)("div",{className:"flex items-center bg-white/10 backdrop-blur-sm px-4 py-2 rounded-full",children:[(0,r.jsx)(n.A,{className:"text-blue-300 mr-2"}),(0,r.jsx)("span",{className:"text-white text-sm",children:"Mahendranagar, Kanchanpur"})]})]}),(0,r.jsxs)("a",{href:"#contact-form",className:"inline-flex items-center bg-blue-600 hover:bg-blue-700 text-white font-bold py-4 px-8 rounded-lg shadow-lg transition-all duration-300 hover:scale-105 transform",children:["Send Message ",(0,r.jsx)(l.A,{className:"ml-2"})]})]})}),(0,r.jsx)("div",{className:"absolute bottom-0 left-0 right-0",children:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1440 120",className:"w-full h-auto",children:(0,r.jsx)("path",{fill:"#ffffff",fillOpacity:"1",d:"M0,64L80,69.3C160,75,320,85,480,80C640,75,800,53,960,48C1120,43,1280,53,1360,58.7L1440,64L1440,120L1360,120C1280,120,1120,120,960,120C800,120,640,120,480,120C320,120,160,120,80,120L0,120Z"})})})]}),(0,r.jsx)("section",{className:"py-16 relative z-10",children:(0,r.jsx)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"grid md:grid-cols-3 gap-8",children:[(0,r.jsxs)("div",{className:"bg-white rounded-2xl shadow-xl p-8 border border-gray-100 hover:border-indigo-100 transition-all duration-300 hover:shadow-indigo-100/20 group",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mb-6 group-hover:bg-indigo-200 transition-colors",children:(0,r.jsx)(n.A,{className:"text-indigo-600 text-2xl"})}),(0,r.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-3",children:"Visit Us"}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:y.address}),(0,r.jsxs)("a",{href:"https://maps.app.goo.gl/Ehu1U2FZzjRUsGEB6",target:"_blank",rel:"noopener noreferrer",className:"inline-flex items-center text-indigo-600 font-semibold hover:text-indigo-800 transition-colors group",children:["Get Directions ",(0,r.jsx)(l.A,{className:"ml-2 group-hover:translate-x-1 transition-transform"})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-2xl shadow-xl p-8 border border-gray-100 hover:border-indigo-100 transition-all duration-300 hover:shadow-indigo-100/20 group",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mb-6 group-hover:bg-indigo-200 transition-colors",children:(0,r.jsx)(a.A,{className:"text-indigo-600 text-2xl"})}),(0,r.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-3",children:"Call Us"}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:"Have questions? Call us directly at:"}),(0,r.jsx)("a",{href:`tel:${y.phone.replace(/\s+/g,"")}`,className:"text-xl font-bold text-indigo-600 hover:text-indigo-800 transition-colors block mb-4",children:y.phone}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Available during office hours"})]}),(0,r.jsxs)("div",{className:"bg-white rounded-2xl shadow-xl p-8 border border-gray-100 hover:border-indigo-100 transition-all duration-300 hover:shadow-indigo-100/20 group",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mb-6 group-hover:bg-indigo-200 transition-colors",children:(0,r.jsx)(i.A,{className:"text-indigo-600 text-2xl"})}),(0,r.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-3",children:"Email Us"}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:"For general inquiries:"}),(0,r.jsx)("a",{href:`mailto:${y.email}`,className:"text-indigo-600 hover:text-indigo-800 transition-colors block mb-4 font-medium",children:y.email}),(0,r.jsx)("p",{className:"text-gray-600 mb-2",children:"For incubation center:"}),(0,r.jsx)("a",{href:`mailto:${y.incubationEmail}`,className:"text-indigo-600 hover:text-indigo-800 transition-colors block font-medium",children:y.incubationEmail})]})]})})}),(0,r.jsx)("section",{id:"contact-form",className:"py-16 md:py-24 relative z-10",children:(0,r.jsxs)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:[(0,r.jsxs)("div",{className:"max-w-3xl mx-auto text-center mb-16",children:[(0,r.jsx)("div",{className:"inline-block mb-4",children:(0,r.jsx)("div",{className:"flex items-center justify-center w-16 h-16 mx-auto bg-indigo-100 rounded-full mb-4",children:(0,r.jsx)(o.A,{className:"h-8 w-8 text-indigo-600"})})}),(0,r.jsx)("h2",{className:"text-3xl md:text-4xl font-bold text-gray-900 mb-4",children:"Send Us a Message"}),(0,r.jsx)("div",{className:"w-24 h-1 bg-indigo-600 mx-auto mb-6 rounded-full"}),(0,r.jsx)("p",{className:"text-xl text-gray-600 leading-relaxed",children:"Have questions about our incubation programs or want to learn more? Fill out the form below and we'll get back to you as soon as possible."})]}),(0,r.jsxs)("div",{className:"grid lg:grid-cols-2 gap-12 items-start",children:[(0,r.jsx)(u,{}),(0,r.jsxs)("div",{className:"space-y-12",children:[(0,r.jsx)(f,{details:y}),(0,r.jsxs)("div",{className:"bg-white p-8 md:p-10 rounded-2xl shadow-xl border border-gray-100 hover:border-indigo-100 transition-all duration-300 hover:shadow-indigo-100/20",children:[(0,r.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Information Officer"}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"relative w-20 h-20 rounded-full overflow-hidden mr-6 border-2 border-indigo-100",children:(0,r.jsx)(j.default,{src:k.photoUrl,alt:k.name,fill:!0,className:"object-cover"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"text-xl font-bold text-gray-900",children:k.name}),(0,r.jsx)("p",{className:"text-gray-600 mb-2",children:"Information Officer"}),(0,r.jsxs)("div",{className:"flex items-center text-indigo-600 mb-1",children:[(0,r.jsx)(a.A,{className:"mr-2 text-sm"}),(0,r.jsx)("a",{href:`tel:${k.phone}`,className:"hover:text-indigo-800 transition-colors",children:k.phone})]}),(0,r.jsxs)("div",{className:"flex items-center text-indigo-600",children:[(0,r.jsx)(i.A,{className:"mr-2 text-sm"}),(0,r.jsx)("a",{href:`mailto:${k.email}`,className:"hover:text-indigo-800 transition-colors",children:k.email})]})]})]})]}),(0,r.jsx)(v,{embedUrl:y.mapEmbedUrl,address:y.address})]})]})]})}),(0,r.jsxs)("section",{className:"py-16 bg-gradient-to-r from-indigo-900 to-blue-900 text-white relative overflow-hidden",children:[(0,r.jsxs)("div",{className:"absolute inset-0 overflow-hidden",children:[(0,r.jsx)("div",{className:"absolute top-0 right-0 w-64 h-64 rounded-full bg-indigo-500 opacity-10"}),(0,r.jsx)("div",{className:"absolute bottom-0 left-0 w-96 h-96 rounded-full bg-blue-500 opacity-10"})]}),(0,r.jsx)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8 relative z-10",children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto text-center",children:[(0,r.jsx)("h2",{className:"text-3xl md:text-4xl font-bold mb-6",children:"Ready to Join Our Incubation Center?"}),(0,r.jsx)("p",{className:"text-xl text-indigo-100 mb-10 leading-relaxed",children:"Apply now to transform your innovative idea into a successful business with our expert mentorship, resources, and collaborative ecosystem."}),(0,r.jsxs)("div",{className:"flex flex-wrap justify-center gap-6",children:[(0,r.jsx)(N(),{href:"/apply",className:"bg-white text-indigo-900 hover:bg-indigo-50 font-bold py-4 px-8 rounded-lg shadow-lg transition-all duration-300 hover:scale-105 transform",children:"Apply Now"}),(0,r.jsx)(N(),{href:"/programs",className:"bg-transparent border-2 border-white text-white hover:bg-white/10 font-bold py-4 px-8 rounded-lg shadow-lg transition-all duration-300 hover:scale-105 transform",children:"Explore Programs"})]})]})})]})]})}},79551:e=>{"use strict";e.exports=require("url")}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[447,172,658,599,54],()=>t(26002));module.exports=r})();