{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/fwu_incubation%28react%2Blaravel%29/incubation/frontend/src/app/components/home/<USER>"], "sourcesContent": ["// components/home/<USER>\n\"use client\"\r\nimport Image from 'next/image';\r\nimport { Check, ArrowRight } from 'lucide-react';\r\nimport { useEffect, useState, useRef } from 'react';\r\nimport Link from 'next/link';\r\n\r\nconst IntroSection = () => {\r\n  const [isVisible, setIsVisible] = useState(false);\r\n  const sectionRef = useRef(null);\r\n\r\n  useEffect(() => {\r\n    const observer = new IntersectionObserver(\r\n      ([entry]) => {\r\n        if (entry.isIntersecting) {\r\n          setIsVisible(true);\r\n          observer.disconnect();\r\n        }\r\n      },\r\n      { threshold: 0.1 }\r\n    );\r\n\r\n    const currentRef = sectionRef.current;\r\n    if (currentRef) {\r\n      observer.observe(currentRef);\r\n    }\r\n\r\n    return () => {\r\n      if (currentRef) {\r\n        observer.unobserve(currentRef);\r\n      }\r\n    };\r\n  }, []);\r\n\r\n  return (\r\n    <section ref={sectionRef} className=\"py-24 md:py-32 bg-white relative overflow-hidden\">\r\n      {/* Background decorative elements */}\r\n      <div className=\"absolute top-0 right-0 w-[600px] h-[600px] bg-blue-50 rounded-full opacity-70 blur-3xl -translate-x-1/3 -translate-y-1/3\"></div>\r\n      <div className=\"absolute bottom-0 left-0 w-[500px] h-[500px] bg-indigo-50 rounded-full opacity-70 blur-3xl translate-x-1/4 translate-y-1/4\"></div>\r\n\r\n      {/* Subtle pattern overlay */}\r\n      <div className=\"absolute inset-0 opacity-5\"\r\n        style={{\r\n          backgroundImage: 'radial-gradient(#4338ca 1px, transparent 1px)',\r\n          backgroundSize: '40px 40px'\r\n        }}>\r\n      </div>\r\n\r\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 relative z-10\">\r\n        {/* Section header */}\r\n        <div className={`max-w-3xl mx-auto text-center mb-16 transition-all duration-700 transform ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>\r\n          <div className=\"inline-block mb-4\">\r\n            <div className=\"flex items-center justify-center w-16 h-16 mx-auto bg-indigo-100 rounded-full mb-4\">\r\n              <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-8 w-8 text-indigo-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4\" />\r\n              </svg>\r\n            </div>\r\n          </div>\r\n          <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">FWU Incubation Center</h2>\r\n          <div className=\"w-24 h-1 bg-indigo-600 mx-auto mb-6 rounded-full\"></div>\r\n          <p className=\"text-xl text-gray-600 leading-relaxed\">\r\n            Transforming innovative ideas into successful ventures through mentorship, resources, and a collaborative ecosystem.\r\n          </p>\r\n        </div>\r\n\r\n        <div className=\"grid md:grid-cols-2 gap-16 items-center\">\r\n          {/* Left content */}\r\n          <div className={`space-y-8 transition-all duration-700 delay-100 transform ${isVisible ? 'opacity-100 translate-x-0' : 'opacity-0 -translate-x-10'}`}>\r\n            <div className=\"bg-white rounded-2xl shadow-xl p-8 border border-gray-100 hover:border-indigo-100 transition-all duration-300 hover:shadow-indigo-100/20\">\r\n              <h3 className=\"text-2xl font-bold text-gray-900 mb-4 flex items-center\">\r\n                <span className=\"flex items-center justify-center w-10 h-10 bg-indigo-100 rounded-full text-indigo-600 mr-4\">\r\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\r\n                  </svg>\r\n                </span>\r\n                Our Mission\r\n              </h3>\r\n              <p className=\"text-gray-600 leading-relaxed mb-6\">\r\n                The FWU Incubation Center aims to foster innovation and entrepreneurship by providing a supportive environment for startups to grow and succeed. We bridge the gap between academic research and commercial applications.\r\n              </p>\r\n              <p className=\"text-gray-600 leading-relaxed\">\r\n                Our strategic location at Far Western University gives access to local talent and provides an opportunity for the overall development of the region through innovation and technology transfer.\r\n              </p>\r\n            </div>\r\n\r\n            <div className=\"bg-gradient-to-br from-indigo-50 to-blue-50 rounded-2xl p-8\">\r\n              <h3 className=\"text-xl font-bold text-indigo-900 mb-6\">What We Offer</h3>\r\n              <div className=\"grid sm:grid-cols-2 gap-4\">\r\n                {[\r\n                  'Mentorship & Guidance',\r\n                  'Workspace & Resources',\r\n                  'Funding Opportunities',\r\n                  'Networking Events',\r\n                  'Technical Support',\r\n                  'Business Development'\r\n                ].map((item, index) => (\r\n                  <div\r\n                    key={index}\r\n                    className=\"flex items-center p-3 bg-white rounded-xl shadow-sm transition-all duration-300 hover:shadow-md hover:-translate-y-1 transform\"\r\n                    style={{\r\n                      transitionDelay: `${index * 100}ms`,\r\n                      animation: isVisible ? `fadeSlideIn 0.5s ease-out forwards ${300 + index * 100}ms` : 'none',\r\n                      opacity: 0,\r\n                      transform: 'translateY(10px)'\r\n                    }}\r\n                  >\r\n                    <div className=\"flex-shrink-0 w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center mr-3 text-indigo-600\">\r\n                      <Check className=\"w-4 h-4\" />\r\n                    </div>\r\n                    <span className=\"text-gray-700 font-medium\">{item}</span>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n\r\n              <div className=\"mt-8 text-center\">\r\n                <Link href=\"/programs\" className=\"inline-flex items-center text-indigo-600 font-semibold hover:text-indigo-800 transition-colors group\">\r\n                  Explore Our Programs\r\n                  <ArrowRight className=\"ml-2 group-hover:translate-x-1 transition-transform\" />\r\n                </Link>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Right content - Image with overlays */}\r\n          <div className={`transition-all duration-700 delay-300 transform ${isVisible ? 'opacity-100 translate-x-0' : 'opacity-0 translate-x-10'}`}>\r\n            <div className=\"relative rounded-2xl overflow-hidden shadow-2xl group\">\r\n              {/* Main image */}\r\n              <div className=\"relative h-[600px] w-full\">\r\n                <Image\r\n                  src=\"https://images.unsplash.com/photo-1522202176988-66273c2fd55f?q=80&w=1000&auto=format&fit=crop\"\r\n                  alt=\"FWU Incubation Center\"\r\n                  fill\r\n                  sizes=\"(max-width: 768px) 100vw, 50vw\"\r\n                  className=\"object-cover object-center transform group-hover:scale-105 transition-transform duration-700 ease-in-out\"\r\n                />\r\n                <div className=\"absolute inset-0 bg-gradient-to-t from-indigo-900/70 via-indigo-900/30 to-transparent\"></div>\r\n              </div>\r\n\r\n              {/* Floating info cards */}\r\n              <div className=\"absolute top-6 right-6 bg-white rounded-xl shadow-lg p-4 max-w-[200px] transform transition-transform duration-500 group-hover:-translate-y-2\">\r\n                <div className=\"flex items-center mb-2\">\r\n                  <div className=\"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3\">\r\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-4 w-4 text-blue-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\" />\r\n                    </svg>\r\n                  </div>\r\n                  <h4 className=\"font-bold text-gray-900\">20+ Startups</h4>\r\n                </div>\r\n                <p className=\"text-sm text-gray-600\">Currently incubating innovative startups across various sectors</p>\r\n              </div>\r\n\r\n              <div className=\"absolute bottom-6 left-6 bg-white rounded-xl shadow-lg p-4 max-w-[240px] transform transition-transform duration-500 group-hover:translate-y-2\">\r\n                <h3 className=\"text-lg font-bold text-gray-900 mb-2\">Join Our Community</h3>\r\n                <p className=\"text-sm text-gray-600 mb-3\">\r\n                  Be part of a thriving ecosystem of innovators, mentors, and industry experts\r\n                </p>\r\n                <Link href=\"/apply\" className=\"inline-flex items-center text-sm font-medium text-indigo-600 hover:text-indigo-800\">\r\n                  Apply Now <ArrowRight className=\"ml-1 w-3 h-3\" />\r\n                </Link>\r\n              </div>\r\n\r\n              {/* Floating dots decoration */}\r\n              <div className=\"absolute inset-0 pointer-events-none\">\r\n                {[...Array(8)].map((_, i) => (\r\n                  <div\r\n                    key={i}\r\n                    className=\"absolute w-2 h-2 bg-white rounded-full\"\r\n                    style={{\r\n                      top: `${20 + Math.random() * 60}%`,\r\n                      left: `${20 + Math.random() * 60}%`,\r\n                      animation: `float ${3 + Math.random() * 4}s ease-in-out infinite ${Math.random() * 2}s`\r\n                    }}\r\n                  ></div>\r\n                ))}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* CSS Animations */}\r\n      <style jsx>{`\r\n        @keyframes fadeSlideIn {\r\n          from {\r\n            opacity: 0;\r\n            transform: translateY(10px);\r\n          }\r\n          to {\r\n            opacity: 1;\r\n            transform: translateY(0);\r\n          }\r\n        }\r\n        @keyframes float {\r\n          0%, 100% { transform: translateY(0) scale(1); }\r\n          50% { transform: translateY(-10px) scale(1.2); }\r\n        }\r\n      `}</style>\r\n    </section>\r\n  );\r\n};\r\nexport default IntroSection;"], "names": [], "mappings": "AAAA,mCAAmC;;;;;;AAEnC;AACA;AAAA;AACA;AACA;;;AAJA;;;;;;AAMA,MAAM,eAAe;;IACnB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAE1B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM,WAAW,IAAI;0CACnB,CAAC,CAAC,MAAM;oBACN,IAAI,MAAM,cAAc,EAAE;wBACxB,aAAa;wBACb,SAAS,UAAU;oBACrB;gBACF;yCACA;gBAAE,WAAW;YAAI;YAGnB,MAAM,aAAa,WAAW,OAAO;YACrC,IAAI,YAAY;gBACd,SAAS,OAAO,CAAC;YACnB;YAEA;0CAAO;oBACL,IAAI,YAAY;wBACd,SAAS,SAAS,CAAC;oBACrB;gBACF;;QACF;iCAAG,EAAE;IAEL,qBACE,6LAAC;QAAQ,KAAK;kDAAsB;;0BAElC,6LAAC;0DAAc;;;;;;0BACf,6LAAC;0DAAc;;;;;;0BAGf,6LAAC;gBACC,OAAO;oBACL,iBAAiB;oBACjB,gBAAgB;gBAClB;0DAJa;;;;;;0BAOf,6LAAC;0DAAc;;kCAEb,6LAAC;kEAAe,CAAC,0EAA0E,EAAE,YAAY,8BAA8B,4BAA4B;;0CACjK,6LAAC;0EAAc;0CACb,cAAA,6LAAC;8EAAc;8CACb,cAAA,6LAAC;wCAAI,OAAM;wCAAiE,MAAK;wCAAO,SAAQ;wCAAY,QAAO;kFAAjE;kDAChD,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;0CAI3E,6LAAC;0EAAa;0CAAoD;;;;;;0CAClE,6LAAC;0EAAc;;;;;;0CACf,6LAAC;0EAAY;0CAAwC;;;;;;;;;;;;kCAKvD,6LAAC;kEAAc;;0CAEb,6LAAC;0EAAe,CAAC,0DAA0D,EAAE,YAAY,8BAA8B,6BAA6B;;kDAClJ,6LAAC;kFAAc;;0DACb,6LAAC;0FAAa;;kEACZ,6LAAC;kGAAe;kEACd,cAAA,6LAAC;4DAAI,OAAM;4DAAiD,MAAK;4DAAO,SAAQ;4DAAY,QAAO;sGAAjD;sEAChD,cAAA,6LAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;;;;;;;oDAElE;;;;;;;0DAGT,6LAAC;0FAAY;0DAAqC;;;;;;0DAGlD,6LAAC;0FAAY;0DAAgC;;;;;;;;;;;;kDAK/C,6LAAC;kFAAc;;0DACb,6LAAC;0FAAa;0DAAyC;;;;;;0DACvD,6LAAC;0FAAc;0DACZ;oDACC;oDACA;oDACA;oDACA;oDACA;oDACA;iDACD,CAAC,GAAG,CAAC,CAAC,MAAM,sBACX,6LAAC;wDAGC,OAAO;4DACL,iBAAiB,GAAG,QAAQ,IAAI,EAAE,CAAC;4DACnC,WAAW,YAAY,CAAC,mCAAmC,EAAE,MAAM,QAAQ,IAAI,EAAE,CAAC,GAAG;4DACrF,SAAS;4DACT,WAAW;wDACb;kGANU;;0EAQV,6LAAC;0GAAc;0EACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;;;;;;0EAEnB,6LAAC;0GAAe;0EAA6B;;;;;;;uDAZxC;;;;;;;;;;0DAiBX,6LAAC;0FAAc;0DACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAY,WAAU;;wDAAuG;sEAEtI,6LAAC,qNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAO9B,6LAAC;0EAAe,CAAC,gDAAgD,EAAE,YAAY,8BAA8B,4BAA4B;0CACvI,cAAA,6LAAC;8EAAc;;sDAEb,6LAAC;sFAAc;;8DACb,6LAAC,gIAAA,CAAA,UAAK;oDACJ,KAAI;oDACJ,KAAI;oDACJ,IAAI;oDACJ,OAAM;oDACN,WAAU;;;;;;8DAEZ,6LAAC;8FAAc;;;;;;;;;;;;sDAIjB,6LAAC;sFAAc;;8DACb,6LAAC;8FAAc;;sEACb,6LAAC;sGAAc;sEACb,cAAA,6LAAC;gEAAI,OAAM;gEAA+D,MAAK;gEAAO,SAAQ;gEAAY,QAAO;0GAA/D;0EAChD,cAAA,6LAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAa;oEAAG,GAAE;;;;;;;;;;;;;;;;;sEAGzE,6LAAC;sGAAa;sEAA0B;;;;;;;;;;;;8DAE1C,6LAAC;8FAAY;8DAAwB;;;;;;;;;;;;sDAGvC,6LAAC;sFAAc;;8DACb,6LAAC;8FAAa;8DAAuC;;;;;;8DACrD,6LAAC;8FAAY;8DAA6B;;;;;;8DAG1C,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAS,WAAU;;wDAAqF;sEACvG,6LAAC,qNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;;;;;;;;;;;;;sDAKpC,6LAAC;sFAAc;sDACZ;mDAAI,MAAM;6CAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC;oDAGC,OAAO;wDACL,KAAK,GAAG,KAAK,KAAK,MAAM,KAAK,GAAG,CAAC,CAAC;wDAClC,MAAM,GAAG,KAAK,KAAK,MAAM,KAAK,GAAG,CAAC,CAAC;wDACnC,WAAW,CAAC,MAAM,EAAE,IAAI,KAAK,MAAM,KAAK,EAAE,uBAAuB,EAAE,KAAK,MAAM,KAAK,EAAE,CAAC,CAAC;oDACzF;8FALU;mDADL;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkCzB;GAhMM;KAAA;uCAiMS", "debugId": null}}, {"offset": {"line": 531, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/fwu_incubation%28react%2Blaravel%29/incubation/frontend/src/app/components/home/<USER>"], "sourcesContent": ["// components/home/<USER>\nimport Image from 'next/image';\r\nimport Link from 'next/link';\r\nimport { ArrowRight, ExternalLink, Award } from 'lucide-react';\r\n\r\nexport interface Startup {\r\n  id: string;\r\n  name: string;\r\n  logoUrl: string;\r\n  description: string;\r\n  website: string;\r\n  industry: string;\r\n  founded?: string;\r\n  stage?: string;\r\n}\r\n\r\ninterface StartupCardProps {\r\n  startup: Startup;\r\n}\r\n\r\nconst StartupCard: React.FC<StartupCardProps> = ({ startup }) => {\r\n  return (\r\n    <div className=\"bg-white rounded-2xl shadow-lg overflow-hidden border border-gray-100 hover:border-indigo-100 transition-all duration-300 hover:shadow-indigo-100/20 h-full flex flex-col group\">\r\n      {/* Top accent bar */}\r\n      <div className=\"h-1.5 bg-gradient-to-r from-indigo-500 to-blue-500\"></div>\r\n\r\n      <div className=\"p-6 flex flex-col items-center text-center h-full\">\r\n        {/* Logo with glow effect on hover */}\r\n        <div className=\"relative w-20 h-20 mb-5\">\r\n          <div className=\"absolute inset-0 bg-indigo-100 rounded-full opacity-30 group-hover:opacity-70 transition-opacity duration-300 scale-[1.15] group-hover:scale-[1.3] blur-md\"></div>\r\n          <div className=\"relative w-20 h-20 rounded-full overflow-hidden border-2 border-indigo-100 shadow-md group-hover:shadow-indigo-200 transition-all duration-300 z-10\">\r\n            <Image\r\n              src={startup.logoUrl}\r\n              alt={`${startup.name} logo`}\r\n              fill\r\n              sizes=\"80px\"\r\n              className=\"object-cover\"\r\n            />\r\n          </div>\r\n\r\n          {/* Decorative elements */}\r\n          <div className=\"absolute -top-1 -right-1 w-5 h-5 bg-indigo-100 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-0\"></div>\r\n          <div className=\"absolute -bottom-1 -left-1 w-3 h-3 bg-blue-100 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-0\"></div>\r\n        </div>\r\n\r\n        {/* Startup name with gradient text on hover */}\r\n        <h3 className=\"text-xl font-bold text-gray-900 mb-2 group-hover:text-transparent group-hover:bg-clip-text group-hover:bg-gradient-to-r group-hover:from-indigo-600 group-hover:to-blue-600 transition-all duration-300\">\r\n          {startup.name}\r\n        </h3>\r\n\r\n        {/* Category tag */}\r\n        <div className=\"bg-indigo-50 text-indigo-700 text-xs font-medium px-3 py-1 rounded-full mb-4 inline-flex items-center\">\r\n          <span className=\"w-1.5 h-1.5 bg-indigo-500 rounded-full mr-1.5\"></span>\r\n          {startup.industry}\r\n        </div>\r\n\r\n        {/* Additional info if available */}\r\n        {(startup.founded || startup.stage) && (\r\n          <div className=\"flex items-center justify-center gap-3 mb-4 text-xs text-gray-500\">\r\n            {startup.founded && (\r\n              <div className=\"flex items-center\">\r\n                <span className=\"mr-1\">Founded:</span>\r\n                <span className=\"font-medium text-gray-700\">{startup.founded}</span>\r\n              </div>\r\n            )}\r\n            {startup.stage && (\r\n              <div className=\"flex items-center\">\r\n                <Award className=\"mr-1 text-indigo-500\" />\r\n                <span className=\"font-medium text-gray-700\">{startup.stage}</span>\r\n              </div>\r\n            )}\r\n          </div>\r\n        )}\r\n\r\n        {/* Description */}\r\n        <p className=\"text-gray-600 text-sm mb-6 flex-grow\">{startup.description}</p>\r\n\r\n        {/* Link with hover effect */}\r\n        <Link\r\n          href={startup.website}\r\n          className=\"mt-auto inline-flex items-center text-indigo-600 hover:text-indigo-800 font-semibold transition-colors group\"\r\n        >\r\n          <span>View Details</span>\r\n          <ArrowRight className=\"ml-2 group-hover:translate-x-1 transition-transform duration-300\" />\r\n        </Link>\r\n      </div>\r\n\r\n      {/* Bottom accent with external link */}\r\n      <div className=\"mt-auto border-t border-gray-100 py-3 px-6 flex justify-between items-center bg-gray-50 group-hover:bg-indigo-50 transition-colors duration-300\">\r\n        <span className=\"text-xs text-gray-500\">FWU Incubated</span>\r\n        <Link\r\n          href={startup.website}\r\n          target=\"_blank\"\r\n          rel=\"noopener noreferrer\"\r\n          className=\"text-indigo-500 hover:text-indigo-700\"\r\n          aria-label={`Visit ${startup.name} website`}\r\n        >\r\n          <ExternalLink />\r\n        </Link>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default StartupCard;"], "names": [], "mappings": "AAAA,kCAAkC;;;;;AAClC;AACA;AACA;AAAA;AAAA;;;;;AAiBA,MAAM,cAA0C,CAAC,EAAE,OAAO,EAAE;IAC1D,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;;;;;0BAEf,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;oCACJ,KAAK,QAAQ,OAAO;oCACpB,KAAK,GAAG,QAAQ,IAAI,CAAC,KAAK,CAAC;oCAC3B,IAAI;oCACJ,OAAM;oCACN,WAAU;;;;;;;;;;;0CAKd,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;;;;;;;;;;;;kCAIjB,6LAAC;wBAAG,WAAU;kCACX,QAAQ,IAAI;;;;;;kCAIf,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;;;;;;4BACf,QAAQ,QAAQ;;;;;;;oBAIlB,CAAC,QAAQ,OAAO,IAAI,QAAQ,KAAK,mBAChC,6LAAC;wBAAI,WAAU;;4BACZ,QAAQ,OAAO,kBACd,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAO;;;;;;kDACvB,6LAAC;wCAAK,WAAU;kDAA6B,QAAQ,OAAO;;;;;;;;;;;;4BAG/D,QAAQ,KAAK,kBACZ,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,6LAAC;wCAAK,WAAU;kDAA6B,QAAQ,KAAK;;;;;;;;;;;;;;;;;;kCAOlE,6LAAC;wBAAE,WAAU;kCAAwC,QAAQ,WAAW;;;;;;kCAGxE,6LAAC,+JAAA,CAAA,UAAI;wBACH,MAAM,QAAQ,OAAO;wBACrB,WAAU;;0CAEV,6LAAC;0CAAK;;;;;;0CACN,6LAAC,qNAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;;;;;;;;;;;;;0BAK1B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAK,WAAU;kCAAwB;;;;;;kCACxC,6LAAC,+JAAA,CAAA,UAAI;wBACH,MAAM,QAAQ,OAAO;wBACrB,QAAO;wBACP,KAAI;wBACJ,WAAU;wBACV,cAAY,CAAC,MAAM,EAAE,QAAQ,IAAI,CAAC,QAAQ,CAAC;kCAE3C,cAAA,6LAAC,yNAAA,CAAA,eAAY;;;;;;;;;;;;;;;;;;;;;;AAKvB;KAlFM;uCAoFS", "debugId": null}}, {"offset": {"line": 781, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/fwu_incubation%28react%2Blaravel%29/incubation/frontend/src/app/components/home/<USER>"], "sourcesContent": ["// components/home/<USER>\n\"use client\"\r\nimport StartupCard, { Startup } from './StartupCard';\r\nimport { useEffect, useState, useRef } from 'react';\r\nimport Link from 'next/link';\r\nimport { ArrowRight, Briefcase } from 'lucide-react';\r\n\r\nconst startupsData: Startup[] = [\r\n  {\r\n    id: 's1',\r\n    name: 'EcoHarvest',\r\n    logoUrl: 'https://images.unsplash.com/photo-1526666923127-b2970f64b422?q=80&w=200&auto=format&fit=crop',\r\n    description: 'Sustainable agricultural technology solutions for small-scale farmers.',\r\n    website: '/startups/ecoharvest',\r\n    industry: 'AgriTech',\r\n  },\r\n  {\r\n    id: 's2',\r\n    name: 'MediConnect',\r\n    logoUrl: 'https://images.unsplash.com/photo-1576091160550-2173dba999ef?q=80&w=200&auto=format&fit=crop',\r\n    description: 'Telemedicine platform connecting rural patients with healthcare providers.',\r\n    website: '/startups/mediconnect',\r\n    industry: 'HealthTech',\r\n  },\r\n  {\r\n    id: 's3',\r\n    name: 'EduReach',\r\n    logoUrl: 'https://images.unsplash.com/photo-1503428593586-e225b39bddfe?q=80&w=200&auto=format&fit=crop',\r\n    description: 'Digital education solutions for underserved communities.',\r\n    website: '/startups/edureach',\r\n    industry: 'EdTech',\r\n  },\r\n  {\r\n    id: 's4',\r\n    name: 'SolarLife',\r\n    logoUrl: 'https://images.unsplash.com/photo-**********-52b4451f994b?q=80&w=200&auto=format&fit=crop',\r\n    description: 'Affordable solar energy solutions for homes and small businesses.',\r\n    website: '/startups/solarlife',\r\n    industry: 'CleanTech',\r\n  },\r\n];\r\n\r\nconst FeaturedStartups = () => {\r\n  const [isVisible, setIsVisible] = useState(false);\r\n  const sectionRef = useRef(null);\r\n  const [activeIndex, setActiveIndex] = useState(0);\r\n\r\n  // Intersection Observer for section visibility\r\n  useEffect(() => {\r\n    const observer = new IntersectionObserver(\r\n      ([entry]) => {\r\n        if (entry.isIntersecting) {\r\n          setIsVisible(true);\r\n          observer.disconnect();\r\n        }\r\n      },\r\n      { threshold: 0.1 }\r\n    );\r\n\r\n    const currentRef = sectionRef.current;\r\n    if (currentRef) {\r\n      observer.observe(currentRef);\r\n    }\r\n\r\n    return () => {\r\n      if (currentRef) {\r\n        observer.unobserve(currentRef);\r\n      }\r\n    };\r\n  }, []);\r\n\r\n  // Auto-rotate active card\r\n  useEffect(() => {\r\n    if (!isVisible) return;\r\n\r\n    const interval = setInterval(() => {\r\n      setActiveIndex((prev) => (prev + 1) % startupsData.length);\r\n    }, 3000);\r\n\r\n    return () => clearInterval(interval);\r\n  }, [isVisible]);\r\n\r\n  return (\r\n    <section ref={sectionRef} className=\"py-20 md:py-28 bg-gradient-to-b from-white to-indigo-50 relative overflow-hidden\">\r\n      {/* Background decorative elements */}\r\n      <div className=\"absolute top-0 right-0 w-[600px] h-[600px] bg-indigo-100 rounded-full opacity-30 blur-3xl -translate-x-1/3 -translate-y-1/3\"></div>\r\n      <div className=\"absolute bottom-0 left-0 w-[500px] h-[500px] bg-blue-100 rounded-full opacity-30 blur-3xl translate-x-1/4 translate-y-1/4\"></div>\r\n\r\n      {/* Subtle pattern overlay */}\r\n      <div className=\"absolute inset-0 opacity-5\"\r\n        style={{\r\n          backgroundImage: 'radial-gradient(#4338ca 1px, transparent 1px)',\r\n          backgroundSize: '40px 40px'\r\n        }}>\r\n      </div>\r\n\r\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 relative z-10\">\r\n        {/* Section header */}\r\n        <div className={`max-w-3xl mx-auto text-center mb-16 transition-all duration-700 transform ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>\r\n          <div className=\"inline-block mb-4\">\r\n            <div className=\"flex items-center justify-center w-16 h-16 mx-auto bg-indigo-100 rounded-full mb-4\">\r\n              <Briefcase className=\"h-8 w-8 text-indigo-600\" />\r\n            </div>\r\n          </div>\r\n          <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">Our Featured Startups</h2>\r\n          <div className=\"w-24 h-1 bg-indigo-600 mx-auto mb-6 rounded-full\"></div>\r\n          <p className=\"text-xl text-gray-600 leading-relaxed\">\r\n            Meet the innovative ventures that are transforming ideas into impactful solutions\r\n          </p>\r\n        </div>\r\n\r\n        <div className=\"relative mt-12\">\r\n          {/* Animated highlight circle that moves between cards */}\r\n          <div\r\n            className=\"absolute w-full h-full transition-all duration-500 ease-in-out pointer-events-none\"\r\n            style={{\r\n              clipPath: 'circle(15% at 0% 0%)',\r\n              background: 'radial-gradient(circle, rgba(79, 70, 229, 0.1) 0%, rgba(79, 70, 229, 0) 70%)',\r\n              transform: `translate(${25 * activeIndex}%, 0)`,\r\n            }}\r\n          ></div>\r\n\r\n          {/* Startup Cards */}\r\n          <div className=\"grid md:grid-cols-2 lg:grid-cols-4 gap-8 relative z-10\">\r\n            {startupsData.map((startup, index) => (\r\n              <div\r\n                key={startup.id}\r\n                className={`transform transition-all duration-700 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-20'}`}\r\n                style={{\r\n                  transitionDelay: `${index * 150}ms`,\r\n                  transform: activeIndex === index ? 'scale(1.03)' : 'scale(1)',\r\n                  zIndex: activeIndex === index ? 10 : 1,\r\n                }}\r\n                onMouseEnter={() => setActiveIndex(index)}\r\n              >\r\n                <StartupCard startup={startup} />\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Success metrics */}\r\n        <div className={`mt-20 bg-white rounded-2xl shadow-lg p-8 md:p-10 transition-all duration-700 delay-500 transform ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>\r\n          <div className=\"grid md:grid-cols-2 lg:grid-cols-4 gap-8\">\r\n            <div className=\"text-center\">\r\n              <div className=\"text-4xl font-bold text-indigo-600 mb-2\">₹25M+</div>\r\n              <p className=\"text-gray-600\">Funding Raised</p>\r\n            </div>\r\n            <div className=\"text-center\">\r\n              <div className=\"text-4xl font-bold text-indigo-600 mb-2\">85%</div>\r\n              <p className=\"text-gray-600\">Startup Success Rate</p>\r\n            </div>\r\n            <div className=\"text-center\">\r\n              <div className=\"text-4xl font-bold text-indigo-600 mb-2\">200+</div>\r\n              <p className=\"text-gray-600\">Jobs Created</p>\r\n            </div>\r\n            <div className=\"text-center\">\r\n              <div className=\"text-4xl font-bold text-indigo-600 mb-2\">12</div>\r\n              <p className=\"text-gray-600\">Industry Partners</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* View all startups button */}\r\n        <div className={`mt-12 text-center transition-all duration-700 delay-700 transform ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>\r\n          <Link\r\n            href=\"/startups\"\r\n            className=\"inline-flex items-center bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-3 px-8 rounded-xl shadow-md transition-all duration-300 hover:-translate-y-1\"\r\n          >\r\n            View All Startups\r\n            <ArrowRight className=\"ml-2\" />\r\n          </Link>\r\n          <p className=\"text-gray-500 mt-4 text-sm\">\r\n            Discover more success stories from our incubation center\r\n          </p>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\nexport default FeaturedStartups;"], "names": [], "mappings": "AAAA,uCAAuC;;;;;AAEvC;AACA;AACA;AACA;AAAA;;;AAJA;;;;;AAMA,MAAM,eAA0B;IAC9B;QACE,IAAI;QACJ,MAAM;QACN,SAAS;QACT,aAAa;QACb,SAAS;QACT,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,SAAS;QACT,aAAa;QACb,SAAS;QACT,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,SAAS;QACT,aAAa;QACb,SAAS;QACT,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,SAAS;QACT,aAAa;QACb,SAAS;QACT,UAAU;IACZ;CACD;AAED,MAAM,mBAAmB;;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,+CAA+C;IAC/C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,MAAM,WAAW,IAAI;8CACnB,CAAC,CAAC,MAAM;oBACN,IAAI,MAAM,cAAc,EAAE;wBACxB,aAAa;wBACb,SAAS,UAAU;oBACrB;gBACF;6CACA;gBAAE,WAAW;YAAI;YAGnB,MAAM,aAAa,WAAW,OAAO;YACrC,IAAI,YAAY;gBACd,SAAS,OAAO,CAAC;YACnB;YAEA;8CAAO;oBACL,IAAI,YAAY;wBACd,SAAS,SAAS,CAAC;oBACrB;gBACF;;QACF;qCAAG,EAAE;IAEL,0BAA0B;IAC1B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI,CAAC,WAAW;YAEhB,MAAM,WAAW;uDAAY;oBAC3B;+DAAe,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,aAAa,MAAM;;gBAC3D;sDAAG;YAEH;8CAAO,IAAM,cAAc;;QAC7B;qCAAG;QAAC;KAAU;IAEd,qBACE,6LAAC;QAAQ,KAAK;QAAY,WAAU;;0BAElC,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAI,WAAU;;;;;;0BAGf,6LAAC;gBAAI,WAAU;gBACb,OAAO;oBACL,iBAAiB;oBACjB,gBAAgB;gBAClB;;;;;;0BAGF,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAW,CAAC,0EAA0E,EAAE,YAAY,8BAA8B,4BAA4B;;0CACjK,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,+MAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;;;;;;0CAGzB,6LAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAClE,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAE,WAAU;0CAAwC;;;;;;;;;;;;kCAKvD,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCACC,WAAU;gCACV,OAAO;oCACL,UAAU;oCACV,YAAY;oCACZ,WAAW,CAAC,UAAU,EAAE,KAAK,YAAY,KAAK,CAAC;gCACjD;;;;;;0CAIF,6LAAC;gCAAI,WAAU;0CACZ,aAAa,GAAG,CAAC,CAAC,SAAS,sBAC1B,6LAAC;wCAEC,WAAW,CAAC,sCAAsC,EAAE,YAAY,8BAA8B,4BAA4B;wCAC1H,OAAO;4CACL,iBAAiB,GAAG,QAAQ,IAAI,EAAE,CAAC;4CACnC,WAAW,gBAAgB,QAAQ,gBAAgB;4CACnD,QAAQ,gBAAgB,QAAQ,KAAK;wCACvC;wCACA,cAAc,IAAM,eAAe;kDAEnC,cAAA,6LAAC,mJAAA,CAAA,UAAW;4CAAC,SAAS;;;;;;uCATjB,QAAQ,EAAE;;;;;;;;;;;;;;;;kCAgBvB,6LAAC;wBAAI,WAAW,CAAC,iGAAiG,EAAE,YAAY,8BAA8B,4BAA4B;kCACxL,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAA0C;;;;;;sDACzD,6LAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAE/B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAA0C;;;;;;sDACzD,6LAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAE/B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAA0C;;;;;;sDACzD,6LAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAE/B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAA0C;;;;;;sDACzD,6LAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;;;;;;kCAMnC,6LAAC;wBAAI,WAAW,CAAC,kEAAkE,EAAE,YAAY,8BAA8B,4BAA4B;;0CACzJ,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;oCACX;kDAEC,6LAAC,qNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;;0CAExB,6LAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;;;;;;;;;;;;;;AAOpD;GAzIM;KAAA;uCA2IS", "debugId": null}}, {"offset": {"line": 1192, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/fwu_incubation%28react%2Blaravel%29/incubation/frontend/src/app/components/home/<USER>"], "sourcesContent": ["// components/home/<USER>\nimport Image from 'next/image';\r\nimport { Quote, Star } from 'lucide-react';\r\n\r\nexport interface Testimonial {\r\n  id: string;\r\n  quote: string;\r\n  author: string;\r\n  role: string;\r\n  avatarUrl?: string;\r\n  rating?: number;\r\n}\r\n\r\ninterface TestimonialCardProps {\r\n  testimonial: Testimonial;\r\n}\r\n\r\nconst TestimonialCard: React.FC<TestimonialCardProps> = ({ testimonial }) => {\r\n  return (\r\n    <div className=\"bg-white p-8 md:p-10 rounded-2xl shadow-xl relative h-full flex flex-col border border-gray-100 hover:shadow-indigo-100/30 transition-all duration-300 hover:border-indigo-200 group\">\r\n      {/* Decorative top accent */}\r\n      <div className=\"absolute top-0 left-0 right-0 h-1.5 bg-gradient-to-r from-indigo-600 via-indigo-500 to-blue-500 rounded-t-2xl\"></div>\r\n\r\n      {/* Quote icon with gradient background */}\r\n      <div className=\"absolute -top-5 left-8 w-10 h-10 rounded-full bg-gradient-to-br from-indigo-600 to-indigo-800 flex items-center justify-center shadow-lg transform group-hover:scale-110 transition-transform duration-300\">\r\n        <Quote className=\"text-white text-sm\" />\r\n      </div>\r\n\r\n      {/* Rating stars */}\r\n      <div className=\"flex mb-6 text-yellow-400\">\r\n        {[...Array(testimonial.rating || 5)].map((_, i) => (\r\n          <Star key={i} className=\"mr-1\" />\r\n        ))}\r\n      </div>\r\n\r\n      {/* Quote text */}\r\n      <p className=\"text-gray-700 text-lg leading-relaxed mb-8 flex-grow\">\r\n        <span className=\"italic text-indigo-900 font-medium\">&ldquo;{testimonial.quote}&rdquo;</span>\r\n      </p>\r\n\r\n      {/* Author info */}\r\n      <div className=\"flex items-center mt-auto pt-6 border-t border-gray-100\">\r\n        {testimonial.avatarUrl && (\r\n          <div className=\"relative\">\r\n            <div className=\"absolute inset-0 bg-indigo-100 rounded-full opacity-30 group-hover:opacity-70 transition-opacity duration-300 scale-[1.15] group-hover:scale-[1.3] blur-md\"></div>\r\n            <div className=\"relative w-16 h-16 rounded-full overflow-hidden mr-4 border-2 border-indigo-100 shadow-md group-hover:shadow-indigo-200 transition-all duration-300 z-10\">\r\n              <Image\r\n                src={testimonial.avatarUrl}\r\n                alt={testimonial.author}\r\n                fill\r\n                sizes=\"64px\"\r\n                className=\"object-cover\"\r\n              />\r\n            </div>\r\n          </div>\r\n        )}\r\n        <div>\r\n          <p className=\"font-bold text-gray-900\">{testimonial.author}</p>\r\n          <p className=\"text-sm text-indigo-600\">{testimonial.role}</p>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Decorative elements */}\r\n      <div className=\"absolute top-6 right-6 opacity-10 group-hover:opacity-20 transition-opacity duration-300\">\r\n        <svg width=\"40\" height=\"40\" viewBox=\"0 0 40 40\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n          <path d=\"M0 16L16 0L40 0L40 24L24 40L0 40L0 16Z\" fill=\"#4f46e5\" />\r\n        </svg>\r\n      </div>\r\n\r\n      <div className=\"absolute bottom-6 left-6 opacity-10 group-hover:opacity-20 transition-opacity duration-300\">\r\n        <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n          <circle cx=\"12\" cy=\"12\" r=\"12\" fill=\"#4f46e5\" />\r\n        </svg>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default TestimonialCard;"], "names": [], "mappings": "AAAA,sCAAsC;;;;;AACtC;AACA;AAAA;;;;AAeA,MAAM,kBAAkD,CAAC,EAAE,WAAW,EAAE;IACtE,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;;;;;0BAGf,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;;;;;;0BAInB,6LAAC;gBAAI,WAAU;0BACZ;uBAAI,MAAM,YAAY,MAAM,IAAI;iBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBAC3C,6LAAC,qMAAA,CAAA,OAAI;wBAAS,WAAU;uBAAb;;;;;;;;;;0BAKf,6LAAC;gBAAE,WAAU;0BACX,cAAA,6LAAC;oBAAK,WAAU;;wBAAqC;wBAAQ,YAAY,KAAK;wBAAC;;;;;;;;;;;;0BAIjF,6LAAC;gBAAI,WAAU;;oBACZ,YAAY,SAAS,kBACpB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;oCACJ,KAAK,YAAY,SAAS;oCAC1B,KAAK,YAAY,MAAM;oCACvB,IAAI;oCACJ,OAAM;oCACN,WAAU;;;;;;;;;;;;;;;;;kCAKlB,6LAAC;;0CACC,6LAAC;gCAAE,WAAU;0CAA2B,YAAY,MAAM;;;;;;0CAC1D,6LAAC;gCAAE,WAAU;0CAA2B,YAAY,IAAI;;;;;;;;;;;;;;;;;;0BAK5D,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,OAAM;oBAAK,QAAO;oBAAK,SAAQ;oBAAY,MAAK;oBAAO,OAAM;8BAChE,cAAA,6LAAC;wBAAK,GAAE;wBAAyC,MAAK;;;;;;;;;;;;;;;;0BAI1D,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,OAAM;oBAAK,QAAO;oBAAK,SAAQ;oBAAY,MAAK;oBAAO,OAAM;8BAChE,cAAA,6LAAC;wBAAO,IAAG;wBAAK,IAAG;wBAAK,GAAE;wBAAK,MAAK;;;;;;;;;;;;;;;;;;;;;;AAK9C;KA3DM;uCA6DS", "debugId": null}}, {"offset": {"line": 1404, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/fwu_incubation%28react%2Blaravel%29/incubation/frontend/src/app/components/home/<USER>"], "sourcesContent": ["// components/home/<USER>\n\"use client\"\r\nimport TestimonialCard, { Testimonial } from './TestimonialCard';\r\nimport { useEffect, useState, useRef } from 'react';\r\nimport { MessageSquare, ChevronLeft, ChevronRight } from 'lucide-react';\r\n\r\nconst testimonialsData: Testimonial[] = [\r\n  {\r\n    id: 't1',\r\n    quote: \"The FWU Incubation Center provided me with exceptional mentorship and resources. Their guidance and practical approach helped me transform my idea into a successful startup.\",\r\n    author: '<PERSON><PERSON>',\r\n    role: 'Founder, EcoHarvest',\r\n    avatarUrl: 'https://images.unsplash.com/photo-1494790108377-be9c29b29330?q=80&w=200&auto=format&fit=crop',\r\n    rating: 5,\r\n  },\r\n  {\r\n    id: 't2',\r\n    quote: \"The networking opportunities and funding connections at FWU Incubation Center are outstanding. I was able to secure seed funding within six months thanks to their support.\",\r\n    author: '<PERSON><PERSON>',\r\n    role: 'CEO, MediConnect',\r\n    avatarUrl: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?q=80&w=200&auto=format&fit=crop',\r\n    rating: 5,\r\n  },\r\n  {\r\n    id: 't3',\r\n    quote: \"As a first-time entrepreneur, I found the FWU Incubation Center to be incredibly supportive. Their workshops and mentorship programs gave me the knowledge and confidence to launch my business.\",\r\n    author: 'Nirmala Joshi',\r\n    role: 'Founder, EduReach',\r\n    avatarUrl: 'https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?q=80&w=200&auto=format&fit=crop',\r\n    rating: 5,\r\n  },\r\n];\r\n\r\nconst Testimonials = () => {\r\n  const [isVisible, setIsVisible] = useState(false);\r\n  const [activeIndex, setActiveIndex] = useState(0);\r\n  const sectionRef = useRef(null);\r\n\r\n  // Intersection Observer for section visibility\r\n  useEffect(() => {\r\n    const observer = new IntersectionObserver(\r\n      ([entry]) => {\r\n        if (entry.isIntersecting) {\r\n          setIsVisible(true);\r\n          observer.disconnect();\r\n        }\r\n      },\r\n      { threshold: 0.1 }\r\n    );\r\n\r\n    const currentRef = sectionRef.current;\r\n    if (currentRef) {\r\n      observer.observe(currentRef);\r\n    }\r\n\r\n    return () => {\r\n      if (currentRef) {\r\n        observer.unobserve(currentRef);\r\n      }\r\n    };\r\n  }, []);\r\n\r\n  // Auto-rotate testimonials\r\n  useEffect(() => {\r\n    if (!isVisible) return;\r\n\r\n    const interval = setInterval(() => {\r\n      setActiveIndex((prev) => (prev + 1) % testimonialsData.length);\r\n    }, 5000);\r\n\r\n    return () => clearInterval(interval);\r\n  }, [isVisible]);\r\n\r\n  // Navigation functions\r\n  const goToPrevious = () => {\r\n    setActiveIndex((prev) => (prev === 0 ? testimonialsData.length - 1 : prev - 1));\r\n  };\r\n\r\n  const goToNext = () => {\r\n    setActiveIndex((prev) => (prev + 1) % testimonialsData.length);\r\n  };\r\n\r\n  return (\r\n    <section ref={sectionRef} className=\"py-20 md:py-28 relative overflow-hidden bg-gradient-to-b from-indigo-50 to-white\">\r\n      {/* Background decorative elements */}\r\n      <div className=\"absolute top-0 right-0 w-[600px] h-[600px] bg-indigo-100 rounded-full opacity-30 blur-3xl -translate-x-1/3 -translate-y-1/3\"></div>\r\n      <div className=\"absolute bottom-0 left-0 w-[500px] h-[500px] bg-blue-100 rounded-full opacity-30 blur-3xl translate-x-1/4 translate-y-1/4\"></div>\r\n\r\n      {/* Subtle pattern overlay */}\r\n      <div className=\"absolute inset-0 opacity-5\"\r\n        style={{\r\n          backgroundImage: 'radial-gradient(#4338ca 1px, transparent 1px)',\r\n          backgroundSize: '40px 40px'\r\n        }}>\r\n      </div>\r\n\r\n      {/* Large quote marks */}\r\n      <div className=\"absolute top-20 left-20 text-indigo-200 opacity-20 text-9xl font-serif\">&ldquo;</div>\r\n      <div className=\"absolute bottom-20 right-20 text-indigo-200 opacity-20 text-9xl font-serif\">&rdquo;</div>\r\n\r\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 relative z-10\">\r\n        {/* Section header */}\r\n        <div className={`max-w-3xl mx-auto text-center mb-16 transition-all duration-700 transform ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>\r\n          <div className=\"inline-block mb-4\">\r\n            <div className=\"flex items-center justify-center w-16 h-16 mx-auto bg-indigo-100 rounded-full mb-4\">\r\n              <MessageSquare className=\"h-8 w-8 text-indigo-600\" />\r\n            </div>\r\n          </div>\r\n          <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">What Our Startups Say</h2>\r\n          <div className=\"w-24 h-1 bg-indigo-600 mx-auto mb-6 rounded-full\"></div>\r\n          <p className=\"text-xl text-gray-600 leading-relaxed\">\r\n            Hear from the entrepreneurs who have experienced our incubation program\r\n          </p>\r\n        </div>\r\n\r\n        {/* Testimonial Cards with Animation */}\r\n        <div className=\"mt-16 relative max-w-5xl mx-auto\">\r\n          {/* Navigation buttons */}\r\n          <div className=\"absolute top-1/2 -left-4 md:-left-12 transform -translate-y-1/2 z-20\">\r\n            <button\r\n              onClick={goToPrevious}\r\n              className=\"w-10 h-10 md:w-12 md:h-12 bg-white rounded-full shadow-lg flex items-center justify-center text-indigo-600 hover:text-indigo-800 hover:shadow-xl transition-all duration-300 focus:outline-none\"\r\n              aria-label=\"Previous testimonial\"\r\n            >\r\n              <ChevronLeft className=\"w-5 h-5 md:w-6 md:h-6\" />\r\n            </button>\r\n          </div>\r\n\r\n          <div className=\"absolute top-1/2 -right-4 md:-right-12 transform -translate-y-1/2 z-20\">\r\n            <button\r\n              onClick={goToNext}\r\n              className=\"w-10 h-10 md:w-12 md:h-12 bg-white rounded-full shadow-lg flex items-center justify-center text-indigo-600 hover:text-indigo-800 hover:shadow-xl transition-all duration-300 focus:outline-none\"\r\n              aria-label=\"Next testimonial\"\r\n            >\r\n              <ChevronRight className=\"w-5 h-5 md:w-6 md:h-6\" />\r\n            </button>\r\n          </div>\r\n\r\n          {/* Testimonial slider */}\r\n          <div className=\"relative overflow-hidden px-4\">\r\n            <div\r\n              className=\"flex transition-transform duration-500 ease-in-out\"\r\n              style={{ transform: `translateX(-${activeIndex * 100}%)` }}\r\n            >\r\n              {testimonialsData.map((testimonial, index) => (\r\n                <div\r\n                  key={testimonial.id}\r\n                  className=\"w-full flex-shrink-0 px-4\"\r\n                >\r\n                  <div className={`transition-all duration-500 transform ${activeIndex === index ? 'scale-100 opacity-100' : 'scale-95 opacity-50'}`}>\r\n                    <TestimonialCard testimonial={testimonial} />\r\n                  </div>\r\n                </div>\r\n              ))}\r\n            </div>\r\n          </div>\r\n\r\n          {/* Navigation Dots */}\r\n          <div className=\"flex justify-center mt-10 space-x-2\">\r\n            {testimonialsData.map((_, index) => (\r\n              <button\r\n                key={index}\r\n                onClick={() => setActiveIndex(index)}\r\n                className={`transition-all duration-300 focus:outline-none ${\r\n                  activeIndex === index\r\n                    ? 'w-8 h-2 bg-indigo-600 rounded-full'\r\n                    : 'w-2 h-2 bg-indigo-300 rounded-full hover:bg-indigo-400'\r\n                }`}\r\n                aria-label={`Go to testimonial ${index + 1}`}\r\n              />\r\n            ))}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Call to action */}\r\n        <div className={`mt-16 text-center transition-all duration-700 delay-500 transform ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>\r\n          <p className=\"text-xl font-medium text-gray-900 mb-6\">\r\n            Ready to join our community of successful entrepreneurs?\r\n          </p>\r\n          <a\r\n            href=\"/apply\"\r\n            className=\"inline-flex items-center bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-3 px-8 rounded-xl shadow-md transition-all duration-300 hover:-translate-y-1\"\r\n          >\r\n            Apply Now\r\n          </a>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\nexport default Testimonials;"], "names": [], "mappings": "AAAA,mCAAmC;;;;;AAEnC;AACA;AACA;AAAA;AAAA;;;AAHA;;;;AAKA,MAAM,mBAAkC;IACtC;QACE,IAAI;QACJ,OAAO;QACP,QAAQ;QACR,MAAM;QACN,WAAW;QACX,QAAQ;IACV;IACA;QACE,IAAI;QACJ,OAAO;QACP,QAAQ;QACR,MAAM;QACN,WAAW;QACX,QAAQ;IACV;IACA;QACE,IAAI;QACJ,OAAO;QACP,QAAQ;QACR,MAAM;QACN,WAAW;QACX,QAAQ;IACV;CACD;AAED,MAAM,eAAe;;IACnB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAE1B,+CAA+C;IAC/C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM,WAAW,IAAI;0CACnB,CAAC,CAAC,MAAM;oBACN,IAAI,MAAM,cAAc,EAAE;wBACxB,aAAa;wBACb,SAAS,UAAU;oBACrB;gBACF;yCACA;gBAAE,WAAW;YAAI;YAGnB,MAAM,aAAa,WAAW,OAAO;YACrC,IAAI,YAAY;gBACd,SAAS,OAAO,CAAC;YACnB;YAEA;0CAAO;oBACL,IAAI,YAAY;wBACd,SAAS,SAAS,CAAC;oBACrB;gBACF;;QACF;iCAAG,EAAE;IAEL,2BAA2B;IAC3B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,CAAC,WAAW;YAEhB,MAAM,WAAW;mDAAY;oBAC3B;2DAAe,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,iBAAiB,MAAM;;gBAC/D;kDAAG;YAEH;0CAAO,IAAM,cAAc;;QAC7B;iCAAG;QAAC;KAAU;IAEd,uBAAuB;IACvB,MAAM,eAAe;QACnB,eAAe,CAAC,OAAU,SAAS,IAAI,iBAAiB,MAAM,GAAG,IAAI,OAAO;IAC9E;IAEA,MAAM,WAAW;QACf,eAAe,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,iBAAiB,MAAM;IAC/D;IAEA,qBACE,6LAAC;QAAQ,KAAK;QAAY,WAAU;;0BAElC,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAI,WAAU;;;;;;0BAGf,6LAAC;gBAAI,WAAU;gBACb,OAAO;oBACL,iBAAiB;oBACjB,gBAAgB;gBAClB;;;;;;0BAIF,6LAAC;gBAAI,WAAU;0BAAyE;;;;;;0BACxF,6LAAC;gBAAI,WAAU;0BAA6E;;;;;;0BAE5F,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAW,CAAC,0EAA0E,EAAE,YAAY,8BAA8B,4BAA4B;;0CACjK,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,2NAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;;;;;;;;;;;0CAG7B,6LAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAClE,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAE,WAAU;0CAAwC;;;;;;;;;;;;kCAMvD,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,SAAS;oCACT,WAAU;oCACV,cAAW;8CAEX,cAAA,6LAAC,uNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;;;;;;0CAI3B,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,SAAS;oCACT,WAAU;oCACV,cAAW;8CAEX,cAAA,6LAAC,yNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;;;;;;;;;;;0CAK5B,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,WAAU;oCACV,OAAO;wCAAE,WAAW,CAAC,YAAY,EAAE,cAAc,IAAI,EAAE,CAAC;oCAAC;8CAExD,iBAAiB,GAAG,CAAC,CAAC,aAAa,sBAClC,6LAAC;4CAEC,WAAU;sDAEV,cAAA,6LAAC;gDAAI,WAAW,CAAC,sCAAsC,EAAE,gBAAgB,QAAQ,0BAA0B,uBAAuB;0DAChI,cAAA,6LAAC,uJAAA,CAAA,UAAe;oDAAC,aAAa;;;;;;;;;;;2CAJ3B,YAAY,EAAE;;;;;;;;;;;;;;;0CAY3B,6LAAC;gCAAI,WAAU;0CACZ,iBAAiB,GAAG,CAAC,CAAC,GAAG,sBACxB,6LAAC;wCAEC,SAAS,IAAM,eAAe;wCAC9B,WAAW,CAAC,+CAA+C,EACzD,gBAAgB,QACZ,uCACA,0DACJ;wCACF,cAAY,CAAC,kBAAkB,EAAE,QAAQ,GAAG;uCAPvC;;;;;;;;;;;;;;;;kCAcb,6LAAC;wBAAI,WAAW,CAAC,kEAAkE,EAAE,YAAY,8BAA8B,4BAA4B;;0CACzJ,6LAAC;gCAAE,WAAU;0CAAyC;;;;;;0CAGtD,6LAAC;gCACC,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;AAOX;GA5JM;KAAA;uCA8JS", "debugId": null}}, {"offset": {"line": 1765, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/fwu_incubation%28react%2Blaravel%29/incubation/frontend/src/app/components/home/<USER>"], "sourcesContent": ["// components/home/<USER>\nimport Link from 'next/link';\r\nimport Image from 'next/image';\r\nimport { Calendar, ArrowRight, MapPin } from 'lucide-react';\r\n\r\nexport interface Program {\r\n  id: string;\r\n  title: string;\r\n  date: string;\r\n  description: string;\r\n  category: string;\r\n  imageUrl?: string;\r\n  link: string;\r\n  location?: string;\r\n}\r\n\r\ninterface ProgramCardProps {\r\n  program: Program;\r\n}\r\n\r\nconst ProgramCard: React.FC<ProgramCardProps> = ({ program }) => {\r\n  return (\r\n    <div className=\"bg-white rounded-2xl shadow-lg overflow-hidden border border-gray-100 hover:border-indigo-100 transition-all duration-300 hover:shadow-indigo-100/20 h-full flex flex-col group\">\r\n      {/* Image container */}\r\n      {program.imageUrl && (\r\n        <div className=\"relative h-52 overflow-hidden\">\r\n          <div className=\"absolute inset-0 bg-indigo-900/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-10\"></div>\r\n          <div className=\"relative h-full w-full\">\r\n            <Image\r\n              src={program.imageUrl}\r\n              alt={program.title}\r\n              fill\r\n              sizes=\"(max-width: 768px) 100vw, 33vw\"\r\n              className=\"object-cover transition-transform duration-700 group-hover:scale-110\"\r\n            />\r\n          </div>\r\n\r\n          {/* Category badge */}\r\n          <div className=\"absolute top-4 right-4 z-10\">\r\n            <div className=\"bg-indigo-600 text-white text-xs font-bold px-3 py-1 rounded-full shadow-md\">\r\n              {program.category}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Content */}\r\n      <div className=\"p-6 flex flex-col flex-grow\">\r\n        {/* Date */}\r\n        <div className=\"flex items-center text-sm text-indigo-600 font-medium mb-3\">\r\n          <Calendar className=\"mr-2\" />\r\n          <span>{program.date}</span>\r\n        </div>\r\n\r\n        {/* Title */}\r\n        <h3 className=\"text-xl font-bold text-gray-900 mb-3 group-hover:text-indigo-600 transition-colors\">\r\n          {program.title}\r\n        </h3>\r\n\r\n        {/* Description */}\r\n        <p className=\"text-gray-600 mb-5 flex-grow leading-relaxed\">\r\n          {program.description}\r\n        </p>\r\n\r\n        {/* Location (if available) */}\r\n        {program.location && (\r\n          <div className=\"flex items-center text-sm text-gray-500 mb-4\">\r\n            <MapPin className=\"mr-2 text-indigo-500\" />\r\n            <span>{program.location}</span>\r\n          </div>\r\n        )}\r\n\r\n        {/* CTA Link */}\r\n        <Link\r\n          href={program.link}\r\n          className=\"inline-flex items-center text-indigo-600 font-semibold hover:text-indigo-800 transition-colors group\"\r\n        >\r\n          Learn More <ArrowRight className=\"ml-2 group-hover:translate-x-1 transition-transform\" />\r\n        </Link>\r\n      </div>\r\n\r\n      {/* Bottom accent line */}\r\n      <div className=\"h-1 bg-gradient-to-r from-indigo-500 to-blue-500 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left\"></div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ProgramCard;"], "names": [], "mappings": "AAAA,kCAAkC;;;;;AAClC;AACA;AACA;AAAA;AAAA;;;;;AAiBA,MAAM,cAA0C,CAAC,EAAE,OAAO,EAAE;IAC1D,qBACE,6LAAC;QAAI,WAAU;;YAEZ,QAAQ,QAAQ,kBACf,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;4BACJ,KAAK,QAAQ,QAAQ;4BACrB,KAAK,QAAQ,KAAK;4BAClB,IAAI;4BACJ,OAAM;4BACN,WAAU;;;;;;;;;;;kCAKd,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACZ,QAAQ,QAAQ;;;;;;;;;;;;;;;;;0BAOzB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,6MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,6LAAC;0CAAM,QAAQ,IAAI;;;;;;;;;;;;kCAIrB,6LAAC;wBAAG,WAAU;kCACX,QAAQ,KAAK;;;;;;kCAIhB,6LAAC;wBAAE,WAAU;kCACV,QAAQ,WAAW;;;;;;oBAIrB,QAAQ,QAAQ,kBACf,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,6MAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,6LAAC;0CAAM,QAAQ,QAAQ;;;;;;;;;;;;kCAK3B,6LAAC,+JAAA,CAAA,UAAI;wBACH,MAAM,QAAQ,IAAI;wBAClB,WAAU;;4BACX;0CACY,6LAAC,qNAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;;;;;;;;;;;;;0BAKrC,6LAAC;gBAAI,WAAU;;;;;;;;;;;;AAGrB;KAjEM;uCAmES", "debugId": null}}, {"offset": {"line": 1948, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/fwu_incubation%28react%2Blaravel%29/incubation/frontend/src/app/components/home/<USER>"], "sourcesContent": ["// components/home/<USER>\n\"use client\"\r\nimport { useEffect, useState, useRef } from 'react';\r\nimport ProgramCard, { Program } from './ProgramCard';\r\nimport { ArrowRight, Calendar } from 'lucide-react';\r\nimport Link from 'next/link';\r\nimport Image from 'next/image';\r\n\r\nconst programsData: Program[] = [\r\n  {\r\n    id: '1',\r\n    title: 'InnovateHER: Women in Tech Bootcamp',\r\n    date: 'October 15-20, 2024',\r\n    description: 'An intensive bootcamp designed to empower women with tech skills and entrepreneurial mindset.',\r\n    category: 'Bootcamp',\r\n    imageUrl: 'https://images.unsplash.com/photo-1522202176988-66273c2fd55f?q=80&w=800&auto=format&fit=crop',\r\n    link: '/programs/innovate-her',\r\n  },\r\n  {\r\n    id: '2',\r\n    title: 'Seed Funding Pitch Day',\r\n    date: 'November 5, 2024',\r\n    description: 'Showcase your startup to a panel of investors and compete for seed funding.',\r\n    category: 'Pitch Event',\r\n    imageUrl: 'https://images.unsplash.com/photo-1591115765373-5207764f72e4?q=80&w=800&auto=format&fit=crop',\r\n    link: '/events/pitch-day',\r\n  },\r\n  {\r\n    id: '3',\r\n    title: 'AI & Machine Learning Workshop',\r\n    date: 'December 1-3, 2024',\r\n    description: 'Dive deep into the world of AI and ML with hands-on sessions from industry experts.',\r\n    category: 'Workshop',\r\n    imageUrl: 'https://images.unsplash.com/photo-1581092921461-7d65ca45393a?q=80&w=800&auto=format&fit=crop',\r\n    link: '/workshops/ai-ml',\r\n  },\r\n];\r\n\r\nconst UpcomingPrograms = () => {\r\n  const [isVisible, setIsVisible] = useState(false);\r\n  const sectionRef = useRef(null);\r\n\r\n  useEffect(() => {\r\n    const observer = new IntersectionObserver(\r\n      ([entry]) => {\r\n        if (entry.isIntersecting) {\r\n          setIsVisible(true);\r\n          observer.disconnect();\r\n        }\r\n      },\r\n      { threshold: 0.1 }\r\n    );\r\n\r\n    const currentRef = sectionRef.current;\r\n    if (currentRef) {\r\n      observer.observe(currentRef);\r\n    }\r\n\r\n    return () => {\r\n      if (currentRef) {\r\n        observer.unobserve(currentRef);\r\n      }\r\n    };\r\n  }, []);\r\n\r\n  return (\r\n    <section ref={sectionRef} className=\"py-20 md:py-28 bg-gradient-to-b from-indigo-50 to-white relative overflow-hidden\">\r\n      {/* Background decorative elements */}\r\n      <div className=\"absolute top-0 left-0 w-full h-64 bg-gradient-to-b from-indigo-100/50 to-transparent\"></div>\r\n      <div className=\"absolute -top-24 -right-24 w-96 h-96 bg-indigo-200 rounded-full opacity-20 blur-3xl\"></div>\r\n      <div className=\"absolute -bottom-24 -left-24 w-96 h-96 bg-blue-200 rounded-full opacity-20 blur-3xl\"></div>\r\n\r\n      {/* Subtle pattern overlay */}\r\n      <div className=\"absolute inset-0 opacity-5\"\r\n        style={{\r\n          backgroundImage: 'radial-gradient(#4f46e5 1px, transparent 1px)',\r\n          backgroundSize: '30px 30px'\r\n        }}>\r\n      </div>\r\n\r\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 relative z-10\">\r\n        {/* Section header */}\r\n        <div className={`max-w-3xl mx-auto text-center mb-16 transition-all duration-700 transform ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>\r\n          <div className=\"inline-block mb-4\">\r\n            <div className=\"flex items-center justify-center w-16 h-16 mx-auto bg-indigo-100 rounded-full mb-4\">\r\n              <Calendar className=\"h-8 w-8 text-indigo-600\" />\r\n            </div>\r\n          </div>\r\n          <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">Upcoming Programs & Events</h2>\r\n          <div className=\"w-24 h-1 bg-indigo-600 mx-auto mb-6 rounded-full\"></div>\r\n          <p className=\"text-xl text-gray-600 leading-relaxed\">\r\n            Join our exciting programs designed to accelerate your entrepreneurial journey\r\n          </p>\r\n        </div>\r\n\r\n        {/* Featured event - larger card */}\r\n        <div className={`mb-16 transition-all duration-700 delay-100 transform ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>\r\n          <div className=\"bg-white rounded-2xl shadow-xl overflow-hidden border border-gray-100 hover:border-indigo-100 transition-all duration-300 hover:shadow-indigo-100/20\">\r\n            <div className=\"flex flex-col lg:flex-row\">\r\n              <div className=\"lg:w-1/2 relative h-64 lg:h-auto\">\r\n                <div className=\"relative w-full h-full\">\r\n                  <Image\r\n                    src=\"https://images.unsplash.com/photo-1540575467063-178a50c2df87?q=80&w=1000&auto=format&fit=crop\"\r\n                    alt=\"FWU Innovation Summit\"\r\n                    fill\r\n                    className=\"object-cover\"\r\n                  />\r\n                </div>\r\n                <div className=\"absolute top-4 right-4 bg-indigo-600 text-white text-xs font-bold px-3 py-1 rounded-full\">\r\n                  Featured Event\r\n                </div>\r\n              </div>\r\n              <div className=\"lg:w-1/2 p-8 lg:p-10 flex flex-col\">\r\n                <div className=\"flex items-center text-sm text-indigo-600 font-medium mb-3\">\r\n                  <Calendar className=\"mr-2\" />\r\n                  <span>December 15-17, 2024</span>\r\n                </div>\r\n                <h3 className=\"text-2xl font-bold text-gray-900 mb-4\">FWU Innovation Summit 2024</h3>\r\n                <p className=\"text-gray-600 mb-6 flex-grow\">\r\n                  Join us for our annual innovation summit featuring keynote speakers, panel discussions, startup showcases, and networking opportunities with industry leaders and investors.\r\n                </p>\r\n                <div className=\"flex flex-wrap gap-3 mb-6\">\r\n                  <span className=\"bg-indigo-50 text-indigo-700 text-xs font-medium px-3 py-1 rounded-full\">\r\n                    Networking\r\n                  </span>\r\n                  <span className=\"bg-indigo-50 text-indigo-700 text-xs font-medium px-3 py-1 rounded-full\">\r\n                    Workshops\r\n                  </span>\r\n                  <span className=\"bg-indigo-50 text-indigo-700 text-xs font-medium px-3 py-1 rounded-full\">\r\n                    Pitch Competition\r\n                  </span>\r\n                </div>\r\n                <Link\r\n                  href=\"/events/innovation-summit\"\r\n                  className=\"inline-flex items-center text-indigo-600 font-semibold hover:text-indigo-800 transition-colors group\"\r\n                >\r\n                  Learn More <ArrowRight className=\"ml-2 group-hover:translate-x-1 transition-transform\" />\r\n                </Link>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Program cards */}\r\n        <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-8\">\r\n          {programsData.map((program, index) => (\r\n            <div\r\n              key={program.id}\r\n              className={`transition-all duration-700 transform ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}\r\n              style={{ transitionDelay: `${200 + index * 100}ms` }}\r\n            >\r\n              <ProgramCard program={program} />\r\n            </div>\r\n          ))}\r\n        </div>\r\n\r\n        {/* View all programs button */}\r\n        <div className={`mt-12 text-center transition-all duration-700 delay-500 transform ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>\r\n          <Link\r\n            href=\"/programs\"\r\n            className=\"inline-flex items-center bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-3 px-8 rounded-xl shadow-md transition-all duration-300 hover:-translate-y-1\"\r\n          >\r\n            View All Programs\r\n            <ArrowRight className=\"ml-2\" />\r\n          </Link>\r\n          <p className=\"text-gray-500 mt-4 text-sm\">\r\n            Stay updated with our latest events and programs\r\n          </p>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\nexport default UpcomingPrograms;"], "names": [], "mappings": "AAAA,uCAAuC;;;;;AAEvC;AACA;AACA;AAAA;AACA;AACA;;;AALA;;;;;;AAOA,MAAM,eAA0B;IAC9B;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,aAAa;QACb,UAAU;QACV,UAAU;QACV,MAAM;IACR;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,aAAa;QACb,UAAU;QACV,UAAU;QACV,MAAM;IACR;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,aAAa;QACb,UAAU;QACV,UAAU;QACV,MAAM;IACR;CACD;AAED,MAAM,mBAAmB;;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAE1B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,MAAM,WAAW,IAAI;8CACnB,CAAC,CAAC,MAAM;oBACN,IAAI,MAAM,cAAc,EAAE;wBACxB,aAAa;wBACb,SAAS,UAAU;oBACrB;gBACF;6CACA;gBAAE,WAAW;YAAI;YAGnB,MAAM,aAAa,WAAW,OAAO;YACrC,IAAI,YAAY;gBACd,SAAS,OAAO,CAAC;YACnB;YAEA;8CAAO;oBACL,IAAI,YAAY;wBACd,SAAS,SAAS,CAAC;oBACrB;gBACF;;QACF;qCAAG,EAAE;IAEL,qBACE,6LAAC;QAAQ,KAAK;QAAY,WAAU;;0BAElC,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAI,WAAU;;;;;;0BAGf,6LAAC;gBAAI,WAAU;gBACb,OAAO;oBACL,iBAAiB;oBACjB,gBAAgB;gBAClB;;;;;;0BAGF,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAW,CAAC,0EAA0E,EAAE,YAAY,8BAA8B,4BAA4B;;0CACjK,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;;;;;;0CAGxB,6LAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAClE,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAE,WAAU;0CAAwC;;;;;;;;;;;;kCAMvD,6LAAC;wBAAI,WAAW,CAAC,sDAAsD,EAAE,YAAY,8BAA8B,4BAA4B;kCAC7I,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;oDACJ,KAAI;oDACJ,KAAI;oDACJ,IAAI;oDACJ,WAAU;;;;;;;;;;;0DAGd,6LAAC;gDAAI,WAAU;0DAA2F;;;;;;;;;;;;kDAI5G,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,6MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,6LAAC;kEAAK;;;;;;;;;;;;0DAER,6LAAC;gDAAG,WAAU;0DAAwC;;;;;;0DACtD,6LAAC;gDAAE,WAAU;0DAA+B;;;;;;0DAG5C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAA0E;;;;;;kEAG1F,6LAAC;wDAAK,WAAU;kEAA0E;;;;;;kEAG1F,6LAAC;wDAAK,WAAU;kEAA0E;;;;;;;;;;;;0DAI5F,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;;oDACX;kEACY,6LAAC,qNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQ3C,6LAAC;wBAAI,WAAU;kCACZ,aAAa,GAAG,CAAC,CAAC,SAAS,sBAC1B,6LAAC;gCAEC,WAAW,CAAC,sCAAsC,EAAE,YAAY,8BAA8B,4BAA4B;gCAC1H,OAAO;oCAAE,iBAAiB,GAAG,MAAM,QAAQ,IAAI,EAAE,CAAC;gCAAC;0CAEnD,cAAA,6LAAC,mJAAA,CAAA,UAAW;oCAAC,SAAS;;;;;;+BAJjB,QAAQ,EAAE;;;;;;;;;;kCAUrB,6LAAC;wBAAI,WAAW,CAAC,kEAAkE,EAAE,YAAY,8BAA8B,4BAA4B;;0CACzJ,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;oCACX;kDAEC,6LAAC,qNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;;0CAExB,6LAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;;;;;;;;;;;;;;AAOpD;GAtIM;KAAA;uCAwIS", "debugId": null}}, {"offset": {"line": 2359, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/fwu_incubation%28react%2Blaravel%29/incubation/frontend/src/app/components/shared/SectionTitle.tsx"], "sourcesContent": ["// components/shared/SectionTitle.tsx\r\ninterface SectionTitleProps {\r\n  title: string;\r\n  subtitle?: string;\r\n  align?: 'left' | 'center' | 'right';\r\n  accentColor?: string;\r\n}\r\n\r\nconst SectionTitle: React.FC<SectionTitleProps> = ({\r\n  title,\r\n  subtitle,\r\n  align = 'center',\r\n  accentColor = 'indigo'\r\n}) => {\r\n  const alignmentClass = {\r\n    left: 'text-left',\r\n    center: 'text-center',\r\n    right: 'text-right',\r\n  };\r\n\r\n  const accentColorClass = {\r\n    indigo: 'bg-indigo-600',\r\n    blue: 'bg-blue-600',\r\n    teal: 'bg-teal-600',\r\n    purple: 'bg-purple-600',\r\n    green: 'bg-green-600',\r\n  };\r\n\r\n  const textColorClass = {\r\n    indigo: 'text-indigo-600',\r\n    blue: 'text-blue-600',\r\n    teal: 'text-teal-600',\r\n    purple: 'text-purple-600',\r\n    green: 'text-green-600',\r\n  };\r\n\r\n  return (\r\n    <div className={`mb-12 md:mb-16 ${alignmentClass[align]}`}>\r\n      {subtitle && (\r\n        <p className={`${textColorClass[accentColor as keyof typeof textColorClass]} font-semibold text-sm md:text-base uppercase tracking-wider mb-2`}>\r\n          {subtitle}\r\n        </p>\r\n      )}\r\n      <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900\">\r\n        {title}\r\n      </h2>\r\n      <div className={`mt-4 h-1.5 w-24 ${align === 'center' ? 'mx-auto' : (align === 'right' ? 'ml-auto' : '')} ${accentColorClass[accentColor as keyof typeof accentColorClass]} rounded-full`}></div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default SectionTitle;"], "names": [], "mappings": "AAAA,qCAAqC;;;;;;AAQrC,MAAM,eAA4C,CAAC,EACjD,KAAK,EACL,QAAQ,EACR,QAAQ,QAAQ,EAChB,cAAc,QAAQ,EACvB;IACC,MAAM,iBAAiB;QACrB,MAAM;QACN,QAAQ;QACR,OAAO;IACT;IAEA,MAAM,mBAAmB;QACvB,QAAQ;QACR,MAAM;QACN,MAAM;QACN,QAAQ;QACR,OAAO;IACT;IAEA,MAAM,iBAAiB;QACrB,QAAQ;QACR,MAAM;QACN,MAAM;QACN,QAAQ;QACR,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAC,eAAe,EAAE,cAAc,CAAC,MAAM,EAAE;;YACtD,0BACC,6LAAC;gBAAE,WAAW,GAAG,cAAc,CAAC,YAA2C,CAAC,iEAAiE,CAAC;0BAC3I;;;;;;0BAGL,6LAAC;gBAAG,WAAU;0BACX;;;;;;0BAEH,6LAAC;gBAAI,WAAW,CAAC,gBAAgB,EAAE,UAAU,WAAW,YAAa,UAAU,UAAU,YAAY,GAAI,CAAC,EAAE,gBAAgB,CAAC,YAA6C,CAAC,aAAa,CAAC;;;;;;;;;;;;AAG/L;KAzCM;uCA2CS", "debugId": null}}, {"offset": {"line": 2431, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/fwu_incubation%28react%2Blaravel%29/incubation/frontend/src/app/components/about/IntroAbout.tsx"], "sourcesContent": ["\"use client\"\r\nimport SectionTitle from '../shared/SectionTitle';\r\nimport Image from 'next/image';\r\nimport { Target, Lightbulb, GraduationCap, Building2, Book, Users, Handshake, Scale } from 'lucide-react';\r\nimport { useEffect, useState, useRef } from 'react';\r\nimport Link from 'next/link';\r\n\r\nconst IntroAbout = () => {\r\n  const [isVisible, setIsVisible] = useState(false);\r\n  const sectionRef = useRef(null);\r\n\r\n  useEffect(() => {\r\n    const observer = new IntersectionObserver(\r\n      ([entry]) => {\r\n        if (entry.isIntersecting) {\r\n          setIsVisible(true);\r\n          observer.disconnect();\r\n        }\r\n      },\r\n      { threshold: 0.1 }\r\n    );\r\n\r\n    // Store current ref value in a variable to use in cleanup\r\n    const currentRef = sectionRef.current;\r\n\r\n    if (currentRef) {\r\n      observer.observe(currentRef);\r\n    }\r\n\r\n    return () => {\r\n      // Use the stored ref value in cleanup\r\n      if (currentRef) {\r\n        observer.unobserve(currentRef);\r\n      }\r\n    };\r\n  }, []);\r\n\r\n  return (\r\n    <section ref={sectionRef} className=\"py-16 md:py-24 bg-white relative overflow-hidden\">\r\n      {/* Background decorative elements */}\r\n      <div className=\"absolute top-0 right-0 w-[600px] h-[600px] bg-indigo-100 rounded-full opacity-20 blur-3xl -translate-x-1/3 -translate-y-1/3\"></div>\r\n      <div className=\"absolute bottom-0 left-0 w-[500px] h-[500px] bg-blue-100 rounded-full opacity-20 blur-3xl translate-x-1/4 translate-y-1/4\"></div>\r\n\r\n      {/* Subtle pattern overlay */}\r\n      <div className=\"absolute inset-0 opacity-5\"\r\n        style={{\r\n          backgroundImage: 'radial-gradient(#4338ca 1px, transparent 1px)',\r\n          backgroundSize: '40px 40px'\r\n        }}>\r\n      </div>\r\n\r\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 relative z-10\">\r\n        {/* Introduction Section */}\r\n        <div className=\"grid lg:grid-cols-2 gap-12 items-center mb-16 md:mb-24\">\r\n          <div className={`transition-all duration-700 transform ${isVisible ? 'opacity-100 translate-x-0' : 'opacity-0 -translate-x-10'}`}>\r\n            <SectionTitle\r\n              title=\"Our University\"\r\n              subtitle=\"Excellence in Education\"\r\n              align=\"left\"\r\n              accentColor=\"indigo\"\r\n            />\r\n            <div className=\"space-y-4 text-gray-700\">\r\n              <p className=\"text-lg leading-relaxed\">\r\n                Far Western University (FWU) was established in 2010 by the Act of Parliament as a government-funded\r\n                national university to function as a prime academic institution in the country in terms of academic excellence,\r\n                research-based education, community engagement and partnership.\r\n              </p>\r\n              <p className=\"text-lg leading-relaxed\">\r\n                The central office of the university is located at Bhimdatta Municipality of Kanchanpur district, Far West Province, Nepal.\r\n                The strategic location gives access to local people for higher education and provides an opportunity\r\n                for the overall development of the region.\r\n              </p>\r\n              <p className=\"text-lg leading-relaxed\">\r\n                FWU has been fulfilling its responsibility of making higher education accessible through its constituent campuses\r\n                in nine districts of Far West Province, offering diverse educational programs at both undergraduate and graduate levels.\r\n              </p>\r\n\r\n              <div className=\"pt-4\">\r\n                <Link\r\n                  href=\"/faculty\"\r\n                  className=\"inline-flex items-center px-6 py-3 bg-indigo-600 hover:bg-indigo-700 text-white font-medium rounded-lg transition-all duration-300 shadow-md hover:shadow-lg\"\r\n                >\r\n                  Explore Our Faculties\r\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 ml-2 group-hover:translate-x-1 transition-transform duration-300\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M14 5l7 7m0 0l-7 7m7-7H3\" />\r\n                  </svg>\r\n                </Link>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div className={`relative h-80 md:h-[500px] rounded-2xl shadow-2xl overflow-hidden group transition-all duration-700 delay-300 transform ${isVisible ? 'opacity-100 translate-x-0' : 'opacity-0 translate-x-10'}`}>\r\n            <Image\r\n              src=\"https://www.fwu.edu.np/assets/uploads/about/1743995959-about-image.jpg\"\r\n              alt=\"Far Western University Campus\"\r\n              fill\r\n              sizes=\"(max-width: 768px) 100vw, 50vw\"\r\n              className=\"object-cover object-center transform group-hover:scale-105 transition-transform duration-700 ease-in-out\"\r\n            />\r\n            <div className=\"absolute inset-0 bg-gradient-to-t from-indigo-900/60 to-transparent\"></div>\r\n\r\n            {/* Decorative elements */}\r\n            <div className=\"absolute top-4 right-4 bg-white/90 text-indigo-700 px-4 py-1.5 rounded-full text-sm font-medium shadow-lg\">\r\n              Est. 2010\r\n            </div>\r\n\r\n            <div className=\"absolute bottom-0 left-0 right-0 p-6 text-white\">\r\n              <div className=\"flex items-center\">\r\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 mr-2 text-indigo-300\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\" />\r\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 11a3 3 0 11-6 0 3 3 0 016 0z\" />\r\n                </svg>\r\n                <p className=\"text-lg font-semibold drop-shadow-md\">Bheemdatta Municipality-18, Mahendranagar</p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Mission & Vision Section */}\r\n        <div className={`transition-all duration-700 delay-200 transform ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>\r\n          <SectionTitle\r\n            title=\"Our Guiding Principles\"\r\n            subtitle=\"Mission & Vision\"\r\n            accentColor=\"indigo\"\r\n          />\r\n        </div>\r\n\r\n        <div className=\"grid md:grid-cols-2 gap-8 md:gap-12\">\r\n          {/* Vision Card */}\r\n          <div\r\n            className={`bg-gradient-to-br from-indigo-50 to-white p-8 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 border border-indigo-100 hover:border-indigo-300 relative overflow-hidden transform ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-20'}`}\r\n            style={{ transitionDelay: '300ms' }}\r\n          >\r\n            {/* Background pattern */}\r\n            <div className=\"absolute inset-0 opacity-5\">\r\n              <div className=\"absolute inset-0\" style={{\r\n                backgroundImage: 'radial-gradient(circle, #4f46e5 1px, transparent 1px)',\r\n                backgroundSize: '20px 20px'\r\n              }}></div>\r\n            </div>\r\n\r\n            <div className=\"flex items-center mb-6 relative z-10\">\r\n              <div className=\"bg-gradient-to-r from-indigo-600 to-indigo-700 text-white p-4 rounded-xl mr-5 shadow-lg\">\r\n                <Lightbulb size={28} className=\"animate-pulse-subtle\" />\r\n              </div>\r\n              <h3 className=\"text-2xl font-bold text-indigo-900\">Our Vision</h3>\r\n            </div>\r\n            <p className=\"text-gray-700 leading-relaxed relative z-10 text-lg\">\r\n              To gain recognition as a prime university committed to providing quality education, and enhancing research\r\n              and innovation to meet the needs of society.\r\n            </p>\r\n\r\n            {/* Decorative corner accent */}\r\n            <div className=\"absolute top-0 right-0 w-16 h-16 overflow-hidden\">\r\n              <div className=\"absolute transform rotate-45 bg-indigo-400 text-white w-24 h-3 -top-2 -right-8\"></div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Mission Card */}\r\n          <div\r\n            className={`bg-gradient-to-br from-indigo-50 to-white p-8 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 border border-indigo-100 hover:border-indigo-300 relative overflow-hidden transform ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-20'}`}\r\n            style={{ transitionDelay: '500ms' }}\r\n          >\r\n            {/* Background pattern */}\r\n            <div className=\"absolute inset-0 opacity-5\">\r\n              <div className=\"absolute inset-0\" style={{\r\n                backgroundImage: 'radial-gradient(circle, #4f46e5 1px, transparent 1px)',\r\n                backgroundSize: '20px 20px'\r\n              }}></div>\r\n            </div>\r\n\r\n            <div className=\"flex items-center mb-6 relative z-10\">\r\n              <div className=\"bg-gradient-to-r from-blue-600 to-blue-700 text-white p-4 rounded-xl mr-5 shadow-lg\">\r\n                <Target size={28} className=\"animate-pulse-subtle\" />\r\n              </div>\r\n              <h3 className=\"text-2xl font-bold text-indigo-900\">Our Mission</h3>\r\n            </div>\r\n            <p className=\"text-gray-700 leading-relaxed relative z-10 text-lg\">\r\n              To provide professional and scholarly environment by developing institutional structures and mechanisms\r\n              that enable faculty, students and staff, devising academic programs that contribute towards the advancement\r\n              of knowledge and thereby cultivating active citizenship through teaching, research, collaboration and\r\n              community engagement.\r\n            </p>\r\n\r\n            {/* Decorative corner accent */}\r\n            <div className=\"absolute top-0 right-0 w-16 h-16 overflow-hidden\">\r\n              <div className=\"absolute transform rotate-45 bg-blue-400 text-white w-24 h-3 -top-2 -right-8\"></div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Core Values Section */}\r\n        <div className=\"mt-20\">\r\n          <div className={`transition-all duration-700 delay-600 transform ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>\r\n            <SectionTitle\r\n              title=\"Our Core Values\"\r\n              subtitle=\"Guiding Principles\"\r\n              accentColor=\"indigo\"\r\n            />\r\n          </div>\r\n\r\n          <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-6 mt-8\">\r\n            {[\r\n              {\r\n                icon: <GraduationCap size={24} />,\r\n                title: \"Academic Excellence\",\r\n                description: \"To provide the best possible education considering all aspects of students' academic development by adopting dynamic teaching-learning environment.\",\r\n                color: \"from-indigo-600 to-indigo-700\",\r\n                delay: 700\r\n              },\r\n              {\r\n                icon: <Book size={24} />,\r\n                title: \"Lifelong Learning\",\r\n                description: \"To develop and impart positive attitude through continuous learning to act cautiously even in adverse situation.\",\r\n                color: \"from-blue-600 to-blue-700\",\r\n                delay: 800\r\n              },\r\n              {\r\n                icon: <Scale size={24} />,\r\n                title: \"Accountability\",\r\n                description: \"To encourage sense of responsibility on the processes and outcomes of every action.\",\r\n                color: \"from-purple-600 to-purple-700\",\r\n                delay: 900\r\n              },\r\n              {\r\n                icon: <Handshake size={24} />,\r\n                title: \"Sustainability\",\r\n                description: \"To consider sustainability in all aspects of its operations including research and education.\",\r\n                color: \"from-green-600 to-green-700\",\r\n                delay: 1000\r\n              },\r\n              {\r\n                icon: <Building2 size={24} />,\r\n                title: \"Integrity\",\r\n                description: \"To develop the culture of trust, honesty and fairness in all actions and words.\",\r\n                color: \"from-teal-600 to-teal-700\",\r\n                delay: 1100\r\n              },\r\n              {\r\n                icon: <Users size={24} />,\r\n                title: \"Respect\",\r\n                description: \"To foster the culture of accepting differences in community cultures, values, beliefs and respecting individual rights and diversity.\",\r\n                color: \"from-indigo-600 to-indigo-700\",\r\n                delay: 1200\r\n              }\r\n            ].map((value, index) => (\r\n              <div\r\n                key={index}\r\n                className={`bg-white p-6 rounded-xl shadow-md hover:shadow-xl transition-all duration-500 border border-gray-100 hover:border-indigo-200 transform ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'} group hover:-translate-y-1`}\r\n                style={{ transitionDelay: `${value.delay}ms` }}\r\n              >\r\n                <div className={`bg-gradient-to-r ${value.color} text-white p-3 rounded-xl mb-4 w-14 h-14 flex items-center justify-center shadow-md group-hover:scale-110 transition-transform duration-300`}>\r\n                  {value.icon}\r\n                </div>\r\n                <h3 className=\"text-xl font-semibold text-gray-900 mb-2 group-hover:text-indigo-700 transition-colors\">{value.title}</h3>\r\n                <p className=\"text-gray-600\">{value.description}</p>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Goals Section */}\r\n        <div className=\"mt-20\">\r\n          <div className={`transition-all duration-700 delay-800 transform ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>\r\n            <SectionTitle\r\n              title=\"Our Strategic Goals\"\r\n              subtitle=\"Driving Excellence\"\r\n              accentColor=\"indigo\"\r\n            />\r\n          </div>\r\n\r\n          <div className=\"bg-gradient-to-br from-indigo-50 to-white p-8 rounded-2xl shadow-lg border border-indigo-100 mt-8\">\r\n            <div className=\"space-y-6\">\r\n              <div className=\"flex items-start\">\r\n                <div className=\"flex-shrink-0 w-10 h-10 rounded-full bg-indigo-100 flex items-center justify-center mt-0.5 mr-4\">\r\n                  <div className=\"w-5 h-5 rounded-full bg-indigo-600 flex items-center justify-center text-white font-bold text-xs\">1</div>\r\n                </div>\r\n                <p className=\"text-lg text-gray-700\">\r\n                  To provide quality education and equip students with knowledge, skills, positive attitude and values.\r\n                </p>\r\n              </div>\r\n\r\n              <div className=\"flex items-start\">\r\n                <div className=\"flex-shrink-0 w-10 h-10 rounded-full bg-indigo-100 flex items-center justify-center mt-0.5 mr-4\">\r\n                  <div className=\"w-5 h-5 rounded-full bg-indigo-600 flex items-center justify-center text-white font-bold text-xs\">2</div>\r\n                </div>\r\n                <p className=\"text-lg text-gray-700\">\r\n                  To create opportunities and support for research by enhancing the public engagement.\r\n                </p>\r\n              </div>\r\n\r\n              <div className=\"flex items-start\">\r\n                <div className=\"flex-shrink-0 w-10 h-10 rounded-full bg-indigo-100 flex items-center justify-center mt-0.5 mr-4\">\r\n                  <div className=\"w-5 h-5 rounded-full bg-indigo-600 flex items-center justify-center text-white font-bold text-xs\">3</div>\r\n                </div>\r\n                <p className=\"text-lg text-gray-700\">\r\n                  To ensure the financial and environmental sustainability of the University.\r\n                </p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* CSS Animations */}\r\n      <style jsx>{`\r\n        @keyframes pulse-subtle {\r\n          0%, 100% { transform: scale(1); }\r\n          50% { transform: scale(1.05); }\r\n        }\r\n        .animate-pulse-subtle {\r\n          animation: pulse-subtle 3s ease-in-out infinite;\r\n        }\r\n      `}</style>\r\n    </section>\r\n  );\r\n};\r\n\r\nexport default IntroAbout;"], "names": [], "mappings": ";;;;;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;;;AALA;;;;;;;AAOA,MAAM,aAAa;;IACjB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAE1B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,MAAM,WAAW,IAAI;wCACnB,CAAC,CAAC,MAAM;oBACN,IAAI,MAAM,cAAc,EAAE;wBACxB,aAAa;wBACb,SAAS,UAAU;oBACrB;gBACF;uCACA;gBAAE,WAAW;YAAI;YAGnB,0DAA0D;YAC1D,MAAM,aAAa,WAAW,OAAO;YAErC,IAAI,YAAY;gBACd,SAAS,OAAO,CAAC;YACnB;YAEA;wCAAO;oBACL,sCAAsC;oBACtC,IAAI,YAAY;wBACd,SAAS,SAAS,CAAC;oBACrB;gBACF;;QACF;+BAAG,EAAE;IAEL,qBACE,6LAAC;QAAQ,KAAK;kDAAsB;;0BAElC,6LAAC;0DAAc;;;;;;0BACf,6LAAC;0DAAc;;;;;;0BAGf,6LAAC;gBACC,OAAO;oBACL,iBAAiB;oBACjB,gBAAgB;gBAClB;0DAJa;;;;;;0BAOf,6LAAC;0DAAc;;kCAEb,6LAAC;kEAAc;;0CACb,6LAAC;0EAAe,CAAC,sCAAsC,EAAE,YAAY,8BAA8B,6BAA6B;;kDAC9H,6LAAC,sJAAA,CAAA,UAAY;wCACX,OAAM;wCACN,UAAS;wCACT,OAAM;wCACN,aAAY;;;;;;kDAEd,6LAAC;kFAAc;;0DACb,6LAAC;0FAAY;0DAA0B;;;;;;0DAKvC,6LAAC;0FAAY;0DAA0B;;;;;;0DAKvC,6LAAC;0FAAY;0DAA0B;;;;;;0DAKvC,6LAAC;0FAAc;0DACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;;wDACX;sEAEC,6LAAC;4DAAI,OAAM;4DAAkH,MAAK;4DAAO,SAAQ;4DAAY,QAAO;sGAAlH;sEAChD,cAAA,6LAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAO/E,6LAAC;0EAAe,CAAC,wHAAwH,EAAE,YAAY,8BAA8B,4BAA4B;;kDAC/M,6LAAC,gIAAA,CAAA,UAAK;wCACJ,KAAI;wCACJ,KAAI;wCACJ,IAAI;wCACJ,OAAM;wCACN,WAAU;;;;;;kDAEZ,6LAAC;kFAAc;;;;;;kDAGf,6LAAC;kFAAc;kDAA4G;;;;;;kDAI3H,6LAAC;kFAAc;kDACb,cAAA,6LAAC;sFAAc;;8DACb,6LAAC;oDAAI,OAAM;oDAAsE,MAAK;oDAAO,SAAQ;oDAAY,QAAO;8FAAtE;;sEAChD,6LAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;sEACrE,6LAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;;;;;;;8DAEvE,6LAAC;8FAAY;8DAAuC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO5D,6LAAC;kEAAe,CAAC,gDAAgD,EAAE,YAAY,8BAA8B,4BAA4B;kCACvI,cAAA,6LAAC,sJAAA,CAAA,UAAY;4BACX,OAAM;4BACN,UAAS;4BACT,aAAY;;;;;;;;;;;kCAIhB,6LAAC;kEAAc;;0CAEb,6LAAC;gCAEC,OAAO;oCAAE,iBAAiB;gCAAQ;0EADvB,CAAC,qMAAqM,EAAE,YAAY,8BAA8B,4BAA4B;;kDAIzR,6LAAC;kFAAc;kDACb,cAAA,6LAAC;4CAAiC,OAAO;gDACvC,iBAAiB;gDACjB,gBAAgB;4CAClB;sFAHe;;;;;;;;;;;kDAMjB,6LAAC;kFAAc;;0DACb,6LAAC;0FAAc;0DACb,cAAA,6LAAC,+MAAA,CAAA,YAAS;oDAAC,MAAM;oDAAI,WAAU;;;;;;;;;;;0DAEjC,6LAAC;0FAAa;0DAAqC;;;;;;;;;;;;kDAErD,6LAAC;kFAAY;kDAAsD;;;;;;kDAMnE,6LAAC;kFAAc;kDACb,cAAA,6LAAC;sFAAc;;;;;;;;;;;;;;;;;0CAKnB,6LAAC;gCAEC,OAAO;oCAAE,iBAAiB;gCAAQ;0EADvB,CAAC,qMAAqM,EAAE,YAAY,8BAA8B,4BAA4B;;kDAIzR,6LAAC;kFAAc;kDACb,cAAA,6LAAC;4CAAiC,OAAO;gDACvC,iBAAiB;gDACjB,gBAAgB;4CAClB;sFAHe;;;;;;;;;;;kDAMjB,6LAAC;kFAAc;;0DACb,6LAAC;0FAAc;0DACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;oDAAC,MAAM;oDAAI,WAAU;;;;;;;;;;;0DAE9B,6LAAC;0FAAa;0DAAqC;;;;;;;;;;;;kDAErD,6LAAC;kFAAY;kDAAsD;;;;;;kDAQnE,6LAAC;kFAAc;kDACb,cAAA,6LAAC;sFAAc;;;;;;;;;;;;;;;;;;;;;;;kCAMrB,6LAAC;kEAAc;;0CACb,6LAAC;0EAAe,CAAC,gDAAgD,EAAE,YAAY,8BAA8B,4BAA4B;0CACvI,cAAA,6LAAC,sJAAA,CAAA,UAAY;oCACX,OAAM;oCACN,UAAS;oCACT,aAAY;;;;;;;;;;;0CAIhB,6LAAC;0EAAc;0CACZ;oCACC;wCACE,oBAAM,6LAAC,2NAAA,CAAA,gBAAa;4CAAC,MAAM;;;;;;wCAC3B,OAAO;wCACP,aAAa;wCACb,OAAO;wCACP,OAAO;oCACT;oCACA;wCACE,oBAAM,6LAAC,qMAAA,CAAA,OAAI;4CAAC,MAAM;;;;;;wCAClB,OAAO;wCACP,aAAa;wCACb,OAAO;wCACP,OAAO;oCACT;oCACA;wCACE,oBAAM,6LAAC,uMAAA,CAAA,QAAK;4CAAC,MAAM;;;;;;wCACnB,OAAO;wCACP,aAAa;wCACb,OAAO;wCACP,OAAO;oCACT;oCACA;wCACE,oBAAM,6LAAC,+MAAA,CAAA,YAAS;4CAAC,MAAM;;;;;;wCACvB,OAAO;wCACP,aAAa;wCACb,OAAO;wCACP,OAAO;oCACT;oCACA;wCACE,oBAAM,6LAAC,mNAAA,CAAA,YAAS;4CAAC,MAAM;;;;;;wCACvB,OAAO;wCACP,aAAa;wCACb,OAAO;wCACP,OAAO;oCACT;oCACA;wCACE,oBAAM,6LAAC,uMAAA,CAAA,QAAK;4CAAC,MAAM;;;;;;wCACnB,OAAO;wCACP,aAAa;wCACb,OAAO;wCACP,OAAO;oCACT;iCACD,CAAC,GAAG,CAAC,CAAC,OAAO,sBACZ,6LAAC;wCAGC,OAAO;4CAAE,iBAAiB,GAAG,MAAM,KAAK,CAAC,EAAE,CAAC;wCAAC;kFADlC,CAAC,uIAAuI,EAAE,YAAY,8BAA8B,2BAA2B,2BAA2B,CAAC;;0DAGtP,6LAAC;0FAAe,CAAC,iBAAiB,EAAE,MAAM,KAAK,CAAC,4IAA4I,CAAC;0DAC1L,MAAM,IAAI;;;;;;0DAEb,6LAAC;0FAAa;0DAA0F,MAAM,KAAK;;;;;;0DACnH,6LAAC;0FAAY;0DAAiB,MAAM,WAAW;;;;;;;uCAR1C;;;;;;;;;;;;;;;;kCAeb,6LAAC;kEAAc;;0CACb,6LAAC;0EAAe,CAAC,gDAAgD,EAAE,YAAY,8BAA8B,4BAA4B;0CACvI,cAAA,6LAAC,sJAAA,CAAA,UAAY;oCACX,OAAM;oCACN,UAAS;oCACT,aAAY;;;;;;;;;;;0CAIhB,6LAAC;0EAAc;0CACb,cAAA,6LAAC;8EAAc;;sDACb,6LAAC;sFAAc;;8DACb,6LAAC;8FAAc;8DACb,cAAA,6LAAC;kGAAc;kEAAmG;;;;;;;;;;;8DAEpH,6LAAC;8FAAY;8DAAwB;;;;;;;;;;;;sDAKvC,6LAAC;sFAAc;;8DACb,6LAAC;8FAAc;8DACb,cAAA,6LAAC;kGAAc;kEAAmG;;;;;;;;;;;8DAEpH,6LAAC;8FAAY;8DAAwB;;;;;;;;;;;;sDAKvC,6LAAC;sFAAc;;8DACb,6LAAC;8FAAc;8DACb,cAAA,6LAAC;kGAAc;kEAAmG;;;;;;;;;;;8DAEpH,6LAAC;8FAAY;8DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqBrD;GArTM;KAAA;uCAuTS", "debugId": null}}, {"offset": {"line": 3223, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/fwu_incubation%28react%2Blaravel%29/incubation/frontend/src/app/page.tsx"], "sourcesContent": ["// \"use client\"\n// import React, { useEffect } from 'react'\n\n// function Home() {\n//   const [data, setData] = React.useState(null)\n// useEffect(() => {\n//     fetch(\"http://127.0.0.1:8000/api/test\")\n//       .then((res) => res.json())\n//       .then((data) => setData(data))\n//   }, []);\n//   console.log(data)\n//   return (\n//     <div>\n//       <h1>Home</h1>\n//     </div>\n//   )\n// }\n\n// export default Home\n\n\"use client\"\nimport HeroBanner from './components/home/<USER>';\nimport IntroSection from './components/home/<USER>';\nimport FeaturedStartups from './components/home/<USER>';\nimport Testimonials from './components/home/<USER>';\nimport UpcomingPrograms from './components/home/<USER>';\nimport IntroAbout from './components/about/IntroAbout';\nimport { Building2, GraduationCap, Users, ChevronDown, ArrowRight, Book, Award, MapPin, Calendar } from 'lucide-react';\nimport Image from 'next/image';\nimport Link from 'next/link';\nimport { useEffect ,useState, useRef } from 'react';\n\n\nexport default function HomePage() {\n    const [statsVisible, setStatsVisible] = useState(false);\n    const [isVisible, setIsVisible] = useState(false);\n    const heroRef = useRef(null);\n    const statsRef = useRef(null);\n  \n    useEffect(() => {\n      setIsVisible(true);\n  \n      const heroObserver = new IntersectionObserver(\n        ([entry]) => {\n          if (entry.isIntersecting) {\n            setIsVisible(true);\n            heroObserver.disconnect();\n          }\n        },\n        { threshold: 0.1 }\n      );\n  \n      const statsObserver = new IntersectionObserver(\n        ([entry]) => {\n          if (entry.isIntersecting) {\n            setStatsVisible(true);\n            statsObserver.disconnect();\n          }\n        },\n        { threshold: 0.1 }\n      );\n  \n      // Store current ref values in variables to use in cleanup\n      const currentHeroRef = heroRef.current;\n      const currentStatsRef = statsRef.current;\n  \n      if (currentHeroRef) {\n        heroObserver.observe(currentHeroRef);\n      }\n  \n      if (currentStatsRef) {\n        statsObserver.observe(currentStatsRef);\n      }\n  \n      return () => {\n        // Use the stored ref values in cleanup\n        if (currentHeroRef) {\n          heroObserver.unobserve(currentHeroRef);\n        }\n        if (currentStatsRef) {\n          statsObserver.unobserve(currentStatsRef);\n        }\n      };\n    }, []);\n  return (\n    <>\n\n      <main>\n        {/* <HeroBanner /> */}\n              {/* Hero Banner with Parallax and Animation Effects */}\n        <div\n          ref={heroRef}\n          className=\"relative min-h-[600px] md:min-h-[700px] flex items-center justify-center overflow-hidden\"\n        >\n          {/* Background image with overlay */}\n          <div className=\"absolute inset-0 z-0\">\n            <Image\n              src=\"https://images.unsplash.com/photo-1541339907198-e08756dedf3f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80\"\n              alt=\"Far Western University Campus\"\n              fill\n              priority\n              className=\"object-cover object-center\"\n            />\n            <div className=\"absolute inset-0 bg-gradient-to-r from-indigo-900/90 via-indigo-800/85 to-blue-900/80\"></div>\n          </div>\n\n          {/* Animated particles */}\n          <div className=\"absolute inset-0 z-10 overflow-hidden\">\n            {[...Array(30)].map((_, i) => (\n              <div\n                key={i}\n                className=\"absolute w-1.5 h-1.5 bg-white/30 rounded-full\"\n                style={{\n                  top: `${Math.random() * 100}%`,\n                  left: `${Math.random() * 100}%`,\n                  animation: `float ${5 + Math.random() * 10}s infinite ease-in-out`,\n                  animationDelay: `${Math.random() * 5}s`\n                }}\n              ></div>\n            ))}\n          </div>\n\n          {/* Decorative elements */}\n          <div className=\"absolute top-1/4 right-1/4 w-64 h-64 bg-indigo-500/10 rounded-full blur-3xl\"></div>\n          <div className=\"absolute bottom-1/4 left-1/3 w-64 h-64 bg-blue-500/10 rounded-full blur-3xl\"></div>\n\n          {/* Content with animations */}\n          <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 relative z-20 text-center py-16\">\n            <div className={`transition-all duration-1000 transform ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>\n              <div className=\"inline-block mb-6\">\n                <div className=\"flex items-center justify-center space-x-2 bg-white/10 backdrop-blur-sm px-4 py-2 rounded-full border border-white/20\">\n                  <Building2 className=\"text-indigo-300\" />\n                  <span className=\"text-white font-medium\">Established 2010</span>\n                </div>\n              </div>\n\n              <h1 className=\"text-5xl sm:text-6xl md:text-7xl font-bold mb-6 leading-tight\">\n                {/* <span className=\"block mb-2 text-white\">About</span> */}\n                <span className=\"text-transparent bg-clip-text bg-gradient-to-r from-indigo-300 via-purple-300 to-blue-300\">\n                  Far Western University\n                </span>\n              </h1>\n\n              <p className=\"text-xl text-indigo-100 max-w-3xl mx-auto leading-relaxed mb-10\">\n                A premier institution in Nepal dedicated to academic excellence, research-based education, and community engagement in the Far Western region.\n              </p>\n\n              {/* Action buttons */}\n              <div className=\"flex flex-wrap justify-center gap-4 mt-8\">\n                <Link\n                  href=\"#university-intro\"\n                  className=\"px-6 py-3 bg-indigo-600 hover:bg-indigo-700 text-white font-medium rounded-lg transition-all duration-300 flex items-center group\"\n                >\n                  Learn More\n                  <ChevronDown className=\"ml-2 group-hover:translate-y-1 transition-transform duration-300\" />\n                </Link>\n                <Link\n                  href=\"/contact\"\n                  className=\"px-6 py-3 bg-white/10 hover:bg-white/20 backdrop-blur-sm text-white font-medium rounded-lg transition-all duration-300 border border-white/20 flex items-center group\"\n                >\n                  Contact Us\n                  <ArrowRight className=\"ml-2 group-hover:translate-x-1 transition-transform duration-300\" />\n                </Link>\n              </div>\n            </div>\n          </div>\n\n          {/* Bottom wave */}\n          <div className=\"absolute bottom-0 left-0 right-0 z-10\">\n            <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 1440 120\" className=\"w-full h-auto\">\n              <path\n                fill=\"#ffffff\"\n                fillOpacity=\"1\"\n                d=\"M0,64L80,69.3C160,75,320,85,480,80C640,75,800,53,960,48C1120,43,1280,53,1360,58.7L1440,64L1440,120L1360,120C1280,120,1120,120,960,120C800,120,640,120,480,120C320,120,160,120,80,120L0,120Z\"\n                style={{animation: 'wave 15s ease-in-out infinite'}}\n              ></path>\n            </svg>\n          </div>\n        </div>\n         {/* Quick Stats Section */}\n        <div\n          ref={statsRef}\n          className=\"py-16 bg-gradient-to-b from-white to-indigo-50 relative\"\n        >\n          <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n            <div className=\"max-w-7xl mx-auto\">\n              <div className=\"grid grid-cols-2 md:grid-cols-4 gap-8\">\n                {[\n                  { icon: <GraduationCap className=\"text-indigo-600\" size={28} />, count: 491, label: \"Teachers\", delay: 100 },\n                  { icon: <Users className=\"text-indigo-600\" size={28} />, count: 17238, label: \"Students\", delay: 300 },\n                  { icon: <Book className=\"text-indigo-600\" size={28} />, count: 180, label: \"Staff\", delay: 500 },\n                  { icon: <Award className=\"text-indigo-600\" size={28} />, count: 17909, label: \"Users\", delay: 700 }\n                ].map((stat, index) => (\n                  <div\n                    key={index}\n                    className={`bg-white p-6 rounded-2xl shadow-lg border border-indigo-100 flex flex-col items-center justify-center transform transition-all duration-700 ${\n                      statsVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'\n                    }`}\n                    style={{ transitionDelay: `${stat.delay}ms` }}\n                  >\n                    <div className=\"w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mb-4\">\n                      {stat.icon}\n                    </div>\n                    <div className=\"text-3xl md:text-4xl font-bold text-indigo-900 mb-1\">\n                      {statsVisible ? stat.count : 0}\n                    </div>\n                    <div className=\"text-indigo-600 font-medium\">{stat.label}</div>\n                  </div>\n                ))}\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Main content sections */}\n        <div id=\"university-intro\">\n          <IntroAbout />\n        </div>\n        <IntroSection />\n        <UpcomingPrograms />\n        <FeaturedStartups/>\n        <Testimonials />\n      </main>\n\n    </>\n  );\n}"], "names": [], "mappings": "AAAA,eAAe;AACf,2CAA2C;AAE3C,oBAAoB;AACpB,iDAAiD;AACjD,oBAAoB;AACpB,8CAA8C;AAC9C,mCAAmC;AACnC,uCAAuC;AACvC,YAAY;AACZ,sBAAsB;AACtB,aAAa;AACb,YAAY;AACZ,sBAAsB;AACtB,aAAa;AACb,MAAM;AACN,IAAI;AAEJ,sBAAsB;;;;;AAItB;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;AAVA;;;;;;;;;;AAae,SAAS;;IACpB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACvB,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAExB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,aAAa;YAEb,MAAM,eAAe,IAAI;sCACvB,CAAC,CAAC,MAAM;oBACN,IAAI,MAAM,cAAc,EAAE;wBACxB,aAAa;wBACb,aAAa,UAAU;oBACzB;gBACF;qCACA;gBAAE,WAAW;YAAI;YAGnB,MAAM,gBAAgB,IAAI;sCACxB,CAAC,CAAC,MAAM;oBACN,IAAI,MAAM,cAAc,EAAE;wBACxB,gBAAgB;wBAChB,cAAc,UAAU;oBAC1B;gBACF;qCACA;gBAAE,WAAW;YAAI;YAGnB,0DAA0D;YAC1D,MAAM,iBAAiB,QAAQ,OAAO;YACtC,MAAM,kBAAkB,SAAS,OAAO;YAExC,IAAI,gBAAgB;gBAClB,aAAa,OAAO,CAAC;YACvB;YAEA,IAAI,iBAAiB;gBACnB,cAAc,OAAO,CAAC;YACxB;YAEA;sCAAO;oBACL,uCAAuC;oBACvC,IAAI,gBAAgB;wBAClB,aAAa,SAAS,CAAC;oBACzB;oBACA,IAAI,iBAAiB;wBACnB,cAAc,SAAS,CAAC;oBAC1B;gBACF;;QACF;6BAAG,EAAE;IACP,qBACE;kBAEE,cAAA,6LAAC;;8BAGC,6LAAC;oBACC,KAAK;oBACL,WAAU;;sCAGV,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,gIAAA,CAAA,UAAK;oCACJ,KAAI;oCACJ,KAAI;oCACJ,IAAI;oCACJ,QAAQ;oCACR,WAAU;;;;;;8CAEZ,6LAAC;oCAAI,WAAU;;;;;;;;;;;;sCAIjB,6LAAC;4BAAI,WAAU;sCACZ;mCAAI,MAAM;6BAAI,CAAC,GAAG,CAAC,CAAC,GAAG,kBACtB,6LAAC;oCAEC,WAAU;oCACV,OAAO;wCACL,KAAK,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;wCAC9B,MAAM,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;wCAC/B,WAAW,CAAC,MAAM,EAAE,IAAI,KAAK,MAAM,KAAK,GAAG,sBAAsB,CAAC;wCAClE,gBAAgB,GAAG,KAAK,MAAM,KAAK,EAAE,CAAC,CAAC;oCACzC;mCAPK;;;;;;;;;;sCAaX,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;;;;;sCAGf,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAW,CAAC,uCAAuC,EAAE,YAAY,8BAA8B,4BAA4B;;kDAC9H,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,mNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;8DACrB,6LAAC;oDAAK,WAAU;8DAAyB;;;;;;;;;;;;;;;;;kDAI7C,6LAAC;wCAAG,WAAU;kDAEZ,cAAA,6LAAC;4CAAK,WAAU;sDAA4F;;;;;;;;;;;kDAK9G,6LAAC;wCAAE,WAAU;kDAAkE;;;;;;kDAK/E,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;;oDACX;kEAEC,6LAAC,uNAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;;;;;;;0DAEzB,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;;oDACX;kEAEC,6LAAC,qNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAO9B,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,OAAM;gCAA6B,SAAQ;gCAAe,WAAU;0CACvE,cAAA,6LAAC;oCACC,MAAK;oCACL,aAAY;oCACZ,GAAE;oCACF,OAAO;wCAAC,WAAW;oCAA+B;;;;;;;;;;;;;;;;;;;;;;8BAM1D,6LAAC;oBACC,KAAK;oBACL,WAAU;8BAEV,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACZ;oCACC;wCAAE,oBAAM,6LAAC,2NAAA,CAAA,gBAAa;4CAAC,WAAU;4CAAkB,MAAM;;;;;;wCAAQ,OAAO;wCAAK,OAAO;wCAAY,OAAO;oCAAI;oCAC3G;wCAAE,oBAAM,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;4CAAkB,MAAM;;;;;;wCAAQ,OAAO;wCAAO,OAAO;wCAAY,OAAO;oCAAI;oCACrG;wCAAE,oBAAM,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;4CAAkB,MAAM;;;;;;wCAAQ,OAAO;wCAAK,OAAO;wCAAS,OAAO;oCAAI;oCAC/F;wCAAE,oBAAM,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;4CAAkB,MAAM;;;;;;wCAAQ,OAAO;wCAAO,OAAO;wCAAS,OAAO;oCAAI;iCACnG,CAAC,GAAG,CAAC,CAAC,MAAM,sBACX,6LAAC;wCAEC,WAAW,CAAC,4IAA4I,EACtJ,eAAe,8BAA8B,4BAC7C;wCACF,OAAO;4CAAE,iBAAiB,GAAG,KAAK,KAAK,CAAC,EAAE,CAAC;wCAAC;;0DAE5C,6LAAC;gDAAI,WAAU;0DACZ,KAAK,IAAI;;;;;;0DAEZ,6LAAC;gDAAI,WAAU;0DACZ,eAAe,KAAK,KAAK,GAAG;;;;;;0DAE/B,6LAAC;gDAAI,WAAU;0DAA+B,KAAK,KAAK;;;;;;;uCAZnD;;;;;;;;;;;;;;;;;;;;;;;;;8BAqBjB,6LAAC;oBAAI,IAAG;8BACN,cAAA,6LAAC,mJAAA,CAAA,UAAU;;;;;;;;;;8BAEb,6LAAC,oJAAA,CAAA,UAAY;;;;;8BACb,6LAAC,wJAAA,CAAA,UAAgB;;;;;8BACjB,6LAAC,wJAAA,CAAA,UAAgB;;;;;8BACjB,6LAAC,oJAAA,CAAA,UAAY;;;;;;;;;;;;AAKrB;GAjMwB;KAAA", "debugId": null}}]}