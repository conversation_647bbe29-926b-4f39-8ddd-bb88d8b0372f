(()=>{var e={};e.id=893,e.ids=[893],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},25960:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>d});var a=r(65239),s=r(48088),l=r(88170),i=r.n(l),o=r(30893),n={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>o[e]);r.d(t,n);let d={children:["",{children:["projects",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,32417)),"D:\\fwu_incubation(react+laravel)\\incubation\\frontend\\src\\app\\projects\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,53862)),"D:\\fwu_incubation(react+laravel)\\incubation\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["D:\\fwu_incubation(react+laravel)\\incubation\\frontend\\src\\app\\projects\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/projects/page",pathname:"/projects",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32183:(e,t,r)=>{Promise.resolve().then(r.bind(r,32417))},32417:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\fwu_incubation(react+laravel)\\\\incubation\\\\frontend\\\\src\\\\app\\\\projects\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\fwu_incubation(react+laravel)\\incubation\\frontend\\src\\app\\projects\\page.tsx","default")},33873:e=>{"use strict";e.exports=require("path")},37360:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]])},38194:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>v});var a=r(60687),s=r(43210),l=r(99270),i=r(30474);let o=({searchTerm:e,onSearchChange:t})=>{let[r,o]=(0,s.useState)(!1);return(0,s.useEffect)(()=>{o(!0)},[]),(0,a.jsxs)("section",{className:"relative bg-gradient-to-r from-indigo-900 via-purple-800 to-indigo-900 text-white overflow-hidden",children:[(0,a.jsxs)("div",{className:"absolute inset-0 overflow-hidden",children:[(0,a.jsx)("div",{className:"absolute top-20 left-10 w-64 h-64 rounded-full bg-purple-500 opacity-10 animate-float-slow"}),(0,a.jsx)("div",{className:"absolute bottom-20 right-10 w-96 h-96 rounded-full bg-indigo-500 opacity-10 animate-float-reverse"}),(0,a.jsx)("div",{className:"absolute top-1/2 left-1/4 w-32 h-32 rounded-full bg-blue-500 opacity-5 animate-pulse"}),(0,a.jsx)("div",{className:"absolute inset-0 opacity-10",style:{backgroundImage:"radial-gradient(circle, rgba(255,255,255,0.1) 1px, transparent 1px)",backgroundSize:"30px 30px"}})]}),(0,a.jsx)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8 relative z-10 py-20 md:py-28",children:(0,a.jsx)("div",{className:"max-w-4xl mx-auto",children:(0,a.jsxs)("div",{className:"flex flex-col md:flex-row items-center",children:[(0,a.jsxs)("div",{className:`md:w-1/2 text-center md:text-left transition-all duration-1000 transform ${r?"opacity-100 translate-x-0":"opacity-0 -translate-x-10"}`,children:[(0,a.jsx)("div",{className:"inline-block mb-6 p-2 bg-purple-800/30 rounded-full",children:(0,a.jsx)("div",{className:"px-4 py-1 bg-purple-700/50 rounded-full",children:(0,a.jsx)("span",{className:"text-purple-100 font-medium",children:"FWU Incubation Center"})})}),(0,a.jsxs)("h1",{className:"text-4xl sm:text-5xl font-extrabold mb-6 leading-tight",children:["Innovative ",(0,a.jsx)("span",{className:"text-transparent bg-clip-text bg-gradient-to-r from-purple-300 to-indigo-300",children:"Projects"})]}),(0,a.jsx)("p",{className:"text-xl text-indigo-100 mb-8 leading-relaxed",children:"Discover groundbreaking projects and innovations from the Far Western University Incubation Center."}),(0,a.jsxs)("div",{className:"relative max-w-md mx-auto md:mx-0",children:[(0,a.jsx)("input",{type:"text",placeholder:"Search projects...",value:e,onChange:e=>t(e.target.value),className:"w-full pl-12 pr-4 py-4 bg-white/10 backdrop-blur-sm border border-white/20 rounded-full focus:ring-2 focus:ring-white/50 focus:border-transparent shadow-lg transition-colors text-white placeholder-white/70"}),(0,a.jsx)(l.A,{className:"absolute left-4 top-1/2 transform -translate-y-1/2 text-white/70",size:20})]})]}),(0,a.jsx)("div",{className:`md:w-1/2 mt-10 md:mt-0 transition-all duration-1000 delay-300 transform ${r?"opacity-100 translate-x-0":"opacity-0 translate-x-10"}`,children:(0,a.jsxs)("div",{className:"relative h-64 md:h-80 w-full",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-purple-500/20 to-indigo-500/20 rounded-2xl transform rotate-3 scale-105"}),(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-purple-500/20 to-indigo-500/20 rounded-2xl transform -rotate-3 scale-105"}),(0,a.jsxs)("div",{className:"relative h-full w-full rounded-2xl overflow-hidden shadow-2xl border border-white/10",children:[(0,a.jsx)(i.default,{src:"https://images.unsplash.com/photo-1581094794329-c8112a89af12?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80",alt:"FWU Incubation Center Projects",fill:!0,className:"object-cover"}),(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-indigo-900/80 to-transparent"}),(0,a.jsxs)("div",{className:"absolute bottom-0 left-0 right-0 p-6",children:[(0,a.jsx)("span",{className:"bg-purple-600/80 text-white text-xs font-bold px-3 py-1 rounded-full backdrop-blur-sm",children:"Innovation Hub"}),(0,a.jsx)("h3",{className:"text-white text-xl font-bold mt-2",children:"Transforming Ideas into Reality"})]})]})]})})]})})}),(0,a.jsx)("div",{className:"absolute bottom-0 left-0 right-0",children:(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1440 120",className:"w-full h-auto",children:(0,a.jsx)("path",{fill:"#ffffff",fillOpacity:"1",d:"M0,64L80,69.3C160,75,320,85,480,80C640,75,800,53,960,48C1120,43,1280,53,1360,58.7L1440,64L1440,120L1360,120C1280,120,1120,120,960,120C800,120,640,120,480,120C320,120,160,120,80,120L0,120Z"})})})]})};var n=r(80462);let d=({categories:e,selectedCategory:t,onCategoryChange:r})=>{let[l,i]=(0,s.useState)(!1);(0,s.useEffect)(()=>{i(!0)},[]);let o=e=>({Technology:"bg-blue-500 border-blue-600 hover:bg-blue-600",Agriculture:"bg-green-500 border-green-600 hover:bg-green-600",Healthcare:"bg-red-500 border-red-600 hover:bg-red-600",Education:"bg-yellow-500 border-yellow-600 hover:bg-yellow-600",Environment:"bg-emerald-500 border-emerald-600 hover:bg-emerald-600",All:"bg-purple-600 border-purple-700 hover:bg-purple-700"})[e]||"bg-gray-500 border-gray-600 hover:bg-gray-600",d=e=>({Technology:"text-blue-700 border-blue-300 bg-blue-50 hover:bg-blue-100",Agriculture:"text-green-700 border-green-300 bg-green-50 hover:bg-green-100",Healthcare:"text-red-700 border-red-300 bg-red-50 hover:bg-red-100",Education:"text-yellow-700 border-yellow-300 bg-yellow-50 hover:bg-yellow-100",Environment:"text-emerald-700 border-emerald-300 bg-emerald-50 hover:bg-emerald-100",All:"text-purple-700 border-purple-300 bg-purple-50 hover:bg-purple-100"})[e]||"text-gray-700 border-gray-300 bg-gray-50 hover:bg-gray-100";return(0,a.jsx)("section",{className:"py-6 bg-white shadow-md sticky top-20 z-30",children:(0,a.jsx)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:`transition-all duration-700 transform ${l?"opacity-100 translate-y-0":"opacity-0 translate-y-10"}`,children:[(0,a.jsxs)("div",{className:"flex items-center mb-4",children:[(0,a.jsx)(n.A,{className:"text-purple-600 mr-2",size:20}),(0,a.jsx)("h2",{className:"text-lg font-bold text-gray-800",children:"Filter by Category"})]}),(0,a.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,a.jsx)("button",{onClick:()=>r(""),className:`px-4 py-2 rounded-full text-sm font-medium transition-all duration-300 border ${""===t?`text-white ${o("All")}`:d("All")}`,children:"All Categories"}),e.map(e=>(0,a.jsx)("button",{onClick:()=>r(e),className:`px-4 py-2 rounded-full text-sm font-medium transition-all duration-300 border ${t===e?`text-white ${o(e)}`:d(e)}`,children:e},e))]})]})})})};var c=r(85814),m=r.n(c),x=r(40228),u=r(41312),p=r(70334);let g=({featuredProjects:e})=>{let[t,r]=(0,s.useState)(!1);if((0,s.useEffect)(()=>{r(!0)},[]),!e||0===e.length)return null;let l=e=>({Technology:"bg-blue-500/10 text-blue-700 border-blue-300",Agriculture:"bg-green-500/10 text-green-700 border-green-300",Healthcare:"bg-red-500/10 text-red-700 border-red-300",Education:"bg-yellow-500/10 text-yellow-700 border-yellow-300",Environment:"bg-emerald-500/10 text-emerald-700 border-emerald-300"})[e]||"bg-gray-500/10 text-gray-700 border-gray-300";return(0,a.jsx)("section",{className:"py-16 bg-gray-50",children:(0,a.jsxs)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:`mb-12 text-center transition-all duration-700 transform ${t?"opacity-100 translate-y-0":"opacity-0 translate-y-10"}`,children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Featured Projects"}),(0,a.jsx)("div",{className:"w-24 h-1 bg-purple-600 mx-auto mb-4 rounded-full"}),(0,a.jsx)("p",{className:"text-gray-600 max-w-2xl mx-auto",children:"Explore our most innovative and impactful projects from the Far Western University Incubation Center"})]}),(0,a.jsx)("div",{className:"grid md:grid-cols-3 gap-8",children:e.map((e,r)=>(0,a.jsxs)("div",{className:`bg-white rounded-xl shadow-lg overflow-hidden group hover:shadow-xl transition-all duration-500 transform ${t?"opacity-100 translate-y-0":"opacity-0 translate-y-10"}`,style:{transitionDelay:`${(r+1)*100}ms`},children:[(0,a.jsxs)("div",{className:"relative h-48 overflow-hidden",children:[e.imageUrl?(0,a.jsx)(i.default,{src:e.imageUrl,alt:e.title,fill:!0,className:"object-cover transition-transform duration-700 group-hover:scale-105"}):(0,a.jsx)("div",{className:"w-full h-full bg-gradient-to-r from-purple-500 to-indigo-500"}),(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"}),(0,a.jsx)("div",{className:"absolute top-4 left-4",children:(0,a.jsx)("span",{className:`text-xs font-semibold px-3 py-1 rounded-full border ${l(e.category)}`,children:e.category})}),(0,a.jsx)("div",{className:"absolute bottom-4 right-4",children:(0,a.jsx)("span",{className:"bg-black/50 text-white text-xs px-2 py-1 rounded backdrop-blur-sm",children:e.status})})]}),(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-2 group-hover:text-purple-600 transition-colors",children:e.title}),(0,a.jsxs)("div",{className:"flex items-center text-gray-500 text-sm mb-4",children:[(0,a.jsx)(x.A,{className:"mr-2 text-purple-500"}),(0,a.jsxs)("span",{children:["Started: ",e.startDate]})]}),(0,a.jsx)("p",{className:"text-gray-600 mb-6 line-clamp-3",children:e.description}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(u.A,{className:"text-purple-500 mr-2"}),(0,a.jsxs)("span",{className:"text-sm text-gray-500",children:[e.teamSize," Team Members"]})]}),(0,a.jsxs)(m(),{href:`/projects/${e.id}`,className:"inline-flex items-center text-purple-600 font-semibold hover:text-purple-800 transition-colors group",children:["Details ",(0,a.jsx)(p.A,{className:"ml-2 group-hover:translate-x-1 transition-transform"})]})]})]})]},e.id))})]})})};var h=r(37360),b=r(48730);let f=({project:e,index:t})=>{let[r,l]=(0,s.useState)(!1);return(0,s.useEffect)(()=>{let e=setTimeout(()=>{l(!0)},100*t);return()=>clearTimeout(e)},[t]),(0,a.jsx)("article",{className:`bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-500 overflow-hidden group transform ${r?"opacity-100 translate-y-0":"opacity-0 translate-y-10"}`,children:(0,a.jsxs)("div",{className:"flex flex-col md:flex-row",children:[e.imageUrl&&(0,a.jsxs)("div",{className:"md:w-1/3 w-full h-56 md:h-auto relative flex-shrink-0 overflow-hidden",children:[(0,a.jsx)(i.default,{src:e.imageUrl,alt:e.title,fill:!0,className:"object-cover transition-transform duration-700 group-hover:scale-105 md:rounded-l-xl md:rounded-r-none rounded-t-xl"}),(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/30 to-transparent md:bg-gradient-to-l"})]}),(0,a.jsxs)("div",{className:`p-6 md:p-8 flex flex-col flex-grow ${e.imageUrl?"md:w-2/3":"w-full"}`,children:[(0,a.jsxs)("div",{className:"mb-4 flex flex-wrap items-center gap-x-4 gap-y-2",children:[(0,a.jsxs)("span",{className:`text-xs font-semibold px-3 py-1 rounded-full border ${{Technology:"bg-blue-500/10 text-blue-700 border-blue-300",Agriculture:"bg-green-500/10 text-green-700 border-green-300",Healthcare:"bg-red-500/10 text-red-700 border-red-300",Education:"bg-yellow-500/10 text-yellow-700 border-yellow-300",Environment:"bg-emerald-500/10 text-emerald-700 border-emerald-300"}[e.category]||"bg-gray-500/10 text-gray-700 border-gray-300"}`,children:[(0,a.jsx)(h.A,{className:"inline mr-1.5 mb-0.5"}),e.category]}),(0,a.jsxs)("span",{className:`text-xs font-semibold px-3 py-1 rounded-full border ${{Completed:"bg-green-500/10 text-green-700 border-green-300","In Progress":"bg-blue-500/10 text-blue-700 border-blue-300",Planning:"bg-yellow-500/10 text-yellow-700 border-yellow-300","On Hold":"bg-red-500/10 text-red-700 border-red-300"}[e.status]||"bg-gray-500/10 text-gray-700 border-gray-300"}`,children:[(0,a.jsx)(b.A,{className:"inline mr-1.5 mb-0.5"}),e.status]}),(0,a.jsxs)("div",{className:"flex items-center text-sm text-gray-500",children:[(0,a.jsx)(x.A,{className:"mr-1.5 text-purple-500"}),(0,a.jsxs)("span",{children:["Started: ",e.startDate]})]})]}),(0,a.jsx)("h2",{className:"text-xl lg:text-2xl font-bold text-gray-900 mb-3 group-hover:text-purple-600 transition-colors leading-tight",children:(0,a.jsx)(m(),{href:`/projects/${e.id}`,className:"hover:underline",children:e.title})}),(0,a.jsx)("p",{className:"text-gray-600 mb-5 line-clamp-3 flex-grow",children:e.description}),(0,a.jsxs)("div",{className:"mt-auto pt-4 border-t border-gray-100 flex flex-wrap justify-between items-center",children:[(0,a.jsxs)("div",{className:"flex items-center text-sm text-gray-500 mb-2 md:mb-0",children:[(0,a.jsx)(u.A,{className:"mr-1.5 text-purple-500"}),(0,a.jsxs)("span",{children:[e.teamSize," Team Members"]})]}),(0,a.jsxs)(m(),{href:`/projects/${e.id}`,className:"inline-flex items-center text-purple-600 hover:text-purple-800 font-semibold transition-colors",children:["View Project ",(0,a.jsx)(p.A,{className:"ml-2 group-hover:translate-x-1 transition-transform"})]})]})]})]})})};var y=r(40421);function v(){let e=[{id:"p1",title:"Smart Agriculture Monitoring System",description:"An IoT-based system for monitoring soil moisture, temperature, and other environmental factors to optimize crop growth and water usage in the Far Western region of Nepal.",category:"Agriculture",status:"In Progress",startDate:"June 2023",teamSize:5,imageUrl:"https://images.unsplash.com/photo-1625246333195-78d9c38ad449?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80"},{id:"p2",title:"Renewable Energy Solutions for Rural Communities",description:"Developing affordable solar and micro-hydro power solutions for remote villages in the Far Western region, providing clean energy access to underserved communities.",category:"Environment",status:"In Progress",startDate:"March 2023",teamSize:7,imageUrl:"https://images.unsplash.com/photo-1509391366360-2e959784a276?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80"},{id:"p3",title:"Mobile Health Clinic Application",description:"A mobile application connecting remote communities with healthcare professionals, enabling telemedicine consultations and health monitoring for areas with limited healthcare access.",category:"Healthcare",status:"Completed",startDate:"January 2023",teamSize:4,imageUrl:"https://images.unsplash.com/photo-1576091160550-2173dba999ef?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80"},{id:"p4",title:"Digital Literacy Program for Rural Schools",description:"An educational initiative providing computer equipment, training, and digital curriculum to schools in rural areas, bridging the digital divide for students in the Far Western region.",category:"Education",status:"In Progress",startDate:"August 2023",teamSize:6,imageUrl:"https://images.unsplash.com/photo-1503676260728-1c00da094a0b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80"},{id:"p5",title:"Water Purification Technology for Rural Areas",description:"Developing low-cost, sustainable water purification systems using locally available materials to provide clean drinking water to communities facing water quality challenges.",category:"Environment",status:"Planning",startDate:"October 2023",teamSize:3,imageUrl:"https://images.unsplash.com/photo-1581244277943-fe4a9c777189?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80"},{id:"p6",title:"AI-Powered Crop Disease Detection",description:"A machine learning application that helps farmers identify crop diseases through smartphone photos, providing immediate diagnosis and treatment recommendations to prevent crop loss.",category:"Technology",status:"In Progress",startDate:"May 2023",teamSize:4,imageUrl:"https://images.unsplash.com/photo-1530836369250-ef72a3f5cda8?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80"},{id:"p7",title:"Biodiversity Conservation and Monitoring",description:"A research project documenting and preserving the unique biodiversity of the Far Western region, involving local communities in conservation efforts and sustainable resource management.",category:"Environment",status:"In Progress",startDate:"February 2023",teamSize:8,imageUrl:"https://images.unsplash.com/photo-1500829243541-74b677fecc30?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80"},{id:"p8",title:"Traditional Knowledge Digital Archive",description:"Documenting and preserving traditional knowledge, practices, and cultural heritage of indigenous communities in the Far Western region through digital archiving and community engagement.",category:"Education",status:"On Hold",startDate:"April 2023",teamSize:5,imageUrl:"https://images.unsplash.com/photo-1532619675605-1ede6c2ed2b0?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80"}],[t,r]=(0,s.useState)(""),[l,i]=(0,s.useState)(""),n=Array.from(new Set(e.map(e=>e.category))).sort(),c=e.slice(0,3),x=e.filter(e=>e.title.toLowerCase().includes(t.toLowerCase())||e.description.toLowerCase().includes(t.toLowerCase())).filter(e=>!l||e.category===l);return(0,a.jsxs)("main",{className:"bg-white",children:[(0,a.jsx)(o,{searchTerm:t,onSearchChange:r}),(0,a.jsx)(d,{categories:n,selectedCategory:l,onCategoryChange:i}),!t&&!l&&(0,a.jsx)(g,{featuredProjects:c}),(0,a.jsx)("section",{className:"py-16 md:py-20 bg-gray-50",children:(0,a.jsxs)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"mb-12 text-center",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-2",children:t||l?"Search Results":"All Projects"}),(0,a.jsx)("div",{className:"w-24 h-1 bg-purple-600 mx-auto mb-4 rounded-full"}),(0,a.jsx)("p",{className:"text-gray-600 max-w-2xl mx-auto",children:t||l?`Showing ${x.length} result${1!==x.length?"s":""}`:"Explore all innovative projects from the Far Western University Incubation Center"})]}),x.length>0?(0,a.jsx)("div",{className:"max-w-4xl mx-auto space-y-8",children:x.map((e,t)=>(0,a.jsx)(f,{project:e,index:t},e.id))}):(0,a.jsxs)("div",{className:"text-center py-12 bg-white rounded-xl shadow-md max-w-2xl mx-auto",children:[(0,a.jsx)(y.A,{title:"No Projects Found",subtitle:"Try adjusting your search or filters"}),(0,a.jsx)("p",{className:"text-gray-600 mt-4 px-6",children:"We couldn't find any projects matching your criteria. Please try different keywords or browse all categories."}),(0,a.jsx)("button",{onClick:()=>{r(""),i("")},className:"mt-6 px-6 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors",children:"View All Projects"})]})]})}),(0,a.jsx)("section",{className:"py-16 bg-gradient-to-r from-purple-900 to-indigo-900 text-white",children:(0,a.jsx)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"max-w-3xl mx-auto text-center",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold mb-6",children:"Have an Innovative Project Idea?"}),(0,a.jsx)("p",{className:"text-xl text-indigo-100 mb-8",children:"The Far Western University Incubation Center is looking for innovative project proposals. Submit your idea and join our community of innovators."}),(0,a.jsxs)(m(),{href:"/submit-proposal",className:"inline-flex items-center px-6 py-3 bg-white text-purple-900 font-bold rounded-lg hover:bg-indigo-100 transition-colors",children:["Submit Your Proposal ",(0,a.jsx)(p.A,{className:"ml-2"})]})]})})})]})}},40228:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},40421:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var a=r(60687);let s=({title:e,subtitle:t,align:r="center",accentColor:s="indigo"})=>(0,a.jsxs)("div",{className:`mb-12 md:mb-16 ${{left:"text-left",center:"text-center",right:"text-right"}[r]}`,children:[t&&(0,a.jsx)("p",{className:`${{indigo:"text-indigo-600",blue:"text-blue-600",teal:"text-teal-600",purple:"text-purple-600",green:"text-green-600"}[s]} font-semibold text-sm md:text-base uppercase tracking-wider mb-2`,children:t}),(0,a.jsx)("h2",{className:"text-3xl md:text-4xl font-bold text-gray-900",children:e}),(0,a.jsx)("div",{className:`mt-4 h-1.5 w-24 ${"center"===r?"mx-auto":"right"===r?"ml-auto":""} ${{indigo:"bg-indigo-600",blue:"bg-blue-600",teal:"bg-teal-600",purple:"bg-purple-600",green:"bg-green-600"}[s]} rounded-full`})]})},48730:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68975:(e,t,r)=>{Promise.resolve().then(r.bind(r,38194))},70334:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});var a=r(31658);let s=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},79551:e=>{"use strict";e.exports=require("url")},80462:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},99270:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[447,172,658,54],()=>r(25960));module.exports=a})();