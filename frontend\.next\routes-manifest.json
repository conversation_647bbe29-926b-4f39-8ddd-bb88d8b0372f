{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [], "dynamicRoutes": [{"page": "/news/[slug]", "regex": "^/news/([^/]+?)(?:/)?$", "routeKeys": {"nxtPslug": "nxtPslug"}, "namedRegex": "^/news/(?<nxtPslug>[^/]+?)(?:/)?$"}, {"page": "/programs/[slug]", "regex": "^/programs/([^/]+?)(?:/)?$", "routeKeys": {"nxtPslug": "nxtPslug"}, "namedRegex": "^/programs/(?<nxtPslug>[^/]+?)(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/about", "regex": "^/about(?:/)?$", "routeKeys": {}, "namedRegex": "^/about(?:/)?$"}, {"page": "/apply", "regex": "^/apply(?:/)?$", "routeKeys": {}, "namedRegex": "^/apply(?:/)?$"}, {"page": "/careers", "regex": "^/careers(?:/)?$", "routeKeys": {}, "namedRegex": "^/careers(?:/)?$"}, {"page": "/community", "regex": "^/community(?:/)?$", "routeKeys": {}, "namedRegex": "^/community(?:/)?$"}, {"page": "/contact", "regex": "^/contact(?:/)?$", "routeKeys": {}, "namedRegex": "^/contact(?:/)?$"}, {"page": "/dashboard", "regex": "^/dashboard(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard(?:/)?$"}, {"page": "/dashboard/applicants", "regex": "^/dashboard/applicants(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/applicants(?:/)?$"}, {"page": "/dashboard/application", "regex": "^/dashboard/application(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/application(?:/)?$"}, {"page": "/dashboard/committee", "regex": "^/dashboard/committee(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/committee(?:/)?$"}, {"page": "/dashboard/gallery-images", "regex": "^/dashboard/gallery\\-images(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/gallery\\-images(?:/)?$"}, {"page": "/dashboard/news", "regex": "^/dashboard/news(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/news(?:/)?$"}, {"page": "/dashboard/notice", "regex": "^/dashboard/notice(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/notice(?:/)?$"}, {"page": "/dashboard/photo-gallery", "regex": "^/dashboard/photo\\-gallery(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/photo\\-gallery(?:/)?$"}, {"page": "/faculty", "regex": "^/faculty(?:/)?$", "routeKeys": {}, "namedRegex": "^/faculty(?:/)?$"}, {"page": "/faq", "regex": "^/faq(?:/)?$", "routeKeys": {}, "namedRegex": "^/faq(?:/)?$"}, {"page": "/favicon.ico", "regex": "^/favicon\\.ico(?:/)?$", "routeKeys": {}, "namedRegex": "^/favicon\\.ico(?:/)?$"}, {"page": "/gallery", "regex": "^/gallery(?:/)?$", "routeKeys": {}, "namedRegex": "^/gallery(?:/)?$"}, {"page": "/login", "regex": "^/login(?:/)?$", "routeKeys": {}, "namedRegex": "^/login(?:/)?$"}, {"page": "/news", "regex": "^/news(?:/)?$", "routeKeys": {}, "namedRegex": "^/news(?:/)?$"}, {"page": "/programs", "regex": "^/programs(?:/)?$", "routeKeys": {}, "namedRegex": "^/programs(?:/)?$"}, {"page": "/projects", "regex": "^/projects(?:/)?$", "routeKeys": {}, "namedRegex": "^/projects(?:/)?$"}, {"page": "/research", "regex": "^/research(?:/)?$", "routeKeys": {}, "namedRegex": "^/research(?:/)?$"}, {"page": "/signup", "regex": "^/signup(?:/)?$", "routeKeys": {}, "namedRegex": "^/signup(?:/)?$"}, {"page": "/startups", "regex": "^/startups(?:/)?$", "routeKeys": {}, "namedRegex": "^/startups(?:/)?$"}, {"page": "/submit-proposal", "regex": "^/submit\\-proposal(?:/)?$", "routeKeys": {}, "namedRegex": "^/submit\\-proposal(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc", "prefetchSegmentHeader": "Next-Router-Segment-Prefetch", "prefetchSegmentSuffix": ".segment.rsc", "prefetchSegmentDirSuffix": ".segments"}, "rewriteHeaders": {"pathHeader": "x-nextjs-rewritten-path", "queryHeader": "x-nextjs-rewritten-query"}, "rewrites": []}