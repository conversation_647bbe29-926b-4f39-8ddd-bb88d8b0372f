"use strict";exports.id=610,exports.ids=[610],exports.modules={14952:(e,t,r)=>{r.d(t,{A:()=>s});let s=(0,r(62688).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},25541:(e,t,r)=>{r.d(t,{A:()=>s});let s=(0,r(62688).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},28947:(e,t,r)=>{r.d(t,{A:()=>s});let s=(0,r(62688).A)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},31158:(e,t,r)=>{r.d(t,{A:()=>s});let s=(0,r(62688).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},40228:(e,t,r)=>{r.d(t,{A:()=>s});let s=(0,r(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},47033:(e,t,r)=>{r.d(t,{A:()=>s});let s=(0,r(62688).A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},51423:(e,t,r)=>{r.d(t,{I:()=>T});var s=r(39850),i=r(33465),a=r(61489),n=r(35536),h=r(73458),u=r(31212),c=class extends n.Q{constructor(e,t){super(),this.options=t,this.#e=e,this.#t=null,this.#r=(0,h.T)(),this.options.experimental_prefetchInRender||this.#r.reject(Error("experimental_prefetchInRender feature flag is not enabled")),this.bindMethods(),this.setOptions(t)}#e;#s=void 0;#i=void 0;#a=void 0;#n;#h;#r;#t;#u;#c;#l;#o;#d;#p;#y=new Set;bindMethods(){this.refetch=this.refetch.bind(this)}onSubscribe(){1===this.listeners.size&&(this.#s.addObserver(this),l(this.#s,this.options)?this.#f():this.updateResult(),this.#R())}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return o(this.#s,this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return o(this.#s,this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,this.#v(),this.#m(),this.#s.removeObserver(this)}setOptions(e){let t=this.options,r=this.#s;if(this.options=this.#e.defaultQueryOptions(e),void 0!==this.options.enabled&&"boolean"!=typeof this.options.enabled&&"function"!=typeof this.options.enabled&&"boolean"!=typeof(0,u.Eh)(this.options.enabled,this.#s))throw Error("Expected enabled to be a boolean or a callback that returns a boolean");this.#Q(),this.#s.setOptions(this.options),t._defaulted&&!(0,u.f8)(this.options,t)&&this.#e.getQueryCache().notify({type:"observerOptionsUpdated",query:this.#s,observer:this});let s=this.hasListeners();s&&d(this.#s,r,this.options,t)&&this.#f(),this.updateResult(),s&&(this.#s!==r||(0,u.Eh)(this.options.enabled,this.#s)!==(0,u.Eh)(t.enabled,this.#s)||(0,u.d2)(this.options.staleTime,this.#s)!==(0,u.d2)(t.staleTime,this.#s))&&this.#b();let i=this.#k();s&&(this.#s!==r||(0,u.Eh)(this.options.enabled,this.#s)!==(0,u.Eh)(t.enabled,this.#s)||i!==this.#p)&&this.#g(i)}getOptimisticResult(e){var t,r;let s=this.#e.getQueryCache().build(this.#e,e),i=this.createResult(s,e);return t=this,r=i,(0,u.f8)(t.getCurrentResult(),r)||(this.#a=i,this.#h=this.options,this.#n=this.#s.state),i}getCurrentResult(){return this.#a}trackResult(e,t){return new Proxy(e,{get:(e,r)=>(this.trackProp(r),t?.(r),Reflect.get(e,r))})}trackProp(e){this.#y.add(e)}getCurrentQuery(){return this.#s}refetch({...e}={}){return this.fetch({...e})}fetchOptimistic(e){let t=this.#e.defaultQueryOptions(e),r=this.#e.getQueryCache().build(this.#e,t);return r.fetch().then(()=>this.createResult(r,t))}fetch(e){return this.#f({...e,cancelRefetch:e.cancelRefetch??!0}).then(()=>(this.updateResult(),this.#a))}#f(e){this.#Q();let t=this.#s.fetch(this.options,e);return e?.throwOnError||(t=t.catch(u.lQ)),t}#b(){this.#v();let e=(0,u.d2)(this.options.staleTime,this.#s);if(u.S$||this.#a.isStale||!(0,u.gn)(e))return;let t=(0,u.j3)(this.#a.dataUpdatedAt,e);this.#o=setTimeout(()=>{this.#a.isStale||this.updateResult()},t+1)}#k(){return("function"==typeof this.options.refetchInterval?this.options.refetchInterval(this.#s):this.options.refetchInterval)??!1}#g(e){this.#m(),this.#p=e,!u.S$&&!1!==(0,u.Eh)(this.options.enabled,this.#s)&&(0,u.gn)(this.#p)&&0!==this.#p&&(this.#d=setInterval(()=>{(this.options.refetchIntervalInBackground||s.m.isFocused())&&this.#f()},this.#p))}#R(){this.#b(),this.#g(this.#k())}#v(){this.#o&&(clearTimeout(this.#o),this.#o=void 0)}#m(){this.#d&&(clearInterval(this.#d),this.#d=void 0)}createResult(e,t){let r,s=this.#s,i=this.options,n=this.#a,c=this.#n,o=this.#h,y=e!==s?e.state:this.#i,{state:f}=e,R={...f},v=!1;if(t._optimisticResults){let r=this.hasListeners(),n=!r&&l(e,t),h=r&&d(e,s,t,i);(n||h)&&(R={...R,...(0,a.k)(f.data,e.options)}),"isRestoring"===t._optimisticResults&&(R.fetchStatus="idle")}let{error:m,errorUpdatedAt:Q,status:b}=R;r=R.data;let k=!1;if(void 0!==t.placeholderData&&void 0===r&&"pending"===b){let e;n?.isPlaceholderData&&t.placeholderData===o?.placeholderData?(e=n.data,k=!0):e="function"==typeof t.placeholderData?t.placeholderData(this.#l?.state.data,this.#l):t.placeholderData,void 0!==e&&(b="success",r=(0,u.pl)(n?.data,e,t),v=!0)}if(t.select&&void 0!==r&&!k)if(n&&r===c?.data&&t.select===this.#u)r=this.#c;else try{this.#u=t.select,r=t.select(r),r=(0,u.pl)(n?.data,r,t),this.#c=r,this.#t=null}catch(e){this.#t=e}this.#t&&(m=this.#t,r=this.#c,Q=Date.now(),b="error");let g="fetching"===R.fetchStatus,I="pending"===b,x="error"===b,A=I&&g,O=void 0!==r,T={status:b,fetchStatus:R.fetchStatus,isPending:I,isSuccess:"success"===b,isError:x,isInitialLoading:A,isLoading:A,data:r,dataUpdatedAt:R.dataUpdatedAt,error:m,errorUpdatedAt:Q,failureCount:R.fetchFailureCount,failureReason:R.fetchFailureReason,errorUpdateCount:R.errorUpdateCount,isFetched:R.dataUpdateCount>0||R.errorUpdateCount>0,isFetchedAfterMount:R.dataUpdateCount>y.dataUpdateCount||R.errorUpdateCount>y.errorUpdateCount,isFetching:g,isRefetching:g&&!I,isLoadingError:x&&!O,isPaused:"paused"===R.fetchStatus,isPlaceholderData:v,isRefetchError:x&&O,isStale:p(e,t),refetch:this.refetch,promise:this.#r};if(this.options.experimental_prefetchInRender){let t=e=>{"error"===T.status?e.reject(T.error):void 0!==T.data&&e.resolve(T.data)},r=()=>{t(this.#r=T.promise=(0,h.T)())},i=this.#r;switch(i.status){case"pending":e.queryHash===s.queryHash&&t(i);break;case"fulfilled":("error"===T.status||T.data!==i.value)&&r();break;case"rejected":("error"!==T.status||T.error!==i.reason)&&r()}}return T}updateResult(){let e=this.#a,t=this.createResult(this.#s,this.options);this.#n=this.#s.state,this.#h=this.options,void 0!==this.#n.data&&(this.#l=this.#s),(0,u.f8)(t,e)||(this.#a=t,this.#I({listeners:(()=>{if(!e)return!0;let{notifyOnChangeProps:t}=this.options,r="function"==typeof t?t():t;if("all"===r||!r&&!this.#y.size)return!0;let s=new Set(r??this.#y);return this.options.throwOnError&&s.add("error"),Object.keys(this.#a).some(t=>this.#a[t]!==e[t]&&s.has(t))})()}))}#Q(){let e=this.#e.getQueryCache().build(this.#e,this.options);if(e===this.#s)return;let t=this.#s;this.#s=e,this.#i=e.state,this.hasListeners()&&(t?.removeObserver(this),e.addObserver(this))}onQueryUpdate(){this.updateResult(),this.hasListeners()&&this.#R()}#I(e){i.jG.batch(()=>{e.listeners&&this.listeners.forEach(e=>{e(this.#a)}),this.#e.getQueryCache().notify({query:this.#s,type:"observerResultsUpdated"})})}};function l(e,t){return!1!==(0,u.Eh)(t.enabled,e)&&void 0===e.state.data&&("error"!==e.state.status||!1!==t.retryOnMount)||void 0!==e.state.data&&o(e,t,t.refetchOnMount)}function o(e,t,r){if(!1!==(0,u.Eh)(t.enabled,e)&&"static"!==(0,u.d2)(t.staleTime,e)){let s="function"==typeof r?r(e):r;return"always"===s||!1!==s&&p(e,t)}return!1}function d(e,t,r,s){return(e!==t||!1===(0,u.Eh)(s.enabled,e))&&(!r.suspense||"error"!==e.state.status)&&p(e,r)}function p(e,t){return!1!==(0,u.Eh)(t.enabled,e)&&e.isStaleByTime((0,u.d2)(t.staleTime,e))}var y=r(43210),f=r(8693);r(60687);var R=y.createContext(function(){let e=!1;return{clearReset:()=>{e=!1},reset:()=>{e=!0},isReset:()=>e}}()),v=()=>y.useContext(R),m=(e,t)=>{(e.suspense||e.throwOnError||e.experimental_prefetchInRender)&&!t.isReset()&&(e.retryOnMount=!1)},Q=e=>{y.useEffect(()=>{e.clearReset()},[e])},b=({result:e,errorResetBoundary:t,throwOnError:r,query:s,suspense:i})=>e.isError&&!t.isReset()&&!e.isFetching&&s&&(i&&void 0===e.data||(0,u.GU)(r,[e.error,s])),k=y.createContext(!1),g=()=>y.useContext(k);k.Provider;var I=e=>{if(e.suspense){let t=e=>"static"===e?e:Math.max(e??1e3,1e3),r=e.staleTime;e.staleTime="function"==typeof r?(...e)=>t(r(...e)):t(r),"number"==typeof e.gcTime&&(e.gcTime=Math.max(e.gcTime,1e3))}},x=(e,t)=>e.isLoading&&e.isFetching&&!t,A=(e,t)=>e?.suspense&&t.isPending,O=(e,t,r)=>t.fetchOptimistic(e).catch(()=>{r.clearReset()});function T(e,t){return function(e,t,r){let s=g(),a=v(),n=(0,f.jE)(r),h=n.defaultQueryOptions(e);n.getDefaultOptions().queries?._experimental_beforeQuery?.(h),h._optimisticResults=s?"isRestoring":"optimistic",I(h),m(h,a),Q(a);let c=!n.getQueryCache().get(h.queryHash),[l]=y.useState(()=>new t(n,h)),o=l.getOptimisticResult(h),d=!s&&!1!==e.subscribed;if(y.useSyncExternalStore(y.useCallback(e=>{let t=d?l.subscribe(i.jG.batchCalls(e)):u.lQ;return l.updateResult(),t},[l,d]),()=>l.getCurrentResult(),()=>l.getCurrentResult()),y.useEffect(()=>{l.setOptions(h)},[h,l]),A(h,o))throw O(h,l,a);if(b({result:o,errorResetBoundary:a,throwOnError:h.throwOnError,query:n.getQueryCache().get(h.queryHash),suspense:h.suspense}))throw o.error;if(n.getDefaultOptions().queries?._experimental_afterQuery?.(h,o),h.experimental_prefetchInRender&&!u.S$&&x(o,s)){let e=c?O(h,l,a):n.getQueryCache().get(h.queryHash)?.promise;e?.catch(u.lQ).finally(()=>{l.updateResult()})}return h.notifyOnChangeProps?o:l.trackResult(o)}(e,c,t)}},56085:(e,t,r)=>{r.d(t,{A:()=>s});let s=(0,r(62688).A)("sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]])},58869:(e,t,r)=>{r.d(t,{A:()=>s});let s=(0,r(62688).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},75034:(e,t,r)=>{r.d(t,{A:()=>s});let s=(0,r(62688).A)("pen-line",[["path",{d:"M12 20h9",key:"t2du7b"}],["path",{d:"M16.376 3.622a1 1 0 0 1 3.002 3.002L7.368 18.635a2 2 0 0 1-.855.506l-2.872.838a.5.5 0 0 1-.62-.62l.838-2.872a2 2 0 0 1 .506-.854z",key:"1ykcvy"}]])},80462:(e,t,r)=>{r.d(t,{A:()=>s});let s=(0,r(62688).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},82080:(e,t,r)=>{r.d(t,{A:()=>s});let s=(0,r(62688).A)("book-open",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]])},86561:(e,t,r)=>{r.d(t,{A:()=>s});let s=(0,r(62688).A)("award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]])},88233:(e,t,r)=>{r.d(t,{A:()=>s});let s=(0,r(62688).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},99270:(e,t,r)=>{r.d(t,{A:()=>s});let s=(0,r(62688).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])}};