{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/fwu_incubation%28react%2Blaravel%29/incubation/frontend/src/app/components/shared/SectionTitle.tsx"], "sourcesContent": ["// components/shared/SectionTitle.tsx\r\ninterface SectionTitleProps {\r\n  title: string;\r\n  subtitle?: string;\r\n  align?: 'left' | 'center' | 'right';\r\n  accentColor?: string;\r\n}\r\n\r\nconst SectionTitle: React.FC<SectionTitleProps> = ({\r\n  title,\r\n  subtitle,\r\n  align = 'center',\r\n  accentColor = 'indigo'\r\n}) => {\r\n  const alignmentClass = {\r\n    left: 'text-left',\r\n    center: 'text-center',\r\n    right: 'text-right',\r\n  };\r\n\r\n  const accentColorClass = {\r\n    indigo: 'bg-indigo-600',\r\n    blue: 'bg-blue-600',\r\n    teal: 'bg-teal-600',\r\n    purple: 'bg-purple-600',\r\n    green: 'bg-green-600',\r\n  };\r\n\r\n  const textColorClass = {\r\n    indigo: 'text-indigo-600',\r\n    blue: 'text-blue-600',\r\n    teal: 'text-teal-600',\r\n    purple: 'text-purple-600',\r\n    green: 'text-green-600',\r\n  };\r\n\r\n  return (\r\n    <div className={`mb-12 md:mb-16 ${alignmentClass[align]}`}>\r\n      {subtitle && (\r\n        <p className={`${textColorClass[accentColor as keyof typeof textColorClass]} font-semibold text-sm md:text-base uppercase tracking-wider mb-2`}>\r\n          {subtitle}\r\n        </p>\r\n      )}\r\n      <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900\">\r\n        {title}\r\n      </h2>\r\n      <div className={`mt-4 h-1.5 w-24 ${align === 'center' ? 'mx-auto' : (align === 'right' ? 'ml-auto' : '')} ${accentColorClass[accentColor as keyof typeof accentColorClass]} rounded-full`}></div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default SectionTitle;"], "names": [], "mappings": "AAAA,qCAAqC;;;;;;AAQrC,MAAM,eAA4C,CAAC,EACjD,KAAK,EACL,QAAQ,EACR,QAAQ,QAAQ,EAChB,cAAc,QAAQ,EACvB;IACC,MAAM,iBAAiB;QACrB,MAAM;QACN,QAAQ;QACR,OAAO;IACT;IAEA,MAAM,mBAAmB;QACvB,QAAQ;QACR,MAAM;QACN,MAAM;QACN,QAAQ;QACR,OAAO;IACT;IAEA,MAAM,iBAAiB;QACrB,QAAQ;QACR,MAAM;QACN,MAAM;QACN,QAAQ;QACR,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAC,eAAe,EAAE,cAAc,CAAC,MAAM,EAAE;;YACtD,0BACC,6LAAC;gBAAE,WAAW,GAAG,cAAc,CAAC,YAA2C,CAAC,iEAAiE,CAAC;0BAC3I;;;;;;0BAGL,6LAAC;gBAAG,WAAU;0BACX;;;;;;0BAEH,6LAAC;gBAAI,WAAW,CAAC,gBAAgB,EAAE,UAAU,WAAW,YAAa,UAAU,UAAU,YAAY,GAAI,CAAC,EAAE,gBAAgB,CAAC,YAA6C,CAAC,aAAa,CAAC;;;;;;;;;;;;AAG/L;KAzCM;uCA2CS", "debugId": null}}, {"offset": {"line": 79, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/fwu_incubation%28react%2Blaravel%29/incubation/frontend/src/app/components/programs/GalleryImageCard.tsx"], "sourcesContent": ["\"use client\"\r\nimport Image from 'next/image';\r\nimport Link from 'next/link';\r\n\r\nexport interface GalleryImageCardProps {\r\n  imageUrl: string;\r\n  altText: string;\r\n  caption?: string;\r\n  eventDate?: string;\r\n  isLoaded?: boolean;\r\n}\r\n\r\nconst GalleryImageCard: React.FC<GalleryImageCardProps> = ({\r\n  imageUrl,\r\n  altText,\r\n  caption,\r\n  eventDate,\r\n  isLoaded = true\r\n}) => {\r\n  return (\r\n    <Link href=\"/gallery\" className=\"block\">\r\n      <div className=\"relative group rounded-lg overflow-hidden shadow-lg h-64 transform hover:shadow-2xl transition-all duration-300 hover:-translate-y-1 bg-gray-200\">\r\n        <div className=\"w-full h-full relative\">\r\n          {isLoaded && (\r\n            <Image\r\n              src={imageUrl}\r\n              alt={altText}\r\n              fill\r\n              className=\"object-cover transform group-hover:scale-110 transition-transform duration-500 ease-in-out\"\r\n              onError={(e) => {\r\n                // Fallback to a placeholder on error\r\n                const target = e.target as HTMLImageElement;\r\n                target.src = \"https://via.placeholder.com/800x600/e2e8f0/475569?text=FWU+Incubation\";\r\n              }}\r\n            />\r\n          )}\r\n        </div>\r\n        {(caption || eventDate) && (\r\n          <div className=\"absolute inset-0 bg-gradient-to-t from-black/70 via-black/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex flex-col justify-end p-4\">\r\n            {caption && <h4 className=\"text-white text-lg font-semibold\">{caption}</h4>}\r\n            {eventDate && <p className=\"text-gray-300 text-sm\">{eventDate}</p>}\r\n          </div>\r\n        )}\r\n      </div>\r\n    </Link>\r\n  );\r\n};\r\n\r\nexport default GalleryImageCard;"], "names": [], "mappings": ";;;;AACA;AACA;AAFA;;;;AAYA,MAAM,mBAAoD,CAAC,EACzD,QAAQ,EACR,OAAO,EACP,OAAO,EACP,SAAS,EACT,WAAW,IAAI,EAChB;IACC,qBACE,6LAAC,+JAAA,CAAA,UAAI;QAAC,MAAK;QAAW,WAAU;kBAC9B,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACZ,0BACC,6LAAC,gIAAA,CAAA,UAAK;wBACJ,KAAK;wBACL,KAAK;wBACL,IAAI;wBACJ,WAAU;wBACV,SAAS,CAAC;4BACR,qCAAqC;4BACrC,MAAM,SAAS,EAAE,MAAM;4BACvB,OAAO,GAAG,GAAG;wBACf;;;;;;;;;;;gBAIL,CAAC,WAAW,SAAS,mBACpB,6LAAC;oBAAI,WAAU;;wBACZ,yBAAW,6LAAC;4BAAG,WAAU;sCAAoC;;;;;;wBAC7D,2BAAa,6LAAC;4BAAE,WAAU;sCAAyB;;;;;;;;;;;;;;;;;;;;;;;AAMhE;KAlCM;uCAoCS", "debugId": null}}, {"offset": {"line": 168, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/fwu_incubation%28react%2Blaravel%29/incubation/frontend/src/app/components/programs/PastEventsGallerySection%20.tsx"], "sourcesContent": ["\"use client\"\r\nimport SectionTitle from '../shared/SectionTitle';\r\nimport GalleryImageCard from './GalleryImageCard';\r\nimport Link from 'next/link';\r\nimport { useEffect, useState } from 'react';\r\n\r\n// Gallery images data with Unsplash images\r\nconst pastEventsData = [\r\n  {\r\n    id: 'pe1',\r\n    imageUrl: 'https://images.unsplash.com/photo-1517245386807-bb43f82c33c4?q=80&w=800&auto=format&fit=crop',\r\n    altText: 'Incubation Center Planning Meeting',\r\n    caption: 'Incubation Center Planning Meeting',\r\n    eventDate: 'March 15, 2025',\r\n  },\r\n  {\r\n    id: 'pe2',\r\n    imageUrl: 'https://images.unsplash.com/photo-1519389950473-47ba0277781c?q=80&w=800&auto=format&fit=crop',\r\n    altText: 'Startup Mentorship Session',\r\n    caption: 'Startup Mentorship Session',\r\n    eventDate: 'March 20, 2025',\r\n  },\r\n  {\r\n    id: 'pe3',\r\n    imageUrl: 'https://images.unsplash.com/photo-1531482615713-2afd69097998?q=80&w=800&auto=format&fit=crop',\r\n    altText: 'Entrepreneurship Skills Development',\r\n    caption: 'Entrepreneurship Skills Development',\r\n    eventDate: 'January 25, 2025',\r\n  },\r\n  {\r\n    id: 'pe4',\r\n    imageUrl: 'https://images.unsplash.com/photo-1540575467063-178a50c2df87?q=80&w=800&auto=format&fit=crop',\r\n    altText: 'International Conference on Innovation',\r\n    caption: 'International Conference on Innovation',\r\n    eventDate: 'February 15, 2025',\r\n  },\r\n  {\r\n    id: 'pe5',\r\n    imageUrl: 'https://images.unsplash.com/photo-1504384308090-c894fdcc538d?q=80&w=800&auto=format&fit=crop',\r\n    altText: 'Innovation Hackathon',\r\n    caption: 'Innovation Hackathon',\r\n    eventDate: 'April 12-13, 2025',\r\n  },\r\n  {\r\n    id: 'pe6',\r\n    imageUrl: 'https://images.unsplash.com/photo-1560439514-4e9645039924?q=80&w=800&auto=format&fit=crop',\r\n    altText: 'MOU Signing with Industry Partners',\r\n    caption: 'MOU Signing with Industry Partners',\r\n    eventDate: 'December 10, 2024',\r\n  },\r\n];\r\n\r\nconst PastEventsGallerySection = () => {\r\n  const [isLoaded, setIsLoaded] = useState(false);\r\n\r\n  // Simulate image loading\r\n  useEffect(() => {\r\n    setIsLoaded(true);\r\n  }, []);\r\n\r\n  return (\r\n    <section className=\"py-16 md:py-24 bg-gradient-to-b from-brand-light to-white\">\r\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\r\n        <SectionTitle title=\"FWU Incubation Center Gallery\" subtitle=\"Moments & Memories\" />\r\n\r\n        <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8 mt-12\">\r\n          {pastEventsData.map((event, index) => (\r\n            <div\r\n              key={event.id}\r\n              className=\"opacity-0 animate-fadeIn\"\r\n              style={{\r\n                animationDelay: `${index * 150}ms`,\r\n                animationFillMode: 'forwards'\r\n              }}\r\n            >\r\n              <GalleryImageCard\r\n                imageUrl={event.imageUrl}\r\n                altText={event.altText}\r\n                caption={event.caption}\r\n                eventDate={event.eventDate}\r\n                isLoaded={isLoaded}\r\n              />\r\n            </div>\r\n          ))}\r\n        </div>\r\n\r\n        <div className=\"text-center mt-12 opacity-0 animate-fadeIn animation-delay-1000\" style={{ animationFillMode: 'forwards' }}>\r\n          <Link\r\n            href=\"/gallery\"\r\n            className=\"inline-block bg-brand-primary hover:bg-brand-primary-dark border border-blue-400 font-semibold py-3 px-8 rounded-lg shadow-md transition-all duration-300 hover:scale-105 transform\"\r\n          >\r\n            View Full Gallery\r\n          </Link>\r\n          <p className=\"text-gray-600 mt-2\">\r\n            Explore more photos from our events, workshops, and partnerships\r\n          </p>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\nexport default PastEventsGallerySection;"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;;;AAJA;;;;;AAMA,2CAA2C;AAC3C,MAAM,iBAAiB;IACrB;QACE,IAAI;QACJ,UAAU;QACV,SAAS;QACT,SAAS;QACT,WAAW;IACb;IACA;QACE,IAAI;QACJ,UAAU;QACV,SAAS;QACT,SAAS;QACT,WAAW;IACb;IACA;QACE,IAAI;QACJ,UAAU;QACV,SAAS;QACT,SAAS;QACT,WAAW;IACb;IACA;QACE,IAAI;QACJ,UAAU;QACV,SAAS;QACT,SAAS;QACT,WAAW;IACb;IACA;QACE,IAAI;QACJ,UAAU;QACV,SAAS;QACT,SAAS;QACT,WAAW;IACb;IACA;QACE,IAAI;QACJ,UAAU;QACV,SAAS;QACT,SAAS;QACT,WAAW;IACb;CACD;AAED,MAAM,2BAA2B;;IAC/B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,yBAAyB;IACzB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8CAAE;YACR,YAAY;QACd;6CAAG,EAAE;IAEL,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,sJAAA,CAAA,UAAY;oBAAC,OAAM;oBAAgC,UAAS;;;;;;8BAE7D,6LAAC;oBAAI,WAAU;8BACZ,eAAe,GAAG,CAAC,CAAC,OAAO,sBAC1B,6LAAC;4BAEC,WAAU;4BACV,OAAO;gCACL,gBAAgB,GAAG,QAAQ,IAAI,EAAE,CAAC;gCAClC,mBAAmB;4BACrB;sCAEA,cAAA,6LAAC,4JAAA,CAAA,UAAgB;gCACf,UAAU,MAAM,QAAQ;gCACxB,SAAS,MAAM,OAAO;gCACtB,SAAS,MAAM,OAAO;gCACtB,WAAW,MAAM,SAAS;gCAC1B,UAAU;;;;;;2BAZP,MAAM,EAAE;;;;;;;;;;8BAkBnB,6LAAC;oBAAI,WAAU;oBAAkE,OAAO;wBAAE,mBAAmB;oBAAW;;sCACtH,6LAAC,+JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;sCACX;;;;;;sCAGD,6LAAC;4BAAE,WAAU;sCAAqB;;;;;;;;;;;;;;;;;;;;;;;AAO5C;GAhDM;KAAA;uCAkDS", "debugId": null}}, {"offset": {"line": 334, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/fwu_incubation%28react%2Blaravel%29/incubation/frontend/src/app/components/programs/UpcomingEventCard.tsx"], "sourcesContent": ["\"use client\"\r\nimport { Calendar, Clock, MapPin, ChevronRight } from 'lucide-react';\r\nimport { useState } from 'react';\r\n\r\nexport interface UpcomingEvent {\r\n  id: string;\r\n  date: string; // e.g., \"Oct 25\"\r\n  fullDate: string; // e.g., \"October 25, 2024\"\r\n  time?: string; // e.g., \"10:00 AM - 04:00 PM\"\r\n  title: string;\r\n  type: string; // e.g., \"Workshop\", \"Deadline\", \"Networking\"\r\n  location?: string; // e.g., \"Online\" or \"FWU Auditorium\"\r\n  description: string;\r\n  color?: string; // Tailwind color class e.g. 'bg-blue-500'\r\n  detailedDescription?: string; // More details for expanded view\r\n  registrationLink?: string; // Link to register for the event\r\n}\r\n\r\ninterface UpcomingEventCardProps {\r\n  event: UpcomingEvent;\r\n}\r\n\r\nconst UpcomingEventCard: React.FC<UpcomingEventCardProps> = ({ event }) => {\r\n  const [isExpanded, setIsExpanded] = useState(false);\r\n  const typeColor = event.color || 'bg-brand-accent';\r\n\r\n  const toggleExpand = () => {\r\n    setIsExpanded(!isExpanded);\r\n  };\r\n\r\n  return (\r\n    <div className=\"bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden flex flex-col group opacity-0 animate-fadeIn hover:shadow-xl transition-shadow duration-300\">\r\n      <div className=\"flex flex-col md:flex-row\">\r\n        {/* Date Column */}\r\n        <div\r\n          className={`p-8 md:w-1/4 flex flex-col items-center justify-center text-white ${typeColor} transition-transform duration-300 relative overflow-hidden`}\r\n        >\r\n          {/* Background pattern - using CSS pattern instead of image */}\r\n          <div className=\"absolute inset-0 opacity-20\"\r\n               style={{\r\n                 backgroundImage: 'radial-gradient(#ffffff 1px, transparent 1px)',\r\n                 backgroundSize: '10px 10px'\r\n               }}>\r\n          </div>\r\n\r\n          <div className=\"relative\">\r\n            <div className=\"text-5xl font-bold mb-1\">{event.date.split(' ')[1]}</div>\r\n            <div className=\"text-lg uppercase font-medium\">{event.date.split(' ')[0]}</div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Content Column */}\r\n        <div className=\"p-8 flex-grow\">\r\n          <div className=\"flex justify-between items-start mb-4\">\r\n            <span className={`inline-block px-4 py-1 text-xs font-semibold text-white ${typeColor} rounded-full`}>\r\n              {event.type}\r\n            </span>\r\n            <button\r\n              onClick={toggleExpand}\r\n              className=\"w-8 h-8 flex items-center justify-center bg-gray-100 hover:bg-gray-200 text-gray-600 rounded-full focus:outline-none transition-all duration-200 hover:scale-110 active:scale-95\"\r\n              aria-label={isExpanded ? \"Collapse details\" : \"Expand details\"}\r\n            >\r\n              <div className={`transform transition-transform duration-300 ${isExpanded ? 'rotate-90' : 'rotate-0'}`}>\r\n                <ChevronRight size={18} />\r\n              </div>\r\n            </button>\r\n          </div>\r\n\r\n          <h3 className=\"text-xl font-bold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors\">\r\n            {event.title}\r\n          </h3>\r\n\r\n          <p className=\"text-gray-600 mb-5 leading-relaxed\">{event.description}</p>\r\n\r\n          <div className=\"flex flex-wrap gap-4 text-sm text-gray-600\">\r\n            <div className=\"flex items-center bg-gray-50 px-3 py-1 rounded-full\">\r\n              <Calendar className=\"mr-2 text-blue-500\" /> {event.fullDate}\r\n            </div>\r\n            {event.time && (\r\n              <div className=\"flex items-center bg-gray-50 px-3 py-1 rounded-full\">\r\n                <Clock className=\"mr-2 text-blue-500\" /> {event.time}\r\n              </div>\r\n            )}\r\n            {event.location && (\r\n              <div className=\"flex items-center bg-gray-50 px-3 py-1 rounded-full\">\r\n                <MapPin className=\"mr-2 text-blue-500\" /> {event.location}\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Expandable Details Section */}\r\n      <div\r\n        className={`px-8 pb-8 pt-0 border-t border-gray-100 mt-2 overflow-hidden transition-all duration-500 ease-in-out ${\r\n          isExpanded ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'\r\n        }`}\r\n      >\r\n        <div className=\"pt-6 text-gray-700 leading-relaxed\">\r\n          <p>{event.detailedDescription || \"More details about this event will be announced soon. Stay tuned for updates!\"}</p>\r\n        </div>\r\n\r\n        {event.registrationLink && (\r\n          <div className=\"mt-6 flex\">\r\n            <a\r\n              href={event.registrationLink}\r\n              className=\"inline-flex items-center bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-6 rounded-lg transition-all duration-300 hover:scale-105 active:scale-95\"\r\n            >\r\n              <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\" />\r\n              </svg>\r\n              Register Now\r\n            </a>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default UpcomingEventCard;"], "names": [], "mappings": ";;;;AACA;AAAA;AAAA;AAAA;AACA;;;AAFA;;;AAsBA,MAAM,oBAAsD,CAAC,EAAE,KAAK,EAAE;;IACpE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,YAAY,MAAM,KAAK,IAAI;IAEjC,MAAM,eAAe;QACnB,cAAc,CAAC;IACjB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBACC,WAAW,CAAC,kEAAkE,EAAE,UAAU,2DAA2D,CAAC;;0CAGtJ,6LAAC;gCAAI,WAAU;gCACV,OAAO;oCACL,iBAAiB;oCACjB,gBAAgB;gCAClB;;;;;;0CAGL,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAA2B,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;;;;;;kDAClE,6LAAC;wCAAI,WAAU;kDAAiC,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;;;;;;;;;;;;;;;;;;kCAK5E,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAW,CAAC,wDAAwD,EAAE,UAAU,aAAa,CAAC;kDACjG,MAAM,IAAI;;;;;;kDAEb,6LAAC;wCACC,SAAS;wCACT,WAAU;wCACV,cAAY,aAAa,qBAAqB;kDAE9C,cAAA,6LAAC;4CAAI,WAAW,CAAC,4CAA4C,EAAE,aAAa,cAAc,YAAY;sDACpG,cAAA,6LAAC,yNAAA,CAAA,eAAY;gDAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;0CAK1B,6LAAC;gCAAG,WAAU;0CACX,MAAM,KAAK;;;;;;0CAGd,6LAAC;gCAAE,WAAU;0CAAsC,MAAM,WAAW;;;;;;0CAEpE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAuB;4CAAE,MAAM,QAAQ;;;;;;;oCAE5D,MAAM,IAAI,kBACT,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAAuB;4CAAE,MAAM,IAAI;;;;;;;oCAGvD,MAAM,QAAQ,kBACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,6MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAuB;4CAAE,MAAM,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;0BAQnE,6LAAC;gBACC,WAAW,CAAC,qGAAqG,EAC/G,aAAa,yBAAyB,qBACtC;;kCAEF,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;sCAAG,MAAM,mBAAmB,IAAI;;;;;;;;;;;oBAGlC,MAAM,gBAAgB,kBACrB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,MAAM,MAAM,gBAAgB;4BAC5B,WAAU;;8CAEV,6LAAC;oCAAI,OAAM;oCAA6B,WAAU;oCAAe,MAAK;oCAAO,SAAQ;oCAAY,QAAO;8CACtG,cAAA,6LAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;gCACjE;;;;;;;;;;;;;;;;;;;;;;;;AAQpB;GAhGM;KAAA;uCAkGS", "debugId": null}}, {"offset": {"line": 623, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/fwu_incubation%28react%2Blaravel%29/incubation/frontend/src/app/components/programs/ProgramCalendarSection%20.tsx"], "sourcesContent": ["\"use client\"\r\nimport UpcomingEventCard, { UpcomingEvent } from './UpcomingEventCard';\r\nimport { Calendar, Filter, Search } from 'lucide-react';\r\nimport { useState } from 'react';\r\n\r\n// Enhanced Dummy Data with more details\r\nconst upcomingEventsData: UpcomingEvent[] = [\r\n  {\r\n    id: 'ue1',\r\n    date: 'NOV 15',\r\n    fullDate: 'November 15, 2024',\r\n    time: '09:00 AM - 05:00 PM',\r\n    title: 'Design Thinking Workshop for Innovators',\r\n    type: 'Workshop',\r\n    location: 'FWU Incubation Hall A',\r\n    description: 'Learn human-centered design principles to create impactful solutions.',\r\n    color: 'bg-purple-500',\r\n    detailedDescription: 'This full-day workshop will introduce participants to the core principles of design thinking and how to apply them to solve complex problems. Led by industry experts, you will learn techniques for empathizing with users, defining problems, ideating solutions, prototyping, and testing. By the end of the workshop, you will have a practical toolkit for approaching innovation challenges with a human-centered mindset.',\r\n    registrationLink: '/register/design-thinking-workshop'\r\n  },\r\n  {\r\n    id: 'ue2',\r\n    date: 'NOV 28',\r\n    fullDate: 'November 28, 2024',\r\n    title: 'Application Deadline: Winter Cohort 2025',\r\n    type: 'Deadline',\r\n    description: 'Submit your startup applications for the upcoming winter incubation program.',\r\n    color: 'bg-red-500',\r\n    detailedDescription: 'The Winter Cohort 2025 is our flagship 12-week incubation program designed for early-stage startups ready to accelerate their growth. Selected startups will receive mentorship, workspace, seed funding opportunities, and access to our network of investors and industry partners. Applications must include your business plan, team information, current traction, and growth strategy.',\r\n    registrationLink: '/apply/winter-cohort-2025'\r\n  },\r\n  {\r\n    id: 'ue3',\r\n    date: 'DEC 05',\r\n    fullDate: 'December 05, 2024',\r\n    time: '02:00 PM - 04:00 PM',\r\n    title: 'Investor Connect: Meet & Greet',\r\n    type: 'Networking',\r\n    location: 'Online (Zoom)',\r\n    description: 'An opportunity for selected startups to interact with potential investors.',\r\n    color: 'bg-teal-500',\r\n    detailedDescription: 'This exclusive virtual networking event brings together promising startups and potential investors in a structured yet casual format. Each startup will have the opportunity to introduce their venture in a brief pitch, followed by breakout rooms for more in-depth conversations with interested investors. This is not a formal pitching event but rather a chance to build relationships that could lead to future investment opportunities.',\r\n    registrationLink: '/register/investor-connect'\r\n  },\r\n  {\r\n    id: 'ue4',\r\n    date: 'DEC 12',\r\n    fullDate: 'December 12-14, 2024',\r\n    title: 'FinTech Hackathon Challenge',\r\n    type: 'Hackathon',\r\n    location: 'FWU Main Auditorium',\r\n    description: 'Develop innovative solutions for the financial technology sector and win prizes.',\r\n    color: 'bg-blue-600',\r\n    detailedDescription: 'Join us for an intensive 48-hour hackathon focused on developing innovative solutions for the financial technology sector. Participants will form teams to tackle real-world challenges provided by our industry partners. Cash prizes totaling $10,000 will be awarded to the top three teams, with the first-place team also receiving incubation support to develop their solution further. All skill levels are welcome, and mentors will be available throughout the event.',\r\n    registrationLink: '/register/fintech-hackathon'\r\n  },\r\n  {\r\n    id: 'ue5',\r\n    date: 'JAN 10',\r\n    fullDate: 'January 10, 2025',\r\n    time: '10:00 AM - 12:00 PM',\r\n    title: 'Funding Strategies for Early-Stage Startups',\r\n    type: 'Workshop',\r\n    location: 'FWU Incubation Center',\r\n    description: 'Learn about different funding options and how to approach investors effectively.',\r\n    color: 'bg-purple-500',\r\n    detailedDescription: 'This workshop will cover various funding strategies available to early-stage startups, including bootstrapping, angel investment, venture capital, grants, and crowdfunding. Our expert speakers will share insights on when to pursue each option, how to prepare your startup for investment, and tactics for successful fundraising. The session will include case studies of successful funding journeys and common pitfalls to avoid.',\r\n    registrationLink: '/register/funding-strategies-workshop'\r\n  },\r\n];\r\n\r\n// Helper to sort events by fullDate (simplistic, assumes \"Month Day, Year\" format)\r\nconst sortEvents = (events: UpcomingEvent[]): UpcomingEvent[] => {\r\n  return events.sort((a, b) => new Date(a.fullDate).getTime() - new Date(b.fullDate).getTime());\r\n};\r\n\r\n// Get unique event types for filtering\r\nconst getUniqueEventTypes = (events: UpcomingEvent[]): string[] => {\r\n  return Array.from(new Set(events.map(event => event.type))).sort();\r\n};\r\n\r\nconst ProgramCalendarSection = () => {\r\n  const [searchTerm, setSearchTerm] = useState('');\r\n  const [selectedType, setSelectedType] = useState('');\r\n\r\n  const eventTypes = getUniqueEventTypes(upcomingEventsData);\r\n\r\n  // Filter events based on search term and selected type\r\n  const filteredEvents = upcomingEventsData\r\n    .filter(event =>\r\n      event.title.toLowerCase().includes(searchTerm.toLowerCase()) ||\r\n      event.description.toLowerCase().includes(searchTerm.toLowerCase())\r\n    )\r\n    .filter(event =>\r\n      selectedType ? event.type === selectedType : true\r\n    );\r\n\r\n  const sortedEvents = sortEvents(filteredEvents);\r\n\r\n  return (\r\n    <section className=\"py-20 md:py-28 bg-white\">\r\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\r\n        {/* Section header with decorative elements */}\r\n        <div className=\"relative mb-16\">\r\n          <div className=\"absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-24 h-1 bg-blue-200 rounded-full\"></div>\r\n          <div className=\"text-center\">\r\n            <p className=\"text-blue-600 font-semibold mb-2\">Stay Informed</p>\r\n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900\">Upcoming Events at FWU Incubation Center</h2>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Search and Filter Controls */}\r\n        <div className=\"mb-12 mt-8 bg-white p-6 rounded-xl shadow-lg border border-gray-100\">\r\n          <div className=\"flex flex-col md:flex-row gap-6\">\r\n            <div className=\"relative flex-grow\">\r\n              <div className=\"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none\">\r\n                <Search className=\"text-blue-500\" />\r\n              </div>\r\n              <input\r\n                type=\"text\"\r\n                placeholder=\"Search events...\"\r\n                value={searchTerm}\r\n                onChange={(e) => setSearchTerm(e.target.value)}\r\n                className=\"block w-full pl-12 pr-4 py-3 border-2 border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-400 focus:border-blue-400 transition-colors\"\r\n              />\r\n            </div>\r\n\r\n            <div className=\"relative w-full md:w-auto\">\r\n              <div className=\"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none\">\r\n                <Filter className=\"text-blue-500\" />\r\n              </div>\r\n              <select\r\n                value={selectedType}\r\n                onChange={(e) => setSelectedType(e.target.value)}\r\n                className=\"block w-full md:w-56 pl-12 pr-10 py-3 border-2 border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-400 focus:border-blue-400 transition-colors appearance-none bg-no-repeat bg-right\"\r\n                style={{ backgroundImage: \"url(\\\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e\\\")\", backgroundSize: \"1.5em 1.5em\", backgroundPosition: \"right 0.75rem center\" }}\r\n              >\r\n                <option value=\"\">All Event Types</option>\r\n                {eventTypes.map(type => (\r\n                  <option key={type} value={type}>{type}</option>\r\n                ))}\r\n              </select>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Events List */}\r\n        {sortedEvents.length > 0 ? (\r\n          <div className=\"space-y-10\">\r\n            {sortedEvents.map((event, index) => (\r\n              <div\r\n                key={event.id}\r\n                className=\"opacity-0 animate-fadeIn\"\r\n                style={{ animationDelay: `${index * 150}ms`, animationFillMode: 'forwards' }}\r\n              >\r\n                <UpcomingEventCard event={event} />\r\n              </div>\r\n            ))}\r\n          </div>\r\n        ) : (\r\n          <div className=\"text-center py-20 bg-white rounded-xl shadow-lg border border-gray-100 opacity-0 animate-fadeIn\">\r\n            <div className=\"w-20 h-20 mx-auto bg-blue-50 rounded-full flex items-center justify-center mb-6\">\r\n              <Calendar className=\"text-blue-500 text-3xl\" />\r\n            </div>\r\n            <h3 className=\"text-2xl font-bold text-gray-900 mb-3\">No Events Found</h3>\r\n            <p className=\"text-gray-600 max-w-md mx-auto\">\r\n              {searchTerm || selectedType\r\n                ? \"No events match your current search criteria. Try adjusting your filters.\"\r\n                : \"No upcoming events scheduled at the moment. Please check back soon!\"}\r\n            </p>\r\n          </div>\r\n        )}\r\n\r\n        {/* Calendar Subscription */}\r\n        <div className=\"mt-20 opacity-0 animate-fadeIn animation-delay-500\" style={{ animationFillMode: 'forwards' }}>\r\n          <div className=\"bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl shadow-lg p-10 text-center\">\r\n            <div className=\"w-16 h-16 mx-auto bg-white rounded-full flex items-center justify-center mb-6 shadow-md\">\r\n              <Calendar className=\"text-blue-600 text-2xl\" />\r\n            </div>\r\n\r\n            <h3 className=\"text-2xl font-bold text-gray-900 mb-3\">Stay Updated with Our Events</h3>\r\n            <p className=\"text-gray-700 max-w-2xl mx-auto mb-8\">\r\n              Subscribe to our calendar to receive automatic updates about upcoming events, workshops,\r\n              and programs at the FWU Incubation Center.\r\n            </p>\r\n\r\n            <a\r\n              href=\"/subscribe-calendar\" // Link to iCal feed or subscription page\r\n              className=\"inline-flex items-center bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-8 rounded-lg shadow-md transition-all duration-300 hover:scale-105 transform\"\r\n            >\r\n              <Calendar className=\"mr-2\" />\r\n              Subscribe to Calendar\r\n            </a>\r\n            <p className=\"text-sm text-gray-600 mt-4\">\r\n              Never miss an event! Add our calendar to your preferred calendar app.\r\n            </p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\nexport default ProgramCalendarSection;"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AAAA;AACA;;;AAHA;;;;AAKA,wCAAwC;AACxC,MAAM,qBAAsC;IAC1C;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,MAAM;QACN,OAAO;QACP,MAAM;QACN,UAAU;QACV,aAAa;QACb,OAAO;QACP,qBAAqB;QACrB,kBAAkB;IACpB;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,OAAO;QACP,MAAM;QACN,aAAa;QACb,OAAO;QACP,qBAAqB;QACrB,kBAAkB;IACpB;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,MAAM;QACN,OAAO;QACP,MAAM;QACN,UAAU;QACV,aAAa;QACb,OAAO;QACP,qBAAqB;QACrB,kBAAkB;IACpB;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,OAAO;QACP,MAAM;QACN,UAAU;QACV,aAAa;QACb,OAAO;QACP,qBAAqB;QACrB,kBAAkB;IACpB;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,MAAM;QACN,OAAO;QACP,MAAM;QACN,UAAU;QACV,aAAa;QACb,OAAO;QACP,qBAAqB;QACrB,kBAAkB;IACpB;CACD;AAED,mFAAmF;AACnF,MAAM,aAAa,CAAC;IAClB,OAAO,OAAO,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,QAAQ,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,QAAQ,EAAE,OAAO;AAC5F;AAEA,uCAAuC;AACvC,MAAM,sBAAsB,CAAC;IAC3B,OAAO,MAAM,IAAI,CAAC,IAAI,IAAI,OAAO,GAAG,CAAC,CAAA,QAAS,MAAM,IAAI,IAAI,IAAI;AAClE;AAEA,MAAM,yBAAyB;;IAC7B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,aAAa,oBAAoB;IAEvC,uDAAuD;IACvD,MAAM,iBAAiB,mBACpB,MAAM,CAAC,CAAA,QACN,MAAM,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACzD,MAAM,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,KAEhE,MAAM,CAAC,CAAA,QACN,eAAe,MAAM,IAAI,KAAK,eAAe;IAGjD,MAAM,eAAe,WAAW;IAEhC,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;8CAAmC;;;;;;8CAChD,6LAAC;oCAAG,WAAU;8CAA+C;;;;;;;;;;;;;;;;;;8BAKjE,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;;;;;;kDAEpB,6LAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wCAC7C,WAAU;;;;;;;;;;;;0CAId,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;;;;;;kDAEpB,6LAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;wCAC/C,WAAU;wCACV,OAAO;4CAAE,iBAAiB;4CAAuO,gBAAgB;4CAAe,oBAAoB;wCAAuB;;0DAE3U,6LAAC;gDAAO,OAAM;0DAAG;;;;;;4CAChB,WAAW,GAAG,CAAC,CAAA,qBACd,6LAAC;oDAAkB,OAAO;8DAAO;mDAApB;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAQtB,aAAa,MAAM,GAAG,kBACrB,6LAAC;oBAAI,WAAU;8BACZ,aAAa,GAAG,CAAC,CAAC,OAAO,sBACxB,6LAAC;4BAEC,WAAU;4BACV,OAAO;gCAAE,gBAAgB,GAAG,QAAQ,IAAI,EAAE,CAAC;gCAAE,mBAAmB;4BAAW;sCAE3E,cAAA,6LAAC,6JAAA,CAAA,UAAiB;gCAAC,OAAO;;;;;;2BAJrB,MAAM,EAAE;;;;;;;;;yCASnB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,6MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;;;;;;sCAEtB,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,6LAAC;4BAAE,WAAU;sCACV,cAAc,eACX,8EACA;;;;;;;;;;;;8BAMV,6LAAC;oBAAI,WAAU;oBAAqD,OAAO;wBAAE,mBAAmB;oBAAW;8BACzG,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,6MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;0CAGtB,6LAAC;gCAAG,WAAU;0CAAwC;;;;;;0CACtD,6LAAC;gCAAE,WAAU;0CAAuC;;;;;;0CAKpD,6LAAC;gCACC,MAAK,sBAAsB,yCAAyC;;gCACpE,WAAU;;kDAEV,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAS;;;;;;;0CAG/B,6LAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQtD;GAxHM;KAAA;uCA0HS", "debugId": null}}, {"offset": {"line": 1035, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/fwu_incubation%28react%2Blaravel%29/incubation/frontend/src/app/components/programs/ProgramApplication.tsx"], "sourcesContent": ["\"use client\"\r\nimport { useState, useEffect } from 'react';\r\n\r\nimport Link from 'next/link';\r\nimport { Calendar, DollarSign, FileText, CheckCircle, AlertCircle, Download, ArrowRight } from 'lucide-react';\r\n\r\ninterface Program {\r\n  applicationTimeline: { stage: string; date: string; description: string }[];\r\n  eligibilityRequirements: string[];\r\n  applicationProcess: string[];\r\n  tuitionFees: string;\r\n  scholarships?: string[];\r\n  nextIntake: string;\r\n}\r\n\r\nconst ProgramApplication = ({ program }: { program: Program }) => {\r\n  const [isVisible, setIsVisible] = useState(false);\r\n\r\n  useEffect(() => {\r\n    setIsVisible(true);\r\n  }, []);\r\n\r\n  return (\r\n    <section className=\"py-12 bg-gray-50\">\r\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\r\n        <div className={`transition-all duration-1000 transform ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>\r\n          <div className=\"max-w-4xl mx-auto\">\r\n            <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">Application Information</h2>\r\n            \r\n            <div className=\"bg-white rounded-xl shadow-md overflow-hidden\">\r\n              {/* Application Timeline */}\r\n              <div className=\"p-6 border-b border-gray-200\">\r\n                <h3 className=\"text-lg font-bold text-gray-900 mb-4 flex items-center\">\r\n                  <Calendar className=\"mr-2 text-blue-600\" />\r\n                  Application Timeline\r\n                </h3>\r\n                \r\n                <div className=\"space-y-6\">\r\n                  {program.applicationTimeline.map((item, index) => (\r\n                    <div \r\n                      key={index} \r\n                      className=\"relative pl-8 pb-6 border-l-2 border-blue-200 last:border-l-0 last:pb-0\"\r\n                      style={{ transitionDelay: `${index * 100}ms` }}\r\n                    >\r\n                      <div className=\"absolute left-[-9px] top-0 w-4 h-4 bg-blue-600 rounded-full\"></div>\r\n                      <div className=\"mb-1 flex items-center\">\r\n                        <h4 className=\"font-medium text-gray-900\">{item.stage}</h4>\r\n                        <span className=\"ml-3 text-sm bg-blue-100 text-blue-700 px-2 py-0.5 rounded\">\r\n                          {item.date}\r\n                        </span>\r\n                      </div>\r\n                      <p className=\"text-gray-600 text-sm\">{item.description}</p>\r\n                    </div>\r\n                  ))}\r\n                </div>\r\n              </div>\r\n              \r\n              {/* Eligibility Requirements */}\r\n              <div className=\"p-6 border-b border-gray-200\">\r\n                <h3 className=\"text-lg font-bold text-gray-900 mb-4 flex items-center\">\r\n                  <CheckCircle className=\"mr-2 text-blue-600\" />\r\n                  Eligibility Requirements\r\n                </h3>\r\n                \r\n                <ul className=\"space-y-3\">\r\n                  {program.eligibilityRequirements.map((requirement, index) => (\r\n                    <li \r\n                      key={index} \r\n                      className=\"flex items-start\"\r\n                      style={{ transitionDelay: `${index * 100}ms` }}\r\n                    >\r\n                      <div className=\"w-5 h-5 bg-green-100 rounded-full flex items-center justify-center text-green-600 mr-3 flex-shrink-0 mt-0.5\">\r\n                        <svg width=\"12\" height=\"12\" viewBox=\"0 0 12 12\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                          <path d=\"M10 3L4.5 8.5L2 6\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\r\n                        </svg>\r\n                      </div>\r\n                      <span className=\"text-gray-700\">{requirement}</span>\r\n                    </li>\r\n                  ))}\r\n                </ul>\r\n              </div>\r\n              \r\n              {/* Application Process */}\r\n              <div className=\"p-6 border-b border-gray-200\">\r\n                <h3 className=\"text-lg font-bold text-gray-900 mb-4 flex items-center\">\r\n                  <FileText className=\"mr-2 text-blue-600\" />\r\n                  Application Process\r\n                </h3>\r\n                \r\n                <ol className=\"space-y-4\">\r\n                  {program.applicationProcess.map((step, index) => (\r\n                    <li \r\n                      key={index} \r\n                      className=\"flex\"\r\n                      style={{ transitionDelay: `${index * 100}ms` }}\r\n                    >\r\n                      <div className=\"w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 mr-3 flex-shrink-0 mt-0.5\">\r\n                        {index + 1}\r\n                      </div>\r\n                      <div>\r\n                        <p className=\"text-gray-700\">{step}</p>\r\n                      </div>\r\n                    </li>\r\n                  ))}\r\n                </ol>\r\n              </div>\r\n              \r\n              {/* Fees and Funding */}\r\n              <div className=\"p-6\">\r\n                <h3 className=\"text-lg font-bold text-gray-900 mb-4 flex items-center\">\r\n                  <DollarSign className=\"mr-2 text-blue-600\" />\r\n                  Fees and Funding\r\n                </h3>\r\n                \r\n                <div className=\"space-y-4\">\r\n                  <div className=\"bg-gray-50 p-4 rounded-lg\">\r\n                    <h4 className=\"font-medium text-gray-900 mb-2\">Tuition Fees</h4>\r\n                    <p className=\"text-gray-700\">{program.tuitionFees}</p>\r\n                  </div>\r\n                  \r\n                  {program.scholarships && program.scholarships.length > 0 && (\r\n                    <div className=\"bg-gray-50 p-4 rounded-lg\">\r\n                      <h4 className=\"font-medium text-gray-900 mb-2\">Scholarships and Financial Aid</h4>\r\n                      <ul className=\"space-y-2\">\r\n                        {program.scholarships.map((scholarship, index) => (\r\n                          <li key={index} className=\"text-gray-700 flex items-start\">\r\n                            <AlertCircle className=\"text-blue-600 mr-2 mt-1 flex-shrink-0\" />\r\n                            <span>{scholarship}</span>\r\n                          </li>\r\n                        ))}\r\n                      </ul>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              </div>\r\n            </div>\r\n            \r\n            {/* Application CTA */}\r\n            <div className=\"mt-8 p-6 bg-blue-600 rounded-xl text-white text-center\">\r\n              <h3 className=\"text-xl font-bold mb-2\">Ready to Apply?</h3>\r\n              <p className=\"mb-6\">\r\n                Applications for the {program.nextIntake} intake are now open. Submit your application today!\r\n              </p>\r\n              <div className=\"flex flex-col sm:flex-row justify-center gap-4\">\r\n                <Link \r\n                  href=\"/submit-application\" \r\n                  className=\"px-6 py-3 bg-white text-blue-600 font-bold rounded-lg hover:bg-blue-50 transition-colors flex items-center justify-center\"\r\n                >\r\n                  Apply Now <ArrowRight className=\"ml-2\" />\r\n                </Link>\r\n                <button \r\n                  className=\"px-6 py-3 bg-blue-700 text-white font-bold rounded-lg hover:bg-blue-800 transition-colors flex items-center justify-center\"\r\n                >\r\n                  Download Application Form <Download className=\"ml-2\" />\r\n                </button>\r\n              </div>\r\n            </div>\r\n            \r\n            {/* Contact Information */}\r\n            <div className=\"mt-8 p-6 bg-gray-100 rounded-xl\">\r\n              <h3 className=\"text-lg font-bold text-gray-900 mb-4\">Have Questions?</h3>\r\n              <p className=\"text-gray-700 mb-4\">\r\n                For more information about the application process or the program, please contact the admissions office:\r\n              </p>\r\n              <div className=\"space-y-2\">\r\n                <p className=\"text-gray-700\">\r\n                  <strong>Email:</strong> <a href=\"mailto:<EMAIL>\" className=\"text-blue-600 hover:underline\"><EMAIL></a>\r\n                </p>\r\n                <p className=\"text-gray-700\">\r\n                  <strong>Phone:</strong> +977-99-521456\r\n                </p>\r\n                <p className=\"text-gray-700\">\r\n                  <strong>Office Hours:</strong> Sunday to Friday, 10:00 AM to 4:00 PM\r\n                </p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\nexport default ProgramApplication;\r\n"], "names": [], "mappings": ";;;;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAJA;;;;AAeA,MAAM,qBAAqB,CAAC,EAAE,OAAO,EAAwB;;IAC3D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,aAAa;QACf;uCAAG,EAAE;IAEL,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAW,CAAC,uCAAuC,EAAE,YAAY,8BAA8B,4BAA4B;0BAC9H,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCAEtD,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC,6MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAuB;;;;;;;sDAI7C,6LAAC;4CAAI,WAAU;sDACZ,QAAQ,mBAAmB,CAAC,GAAG,CAAC,CAAC,MAAM,sBACtC,6LAAC;oDAEC,WAAU;oDACV,OAAO;wDAAE,iBAAiB,GAAG,QAAQ,IAAI,EAAE,CAAC;oDAAC;;sEAE7C,6LAAC;4DAAI,WAAU;;;;;;sEACf,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAG,WAAU;8EAA6B,KAAK,KAAK;;;;;;8EACrD,6LAAC;oEAAK,WAAU;8EACb,KAAK,IAAI;;;;;;;;;;;;sEAGd,6LAAC;4DAAE,WAAU;sEAAyB,KAAK,WAAW;;;;;;;mDAXjD;;;;;;;;;;;;;;;;8CAkBb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC,8NAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;gDAAuB;;;;;;;sDAIhD,6LAAC;4CAAG,WAAU;sDACX,QAAQ,uBAAuB,CAAC,GAAG,CAAC,CAAC,aAAa,sBACjD,6LAAC;oDAEC,WAAU;oDACV,OAAO;wDAAE,iBAAiB,GAAG,QAAQ,IAAI,EAAE,CAAC;oDAAC;;sEAE7C,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAI,OAAM;gEAAK,QAAO;gEAAK,SAAQ;gEAAY,MAAK;gEAAO,OAAM;0EAChE,cAAA,6LAAC;oEAAK,GAAE;oEAAoB,QAAO;oEAAe,aAAY;oEAAI,eAAc;oEAAQ,gBAAe;;;;;;;;;;;;;;;;sEAG3G,6LAAC;4DAAK,WAAU;sEAAiB;;;;;;;mDAT5B;;;;;;;;;;;;;;;;8CAgBb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC,iNAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAuB;;;;;;;sDAI7C,6LAAC;4CAAG,WAAU;sDACX,QAAQ,kBAAkB,CAAC,GAAG,CAAC,CAAC,MAAM,sBACrC,6LAAC;oDAEC,WAAU;oDACV,OAAO;wDAAE,iBAAiB,GAAG,QAAQ,IAAI,EAAE,CAAC;oDAAC;;sEAE7C,6LAAC;4DAAI,WAAU;sEACZ,QAAQ;;;;;;sEAEX,6LAAC;sEACC,cAAA,6LAAC;gEAAE,WAAU;0EAAiB;;;;;;;;;;;;mDAR3B;;;;;;;;;;;;;;;;8CAgBb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC,qNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;gDAAuB;;;;;;;sDAI/C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAAiC;;;;;;sEAC/C,6LAAC;4DAAE,WAAU;sEAAiB,QAAQ,WAAW;;;;;;;;;;;;gDAGlD,QAAQ,YAAY,IAAI,QAAQ,YAAY,CAAC,MAAM,GAAG,mBACrD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAAiC;;;;;;sEAC/C,6LAAC;4DAAG,WAAU;sEACX,QAAQ,YAAY,CAAC,GAAG,CAAC,CAAC,aAAa,sBACtC,6LAAC;oEAAe,WAAU;;sFACxB,6LAAC,uNAAA,CAAA,cAAW;4EAAC,WAAU;;;;;;sFACvB,6LAAC;sFAAM;;;;;;;mEAFA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAavB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAyB;;;;;;8CACvC,6LAAC;oCAAE,WAAU;;wCAAO;wCACI,QAAQ,UAAU;wCAAC;;;;;;;8CAE3C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;;gDACX;8DACW,6LAAC,qNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;sDAElC,6LAAC;4CACC,WAAU;;gDACX;8DAC2B,6LAAC,6MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;sCAMpD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAuC;;;;;;8CACrD,6LAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAGlC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;;8DACX,6LAAC;8DAAO;;;;;;gDAAe;8DAAC,6LAAC;oDAAE,MAAK;oDAA+B,WAAU;8DAAgC;;;;;;;;;;;;sDAE3G,6LAAC;4CAAE,WAAU;;8DACX,6LAAC;8DAAO;;;;;;gDAAe;;;;;;;sDAEzB,6LAAC;4CAAE,WAAU;;8DACX,6LAAC;8DAAO;;;;;;gDAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAShD;GAtKM;KAAA;uCAwKS", "debugId": null}}, {"offset": {"line": 1632, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/fwu_incubation%28react%2Blaravel%29/incubation/frontend/src/app/components/programs/ProgramFaculty.tsx"], "sourcesContent": ["\"use client\"\r\nimport { useState, useEffect } from 'react';\r\nimport { ProgramData } from '@/app/programs/page';\r\nimport Image from 'next/image';\r\nimport { Mail, Link2Icon, ExternalLink } from 'lucide-react';\r\n\r\ninterface ProgramFacultyProps {\r\n  program: ProgramData;\r\n}\r\n\r\nconst ProgramFaculty: React.FC<ProgramFacultyProps> = ({ program }) => {\r\n  const [isVisible, setIsVisible] = useState(false);\r\n\r\n  useEffect(() => {\r\n    setIsVisible(true);\r\n  }, []);\r\n\r\n  if (!program.faculty || program.faculty.length === 0) {\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <section className=\"py-12\">\r\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\r\n        <div className={`transition-all duration-1000 transform ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>\r\n          <div className=\"max-w-4xl mx-auto\">\r\n            <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">Program Faculty</h2>\r\n            \r\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n                {program.faculty.map((faculty: Faculty, index: number) => (\r\n                <div \r\n                  key={faculty.name} \r\n                  className=\"bg-white rounded-xl shadow-md overflow-hidden hover:shadow-lg transition-shadow\"\r\n                  style={{ transitionDelay: `${index * 100}ms` }}\r\n                >\r\n                  <div className=\"flex flex-col sm:flex-row\">\r\n                  {/* Faculty Image */}\r\n                  <div className=\"sm:w-1/3 h-48 sm:h-auto relative\">\r\n                    <Image\r\n                    src={faculty.imageUrl || 'https://images.unsplash.com/photo-1568602471122-7832951cc4c5?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=300&q=80'}\r\n                    alt={faculty.name}\r\n                    fill\r\n                    className=\"object-cover\"\r\n                    />\r\n                  </div>\r\n                  \r\n                  {/* Faculty Info */}\r\n                  <div className=\"sm:w-2/3 p-5\">\r\n                    <h3 className=\"text-lg font-bold text-gray-900 mb-1\">{faculty.name}</h3>\r\n                    <p className=\"text-blue-600 font-medium text-sm mb-3\">{faculty.position}</p>\r\n                    \r\n                    {faculty.specialization && (\r\n                    <div className=\"mb-3\">\r\n                      <p className=\"text-sm font-medium text-gray-500\">Specialization</p>\r\n                      <p className=\"text-gray-700\">{faculty.specialization}</p>\r\n                    </div>\r\n                    )}\r\n                    \r\n                    {faculty.education && (\r\n                    <div className=\"mb-3\">\r\n                      <p className=\"text-sm font-medium text-gray-500\">Education</p>\r\n                      <p className=\"text-gray-700\">{faculty.education}</p>\r\n                    </div>\r\n                    )}\r\n                    \r\n                    <div className=\"flex items-center mt-4 space-x-3\">\r\n                    {faculty.email && (\r\n                      <a \r\n                      href={`mailto:${faculty.email}`} \r\n                      className=\"p-2 bg-gray-100 text-gray-700 rounded-full hover:bg-gray-200 transition-colors\"\r\n                      aria-label={`Email ${faculty.name}`}\r\n                      >\r\n                      <Mail size={16} />\r\n                      </a>\r\n                    )}\r\n                    \r\n                    {faculty.linkedin && (\r\n                      <a \r\n                      href={faculty.linkedin} \r\n                      target=\"_blank\" \r\n                      rel=\"noopener noreferrer\" \r\n                      className=\"p-2 bg-gray-100 text-gray-700 rounded-full hover:bg-gray-200 transition-colors\"\r\n                      aria-label={`LinkedIn profile of ${faculty.name}`}\r\n                      >\r\n                      <Link2Icon size={16} />\r\n                      </a>\r\n                    )}\r\n                    \r\n                    {faculty.profileUrl && (\r\n                      <a \r\n                      href={faculty.profileUrl} \r\n                      target=\"_blank\" \r\n                      rel=\"noopener noreferrer\" \r\n                      className=\"p-2 bg-gray-100 text-gray-700 rounded-full hover:bg-gray-200 transition-colors\"\r\n                      aria-label={`Profile page of ${faculty.name}`}\r\n                      >\r\n                      <ExternalLink size={16} />\r\n                      </a>\r\n                    )}\r\n                    </div>\r\n                  </div>\r\n                  </div>\r\n                </div>\r\n                ))}\r\n            </div>\r\n            \r\n            {/* Department Information */}\r\n            {program.departmentInfo && (\r\n              <div className=\"mt-10 p-6 bg-gray-50 rounded-xl border border-gray-200\">\r\n                <h3 className=\"text-xl font-bold text-gray-900 mb-4\">About the Department</h3>\r\n                <div className=\"prose max-w-none text-gray-700\">\r\n                  <p>{program.departmentInfo}</p>\r\n                </div>\r\n                \r\n                {program.departmentUrl && (\r\n                  <a \r\n                    href={program.departmentUrl} \r\n                    target=\"_blank\" \r\n                    rel=\"noopener noreferrer\" \r\n                    className=\"inline-flex items-center mt-4 text-blue-600 hover:text-blue-800 font-medium transition-colors\"\r\n                  >\r\n                    Visit Department Website <ExternalLink className=\"ml-2\" />\r\n                  </a>\r\n                )}\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\nexport default ProgramFaculty;\r\n"], "names": [], "mappings": ";;;;AACA;AAEA;AACA;AAAA;AAAA;;;AAJA;;;;AAUA,MAAM,iBAAgD,CAAC,EAAE,OAAO,EAAE;;IAChE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,aAAa;QACf;mCAAG,EAAE;IAEL,IAAI,CAAC,QAAQ,OAAO,IAAI,QAAQ,OAAO,CAAC,MAAM,KAAK,GAAG;QACpD,OAAO;IACT;IAEA,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAW,CAAC,uCAAuC,EAAE,YAAY,8BAA8B,4BAA4B;0BAC9H,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCAEtD,6LAAC;4BAAI,WAAU;sCACV,QAAQ,OAAO,CAAC,GAAG,CAAC,CAAC,SAAkB,sBACxC,6LAAC;oCAEC,WAAU;oCACV,OAAO;wCAAE,iBAAiB,GAAG,QAAQ,IAAI,EAAE,CAAC;oCAAC;8CAE7C,cAAA,6LAAC;wCAAI,WAAU;;0DAEf,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;oDACN,KAAK,QAAQ,QAAQ,IAAI;oDACzB,KAAK,QAAQ,IAAI;oDACjB,IAAI;oDACJ,WAAU;;;;;;;;;;;0DAKZ,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAAwC,QAAQ,IAAI;;;;;;kEAClE,6LAAC;wDAAE,WAAU;kEAA0C,QAAQ,QAAQ;;;;;;oDAEtE,QAAQ,cAAc,kBACvB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAE,WAAU;0EAAoC;;;;;;0EACjD,6LAAC;gEAAE,WAAU;0EAAiB,QAAQ,cAAc;;;;;;;;;;;;oDAIrD,QAAQ,SAAS,kBAClB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAE,WAAU;0EAAoC;;;;;;0EACjD,6LAAC;gEAAE,WAAU;0EAAiB,QAAQ,SAAS;;;;;;;;;;;;kEAIjD,6LAAC;wDAAI,WAAU;;4DACd,QAAQ,KAAK,kBACZ,6LAAC;gEACD,MAAM,CAAC,OAAO,EAAE,QAAQ,KAAK,EAAE;gEAC/B,WAAU;gEACV,cAAY,CAAC,MAAM,EAAE,QAAQ,IAAI,EAAE;0EAEnC,cAAA,6LAAC,qMAAA,CAAA,OAAI;oEAAC,MAAM;;;;;;;;;;;4DAIb,QAAQ,QAAQ,kBACf,6LAAC;gEACD,MAAM,QAAQ,QAAQ;gEACtB,QAAO;gEACP,KAAI;gEACJ,WAAU;gEACV,cAAY,CAAC,oBAAoB,EAAE,QAAQ,IAAI,EAAE;0EAEjD,cAAA,6LAAC,+MAAA,CAAA,YAAS;oEAAC,MAAM;;;;;;;;;;;4DAIlB,QAAQ,UAAU,kBACjB,6LAAC;gEACD,MAAM,QAAQ,UAAU;gEACxB,QAAO;gEACP,KAAI;gEACJ,WAAU;gEACV,cAAY,CAAC,gBAAgB,EAAE,QAAQ,IAAI,EAAE;0EAE7C,cAAA,6LAAC,yNAAA,CAAA,eAAY;oEAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;mCAjEnB,QAAQ,IAAI;;;;;;;;;;wBA4EtB,QAAQ,cAAc,kBACrB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAuC;;;;;;8CACrD,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;kDAAG,QAAQ,cAAc;;;;;;;;;;;gCAG3B,QAAQ,aAAa,kBACpB,6LAAC;oCACC,MAAM,QAAQ,aAAa;oCAC3B,QAAO;oCACP,KAAI;oCACJ,WAAU;;wCACX;sDAC0B,6LAAC,yNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUrE;GAzHM;KAAA;uCA2HS", "debugId": null}}, {"offset": {"line": 1941, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/fwu_incubation%28react%2Blaravel%29/incubation/frontend/src/app/components/programs/ProgramCurriculum.tsx"], "sourcesContent": ["\"use client\"\r\nimport { useState, useEffect } from 'react';\r\nimport { ProgramData } from '@/app/programs/page';\r\nimport { ChevronDown, ChevronUp, BookO<PERSON>, Clock, FileText } from 'lucide-react';\r\n\r\ninterface ProgramCurriculumProps {\r\n  program: ProgramData;\r\n}\r\n\r\nconst ProgramCurriculum: React.FC<ProgramCurriculumProps> = ({ program }) => {\r\n  const [isVisible, setIsVisible] = useState(false);\r\n  const [expandedSemesters, setExpandedSemesters] = useState<Record<string, boolean>>({});\r\n\r\n  useEffect(() => {\r\n    setIsVisible(true);\r\n    \r\n    // Initialize with first semester expanded\r\n    if (program.curriculum && program.curriculum.length > 0) {\r\n      setExpandedSemesters({ [program.curriculum[0].semester]: true });\r\n    }\r\n  }, [program.curriculum]);\r\n\r\n  const toggleSemester = (semester: string) => {\r\n    setExpandedSemesters(prev => ({\r\n      ...prev,\r\n      [semester]: !prev[semester]\r\n    }));\r\n  };\r\n\r\n  if (!program.curriculum || program.curriculum.length === 0) {\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <section className=\"py-12 bg-gray-50\">\r\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\r\n        <div className={`transition-all duration-1000 transform ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>\r\n          <div className=\"max-w-4xl mx-auto\">\r\n            <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">Program Curriculum</h2>\r\n            \r\n            <div className=\"bg-white rounded-xl shadow-md overflow-hidden\">\r\n              {program.curriculum.map((semester, semesterIndex) => (\r\n                <div \r\n                  key={semester.semester} \r\n                  className={`border-b border-gray-200 ${semesterIndex === program.curriculum.length - 1 ? 'border-b-0' : ''}`}\r\n                >\r\n                  {/* Semester Header */}\r\n                  <button\r\n                    onClick={() => toggleSemester(semester.semester)}\r\n                    className=\"w-full px-6 py-4 flex items-center justify-between bg-white hover:bg-gray-50 transition-colors\"\r\n                  >\r\n                    <div className=\"flex items-center\">\r\n                      <div className=\"w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 mr-3 flex-shrink-0\">\r\n                        {semesterIndex + 1}\r\n                      </div>\r\n                      <h3 className=\"text-lg font-semibold text-gray-900\">{semester.semester}</h3>\r\n                    </div>\r\n                    <div className=\"flex items-center\">\r\n                      <span className=\"text-sm text-gray-500 mr-4\">{semester.courses.length} Courses</span>\r\n                      {expandedSemesters[semester.semester] ? (\r\n                        <ChevronUp className=\"text-gray-500\" />\r\n                      ) : (\r\n                        <ChevronDown className=\"text-gray-500\" />\r\n                      )}\r\n                    </div>\r\n                  </button>\r\n                  \r\n                  {/* Semester Content */}\r\n                  {expandedSemesters[semester.semester] && (\r\n                    <div className=\"px-6 pb-4\">\r\n                      <div className=\"space-y-4\">\r\n                        {semester.courses.map((course, courseIndex) => (\r\n                          <div \r\n                            key={course.code} \r\n                            className=\"p-4 border border-gray-200 rounded-lg hover:border-blue-300 transition-colors\"\r\n                            style={{ transitionDelay: `${courseIndex * 50}ms` }}\r\n                          >\r\n                            <div className=\"flex flex-col md:flex-row md:items-center md:justify-between mb-2\">\r\n                              <div className=\"flex items-center mb-2 md:mb-0\">\r\n                                <div className=\"w-8 h-8 bg-blue-50 rounded-full flex items-center justify-center text-blue-600 mr-3 flex-shrink-0\">\r\n                                  <BookOpen size={16} />\r\n                                </div>\r\n                                <div>\r\n                                  <h4 className=\"font-medium text-gray-900\">{course.title}</h4>\r\n                                  <p className=\"text-sm text-gray-500\">{course.code}</p>\r\n                                </div>\r\n                              </div>\r\n                              <div className=\"flex items-center gap-4\">\r\n                                <div className=\"flex items-center text-sm text-gray-500\">\r\n                                  <Clock className=\"mr-1.5 text-blue-500\" />\r\n                                  <span>{course.credits} Credits</span>\r\n                                </div>\r\n                                {course.isCore ? (\r\n                                  <span className=\"text-xs font-semibold px-2 py-1 bg-blue-100 text-blue-700 rounded\">\r\n                                    Core\r\n                                  </span>\r\n                                ) : (\r\n                                  <span className=\"text-xs font-semibold px-2 py-1 bg-amber-100 text-amber-700 rounded\">\r\n                                    Elective\r\n                                  </span>\r\n                                )}\r\n                              </div>\r\n                            </div>\r\n                            {course.description && (\r\n                              <div className=\"mt-2 text-sm text-gray-600\">\r\n                                <p>{course.description}</p>\r\n                              </div>\r\n                            )}\r\n                          </div>\r\n                        ))}\r\n                      </div>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              ))}\r\n            </div>\r\n            \r\n            {/* Additional Curriculum Information */}\r\n            {program.additionalCurriculumInfo && (\r\n              <div className=\"mt-8 p-6 bg-blue-50 rounded-xl border border-blue-100\">\r\n                <div className=\"flex items-start\">\r\n                  <FileText className=\"text-blue-600 mt-1 mr-3 flex-shrink-0\" size={20} />\r\n                  <div>\r\n                    <h3 className=\"font-medium text-blue-800 mb-2\">Additional Curriculum Information</h3>\r\n                    <div className=\"text-blue-700 space-y-2\">\r\n                      {program.additionalCurriculumInfo.map((info, index) => (\r\n                        <p key={index}>{info}</p>\r\n                      ))}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\nexport default ProgramCurriculum;\r\n"], "names": [], "mappings": ";;;;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;;;AAHA;;;AASA,MAAM,oBAAsD,CAAC,EAAE,OAAO,EAAE;;IACtE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA2B,CAAC;IAErF,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,aAAa;YAEb,0CAA0C;YAC1C,IAAI,QAAQ,UAAU,IAAI,QAAQ,UAAU,CAAC,MAAM,GAAG,GAAG;gBACvD,qBAAqB;oBAAE,CAAC,QAAQ,UAAU,CAAC,EAAE,CAAC,QAAQ,CAAC,EAAE;gBAAK;YAChE;QACF;sCAAG;QAAC,QAAQ,UAAU;KAAC;IAEvB,MAAM,iBAAiB,CAAC;QACtB,qBAAqB,CAAA,OAAQ,CAAC;gBAC5B,GAAG,IAAI;gBACP,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,SAAS;YAC7B,CAAC;IACH;IAEA,IAAI,CAAC,QAAQ,UAAU,IAAI,QAAQ,UAAU,CAAC,MAAM,KAAK,GAAG;QAC1D,OAAO;IACT;IAEA,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAW,CAAC,uCAAuC,EAAE,YAAY,8BAA8B,4BAA4B;0BAC9H,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCAEtD,6LAAC;4BAAI,WAAU;sCACZ,QAAQ,UAAU,CAAC,GAAG,CAAC,CAAC,UAAU,8BACjC,6LAAC;oCAEC,WAAW,CAAC,yBAAyB,EAAE,kBAAkB,QAAQ,UAAU,CAAC,MAAM,GAAG,IAAI,eAAe,IAAI;;sDAG5G,6LAAC;4CACC,SAAS,IAAM,eAAe,SAAS,QAAQ;4CAC/C,WAAU;;8DAEV,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACZ,gBAAgB;;;;;;sEAEnB,6LAAC;4DAAG,WAAU;sEAAuC,SAAS,QAAQ;;;;;;;;;;;;8DAExE,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;;gEAA8B,SAAS,OAAO,CAAC,MAAM;gEAAC;;;;;;;wDACrE,iBAAiB,CAAC,SAAS,QAAQ,CAAC,iBACnC,6LAAC,mNAAA,CAAA,YAAS;4DAAC,WAAU;;;;;iFAErB,6LAAC,uNAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;;;;;;;;;;;;;wCAM5B,iBAAiB,CAAC,SAAS,QAAQ,CAAC,kBACnC,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;0DACZ,SAAS,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,4BAC7B,6LAAC;wDAEC,WAAU;wDACV,OAAO;4DAAE,iBAAiB,GAAG,cAAc,GAAG,EAAE,CAAC;wDAAC;;0EAElD,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAI,WAAU;0FACb,cAAA,6LAAC,iNAAA,CAAA,WAAQ;oFAAC,MAAM;;;;;;;;;;;0FAElB,6LAAC;;kGACC,6LAAC;wFAAG,WAAU;kGAA6B,OAAO,KAAK;;;;;;kGACvD,6LAAC;wFAAE,WAAU;kGAAyB,OAAO,IAAI;;;;;;;;;;;;;;;;;;kFAGrD,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAI,WAAU;;kGACb,6LAAC,uMAAA,CAAA,QAAK;wFAAC,WAAU;;;;;;kGACjB,6LAAC;;4FAAM,OAAO,OAAO;4FAAC;;;;;;;;;;;;;4EAEvB,OAAO,MAAM,iBACZ,6LAAC;gFAAK,WAAU;0FAAoE;;;;;qGAIpF,6LAAC;gFAAK,WAAU;0FAAsE;;;;;;;;;;;;;;;;;;4DAM3F,OAAO,WAAW,kBACjB,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;8EAAG,OAAO,WAAW;;;;;;;;;;;;uDAhCrB,OAAO,IAAI;;;;;;;;;;;;;;;;mCA9BrB,SAAS,QAAQ;;;;;;;;;;wBA2E3B,QAAQ,wBAAwB,kBAC/B,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,iNAAA,CAAA,WAAQ;wCAAC,WAAU;wCAAwC,MAAM;;;;;;kDAClE,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAiC;;;;;;0DAC/C,6LAAC;gDAAI,WAAU;0DACZ,QAAQ,wBAAwB,CAAC,GAAG,CAAC,CAAC,MAAM,sBAC3C,6LAAC;kEAAe;uDAAR;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYhC;GAjIM;KAAA;uCAmIS", "debugId": null}}, {"offset": {"line": 2319, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/fwu_incubation%28react%2Blaravel%29/incubation/frontend/src/app/components/programs/ProgramDetails.tsx"], "sourcesContent": ["\"use client\"\r\nimport { useState, useEffect } from 'react';\r\nimport { ProgramData } from '@/app/programs/page';\r\nimport Image from 'next/image';\r\nimport { Clock, BookOpen, Calendar, Tag, MapPin, Users, Award } from 'lucide-react';\r\n\r\ninterface ProgramDetailsProps {\r\n  program: ProgramData;\r\n}\r\n\r\nconst ProgramDetails: React.FC<ProgramDetailsProps> = ({ program }) => {\r\n  const [isVisible, setIsVisible] = useState(false);\r\n\r\n  useEffect(() => {\r\n    setIsVisible(true);\r\n  }, []);\r\n\r\n  const getDepartmentStyle = (department: string) => {\r\n    const styles = {\r\n      'Engineering': 'bg-blue-500/10 text-blue-700 border-blue-300',\r\n      'Management': 'bg-amber-500/10 text-amber-700 border-amber-300',\r\n      'Science & Technology': 'bg-green-500/10 text-green-700 border-green-300',\r\n      'Humanities': 'bg-red-500/10 text-red-700 border-red-300',\r\n      'Education': 'bg-purple-500/10 text-purple-700 border-purple-300',\r\n    };\r\n    \r\n    return styles[department as keyof typeof styles] || 'bg-gray-500/10 text-gray-700 border-gray-300';\r\n  };\r\n\r\n  const getLevelStyle = (level: string) => {\r\n    const styles = {\r\n      'Undergraduate': 'bg-teal-500/10 text-teal-700 border-teal-300',\r\n      'Graduate': 'bg-violet-500/10 text-violet-700 border-violet-300',\r\n      'Diploma': 'bg-orange-500/10 text-orange-700 border-orange-300',\r\n      'Certificate': 'bg-cyan-500/10 text-cyan-700 border-cyan-300',\r\n    };\r\n    \r\n    return styles[level as keyof typeof styles] || 'bg-gray-500/10 text-gray-700 border-gray-300';\r\n  };\r\n\r\n  return (\r\n    <section className=\"py-12\">\r\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\r\n        <div className={`transition-all duration-1000 transform ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>\r\n          <div className=\"flex flex-col md:flex-row gap-8\">\r\n            {/* Main Content */}\r\n            <div className=\"md:w-2/3\">\r\n              {/* Program Image */}\r\n              {program.imageUrl && (\r\n                <div className=\"relative h-72 md:h-96 w-full rounded-xl overflow-hidden mb-8\">\r\n                  <Image\r\n                    src={program.imageUrl}\r\n                    alt={program.title}\r\n                    fill\r\n                    className=\"object-cover\"\r\n                    priority\r\n                  />\r\n                  <div className=\"absolute inset-0 bg-gradient-to-t from-black/60 to-transparent\"></div>\r\n                  <div className=\"absolute bottom-0 left-0 right-0 p-6\">\r\n                    <div className=\"flex flex-wrap gap-2 mb-3\">\r\n                      <span className={`text-xs font-semibold px-3 py-1 rounded-full border ${getDepartmentStyle(program.department)}`}>\r\n                        {program.department}\r\n                      </span>\r\n                      <span className={`text-xs font-semibold px-3 py-1 rounded-full border ${getLevelStyle(program.level)}`}>\r\n                        {program.level}\r\n                      </span>\r\n                    </div>\r\n                    <h1 className=\"text-3xl md:text-4xl font-bold text-white\">\r\n                      {program.title}\r\n                    </h1>\r\n                  </div>\r\n                </div>\r\n              )}\r\n\r\n              {/* Program Overview */}\r\n              <div className=\"mb-8\">\r\n                <h2 className=\"text-2xl font-bold text-gray-900 mb-4\">Program Overview</h2>\r\n                <div className=\"prose max-w-none text-gray-700\">\r\n                  <p className=\"mb-4\">{program.description}</p>\r\n                  <p>{program.overview}</p>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Program Highlights */}\r\n              <div className=\"mb-8\">\r\n                <h2 className=\"text-2xl font-bold text-gray-900 mb-4\">Program Highlights</h2>\r\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n                  {program.highlights.map((highlight, index) => (\r\n                    <div \r\n                      key={index} \r\n                      className=\"bg-blue-50 p-4 rounded-lg border border-blue-100 flex\"\r\n                      style={{ transitionDelay: `${(index + 1) * 100}ms` }}\r\n                    >\r\n                      <div className=\"w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 mr-3 flex-shrink-0\">\r\n                        {index + 1}\r\n                      </div>\r\n                      <div>\r\n                        <h3 className=\"font-medium text-gray-900 mb-1\">{highlight.title}</h3>\r\n                        <p className=\"text-sm text-gray-600\">{highlight.description}</p>\r\n                      </div>\r\n                    </div>\r\n                  ))}\r\n                </div>\r\n              </div>\r\n\r\n              {/* Learning Outcomes */}\r\n              <div className=\"mb-8\">\r\n                <h2 className=\"text-2xl font-bold text-gray-900 mb-4\">Learning Outcomes</h2>\r\n                <ul className=\"space-y-3\">\r\n                  {program.learningOutcomes.map((outcome, index) => (\r\n                    <li \r\n                      key={index} \r\n                      className=\"flex items-start\"\r\n                      style={{ transitionDelay: `${(index + 1) * 100}ms` }}\r\n                    >\r\n                      <div className=\"w-6 h-6 bg-green-100 rounded-full flex items-center justify-center text-green-600 mr-3 flex-shrink-0 mt-0.5\">\r\n                        <Check size={14} />\r\n                      </div>\r\n                      <span className=\"text-gray-700\">{outcome}</span>\r\n                    </li>\r\n                  ))}\r\n                </ul>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Sidebar */}\r\n            <div className=\"md:w-1/3\">\r\n              <div className=\"bg-gray-50 rounded-xl p-6 border border-gray-200 sticky top-24\">\r\n                <h3 className=\"text-xl font-bold text-gray-900 mb-4\">Program Information</h3>\r\n                \r\n                <div className=\"space-y-4\">\r\n                  <div className=\"flex items-start\">\r\n                    <Award className=\"text-blue-600 mt-1 mr-3 flex-shrink-0\" />\r\n                    <div>\r\n                      <p className=\"text-sm font-medium text-gray-500\">Degree Awarded</p>\r\n                      <p className=\"text-gray-900\">{program.degreeAwarded}</p>\r\n                    </div>\r\n                  </div>\r\n                  \r\n                  <div className=\"flex items-start\">\r\n                    <Clock className=\"text-blue-600 mt-1 mr-3 flex-shrink-0\" />\r\n                    <div>\r\n                      <p className=\"text-sm font-medium text-gray-500\">Duration</p>\r\n                      <p className=\"text-gray-900\">{program.duration}</p>\r\n                    </div>\r\n                  </div>\r\n                  \r\n                  <div className=\"flex items-start\">\r\n                    <BookOpen className=\"text-blue-600 mt-1 mr-3 flex-shrink-0\" />\r\n                    <div>\r\n                      <p className=\"text-sm font-medium text-gray-500\">Total Credits</p>\r\n                      <p className=\"text-gray-900\">{program.credits} Credits</p>\r\n                    </div>\r\n                  </div>\r\n                  \r\n                  <div className=\"flex items-start\">\r\n                    <Calendar className=\"text-blue-600 mt-1 mr-3 flex-shrink-0\" />\r\n                    <div>\r\n                      <p className=\"text-sm font-medium text-gray-500\">Next Intake</p>\r\n                      <p className=\"text-gray-900\">{program.nextIntake}</p>\r\n                    </div>\r\n                  </div>\r\n                  \r\n                  <div className=\"flex items-start\">\r\n                    <Tag className=\"text-blue-600 mt-1 mr-3 flex-shrink-0\" />\r\n                    <div>\r\n                      <p className=\"text-sm font-medium text-gray-500\">Department</p>\r\n                      <p className=\"text-gray-900\">{program.department}</p>\r\n                    </div>\r\n                  </div>\r\n                  \r\n                  <div className=\"flex items-start\">\r\n                    <MapPin className=\"text-blue-600 mt-1 mr-3 flex-shrink-0\" />\r\n                    <div>\r\n                      <p className=\"text-sm font-medium text-gray-500\">Campus</p>\r\n                      <p className=\"text-gray-900\">{program.campus}</p>\r\n                    </div>\r\n                  </div>\r\n                  \r\n                  <div className=\"flex items-start\">\r\n                    <Users className=\"text-blue-600 mt-1 mr-3 flex-shrink-0\" />\r\n                    <div>\r\n                      <p className=\"text-sm font-medium text-gray-500\">Class Size</p>\r\n                      <p className=\"text-gray-900\">{program.classSize} Students</p>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                \r\n                <div className=\"mt-6 pt-6 border-t border-gray-200\">\r\n                  <button className=\"w-full py-3 px-4 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors\">\r\n                    Apply Now\r\n                  </button>\r\n                  <button className=\"w-full mt-3 py-3 px-4 bg-white text-blue-600 font-medium rounded-lg border border-blue-600 hover:bg-blue-50 transition-colors\">\r\n                    Download Brochure\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\n// FiCheck component for the learning outcomes\r\nconst FiCheck = ({ size }: { size: number }) => (\r\n  <svg \r\n    xmlns=\"http://www.w3.org/2000/svg\" \r\n    width={size} \r\n    height={size} \r\n    viewBox=\"0 0 24 24\" \r\n    fill=\"none\" \r\n    stroke=\"currentColor\" \r\n    strokeWidth=\"2\" \r\n    strokeLinecap=\"round\" \r\n    strokeLinejoin=\"round\"\r\n  >\r\n    <polyline points=\"20 6 9 17 4 12\"></polyline>\r\n  </svg>\r\n);\r\n\r\nexport default ProgramDetails;\r\n"], "names": [], "mappings": ";;;;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAJA;;;;AAUA,MAAM,iBAAgD,CAAC,EAAE,OAAO,EAAE;;IAChE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,aAAa;QACf;mCAAG,EAAE;IAEL,MAAM,qBAAqB,CAAC;QAC1B,MAAM,SAAS;YACb,eAAe;YACf,cAAc;YACd,wBAAwB;YACxB,cAAc;YACd,aAAa;QACf;QAEA,OAAO,MAAM,CAAC,WAAkC,IAAI;IACtD;IAEA,MAAM,gBAAgB,CAAC;QACrB,MAAM,SAAS;YACb,iBAAiB;YACjB,YAAY;YACZ,WAAW;YACX,eAAe;QACjB;QAEA,OAAO,MAAM,CAAC,MAA6B,IAAI;IACjD;IAEA,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAW,CAAC,uCAAuC,EAAE,YAAY,8BAA8B,4BAA4B;0BAC9H,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;gCAEZ,QAAQ,QAAQ,kBACf,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,gIAAA,CAAA,UAAK;4CACJ,KAAK,QAAQ,QAAQ;4CACrB,KAAK,QAAQ,KAAK;4CAClB,IAAI;4CACJ,WAAU;4CACV,QAAQ;;;;;;sDAEV,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAW,CAAC,oDAAoD,EAAE,mBAAmB,QAAQ,UAAU,GAAG;sEAC7G,QAAQ,UAAU;;;;;;sEAErB,6LAAC;4DAAK,WAAW,CAAC,oDAAoD,EAAE,cAAc,QAAQ,KAAK,GAAG;sEACnG,QAAQ,KAAK;;;;;;;;;;;;8DAGlB,6LAAC;oDAAG,WAAU;8DACX,QAAQ,KAAK;;;;;;;;;;;;;;;;;;8CAOtB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAwC;;;;;;sDACtD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAAQ,QAAQ,WAAW;;;;;;8DACxC,6LAAC;8DAAG,QAAQ,QAAQ;;;;;;;;;;;;;;;;;;8CAKxB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAwC;;;;;;sDACtD,6LAAC;4CAAI,WAAU;sDACZ,QAAQ,UAAU,CAAC,GAAG,CAAC,CAAC,WAAW,sBAClC,6LAAC;oDAEC,WAAU;oDACV,OAAO;wDAAE,iBAAiB,GAAG,CAAC,QAAQ,CAAC,IAAI,IAAI,EAAE,CAAC;oDAAC;;sEAEnD,6LAAC;4DAAI,WAAU;sEACZ,QAAQ;;;;;;sEAEX,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;8EAAkC,UAAU,KAAK;;;;;;8EAC/D,6LAAC;oEAAE,WAAU;8EAAyB,UAAU,WAAW;;;;;;;;;;;;;mDATxD;;;;;;;;;;;;;;;;8CAiBb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAwC;;;;;;sDACtD,6LAAC;4CAAG,WAAU;sDACX,QAAQ,gBAAgB,CAAC,GAAG,CAAC,CAAC,SAAS,sBACtC,6LAAC;oDAEC,WAAU;oDACV,OAAO;wDAAE,iBAAiB,GAAG,CAAC,QAAQ,CAAC,IAAI,IAAI,EAAE,CAAC;oDAAC;;sEAEnD,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAM,MAAM;;;;;;;;;;;sEAEf,6LAAC;4DAAK,WAAU;sEAAiB;;;;;;;mDAP5B;;;;;;;;;;;;;;;;;;;;;;sCAef,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAuC;;;;;;kDAErD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,6LAAC;;0EACC,6LAAC;gEAAE,WAAU;0EAAoC;;;;;;0EACjD,6LAAC;gEAAE,WAAU;0EAAiB,QAAQ,aAAa;;;;;;;;;;;;;;;;;;0DAIvD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,6LAAC;;0EACC,6LAAC;gEAAE,WAAU;0EAAoC;;;;;;0EACjD,6LAAC;gEAAE,WAAU;0EAAiB,QAAQ,QAAQ;;;;;;;;;;;;;;;;;;0DAIlD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,iNAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,6LAAC;;0EACC,6LAAC;gEAAE,WAAU;0EAAoC;;;;;;0EACjD,6LAAC;gEAAE,WAAU;;oEAAiB,QAAQ,OAAO;oEAAC;;;;;;;;;;;;;;;;;;;0DAIlD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,6MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,6LAAC;;0EACC,6LAAC;gEAAE,WAAU;0EAAoC;;;;;;0EACjD,6LAAC;gEAAE,WAAU;0EAAiB,QAAQ,UAAU;;;;;;;;;;;;;;;;;;0DAIpD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,mMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;kEACf,6LAAC;;0EACC,6LAAC;gEAAE,WAAU;0EAAoC;;;;;;0EACjD,6LAAC;gEAAE,WAAU;0EAAiB,QAAQ,UAAU;;;;;;;;;;;;;;;;;;0DAIpD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,6MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,6LAAC;;0EACC,6LAAC;gEAAE,WAAU;0EAAoC;;;;;;0EACjD,6LAAC;gEAAE,WAAU;0EAAiB,QAAQ,MAAM;;;;;;;;;;;;;;;;;;0DAIhD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,6LAAC;;0EACC,6LAAC;gEAAE,WAAU;0EAAoC;;;;;;0EACjD,6LAAC;gEAAE,WAAU;;oEAAiB,QAAQ,SAAS;oEAAC;;;;;;;;;;;;;;;;;;;;;;;;;kDAKtD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAO,WAAU;0DAAqG;;;;;;0DAGvH,6LAAC;gDAAO,WAAU;0DAAgI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWpK;GAjMM;KAAA;AAmMN,8CAA8C;AAC9C,MAAM,UAAU,CAAC,EAAE,IAAI,EAAoB,iBACzC,6LAAC;QACC,OAAM;QACN,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,QAAO;QACP,aAAY;QACZ,eAAc;QACd,gBAAe;kBAEf,cAAA,6LAAC;YAAS,QAAO;;;;;;;;;;;MAZf;uCAgBS", "debugId": null}}, {"offset": {"line": 3024, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/fwu_incubation%28react%2Blaravel%29/incubation/frontend/src/app/components/programs/slug/ProgramContent.tsx"], "sourcesContent": ["\"use client\"\r\nimport { useState, useEffect, useRef } from 'react';\r\nimport Link from 'next/link';\r\nimport Image from 'next/image';\r\n\r\nimport {\r\n  ArrowLeft,\r\n  BookOpen,\r\n  Users,\r\n  Calendar,\r\n  FileText,\r\n  Clock,\r\n  MapPin,\r\n  Star,\r\n  Award,\r\n  Target,\r\n  Sparkles,\r\n  ChevronRight,\r\n  Play,\r\n  Download,\r\n  Share2\r\n} from 'lucide-react';\r\nimport ProgramApplication from '../ProgramApplication';\r\nimport ProgramFaculty from '../ProgramFaculty';\r\nimport ProgramCurriculum from '../ProgramCurriculum';\r\nimport ProgramDetails from '../ProgramDetails';\r\nimport { Program } from '../../../../types/program.types';\r\n\r\ninterface ProgramContentProps {\r\n  program: Program | null\r\n}\r\n\r\nconst ProgramContent: React.FC<ProgramContentProps> = ({ program }: ProgramContentProps) => {\r\n  const [activeTab, setActiveTab] = useState('details');\r\n  const [isVisible, setIsVisible] = useState(false);\r\n  const [imageLoaded, setImageLoaded] = useState(false);\r\n  const heroRef = useRef(null);\r\n\r\n  useEffect(() => {\r\n    setIsVisible(true);\r\n  }, []);\r\n\r\n  if (!program) {\r\n    return (\r\n      <main className=\"container mx-auto py-16 px-4 text-center\">\r\n        <div className=\"max-w-lg mx-auto bg-white rounded-xl shadow-lg p-8\">\r\n          <div className=\"text-blue-600 mb-4\">\r\n            <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-16 w-16 mx-auto\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1.5} d=\"M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\r\n            </svg>\r\n          </div>\r\n          <h1 className=\"text-3xl font-bold text-gray-900 mb-4\">Program Not Found</h1>\r\n          <p className=\"text-gray-600 mb-8\">\r\n            The academic program you are looking for does not exist or has been moved to a different location.\r\n          </p>\r\n          <Link \r\n            href=\"/programs\" \r\n            className=\"inline-flex items-center bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors\"\r\n          >\r\n            <ArrowLeft className=\"mr-2\" /> Back to Programs\r\n          </Link>\r\n        </div>\r\n      </main>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <main className=\"bg-white\">\r\n      {/* Enhanced Hero Section */}\r\n      <section\r\n        ref={heroRef}\r\n        className=\"relative min-h-[100vh] flex items-center overflow-hidden bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900\"\r\n      >\r\n        {/* Dynamic Background */}\r\n        <div className=\"absolute inset-0\">\r\n          {/* Hero Background Image */}\r\n          {program.heroImageUrl && (\r\n            <Image\r\n              src={program.heroImageUrl}\r\n              alt={program.title}\r\n              fill\r\n              priority\r\n              className=\"object-cover opacity-30\"\r\n              onLoad={() => setImageLoaded(true)}\r\n            />\r\n          )}\r\n\r\n          {/* Gradient Overlays */}\r\n          <div className=\"absolute inset-0 bg-gradient-to-br from-blue-900/95 via-indigo-900/90 to-purple-900/95\"></div>\r\n          <div className=\"absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent\"></div>\r\n\r\n          {/* Animated Elements */}\r\n          <div className=\"absolute inset-0 overflow-hidden\">\r\n            {/* Floating Particles */}\r\n            {[...Array(30)].map((_, i) => (\r\n              <div\r\n                key={i}\r\n                className={`absolute rounded-full ${\r\n                  i % 4 === 0 ? 'bg-blue-400/30' :\r\n                  i % 4 === 1 ? 'bg-purple-400/30' :\r\n                  i % 4 === 2 ? 'bg-indigo-400/30' : 'bg-pink-400/30'\r\n                }`}\r\n                style={{\r\n                  width: `${Math.random() * 6 + 2}px`,\r\n                  height: `${Math.random() * 6 + 2}px`,\r\n                  top: `${Math.random() * 100}%`,\r\n                  left: `${Math.random() * 100}%`,\r\n                  animation: `float-particle ${\r\n                    8 + Math.random() * 15\r\n                  }s infinite ease-in-out`,\r\n                  animationDelay: `${Math.random() * 8}s`,\r\n                }}\r\n              ></div>\r\n            ))}\r\n\r\n            {/* Glowing Orbs */}\r\n            {[...Array(5)].map((_, i) => (\r\n              <div\r\n                key={`orb-${i}`}\r\n                className={`absolute rounded-full blur-2xl ${\r\n                  i % 3 === 0 ? 'bg-blue-500/15' :\r\n                  i % 3 === 1 ? 'bg-purple-500/15' : 'bg-indigo-500/15'\r\n                }`}\r\n                style={{\r\n                  width: `${Math.random() * 200 + 100}px`,\r\n                  height: `${Math.random() * 200 + 100}px`,\r\n                  top: `${Math.random() * 100}%`,\r\n                  left: `${Math.random() * 100}%`,\r\n                  animation: `pulse-glow ${\r\n                    12 + Math.random() * 8\r\n                  }s infinite ease-in-out`,\r\n                  animationDelay: `${Math.random() * 5}s`,\r\n                }}\r\n              ></div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Main Content */}\r\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 relative z-20\">\r\n          <div className=\"grid lg:grid-cols-2 gap-12 items-center\">\r\n            {/* Left Content */}\r\n            <div className={`transition-all duration-1000 transform ${\r\n              isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'\r\n            }`}>\r\n              {/* Back Navigation */}\r\n              <Link\r\n                href=\"/programs\"\r\n                className=\"inline-flex items-center text-blue-200 hover:text-white mb-8 transition-all duration-300 group\"\r\n              >\r\n                <ArrowLeft className=\"mr-2 group-hover:-translate-x-1 transition-transform duration-300\" />\r\n                Back to Programs\r\n              </Link>\r\n\r\n              {/* Program Badges */}\r\n              <div className=\"mb-6 flex flex-wrap gap-3\">\r\n                <span className=\"px-4 py-2 rounded-full bg-gradient-to-r from-blue-600/80 to-blue-700/80 backdrop-blur-md text-blue-100 border border-blue-400/30 text-sm font-semibold\">\r\n                  {program.category.charAt(0).toUpperCase() + program.category.slice(1)}\r\n                </span>\r\n                <span className=\"px-4 py-2 rounded-full bg-gradient-to-r from-purple-600/80 to-purple-700/80 backdrop-blur-md text-purple-100 border border-purple-400/30 text-sm font-semibold\">\r\n                  {program.level.charAt(0).toUpperCase() + program.level.slice(1).replace('-', ' ')}\r\n                </span>\r\n                <span className=\"px-4 py-2 rounded-full bg-gradient-to-r from-indigo-600/80 to-indigo-700/80 backdrop-blur-md text-indigo-100 border border-indigo-400/30 text-sm font-semibold\">\r\n                  {program.format.charAt(0).toUpperCase() + program.format.slice(1)}\r\n                </span>\r\n              </div>\r\n\r\n              {/* Main Heading */}\r\n              <h1 className=\"text-4xl md:text-5xl lg:text-6xl font-black mb-6 leading-tight\">\r\n                <span className=\"text-transparent bg-clip-text bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 animate-gradient-x\">\r\n                  {program.title}\r\n                </span>\r\n              </h1>\r\n\r\n              {/* Description */}\r\n              <p className=\"text-xl text-blue-100 mb-8 max-w-2xl leading-relaxed\">\r\n                {program.shortDescription}\r\n              </p>\r\n\r\n              {/* Program Details */}\r\n              <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-4 mb-8\">\r\n                <div className=\"flex items-center space-x-3 p-3 bg-white/5 backdrop-blur-sm rounded-xl border border-white/10\">\r\n                  <Clock className=\"text-blue-400\" size={20} />\r\n                  <div>\r\n                    <div className=\"text-white font-medium text-sm\">Duration</div>\r\n                    <div className=\"text-blue-200 text-sm\">{program.duration}</div>\r\n                  </div>\r\n                </div>\r\n                <div className=\"flex items-center space-x-3 p-3 bg-white/5 backdrop-blur-sm rounded-xl border border-white/10\">\r\n                  <Users className=\"text-purple-400\" size={20} />\r\n                  <div>\r\n                    <div className=\"text-white font-medium text-sm\">Capacity</div>\r\n                    <div className=\"text-purple-200 text-sm\">{program.capacity} participants</div>\r\n                  </div>\r\n                </div>\r\n                <div className=\"flex items-center space-x-3 p-3 bg-white/5 backdrop-blur-sm rounded-xl border border-white/10\">\r\n                  <MapPin className=\"text-indigo-400\" size={20} />\r\n                  <div>\r\n                    <div className=\"text-white font-medium text-sm\">Location</div>\r\n                    <div className=\"text-indigo-200 text-sm\">{program.location.venue}</div>\r\n                  </div>\r\n                </div>\r\n                <div className=\"flex items-center space-x-3 p-3 bg-white/5 backdrop-blur-sm rounded-xl border border-white/10\">\r\n                  <Calendar className=\"text-pink-400\" size={20} />\r\n                  <div>\r\n                    <div className=\"text-white font-medium text-sm\">Schedule</div>\r\n                    <div className=\"text-pink-200 text-sm\">{program.schedule}</div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Action Buttons */}\r\n              <div className=\"flex flex-col sm:flex-row gap-4\">\r\n                <Link\r\n                  href=\"/apply\"\r\n                  className=\"group relative px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-bold rounded-2xl transition-all duration-300 hover:shadow-2xl hover:shadow-blue-500/25 hover:-translate-y-1 flex items-center justify-center overflow-hidden\"\r\n                >\r\n                  <div className=\"absolute inset-0 bg-gradient-to-r from-blue-700 to-purple-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\r\n                  <span className=\"relative z-10\">Apply Now</span>\r\n                  <ChevronRight className=\"relative z-10 ml-2 group-hover:translate-x-1 transition-transform duration-300\" />\r\n                </Link>\r\n\r\n                <button className=\"group relative px-8 py-4 bg-white/10 hover:bg-white/20 backdrop-blur-md text-white font-bold rounded-2xl transition-all duration-300 border-2 border-white/30 hover:border-white/50 hover:shadow-2xl hover:shadow-white/10 hover:-translate-y-1 flex items-center justify-center overflow-hidden\">\r\n                  <div className=\"absolute inset-0 bg-white/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\r\n                  <Download className=\"relative z-10 mr-2\" size={20} />\r\n                  <span className=\"relative z-10\">Download Brochure</span>\r\n                </button>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Right Content - Program Highlights */}\r\n            <div className={`transition-all duration-1000 delay-300 transform ${\r\n              isVisible ? 'opacity-100 translate-x-0' : 'opacity-0 translate-x-10'\r\n            }`}>\r\n              <div className=\"relative\">\r\n                {/* Main Card */}\r\n                <div className=\"bg-white/10 backdrop-blur-md rounded-3xl p-8 border border-white/20 shadow-2xl\">\r\n                  <div className=\"flex items-center justify-between mb-6\">\r\n                    <h3 className=\"text-2xl font-bold text-white\">Program Highlights</h3>\r\n                    <Sparkles className=\"text-yellow-400\" size={24} />\r\n                  </div>\r\n\r\n                  {/* Benefits List */}\r\n                  <div className=\"space-y-4\">\r\n                    {program.benefits.slice(0, 4).map((benefit, index) => (\r\n                      <div key={index} className=\"flex items-start space-x-3\">\r\n                        <div className=\"w-6 h-6 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5\">\r\n                          <Star className=\"text-white\" size={12} />\r\n                        </div>\r\n                        <span className=\"text-blue-100 leading-relaxed\">{benefit}</span>\r\n                      </div>\r\n                    ))}\r\n                  </div>\r\n\r\n                  {/* Stats */}\r\n                  {program.stats && (\r\n                    <div className=\"mt-8 pt-6 border-t border-white/20\">\r\n                      <div className=\"grid grid-cols-2 gap-4\">\r\n                        {program.stats.successRate && (\r\n                          <div className=\"text-center\">\r\n                            <div className=\"text-2xl font-bold text-green-400\">{program.stats.successRate}%</div>\r\n                            <div className=\"text-sm text-green-200\">Success Rate</div>\r\n                          </div>\r\n                        )}\r\n                        {program.stats.totalParticipants && (\r\n                          <div className=\"text-center\">\r\n                            <div className=\"text-2xl font-bold text-blue-400\">{program.stats.totalParticipants}+</div>\r\n                            <div className=\"text-sm text-blue-200\">Participants</div>\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n\r\n                {/* Floating Elements */}\r\n                <div className=\"absolute -top-4 -right-4 w-16 h-16 bg-gradient-to-r from-yellow-400 to-orange-400 rounded-2xl flex items-center justify-center shadow-lg\">\r\n                  <Award className=\"text-white\" size={24} />\r\n                </div>\r\n\r\n                <div className=\"absolute -bottom-4 -left-4 w-12 h-12 bg-gradient-to-r from-green-400 to-blue-400 rounded-xl flex items-center justify-center shadow-lg\">\r\n                  <Target className=\"text-white\" size={16} />\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        \r\n        {/* Wave divider */}\r\n        <div className=\"absolute bottom-0 left-0 right-0\">\r\n          <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 1440 120\" className=\"w-full h-auto\">\r\n            <path\r\n              fill=\"#ffffff\"\r\n              fillOpacity=\"1\"\r\n              d=\"M0,64L80,69.3C160,75,320,85,480,80C640,75,800,53,960,48C1120,43,1280,53,1360,58.7L1440,64L1440,120L1360,120C1280,120,1120,120,960,120C800,120,640,120,480,120C320,120,160,120,80,120L0,120Z\"\r\n            ></path>\r\n          </svg>\r\n        </div>\r\n      </section>\r\n      \r\n      {/* Navigation Tabs */}\r\n      <section className=\"bg-white sticky top-20 z-20 shadow-md\">\r\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\r\n          <div className=\"flex overflow-x-auto py-4 scrollbar-hide\">\r\n            <button\r\n              onClick={() => setActiveTab('details')}\r\n              className={`px-5 py-2 rounded-lg mr-2 whitespace-nowrap transition-colors ${\r\n                activeTab === 'details' \r\n                  ? 'bg-blue-600 text-white' \r\n                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'\r\n              }`}\r\n            >\r\n              <BookOpen className=\"inline mr-2 mb-0.5\" />\r\n              Program Details\r\n            </button>\r\n            <button\r\n              onClick={() => setActiveTab('curriculum')}\r\n              className={`px-5 py-2 rounded-lg mr-2 whitespace-nowrap transition-colors ${\r\n                activeTab === 'curriculum' \r\n                  ? 'bg-blue-600 text-white' \r\n                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'\r\n              }`}\r\n            >\r\n              <FileText className=\"inline mr-2 mb-0.5\" />\r\n              Curriculum\r\n            </button>\r\n            <button\r\n              onClick={() => setActiveTab('faculty')}\r\n              className={`px-5 py-2 rounded-lg mr-2 whitespace-nowrap transition-colors ${\r\n                activeTab === 'faculty' \r\n                  ? 'bg-blue-600 text-white' \r\n                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'\r\n              }`}\r\n            >\r\n              <Users className=\"inline mr-2 mb-0.5\" />\r\n              Faculty\r\n            </button>\r\n            <button\r\n              onClick={() => setActiveTab('application')}\r\n              className={`px-5 py-2 rounded-lg whitespace-nowrap transition-colors ${\r\n                activeTab === 'application' \r\n                  ? 'bg-blue-600 text-white' \r\n                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'\r\n              }`}\r\n            >\r\n              <Calendar className=\"inline mr-2 mb-0.5\" />\r\n              Application\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </section>\r\n      \r\n      {/* Content Sections */}\r\n      <div className={`transition-opacity duration-300 ${activeTab === 'details' ? 'opacity-100' : 'opacity-0 hidden'}`}>\r\n        <ProgramDetails program={program} />\r\n      </div>\r\n      \r\n      <div className={`transition-opacity duration-300 ${activeTab === 'curriculum' ? 'opacity-100' : 'opacity-0 hidden'}`}>\r\n        <ProgramCurriculum program={program} />\r\n      </div>\r\n      \r\n      <div className={`transition-opacity duration-300 ${activeTab === 'faculty' ? 'opacity-100' : 'opacity-0 hidden'}`}>\r\n        <ProgramFaculty program={program} />\r\n      </div>\r\n      \r\n      <div className={`transition-opacity duration-300 ${activeTab === 'application' ? 'opacity-100' : 'opacity-0 hidden'}`}>\r\n        <ProgramApplication program={program} />\r\n      </div>\r\n      \r\n      {/* CTA Section */}\r\n      <section className=\"py-16 bg-gradient-to-r from-blue-900 to-indigo-900 text-white\">\r\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\r\n          <div className=\"max-w-3xl mx-auto text-center\">\r\n            <h2 className=\"text-3xl font-bold mb-6\">Ready to Apply?</h2>\r\n            <p className=\"text-xl text-indigo-100 mb-8\">\r\n              {/* Take the next step in your academic journey with Far Western University. Applications for the {program.nextIntake} intake are now open. */}\r\n            </p>\r\n            <div className=\"flex flex-col sm:flex-row justify-center gap-4\">\r\n              <Link \r\n                href=\"/submit-application\" \r\n                className=\"px-6 py-3 bg-white text-blue-900 font-bold rounded-lg hover:bg-blue-50 transition-colors\"\r\n              >\r\n                Apply Now\r\n              </Link>\r\n              <button \r\n                className=\"px-6 py-3 bg-blue-700 text-white font-bold rounded-lg hover:bg-blue-800 transition-colors\"\r\n              >\r\n                Download Brochure\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Wave Divider */}\r\n        <div className=\"absolute bottom-0 left-0 right-0\">\r\n          <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 1440 120\" className=\"w-full h-auto\">\r\n            <defs>\r\n              <linearGradient id=\"waveGradient\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"0%\">\r\n                <stop offset=\"0%\" stopColor=\"#ffffff\" stopOpacity=\"1\"/>\r\n                <stop offset=\"50%\" stopColor=\"#f8fafc\" stopOpacity=\"1\"/>\r\n                <stop offset=\"100%\" stopColor=\"#ffffff\" stopOpacity=\"1\"/>\r\n              </linearGradient>\r\n            </defs>\r\n            <path\r\n              fill=\"url(#waveGradient)\"\r\n              d=\"M0,64L80,69.3C160,75,320,85,480,80C640,75,800,53,960,48C1120,43,1280,53,1360,58.7L1440,64L1440,120L1360,120C1280,120,1120,120,960,120C800,120,640,120,480,120C320,120,160,120,80,120L0,120Z\"\r\n            ></path>\r\n          </svg>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Enhanced Navigation Tabs */}\r\n      <section className=\"bg-white sticky top-0 z-30 shadow-lg border-b border-gray-200\">\r\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\r\n          <div className=\"flex overflow-x-auto py-4 scrollbar-hide\">\r\n            {[\r\n              { id: 'details', label: 'Program Details', icon: BookOpen },\r\n              { id: 'curriculum', label: 'Curriculum', icon: FileText },\r\n              { id: 'faculty', label: 'Faculty', icon: Users },\r\n              { id: 'application', label: 'Application', icon: Calendar }\r\n            ].map((tab) => (\r\n              <button\r\n                key={tab.id}\r\n                onClick={() => setActiveTab(tab.id)}\r\n                className={`flex items-center px-6 py-3 rounded-xl mr-3 whitespace-nowrap transition-all duration-300 font-semibold ${\r\n                  activeTab === tab.id\r\n                    ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-lg transform scale-105'\r\n                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200 hover:scale-105'\r\n                }`}\r\n              >\r\n                <tab.icon className=\"mr-2\" size={18} />\r\n                {tab.label}\r\n              </button>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Content Sections */}\r\n      <div className={`transition-all duration-500 ${activeTab === 'details' ? 'opacity-100' : 'opacity-0 hidden'}`}>\r\n        <ProgramDetails program={program} />\r\n      </div>\r\n\r\n      <div className={`transition-all duration-500 ${activeTab === 'curriculum' ? 'opacity-100' : 'opacity-0 hidden'}`}>\r\n        <ProgramCurriculum program={program} />\r\n      </div>\r\n\r\n      <div className={`transition-all duration-500 ${activeTab === 'faculty' ? 'opacity-100' : 'opacity-0 hidden'}`}>\r\n        <ProgramFaculty program={program} />\r\n      </div>\r\n\r\n      <div className={`transition-all duration-500 ${activeTab === 'application' ? 'opacity-100' : 'opacity-0 hidden'}`}>\r\n        <ProgramApplication program={program} />\r\n      </div>\r\n\r\n      {/* Enhanced CTA Section */}\r\n      <section className=\"py-20 bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900 text-white relative overflow-hidden\">\r\n        {/* Background Elements */}\r\n        <div className=\"absolute inset-0\">\r\n          <div className=\"absolute top-20 right-20 w-64 h-64 bg-blue-500/10 rounded-full blur-3xl\"></div>\r\n          <div className=\"absolute bottom-20 left-20 w-64 h-64 bg-purple-500/10 rounded-full blur-3xl\"></div>\r\n        </div>\r\n\r\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 relative z-10\">\r\n          <div className=\"max-w-4xl mx-auto text-center\">\r\n            <div className=\"inline-block mb-6\">\r\n              <div className=\"flex items-center justify-center space-x-3 bg-white/10 backdrop-blur-md px-6 py-3 rounded-2xl border border-white/20\">\r\n                <Sparkles className=\"text-yellow-400\" size={20} />\r\n                <span className=\"text-white font-semibold\">Ready to Start?</span>\r\n                <Star className=\"text-yellow-400\" size={16} />\r\n              </div>\r\n            </div>\r\n\r\n            <h2 className=\"text-4xl md:text-5xl font-bold mb-6\">\r\n              <span className=\"text-transparent bg-clip-text bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400\">\r\n                Join {program.title}\r\n              </span>\r\n            </h2>\r\n\r\n            <p className=\"text-xl text-blue-100 mb-10 max-w-2xl mx-auto leading-relaxed\">\r\n              Take the next step in your journey. Applications are now open for upcoming cohorts.\r\n              Don't miss this opportunity to transform your future.\r\n            </p>\r\n\r\n            <div className=\"flex flex-col sm:flex-row justify-center gap-6 mb-12\">\r\n              <Link\r\n                href=\"/apply\"\r\n                className=\"group relative px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-bold rounded-2xl transition-all duration-300 hover:shadow-2xl hover:shadow-blue-500/25 hover:-translate-y-1 flex items-center justify-center overflow-hidden\"\r\n              >\r\n                <div className=\"absolute inset-0 bg-gradient-to-r from-blue-700 to-purple-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\r\n                <span className=\"relative z-10\">Apply Now</span>\r\n                <ChevronRight className=\"relative z-10 ml-2 group-hover:translate-x-1 transition-transform duration-300\" />\r\n              </Link>\r\n\r\n              <button className=\"group relative px-8 py-4 bg-white/10 hover:bg-white/20 backdrop-blur-md text-white font-bold rounded-2xl transition-all duration-300 border-2 border-white/30 hover:border-white/50 hover:shadow-2xl hover:shadow-white/10 hover:-translate-y-1 flex items-center justify-center overflow-hidden\">\r\n                <div className=\"absolute inset-0 bg-white/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\r\n                <Share2 className=\"relative z-10 mr-2\" size={20} />\r\n                <span className=\"relative z-10\">Share Program</span>\r\n              </button>\r\n            </div>\r\n\r\n            {/* Next Dates */}\r\n            {program.upcomingDates && program.upcomingDates.length > 0 && (\r\n              <div className=\"bg-white/5 backdrop-blur-sm rounded-2xl p-6 border border-white/10\">\r\n                <h3 className=\"text-xl font-bold mb-4 text-white\">Upcoming Dates</h3>\r\n                <div className=\"grid md:grid-cols-2 gap-4\">\r\n                  {program.upcomingDates.slice(0, 2).map((date, index) => (\r\n                    <div key={index} className=\"bg-white/5 rounded-xl p-4 border border-white/10\">\r\n                      <div className=\"text-blue-300 font-semibold\">{date.date}</div>\r\n                      <div className=\"text-white font-medium\">{date.title}</div>\r\n                      {date.description && (\r\n                        <div className=\"text-blue-200 text-sm mt-1\">{date.description}</div>\r\n                      )}\r\n                    </div>\r\n                  ))}\r\n                </div>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Custom CSS for animations */}\r\n      <style jsx global>{`\r\n        @keyframes float-particle {\r\n          0%, 100% { transform: translate(0, 0) rotate(0deg); }\r\n          25% { transform: translate(10px, -10px) rotate(90deg); }\r\n          50% { transform: translate(-5px, -15px) rotate(180deg); }\r\n          75% { transform: translate(-10px, 5px) rotate(270deg); }\r\n        }\r\n\r\n        @keyframes pulse-glow {\r\n          0%, 100% { opacity: 0.3; transform: scale(1); }\r\n          50% { opacity: 0.6; transform: scale(1.1); }\r\n        }\r\n\r\n        @keyframes gradient-x {\r\n          0%, 100% { background-position: 0% 50%; }\r\n          50% { background-position: 100% 50%; }\r\n        }\r\n\r\n        .animate-gradient-x {\r\n          background-size: 200% 200%;\r\n          animation: gradient-x 3s ease infinite;\r\n        }\r\n\r\n        .scrollbar-hide {\r\n          -ms-overflow-style: none;\r\n          scrollbar-width: none;\r\n        }\r\n\r\n        .scrollbar-hide::-webkit-scrollbar {\r\n          display: none;\r\n        }\r\n      `}</style>\r\n    </main>\r\n  );\r\n};\r\n\r\nexport default ProgramContent;\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAiBA;AACA;AACA;AACA;;;AAzBA;;;;;;;;;;AAgCA,MAAM,iBAAgD,CAAC,EAAE,OAAO,EAAuB;;IACrF,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,aAAa;QACf;mCAAG,EAAE;IAEL,IAAI,CAAC,SAAS;QACZ,qBACE,6LAAC;YAAK,WAAU;sBACd,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,OAAM;4BAA6B,WAAU;4BAAoB,MAAK;4BAAO,SAAQ;4BAAY,QAAO;sCAC3G,cAAA,6LAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAK,GAAE;;;;;;;;;;;;;;;;kCAG3E,6LAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,6LAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAGlC,6LAAC,+JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;;0CAEV,6LAAC,mNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;4BAAS;;;;;;;;;;;;;;;;;;IAKxC;IAEA,qBACE,6LAAC;kDAAe;;0BAEd,6LAAC;gBACC,KAAK;0DACK;;kCAGV,6LAAC;kEAAc;;4BAEZ,QAAQ,YAAY,kBACnB,6LAAC,gIAAA,CAAA,UAAK;gCACJ,KAAK,QAAQ,YAAY;gCACzB,KAAK,QAAQ,KAAK;gCAClB,IAAI;gCACJ,QAAQ;gCACR,WAAU;gCACV,QAAQ,IAAM,eAAe;;;;;;0CAKjC,6LAAC;0EAAc;;;;;;0CACf,6LAAC;0EAAc;;;;;;0CAGf,6LAAC;0EAAc;;oCAEZ;2CAAI,MAAM;qCAAI,CAAC,GAAG,CAAC,CAAC,GAAG,kBACtB,6LAAC;4CAOC,OAAO;gDACL,OAAO,GAAG,KAAK,MAAM,KAAK,IAAI,EAAE,EAAE,CAAC;gDACnC,QAAQ,GAAG,KAAK,MAAM,KAAK,IAAI,EAAE,EAAE,CAAC;gDACpC,KAAK,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;gDAC9B,MAAM,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;gDAC/B,WAAW,CAAC,eAAe,EACzB,IAAI,KAAK,MAAM,KAAK,GACrB,sBAAsB,CAAC;gDACxB,gBAAgB,GAAG,KAAK,MAAM,KAAK,EAAE,CAAC,CAAC;4CACzC;sFAdW,CAAC,sBAAsB,EAChC,IAAI,MAAM,IAAI,mBACd,IAAI,MAAM,IAAI,qBACd,IAAI,MAAM,IAAI,qBAAqB,kBACnC;2CALG;;;;;oCAoBR;2CAAI,MAAM;qCAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC;4CAMC,OAAO;gDACL,OAAO,GAAG,KAAK,MAAM,KAAK,MAAM,IAAI,EAAE,CAAC;gDACvC,QAAQ,GAAG,KAAK,MAAM,KAAK,MAAM,IAAI,EAAE,CAAC;gDACxC,KAAK,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;gDAC9B,MAAM,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;gDAC/B,WAAW,CAAC,WAAW,EACrB,KAAK,KAAK,MAAM,KAAK,EACtB,sBAAsB,CAAC;gDACxB,gBAAgB,GAAG,KAAK,MAAM,KAAK,EAAE,CAAC,CAAC;4CACzC;sFAbW,CAAC,+BAA+B,EACzC,IAAI,MAAM,IAAI,mBACd,IAAI,MAAM,IAAI,qBAAqB,oBACnC;2CAJG,CAAC,IAAI,EAAE,GAAG;;;;;;;;;;;;;;;;;kCAqBvB,6LAAC;kEAAc;kCACb,cAAA,6LAAC;sEAAc;;8CAEb,6LAAC;8EAAe,CAAC,uCAAuC,EACtD,YAAY,8BAA8B,4BAC1C;;sDAEA,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;;8DAEV,6LAAC,mNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;gDAAsE;;;;;;;sDAK7F,6LAAC;sFAAc;;8DACb,6LAAC;8FAAe;8DACb,QAAQ,QAAQ,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK,QAAQ,QAAQ,CAAC,KAAK,CAAC;;;;;;8DAErE,6LAAC;8FAAe;8DACb,QAAQ,KAAK,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK,QAAQ,KAAK,CAAC,KAAK,CAAC,GAAG,OAAO,CAAC,KAAK;;;;;;8DAE/E,6LAAC;8FAAe;8DACb,QAAQ,MAAM,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK,QAAQ,MAAM,CAAC,KAAK,CAAC;;;;;;;;;;;;sDAKnE,6LAAC;sFAAa;sDACZ,cAAA,6LAAC;0FAAe;0DACb,QAAQ,KAAK;;;;;;;;;;;sDAKlB,6LAAC;sFAAY;sDACV,QAAQ,gBAAgB;;;;;;sDAI3B,6LAAC;sFAAc;;8DACb,6LAAC;8FAAc;;sEACb,6LAAC,uMAAA,CAAA,QAAK;4DAAC,WAAU;4DAAgB,MAAM;;;;;;sEACvC,6LAAC;;;8EACC,6LAAC;8GAAc;8EAAiC;;;;;;8EAChD,6LAAC;8GAAc;8EAAyB,QAAQ,QAAQ;;;;;;;;;;;;;;;;;;8DAG5D,6LAAC;8FAAc;;sEACb,6LAAC,uMAAA,CAAA,QAAK;4DAAC,WAAU;4DAAkB,MAAM;;;;;;sEACzC,6LAAC;;;8EACC,6LAAC;8GAAc;8EAAiC;;;;;;8EAChD,6LAAC;8GAAc;;wEAA2B,QAAQ,QAAQ;wEAAC;;;;;;;;;;;;;;;;;;;8DAG/D,6LAAC;8FAAc;;sEACb,6LAAC,6MAAA,CAAA,SAAM;4DAAC,WAAU;4DAAkB,MAAM;;;;;;sEAC1C,6LAAC;;;8EACC,6LAAC;8GAAc;8EAAiC;;;;;;8EAChD,6LAAC;8GAAc;8EAA2B,QAAQ,QAAQ,CAAC,KAAK;;;;;;;;;;;;;;;;;;8DAGpE,6LAAC;8FAAc;;sEACb,6LAAC,6MAAA,CAAA,WAAQ;4DAAC,WAAU;4DAAgB,MAAM;;;;;;sEAC1C,6LAAC;;;8EACC,6LAAC;8GAAc;8EAAiC;;;;;;8EAChD,6LAAC;8GAAc;8EAAyB,QAAQ,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;sDAM9D,6LAAC;sFAAc;;8DACb,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;;sEAEV,6LAAC;sGAAc;;;;;;sEACf,6LAAC;sGAAe;sEAAgB;;;;;;sEAChC,6LAAC,yNAAA,CAAA,eAAY;4DAAC,WAAU;;;;;;;;;;;;8DAG1B,6LAAC;8FAAiB;;sEAChB,6LAAC;sGAAc;;;;;;sEACf,6LAAC,6MAAA,CAAA,WAAQ;4DAAC,WAAU;4DAAqB,MAAM;;;;;;sEAC/C,6LAAC;sGAAe;sEAAgB;;;;;;;;;;;;;;;;;;;;;;;;8CAMtC,6LAAC;8EAAe,CAAC,iDAAiD,EAChE,YAAY,8BAA8B,4BAC1C;8CACA,cAAA,6LAAC;kFAAc;;0DAEb,6LAAC;0FAAc;;kEACb,6LAAC;kGAAc;;0EACb,6LAAC;0GAAa;0EAAgC;;;;;;0EAC9C,6LAAC,6MAAA,CAAA,WAAQ;gEAAC,WAAU;gEAAkB,MAAM;;;;;;;;;;;;kEAI9C,6LAAC;kGAAc;kEACZ,QAAQ,QAAQ,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,SAAS,sBAC1C,6LAAC;0GAA0B;;kFACzB,6LAAC;kHAAc;kFACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;4EAAC,WAAU;4EAAa,MAAM;;;;;;;;;;;kFAErC,6LAAC;kHAAe;kFAAiC;;;;;;;+DAJzC;;;;;;;;;;oDAUb,QAAQ,KAAK,kBACZ,6LAAC;kGAAc;kEACb,cAAA,6LAAC;sGAAc;;gEACZ,QAAQ,KAAK,CAAC,WAAW,kBACxB,6LAAC;8GAAc;;sFACb,6LAAC;sHAAc;;gFAAqC,QAAQ,KAAK,CAAC,WAAW;gFAAC;;;;;;;sFAC9E,6LAAC;sHAAc;sFAAyB;;;;;;;;;;;;gEAG3C,QAAQ,KAAK,CAAC,iBAAiB,kBAC9B,6LAAC;8GAAc;;sFACb,6LAAC;sHAAc;;gFAAoC,QAAQ,KAAK,CAAC,iBAAiB;gFAAC;;;;;;;sFACnF,6LAAC;sHAAc;sFAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DASnD,6LAAC;0FAAc;0DACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;oDAAa,MAAM;;;;;;;;;;;0DAGtC,6LAAC;0FAAc;0DACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;oDAAC,WAAU;oDAAa,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQ/C,6LAAC;kEAAc;kCACb,cAAA,6LAAC;4BAAI,OAAM;4BAA6B,SAAQ;sEAAyB;sCACvE,cAAA,6LAAC;gCACC,MAAK;gCACL,aAAY;gCACZ,GAAE;;;;;;;;;;;;;;;;;;;;;;;0BAOV,6LAAC;0DAAkB;0BACjB,cAAA,6LAAC;8DAAc;8BACb,cAAA,6LAAC;kEAAc;;0CACb,6LAAC;gCACC,SAAS,IAAM,aAAa;0EACjB,CAAC,8DAA8D,EACxE,cAAc,YACV,2BACA,+CACJ;;kDAEF,6LAAC,iNAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAuB;;;;;;;0CAG7C,6LAAC;gCACC,SAAS,IAAM,aAAa;0EACjB,CAAC,8DAA8D,EACxE,cAAc,eACV,2BACA,+CACJ;;kDAEF,6LAAC,iNAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAuB;;;;;;;0CAG7C,6LAAC;gCACC,SAAS,IAAM,aAAa;0EACjB,CAAC,8DAA8D,EACxE,cAAc,YACV,2BACA,+CACJ;;kDAEF,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;oCAAuB;;;;;;;0CAG1C,6LAAC;gCACC,SAAS,IAAM,aAAa;0EACjB,CAAC,yDAAyD,EACnE,cAAc,gBACV,2BACA,+CACJ;;kDAEF,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAuB;;;;;;;;;;;;;;;;;;;;;;;0BAQnD,6LAAC;0DAAe,CAAC,gCAAgC,EAAE,cAAc,YAAY,gBAAgB,oBAAoB;0BAC/G,cAAA,6LAAC,0JAAA,CAAA,UAAc;oBAAC,SAAS;;;;;;;;;;;0BAG3B,6LAAC;0DAAe,CAAC,gCAAgC,EAAE,cAAc,eAAe,gBAAgB,oBAAoB;0BAClH,cAAA,6LAAC,6JAAA,CAAA,UAAiB;oBAAC,SAAS;;;;;;;;;;;0BAG9B,6LAAC;0DAAe,CAAC,gCAAgC,EAAE,cAAc,YAAY,gBAAgB,oBAAoB;0BAC/G,cAAA,6LAAC,0JAAA,CAAA,UAAc;oBAAC,SAAS;;;;;;;;;;;0BAG3B,6LAAC;0DAAe,CAAC,gCAAgC,EAAE,cAAc,gBAAgB,gBAAgB,oBAAoB;0BACnH,cAAA,6LAAC,8JAAA,CAAA,UAAkB;oBAAC,SAAS;;;;;;;;;;;0BAI/B,6LAAC;0DAAkB;;kCACjB,6LAAC;kEAAc;kCACb,cAAA,6LAAC;sEAAc;;8CACb,6LAAC;8EAAa;8CAA0B;;;;;;8CACxC,6LAAC;8EAAY;;;;;;8CAGb,6LAAC;8EAAc;;sDACb,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;sDAGD,6LAAC;sFACW;sDACX;;;;;;;;;;;;;;;;;;;;;;;kCAQP,6LAAC;kEAAc;kCACb,cAAA,6LAAC;4BAAI,OAAM;4BAA6B,SAAQ;sEAAyB;;8CACvE,6LAAC;;8CACC,cAAA,6LAAC;wCAAe,IAAG;wCAAe,IAAG;wCAAK,IAAG;wCAAK,IAAG;wCAAO,IAAG;;;0DAC7D,6LAAC;gDAAK,QAAO;gDAAK,WAAU;gDAAU,aAAY;;;;;;;0DAClD,6LAAC;gDAAK,QAAO;gDAAM,WAAU;gDAAU,aAAY;;;;;;;0DACnD,6LAAC;gDAAK,QAAO;gDAAO,WAAU;gDAAU,aAAY;;;;;;;;;;;;;;;;;;8CAGxD,6LAAC;oCACC,MAAK;oCACL,GAAE;;;;;;;;;;;;;;;;;;;;;;;;0BAOV,6LAAC;0DAAkB;0BACjB,cAAA,6LAAC;8DAAc;8BACb,cAAA,6LAAC;kEAAc;kCACZ;4BACC;gCAAE,IAAI;gCAAW,OAAO;gCAAmB,MAAM,iNAAA,CAAA,WAAQ;4BAAC;4BAC1D;gCAAE,IAAI;gCAAc,OAAO;gCAAc,MAAM,iNAAA,CAAA,WAAQ;4BAAC;4BACxD;gCAAE,IAAI;gCAAW,OAAO;gCAAW,MAAM,uMAAA,CAAA,QAAK;4BAAC;4BAC/C;gCAAE,IAAI;gCAAe,OAAO;gCAAe,MAAM,6MAAA,CAAA,WAAQ;4BAAC;yBAC3D,CAAC,GAAG,CAAC,CAAC,oBACL,6LAAC;gCAEC,SAAS,IAAM,aAAa,IAAI,EAAE;0EACvB,CAAC,wGAAwG,EAClH,cAAc,IAAI,EAAE,GAChB,0FACA,+DACJ;;kDAEF,6LAAC,IAAI,IAAI;wCAAC,WAAU;wCAAO,MAAM;;;;;;oCAChC,IAAI,KAAK;;+BATL,IAAI,EAAE;;;;;;;;;;;;;;;;;;;;0BAiBrB,6LAAC;0DAAe,CAAC,4BAA4B,EAAE,cAAc,YAAY,gBAAgB,oBAAoB;0BAC3G,cAAA,6LAAC,0JAAA,CAAA,UAAc;oBAAC,SAAS;;;;;;;;;;;0BAG3B,6LAAC;0DAAe,CAAC,4BAA4B,EAAE,cAAc,eAAe,gBAAgB,oBAAoB;0BAC9G,cAAA,6LAAC,6JAAA,CAAA,UAAiB;oBAAC,SAAS;;;;;;;;;;;0BAG9B,6LAAC;0DAAe,CAAC,4BAA4B,EAAE,cAAc,YAAY,gBAAgB,oBAAoB;0BAC3G,cAAA,6LAAC,0JAAA,CAAA,UAAc;oBAAC,SAAS;;;;;;;;;;;0BAG3B,6LAAC;0DAAe,CAAC,4BAA4B,EAAE,cAAc,gBAAgB,gBAAgB,oBAAoB;0BAC/G,cAAA,6LAAC,8JAAA,CAAA,UAAkB;oBAAC,SAAS;;;;;;;;;;;0BAI/B,6LAAC;0DAAkB;;kCAEjB,6LAAC;kEAAc;;0CACb,6LAAC;0EAAc;;;;;;0CACf,6LAAC;0EAAc;;;;;;;;;;;;kCAGjB,6LAAC;kEAAc;kCACb,cAAA,6LAAC;sEAAc;;8CACb,6LAAC;8EAAc;8CACb,cAAA,6LAAC;kFAAc;;0DACb,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;gDAAkB,MAAM;;;;;;0DAC5C,6LAAC;0FAAe;0DAA2B;;;;;;0DAC3C,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;gDAAkB,MAAM;;;;;;;;;;;;;;;;;8CAI5C,6LAAC;8EAAa;8CACZ,cAAA,6LAAC;kFAAe;;4CAA0F;4CAClG,QAAQ,KAAK;;;;;;;;;;;;8CAIvB,6LAAC;8EAAY;8CAAgE;;;;;;8CAK7E,6LAAC;8EAAc;;sDACb,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;;8DAEV,6LAAC;8FAAc;;;;;;8DACf,6LAAC;8FAAe;8DAAgB;;;;;;8DAChC,6LAAC,yNAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;;;;;;;sDAG1B,6LAAC;sFAAiB;;8DAChB,6LAAC;8FAAc;;;;;;8DACf,6LAAC,6MAAA,CAAA,SAAM;oDAAC,WAAU;oDAAqB,MAAM;;;;;;8DAC7C,6LAAC;8FAAe;8DAAgB;;;;;;;;;;;;;;;;;;gCAKnC,QAAQ,aAAa,IAAI,QAAQ,aAAa,CAAC,MAAM,GAAG,mBACvD,6LAAC;8EAAc;;sDACb,6LAAC;sFAAa;sDAAoC;;;;;;sDAClD,6LAAC;sFAAc;sDACZ,QAAQ,aAAa,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,MAAM,sBAC5C,6LAAC;8FAA0B;;sEACzB,6LAAC;sGAAc;sEAA+B,KAAK,IAAI;;;;;;sEACvD,6LAAC;sGAAc;sEAA0B,KAAK,KAAK;;;;;;wDAClD,KAAK,WAAW,kBACf,6LAAC;sGAAc;sEAA8B,KAAK,WAAW;;;;;;;mDAJvD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkD9B;GA7gBM;KAAA;uCA+gBS", "debugId": null}}, {"offset": {"line": 4494, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/fwu_incubation%28react%2Blaravel%29/incubation/frontend/src/data/programsData.tsx"], "sourcesContent": ["import { Laptop, Users, Rocket, GraduationCap, Code, Lightbulb, Target, Award } from 'lucide-react';\r\nimport { Program } from '../types/program.types';\r\n\r\nexport const programsData: Program[] = [\r\n  {\r\n    id: 'bootcamp',\r\n    slug: 'intensive-bootcamps',\r\n    icon: <Laptop />,\r\n    title: 'Intensive Bootcamps',\r\n    shortDescription: 'Deep-dive, skill-based training programs designed to rapidly accelerate your knowledge in specific tech and business domains.',\r\n    longDescription: 'Our intensive bootcamps are designed to transform beginners into skilled practitioners in a short period of time. Through hands-on projects, expert mentorship, and a carefully structured curriculum, participants gain practical experience and build a portfolio of work that demonstrates their capabilities.',\r\n    bgColorClass: 'bg-gradient-to-br from-indigo-50 via-white to-purple-50',\r\n    accentColor: 'indigo',\r\n    category: 'bootcamp' as const,\r\n    level: 'all-levels' as const,\r\n    format: 'hybrid' as const,\r\n    duration: '4-12 weeks, depending on program',\r\n    schedule: 'Full-time (Mon-Fri, 9am-5pm) or Part-time options available',\r\n    capacity: 25,\r\n    location: {\r\n      venue: 'FWU Campus',\r\n      address: 'Far Western University Campus',\r\n      city: 'Mahendranagar',\r\n      country: 'Nepal',\r\n      isOnline: true,\r\n      isHybrid: true,\r\n    },\r\n    benefits: [\r\n      'Accelerated learning in a condensed timeframe',\r\n      'Hands-on project-based curriculum',\r\n      'Direct mentorship from industry professionals',\r\n      'Networking opportunities with peers and potential employers',\r\n      'Certificate of completion recognized by industry partners'\r\n    ],\r\n    learningOutcomes: [\r\n      'Master practical skills in chosen technology stack',\r\n      'Build a professional portfolio of projects',\r\n      'Develop problem-solving and critical thinking abilities'\r\n    ],\r\n    eligibilityRequirements: [\r\n      'Basic computer literacy',\r\n      'Commitment to full-time or part-time schedule',\r\n      'Passion for learning and technology'\r\n    ],\r\n    applicationProcess: [\r\n      'Submit application form with background information',\r\n      'Complete technical assessment (if applicable)',\r\n      'Interview with program coordinators',\r\n      'Receive acceptance decision within 2 weeks',\r\n      'Pay program fee to secure your spot'\r\n    ],\r\n    targetAudience: [\r\n      'Career changers seeking new skills',\r\n      'Recent graduates',\r\n      'Working professionals looking to upskill'\r\n    ],\r\n    imageUrl: '/programs/bootcamp-hero.jpg',\r\n    heroImageUrl: 'https://images.unsplash.com/photo-1522202176988-66273c2fd55f?q=80&w=1200&auto=format&fit=crop',\r\n    upcomingDates: [\r\n      {\r\n        date: 'January 15, 2025',\r\n        title: 'Web Development Bootcamp',\r\n        description: 'Learn modern web development with React and Node.js',\r\n        capacity: 25,\r\n        status: 'upcoming' as const\r\n      },\r\n      {\r\n        date: 'March 1, 2025',\r\n        title: 'Data Science Fundamentals',\r\n        description: 'Master data analysis and machine learning basics',\r\n        capacity: 20,\r\n        status: 'upcoming' as const\r\n      },\r\n      {\r\n        date: 'May 10, 2025',\r\n        title: 'Mobile App Development',\r\n        description: 'Build cross-platform mobile applications',\r\n        capacity: 20,\r\n        status: 'upcoming' as const\r\n      }\r\n    ],\r\n    tags: ['technology', 'skills', 'career', 'intensive'],\r\n    featured: true,\r\n    status: 'active' as const,\r\n    createdAt: '2024-01-01T00:00:00Z',\r\n    updatedAt: '2024-11-01T00:00:00Z'\r\n  },\r\n  {\r\n    id: 'hackathon',\r\n    slug: 'innovation-hackathons',\r\n    icon: <Users />,\r\n    title: 'Innovation Hackathons',\r\n    shortDescription: 'High-energy, collaborative events where participants team up to solve real-world challenges and build innovative prototypes within a limited timeframe.',\r\n    longDescription: 'Our hackathons bring together diverse talents to tackle real-world challenges in an intense, collaborative environment. Participants form cross-functional teams and work against the clock to develop innovative solutions, which are then presented to a panel of judges from industry and academia.',\r\n    bgColorClass: 'bg-gradient-to-br from-teal-50 via-white to-cyan-50',\r\n    accentColor: 'teal',\r\n    category: 'hackathon' as const,\r\n    level: 'all-levels' as const,\r\n    format: 'in-person' as const,\r\n    duration: '24-48 hours (weekend events)',\r\n    schedule: 'Quarterly events throughout the year',\r\n    capacity: 100,\r\n    location: {\r\n      venue: 'FWU Innovation Hub',\r\n      address: 'Far Western University Innovation Hub',\r\n      city: 'Mahendranagar',\r\n      country: 'Nepal',\r\n      isOnline: false,\r\n      isHybrid: false,\r\n    },\r\n    benefits: [\r\n      'Develop rapid problem-solving and prototyping skills',\r\n      'Build your network with like-minded innovators',\r\n      'Gain exposure to industry challenges and opportunities',\r\n      'Win prizes and potential funding for your ideas',\r\n      'Receive feedback from industry experts and potential users'\r\n    ],\r\n    learningOutcomes: [\r\n      'Master rapid prototyping techniques',\r\n      'Develop teamwork and collaboration skills',\r\n      'Learn to work under pressure and tight deadlines'\r\n    ],\r\n    eligibilityRequirements: [\r\n      'Open to all skill levels',\r\n      'Team formation encouraged but not required',\r\n      'Commitment to full event participation'\r\n    ],\r\n    applicationProcess: [\r\n      'Register individually or as a team (2-5 members)',\r\n      'Submit your background and areas of expertise',\r\n      'Receive confirmation and pre-event materials',\r\n      'Attend optional pre-hackathon workshops'\r\n    ],\r\n    targetAudience: [\r\n      'Developers and designers',\r\n      'Entrepreneurs and innovators',\r\n      'Students and professionals',\r\n      'Anyone passionate about problem-solving'\r\n    ],\r\n    imageUrl: '/programs/hackathon-hero.jpg',\r\n    heroImageUrl: 'https://images.unsplash.com/photo-1591115765373-5207764f72e4?q=80&w=1200&auto=format&fit=crop',\r\n    upcomingDates: [\r\n      {\r\n        date: 'December 12-14, 2024',\r\n        title: 'FinTech Hackathon Challenge',\r\n        description: 'Build innovative financial technology solutions',\r\n        capacity: 100,\r\n        status: 'upcoming' as const\r\n      },\r\n      {\r\n        date: 'February 20-22, 2025',\r\n        title: 'HealthTech Innovation Weekend',\r\n        description: 'Create healthcare technology solutions',\r\n        capacity: 80,\r\n        status: 'upcoming' as const\r\n      },\r\n      {\r\n        date: 'April 15-17, 2025',\r\n        title: 'Sustainability Solutions Hackathon',\r\n        description: 'Develop sustainable technology solutions',\r\n        capacity: 100,\r\n        status: 'upcoming' as const\r\n      }\r\n    ],\r\n    tags: ['innovation', 'collaboration', 'competition', 'prototyping'],\r\n    featured: true,\r\n    status: 'active' as const,\r\n    createdAt: '2024-01-01T00:00:00Z',\r\n    updatedAt: '2024-11-01T00:00:00Z'\r\n  },\r\n  {\r\n    id: 'demoday',\r\n    slug: 'startup-demo-days',\r\n    icon: <Rocket />,\r\n    title: 'Startup Demo Days',\r\n    shortDescription: 'An exclusive platform for our incubated startups to pitch their ventures to investors, industry leaders, and potential partners.',\r\n    longDescription: 'Demo Days are the culmination of our incubation programs, where startups showcase their progress and pitch to a curated audience of investors, industry partners, and media. These high-visibility events provide startups with the opportunity to secure funding, partnerships, and media coverage.',\r\n    bgColorClass: 'bg-gradient-to-br from-amber-50 via-white to-orange-50',\r\n    accentColor: 'amber',\r\n    category: 'demo-day' as const,\r\n    level: 'advanced' as const,\r\n    format: 'hybrid' as const,\r\n    duration: '1 full day event',\r\n    schedule: 'Bi-annual (Spring and Fall)',\r\n    capacity: 15,\r\n    location: {\r\n      venue: 'FWU Auditorium',\r\n      address: 'Far Western University Main Campus',\r\n      city: 'Mahendranagar',\r\n      country: 'Nepal',\r\n      isOnline: true,\r\n      isHybrid: true,\r\n    },\r\n    benefits: [\r\n      'Pitch to a curated audience of investors and partners',\r\n      'Receive professional pitch coaching and preparation',\r\n      'Network with potential investors and strategic partners',\r\n      'Media exposure and PR opportunities',\r\n      'Post-event introductions to interested stakeholders'\r\n    ],\r\n    learningOutcomes: [\r\n      'Master the art of startup pitching',\r\n      'Develop investor presentation skills',\r\n      'Learn to handle Q&A sessions effectively'\r\n    ],\r\n    eligibilityRequirements: [\r\n      'Must be enrolled in FWU Incubation programs',\r\n      'Startup must meet readiness criteria',\r\n      'Completed pitch preparation workshops'\r\n    ],\r\n    applicationProcess: [\r\n      'Only open to startups in FWU Incubation programs',\r\n      'Selection based on readiness and progress metrics',\r\n      'Mandatory pitch preparation workshops',\r\n      'Final selection by incubation program directors'\r\n    ],\r\n    targetAudience: [\r\n      'Incubated startup founders',\r\n      'Startup teams ready for investment',\r\n      'Entrepreneurs seeking funding'\r\n    ],\r\n    imageUrl: '/programs/demoday-hero.jpg',\r\n    heroImageUrl: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?q=80&w=1200&auto=format&fit=crop',\r\n    upcomingDates: [\r\n      {\r\n        date: 'November 30, 2024',\r\n        title: 'Fall 2024 Demo Day',\r\n        description: 'Showcase your startup to investors and partners',\r\n        capacity: 15,\r\n        status: 'upcoming' as const\r\n      },\r\n      {\r\n        date: 'May 25, 2025',\r\n        title: 'Spring 2025 Demo Day',\r\n        description: 'Present your venture to the investment community',\r\n        capacity: 15,\r\n        status: 'upcoming' as const\r\n      }\r\n    ],\r\n    tags: ['pitching', 'investment', 'networking', 'showcase'],\r\n    featured: true,\r\n    status: 'active' as const,\r\n    createdAt: '2024-01-01T00:00:00Z',\r\n    updatedAt: '2024-11-01T00:00:00Z'\r\n  },\r\n  {\r\n    id: 'workshops',\r\n    slug: 'expert-workshops',\r\n    icon: <GraduationCap />,\r\n    title: 'Expert Workshops',\r\n    shortDescription: 'Focused sessions led by industry experts on crucial topics like marketing, finance, legal aspects, and technology trends.',\r\n    longDescription: 'Our expert workshops provide targeted knowledge and skills development in specific areas critical to startup success. Led by industry practitioners and subject matter experts, these sessions combine theoretical frameworks with practical applications.',\r\n    bgColorClass: 'bg-gradient-to-br from-pink-50 via-white to-rose-50',\r\n    accentColor: 'pink',\r\n    category: 'workshop' as const,\r\n    level: 'all-levels' as const,\r\n    format: 'hybrid' as const,\r\n    duration: '2-4 hours per workshop',\r\n    schedule: 'Monthly workshops on rotating topics',\r\n    capacity: 40,\r\n    location: {\r\n      venue: 'FWU Incubation Center',\r\n      address: 'Far Western University Incubation Center',\r\n      city: 'Mahendranagar',\r\n      country: 'Nepal',\r\n      isOnline: true,\r\n      isHybrid: true,\r\n    },\r\n    benefits: [\r\n      'Learn practical skills directly applicable to your business',\r\n      'Access to industry experts and their networks',\r\n      'Receive personalized feedback on your specific challenges',\r\n      'Connect with peers facing similar challenges',\r\n      'Take home actionable templates and resources'\r\n    ],\r\n    learningOutcomes: [\r\n      'Gain practical business skills',\r\n      'Develop industry-specific knowledge',\r\n      'Build professional networks'\r\n    ],\r\n    eligibilityRequirements: [\r\n      'Open to all entrepreneurs',\r\n      'Some workshops may have prerequisites',\r\n      'Registration required'\r\n    ],\r\n    applicationProcess: [\r\n      'Open to all entrepreneurs and startup team members',\r\n      'Registration required, with priority for FWU incubated startups',\r\n      'Some advanced workshops may have prerequisites'\r\n    ],\r\n    targetAudience: [\r\n      'Entrepreneurs and startup founders',\r\n      'Business professionals',\r\n      'Students interested in entrepreneurship'\r\n    ],\r\n    imageUrl: '/programs/workshop-hero.jpg',\r\n    heroImageUrl: 'https://images.unsplash.com/photo-1515187029135-18ee286d815b?q=80&w=1200&auto=format&fit=crop',\r\n    upcomingDates: [\r\n      {\r\n        date: 'November 15, 2024',\r\n        title: 'Design Thinking Workshop for Innovators',\r\n        description: 'Learn human-centered design principles',\r\n        capacity: 40,\r\n        status: 'upcoming' as const\r\n      },\r\n      {\r\n        date: 'January 10, 2025',\r\n        title: 'Funding Strategies for Early-Stage Startups',\r\n        description: 'Master the art of raising capital',\r\n        capacity: 30,\r\n        status: 'upcoming' as const\r\n      },\r\n      {\r\n        date: 'February 5, 2025',\r\n        title: 'Digital Marketing Essentials',\r\n        description: 'Build effective digital marketing strategies',\r\n        capacity: 40,\r\n        status: 'upcoming' as const\r\n      },\r\n      {\r\n        date: 'March 12, 2025',\r\n        title: 'Legal Fundamentals for Startups',\r\n        description: 'Navigate legal requirements for startups',\r\n        capacity: 35,\r\n        status: 'upcoming' as const\r\n      }\r\n    ],\r\n    tags: ['education', 'skills', 'networking', 'business'],\r\n    featured: false,\r\n    status: 'active' as const,\r\n    createdAt: '2024-01-01T00:00:00Z',\r\n    updatedAt: '2024-11-01T00:00:00Z'\r\n  },\r\n];\r\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAAA;AAAA;;;AAGO,MAAM,eAA0B;IACrC;QACE,IAAI;QACJ,MAAM;QACN,oBAAM,6LAAC,yMAAA,CAAA,SAAM;;;;;QACb,OAAO;QACP,kBAAkB;QAClB,iBAAiB;QACjB,cAAc;QACd,aAAa;QACb,UAAU;QACV,OAAO;QACP,QAAQ;QACR,UAAU;QACV,UAAU;QACV,UAAU;QACV,UAAU;YACR,OAAO;YACP,SAAS;YACT,MAAM;YACN,SAAS;YACT,UAAU;YACV,UAAU;QACZ;QACA,UAAU;YACR;YACA;YACA;YACA;YACA;SACD;QACD,kBAAkB;YAChB;YACA;YACA;SACD;QACD,yBAAyB;YACvB;YACA;YACA;SACD;QACD,oBAAoB;YAClB;YACA;YACA;YACA;YACA;SACD;QACD,gBAAgB;YACd;YACA;YACA;SACD;QACD,UAAU;QACV,cAAc;QACd,eAAe;YACb;gBACE,MAAM;gBACN,OAAO;gBACP,aAAa;gBACb,UAAU;gBACV,QAAQ;YACV;YACA;gBACE,MAAM;gBACN,OAAO;gBACP,aAAa;gBACb,UAAU;gBACV,QAAQ;YACV;YACA;gBACE,MAAM;gBACN,OAAO;gBACP,aAAa;gBACb,UAAU;gBACV,QAAQ;YACV;SACD;QACD,MAAM;YAAC;YAAc;YAAU;YAAU;SAAY;QACrD,UAAU;QACV,QAAQ;QACR,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,oBAAM,6LAAC,uMAAA,CAAA,QAAK;;;;;QACZ,OAAO;QACP,kBAAkB;QAClB,iBAAiB;QACjB,cAAc;QACd,aAAa;QACb,UAAU;QACV,OAAO;QACP,QAAQ;QACR,UAAU;QACV,UAAU;QACV,UAAU;QACV,UAAU;YACR,OAAO;YACP,SAAS;YACT,MAAM;YACN,SAAS;YACT,UAAU;YACV,UAAU;QACZ;QACA,UAAU;YACR;YACA;YACA;YACA;YACA;SACD;QACD,kBAAkB;YAChB;YACA;YACA;SACD;QACD,yBAAyB;YACvB;YACA;YACA;SACD;QACD,oBAAoB;YAClB;YACA;YACA;YACA;SACD;QACD,gBAAgB;YACd;YACA;YACA;YACA;SACD;QACD,UAAU;QACV,cAAc;QACd,eAAe;YACb;gBACE,MAAM;gBACN,OAAO;gBACP,aAAa;gBACb,UAAU;gBACV,QAAQ;YACV;YACA;gBACE,MAAM;gBACN,OAAO;gBACP,aAAa;gBACb,UAAU;gBACV,QAAQ;YACV;YACA;gBACE,MAAM;gBACN,OAAO;gBACP,aAAa;gBACb,UAAU;gBACV,QAAQ;YACV;SACD;QACD,MAAM;YAAC;YAAc;YAAiB;YAAe;SAAc;QACnE,UAAU;QACV,QAAQ;QACR,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,oBAAM,6LAAC,yMAAA,CAAA,SAAM;;;;;QACb,OAAO;QACP,kBAAkB;QAClB,iBAAiB;QACjB,cAAc;QACd,aAAa;QACb,UAAU;QACV,OAAO;QACP,QAAQ;QACR,UAAU;QACV,UAAU;QACV,UAAU;QACV,UAAU;YACR,OAAO;YACP,SAAS;YACT,MAAM;YACN,SAAS;YACT,UAAU;YACV,UAAU;QACZ;QACA,UAAU;YACR;YACA;YACA;YACA;YACA;SACD;QACD,kBAAkB;YAChB;YACA;YACA;SACD;QACD,yBAAyB;YACvB;YACA;YACA;SACD;QACD,oBAAoB;YAClB;YACA;YACA;YACA;SACD;QACD,gBAAgB;YACd;YACA;YACA;SACD;QACD,UAAU;QACV,cAAc;QACd,eAAe;YACb;gBACE,MAAM;gBACN,OAAO;gBACP,aAAa;gBACb,UAAU;gBACV,QAAQ;YACV;YACA;gBACE,MAAM;gBACN,OAAO;gBACP,aAAa;gBACb,UAAU;gBACV,QAAQ;YACV;SACD;QACD,MAAM;YAAC;YAAY;YAAc;YAAc;SAAW;QAC1D,UAAU;QACV,QAAQ;QACR,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,oBAAM,6LAAC,2NAAA,CAAA,gBAAa;;;;;QACpB,OAAO;QACP,kBAAkB;QAClB,iBAAiB;QACjB,cAAc;QACd,aAAa;QACb,UAAU;QACV,OAAO;QACP,QAAQ;QACR,UAAU;QACV,UAAU;QACV,UAAU;QACV,UAAU;YACR,OAAO;YACP,SAAS;YACT,MAAM;YACN,SAAS;YACT,UAAU;YACV,UAAU;QACZ;QACA,UAAU;YACR;YACA;YACA;YACA;YACA;SACD;QACD,kBAAkB;YAChB;YACA;YACA;SACD;QACD,yBAAyB;YACvB;YACA;YACA;SACD;QACD,oBAAoB;YAClB;YACA;YACA;SACD;QACD,gBAAgB;YACd;YACA;YACA;SACD;QACD,UAAU;QACV,cAAc;QACd,eAAe;YACb;gBACE,MAAM;gBACN,OAAO;gBACP,aAAa;gBACb,UAAU;gBACV,QAAQ;YACV;YACA;gBACE,MAAM;gBACN,OAAO;gBACP,aAAa;gBACb,UAAU;gBACV,QAAQ;YACV;YACA;gBACE,MAAM;gBACN,OAAO;gBACP,aAAa;gBACb,UAAU;gBACV,QAAQ;YACV;YACA;gBACE,MAAM;gBACN,OAAO;gBACP,aAAa;gBACb,UAAU;gBACV,QAAQ;YACV;SACD;QACD,MAAM;YAAC;YAAa;YAAU;YAAc;SAAW;QACvD,UAAU;QACV,QAAQ;QACR,WAAW;QACX,WAAW;IACb;CACD", "debugId": null}}, {"offset": {"line": 4880, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/fwu_incubation%28react%2Blaravel%29/incubation/frontend/src/app/programs/page.tsx"], "sourcesContent": ["\"use client\"\r\nimport { useState, useEffect, useRef } from 'react';\r\nimport PastEventsGallerySection from \"../components/programs/PastEventsGallerySection \";\r\nimport ProgramCalendarSection from \"../components/programs/ProgramCalendarSection \";\r\nimport ProgramTypesSection from \"../components/programs/ProgramTypesSection\";\r\nimport {\r\n  ArrowDown,\r\n  Sparkles,\r\n  Target,\r\n  Users,\r\n  Award,\r\n\r\n  Rocket,\r\n  Star,\r\n  ChevronRight,\r\n  Play\r\n} from 'lucide-react';\r\nimport Image from 'next/image';\r\nimport Link from 'next/link';\r\nimport ProgramContent from '../components/programs/slug/ProgramContent';\r\nimport { programsData } from '@/data/programsData';\r\n\r\nexport default function ProgramsPage() {\r\n  const [isVisible, setIsVisible] = useState(false);\r\n  const [activeFeature, setActiveFeature] = useState(0);\r\n  const heroRef = useRef(null);\r\n\r\n  useEffect(() => {\r\n    setIsVisible(true);\r\n\r\n    // Auto-rotate features\r\n    const interval = setInterval(() => {\r\n      setActiveFeature((prev) => (prev + 1) % 4);\r\n    }, 3000);\r\n\r\n    return () => clearInterval(interval);\r\n  }, []);\r\n\r\n  // Function to scroll to the next section smoothly\r\n  const scrollToNextSection = () => {\r\n    const programsSection = document.getElementById('program-types');\r\n    if (programsSection) {\r\n      programsSection.scrollIntoView({ behavior: 'smooth' });\r\n    }\r\n  };\r\n\r\n \r\n  return (\r\n    <>\r\n      {/* Enhanced Hero Section with Modern Design */}\r\n      <section\r\n        ref={heroRef}\r\n        className=\"relative min-h-[100vh] flex items-center overflow-hidden bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900\"\r\n      >\r\n        {/* Dynamic Background Elements */}\r\n        <div className=\"absolute inset-0\">\r\n          {/* Hero Background Image */}\r\n          <Image\r\n            src=\"https://images.unsplash.com/photo-1522202176988-66273c2fd55f?q=80&w=2000&auto=format&fit=crop\"\r\n            alt=\"FWU Incubation Programs\"\r\n            fill\r\n            priority\r\n            className=\"object-cover opacity-20\"\r\n          />\r\n\r\n          {/* Gradient Overlays */}\r\n          <div className=\"absolute inset-0 bg-gradient-to-br from-blue-900/95 via-indigo-900/90 to-purple-900/95\"></div>\r\n          <div className=\"absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent\"></div>\r\n\r\n          {/* Animated Mesh Pattern */}\r\n          <div\r\n            className=\"absolute inset-0 opacity-10\"\r\n            style={{\r\n              backgroundImage: `\r\n                linear-gradient(rgba(59, 130, 246, 0.3) 1px, transparent 1px),\r\n                linear-gradient(90deg, rgba(59, 130, 246, 0.3) 1px, transparent 1px)\r\n              `,\r\n              backgroundSize: '60px 60px',\r\n              animation: 'mesh-drift 25s linear infinite'\r\n            }}\r\n          ></div>\r\n        </div>\r\n\r\n        {/* Floating Elements */}\r\n        <div className=\"absolute inset-0 overflow-hidden\">\r\n          {/* Animated Particles */}\r\n          {[...Array(40)].map((_, i) => (\r\n            <div\r\n              key={i}\r\n              className={`absolute rounded-full ${\r\n                i % 5 === 0 ? 'bg-blue-400/30' :\r\n                i % 5 === 1 ? 'bg-purple-400/30' :\r\n                i % 5 === 2 ? 'bg-indigo-400/30' :\r\n                i % 5 === 3 ? 'bg-pink-400/30' : 'bg-cyan-400/30'\r\n              }`}\r\n              style={{\r\n                width: `${Math.random() * 8 + 3}px`,\r\n                height: `${Math.random() * 8 + 3}px`,\r\n                top: `${Math.random() * 100}%`,\r\n                left: `${Math.random() * 100}%`,\r\n                animation: `float-drift ${\r\n                  10 + Math.random() * 20\r\n                }s infinite ease-in-out`,\r\n                animationDelay: `${Math.random() * 10}s`,\r\n              }}\r\n            ></div>\r\n          ))}\r\n\r\n          {/* Glowing Orbs */}\r\n          {[...Array(6)].map((_, i) => (\r\n            <div\r\n              key={`orb-${i}`}\r\n              className={`absolute rounded-full blur-2xl ${\r\n                i % 3 === 0 ? 'bg-blue-500/15' :\r\n                i % 3 === 1 ? 'bg-purple-500/15' : 'bg-indigo-500/15'\r\n              }`}\r\n              style={{\r\n                width: `${Math.random() * 300 + 150}px`,\r\n                height: `${Math.random() * 300 + 150}px`,\r\n                top: `${Math.random() * 100}%`,\r\n                left: `${Math.random() * 100}%`,\r\n                animation: `pulse-glow ${\r\n                  15 + Math.random() * 10\r\n                }s infinite ease-in-out`,\r\n                animationDelay: `${Math.random() * 8}s`,\r\n              }}\r\n            ></div>\r\n          ))}\r\n        </div>\r\n\r\n        {/* Geometric Decorations */}\r\n        <div className=\"absolute inset-0\">\r\n          <div className=\"absolute top-32 right-32 w-40 h-40 border-2 border-white/10 rounded-full animate-spin-slow\"></div>\r\n          <div className=\"absolute bottom-40 left-32 w-32 h-32 border border-purple-400/20 rotate-45 animate-pulse\"></div>\r\n          <div className=\"absolute top-1/2 right-1/4 w-20 h-20 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-xl rotate-12 animate-float\"></div>\r\n        </div>\r\n\r\n        {/* Main Content */}\r\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 relative z-20\">\r\n          <div className=\"grid lg:grid-cols-2 gap-12 items-center\">\r\n            {/* Left Content */}\r\n            <div className={`text-center lg:text-left transition-all duration-1000 transform ${\r\n              isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'\r\n            }`}>\r\n              {/* Enhanced Badge */}\r\n              <div className=\"inline-block mb-8\">\r\n                <div className=\"flex items-center justify-center lg:justify-start space-x-3 bg-gradient-to-r from-white/20 to-white/10 backdrop-blur-md px-6 py-3 rounded-2xl border border-white/30 shadow-2xl\">\r\n                  <Sparkles className=\"text-yellow-300\" size={20} />\r\n                  <span className=\"text-white font-semibold text-sm tracking-wider uppercase\">\r\n                    Innovation Hub\r\n                  </span>\r\n                  <Star className=\"text-yellow-300\" size={16} />\r\n                </div>\r\n              </div>\r\n\r\n              {/* Main Heading */}\r\n              <h1 className=\"text-5xl sm:text-6xl md:text-7xl lg:text-8xl font-black mb-8 leading-tight\">\r\n                <span className=\"block text-white mb-2\">FWU</span>\r\n                <span className=\"block text-transparent bg-clip-text bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 animate-gradient-x\">\r\n                  Incubation\r\n                </span>\r\n                <span className=\"block text-transparent bg-clip-text bg-gradient-to-r from-purple-400 via-pink-400 to-blue-400 animate-gradient-x-reverse\">\r\n                  Programs\r\n                </span>\r\n              </h1>\r\n\r\n              {/* Description */}\r\n              <p className=\"text-xl sm:text-2xl text-blue-100 max-w-2xl mx-auto lg:mx-0 mb-6 leading-relaxed\">\r\n                Empowering <span className=\"font-semibold text-white\">innovation</span> and\r\n                <span className=\"text-purple-300 font-semibold\"> entrepreneurship</span> at Far Western University\r\n              </p>\r\n\r\n              <p className=\"text-lg text-blue-200 max-w-2xl mx-auto lg:mx-0 mb-10 leading-relaxed\">\r\n                Discover our range of specialized programs designed to support entrepreneurs at every stage of their journey,\r\n                from ideation to market launch. Join our vibrant community of innovators and change-makers.\r\n              </p>\r\n\r\n              {/* Action Buttons */}\r\n              <div className=\"flex flex-col sm:flex-row items-center justify-center lg:justify-start gap-6 mb-12\">\r\n                <button\r\n                  onClick={scrollToNextSection}\r\n                  className=\"group relative px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-bold rounded-2xl transition-all duration-300 hover:shadow-2xl hover:shadow-blue-500/25 hover:-translate-y-1 flex items-center overflow-hidden\"\r\n                >\r\n                  <div className=\"absolute inset-0 bg-gradient-to-r from-blue-700 to-purple-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\r\n                  <span className=\"relative z-10\">Explore Programs</span>\r\n                  <ArrowDown className=\"relative z-10 ml-2 group-hover:translate-y-1 transition-transform duration-300\" />\r\n                </button>\r\n\r\n                <Link\r\n                  href=\"/apply\"\r\n                  className=\"group relative px-8 py-4 bg-white/10 hover:bg-white/20 backdrop-blur-md text-white font-bold rounded-2xl transition-all duration-300 border-2 border-white/30 hover:border-white/50 hover:shadow-2xl hover:shadow-white/10 hover:-translate-y-1 flex items-center overflow-hidden\"\r\n                >\r\n                  <div className=\"absolute inset-0 bg-white/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\r\n                  <span className=\"relative z-10\">Apply Now</span>\r\n                  <ChevronRight className=\"relative z-10 ml-2 group-hover:translate-x-1 transition-transform duration-300\" />\r\n                </Link>\r\n              </div>\r\n\r\n              {/* Feature Highlights */}\r\n              <div className=\"grid grid-cols-2 gap-4 max-w-md mx-auto lg:mx-0\">\r\n                {[\r\n                  { icon: Target, text: \"Expert Mentorship\", color: \"text-blue-400\" },\r\n                  { icon: Users, text: \"Vibrant Community\", color: \"text-purple-400\" },\r\n                  { icon: Rocket, text: \"Launch Support\", color: \"text-pink-400\" },\r\n                  { icon: Award, text: \"Industry Recognition\", color: \"text-yellow-400\" }\r\n                ].map((item, index) => (\r\n                  <div\r\n                    key={index}\r\n                    className={`flex items-center space-x-3 p-3 bg-white/5 backdrop-blur-sm rounded-xl border border-white/10 hover:bg-white/10 transition-all duration-300 group ${\r\n                      activeFeature === index ? 'bg-white/15 border-white/30' : ''\r\n                    }`}\r\n                  >\r\n                    <item.icon className={`${item.color} group-hover:scale-110 transition-transform duration-300`} size={20} />\r\n                    <span className=\"text-white font-medium text-sm\">{item.text}</span>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            </div>\r\n\r\n            {/* Right Content - Video/Image */}\r\n            <div className={`relative transition-all duration-1000 delay-300 transform ${\r\n              isVisible ? 'opacity-100 translate-x-0' : 'opacity-0 translate-x-10'\r\n            }`}>\r\n              <div className=\"relative rounded-3xl overflow-hidden shadow-2xl group\">\r\n                {/* Main Image */}\r\n                <div className=\"relative h-[500px] lg:h-[600px] w-full\">\r\n                  <Image\r\n                    src=\"https://images.unsplash.com/photo-1521737604893-d14cc237f11d?q=80&w=1000&auto=format&fit=crop\"\r\n                    alt=\"FWU Incubation Programs\"\r\n                    fill\r\n                    className=\"object-cover transform group-hover:scale-105 transition-transform duration-700\"\r\n                  />\r\n                  <div className=\"absolute inset-0 bg-gradient-to-t from-indigo-900/70 via-transparent to-transparent\"></div>\r\n\r\n                  {/* Play Button Overlay */}\r\n                  <div className=\"absolute inset-0 flex items-center justify-center\">\r\n                    <button className=\"w-20 h-20 bg-white/20 backdrop-blur-md rounded-full flex items-center justify-center border-2 border-white/30 hover:bg-white/30 transition-all duration-300 group-hover:scale-110\">\r\n                      <Play className=\"text-white ml-1\" size={32} />\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Floating Stats Cards */}\r\n                <div className=\"absolute -bottom-6 -left-6 bg-white/90 backdrop-blur-md p-4 rounded-2xl shadow-xl border border-white/50\">\r\n                  <div className=\"text-2xl font-bold text-indigo-600 mb-1\">20+</div>\r\n                  <div className=\"text-sm text-gray-600\">Active Programs</div>\r\n                </div>\r\n\r\n                <div className=\"absolute -top-6 -right-6 bg-white/90 backdrop-blur-md p-4 rounded-2xl shadow-xl border border-white/50\">\r\n                  <div className=\"text-2xl font-bold text-purple-600 mb-1\">500+</div>\r\n                  <div className=\"text-sm text-gray-600\">Participants</div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Enhanced Stats Section */}\r\n        <div className=\"absolute bottom-20 left-1/2 transform -translate-x-1/2 w-full max-w-4xl px-4\">\r\n          <div className={`grid grid-cols-2 md:grid-cols-4 gap-4 transition-all duration-1000 delay-500 transform ${\r\n            isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'\r\n          }`}>\r\n            {[\r\n              { number: \"20+\", label: \"Active Programs\", color: \"from-blue-500 to-blue-600\" },\r\n              { number: \"500+\", label: \"Participants\", color: \"from-purple-500 to-purple-600\" },\r\n              { number: \"50+\", label: \"Expert Mentors\", color: \"from-indigo-500 to-indigo-600\" },\r\n              { number: \"95%\", label: \"Success Rate\", color: \"from-pink-500 to-pink-600\" }\r\n            ].map((stat, index) => (\r\n              <div\r\n                key={index}\r\n                className=\"bg-white/10 backdrop-blur-md p-4 rounded-2xl border border-white/20 hover:bg-white/20 transition-all duration-300 group\"\r\n              >\r\n                <div className={`text-2xl md:text-3xl font-bold bg-gradient-to-r ${stat.color} bg-clip-text text-transparent mb-1 group-hover:scale-110 transition-transform duration-300`}>\r\n                  {stat.number}\r\n                </div>\r\n                <div className=\"text-white/80 text-sm font-medium\">{stat.label}</div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Scroll Indicator */}\r\n        <div className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2\">\r\n          <button\r\n            onClick={scrollToNextSection}\r\n            className=\"w-12 h-12 bg-white/10 backdrop-blur-md rounded-full flex items-center justify-center border border-white/20 hover:bg-white/20 transition-all duration-300 group animate-bounce\"\r\n            aria-label=\"Scroll down to programs\"\r\n          >\r\n            <ArrowDown className=\"text-white group-hover:translate-y-1 transition-transform duration-300\" size={24} />\r\n          </button>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Program Types Section with ID for scroll targeting */}\r\n      <div id=\"program-types\">\r\n        {/* <ProgramTypesSection /> */}\r\n      </div>\r\n      <ProgramContent program={programsData[0]} />\r\n\r\n      {/* Calendar Section */}\r\n      <ProgramCalendarSection />\r\n\r\n      {/* Past Events Gallery */}\r\n      <PastEventsGallerySection />\r\n\r\n      {/* Custom CSS for animations */}\r\n      <style jsx global>{`\r\n        @keyframes mesh-drift {\r\n          0% { transform: translate(0, 0); }\r\n          100% { transform: translate(-60px, -60px); }\r\n        }\r\n\r\n        @keyframes float-drift {\r\n          0%, 100% { transform: translate(0, 0) rotate(0deg); }\r\n          25% { transform: translate(10px, -10px) rotate(90deg); }\r\n          50% { transform: translate(-5px, -15px) rotate(180deg); }\r\n          75% { transform: translate(-10px, 5px) rotate(270deg); }\r\n        }\r\n\r\n        @keyframes pulse-glow {\r\n          0%, 100% { opacity: 0.3; transform: scale(1); }\r\n          50% { opacity: 0.6; transform: scale(1.1); }\r\n        }\r\n\r\n        @keyframes gradient-x {\r\n          0%, 100% { background-position: 0% 50%; }\r\n          50% { background-position: 100% 50%; }\r\n        }\r\n\r\n        @keyframes gradient-x-reverse {\r\n          0%, 100% { background-position: 100% 50%; }\r\n          50% { background-position: 0% 50%; }\r\n        }\r\n\r\n        .animate-gradient-x {\r\n          background-size: 200% 200%;\r\n          animation: gradient-x 3s ease infinite;\r\n        }\r\n\r\n        .animate-gradient-x-reverse {\r\n          background-size: 200% 200%;\r\n          animation: gradient-x-reverse 3s ease infinite;\r\n        }\r\n\r\n        .animate-float {\r\n          animation: float 6s ease-in-out infinite;\r\n        }\r\n\r\n        .animate-spin-slow {\r\n          animation: spin 20s linear infinite;\r\n        }\r\n\r\n        @keyframes float {\r\n          0%, 100% { transform: translateY(0px) rotate(0deg); }\r\n          50% { transform: translateY(-20px) rotate(180deg); }\r\n        }\r\n      `}</style>\r\n    </>\r\n  );\r\n}"], "names": [], "mappings": ";;;;;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AACA;AACA;AACA;;;AApBA;;;;;;;;;;AAsBe,SAAS;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,aAAa;YAEb,uBAAuB;YACvB,MAAM,WAAW;mDAAY;oBAC3B;2DAAiB,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI;;gBAC1C;kDAAG;YAEH;0CAAO,IAAM,cAAc;;QAC7B;iCAAG,EAAE;IAEL,kDAAkD;IAClD,MAAM,sBAAsB;QAC1B,MAAM,kBAAkB,SAAS,cAAc,CAAC;QAChD,IAAI,iBAAiB;YACnB,gBAAgB,cAAc,CAAC;gBAAE,UAAU;YAAS;QACtD;IACF;IAGA,qBACE;;0BAEE,6LAAC;gBACC,KAAK;0DACK;;kCAGV,6LAAC;kEAAc;;0CAEb,6LAAC,gIAAA,CAAA,UAAK;gCACJ,KAAI;gCACJ,KAAI;gCACJ,IAAI;gCACJ,QAAQ;gCACR,WAAU;;;;;;0CAIZ,6LAAC;0EAAc;;;;;;0CACf,6LAAC;0EAAc;;;;;;0CAGf,6LAAC;gCAEC,OAAO;oCACL,iBAAiB,CAAC;;;cAGlB,CAAC;oCACD,gBAAgB;oCAChB,WAAW;gCACb;0EARU;;;;;;;;;;;;kCAad,6LAAC;kEAAc;;4BAEZ;mCAAI,MAAM;6BAAI,CAAC,GAAG,CAAC,CAAC,GAAG,kBACtB,6LAAC;oCAQC,OAAO;wCACL,OAAO,GAAG,KAAK,MAAM,KAAK,IAAI,EAAE,EAAE,CAAC;wCACnC,QAAQ,GAAG,KAAK,MAAM,KAAK,IAAI,EAAE,EAAE,CAAC;wCACpC,KAAK,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;wCAC9B,MAAM,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;wCAC/B,WAAW,CAAC,YAAY,EACtB,KAAK,KAAK,MAAM,KAAK,GACtB,sBAAsB,CAAC;wCACxB,gBAAgB,GAAG,KAAK,MAAM,KAAK,GAAG,CAAC,CAAC;oCAC1C;8EAfW,CAAC,sBAAsB,EAChC,IAAI,MAAM,IAAI,mBACd,IAAI,MAAM,IAAI,qBACd,IAAI,MAAM,IAAI,qBACd,IAAI,MAAM,IAAI,mBAAmB,kBACjC;mCANG;;;;;4BAqBR;mCAAI,MAAM;6BAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC;oCAMC,OAAO;wCACL,OAAO,GAAG,KAAK,MAAM,KAAK,MAAM,IAAI,EAAE,CAAC;wCACvC,QAAQ,GAAG,KAAK,MAAM,KAAK,MAAM,IAAI,EAAE,CAAC;wCACxC,KAAK,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;wCAC9B,MAAM,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;wCAC/B,WAAW,CAAC,WAAW,EACrB,KAAK,KAAK,MAAM,KAAK,GACtB,sBAAsB,CAAC;wCACxB,gBAAgB,GAAG,KAAK,MAAM,KAAK,EAAE,CAAC,CAAC;oCACzC;8EAbW,CAAC,+BAA+B,EACzC,IAAI,MAAM,IAAI,mBACd,IAAI,MAAM,IAAI,qBAAqB,oBACnC;mCAJG,CAAC,IAAI,EAAE,GAAG;;;;;;;;;;;kCAoBrB,6LAAC;kEAAc;;0CACb,6LAAC;0EAAc;;;;;;0CACf,6LAAC;0EAAc;;;;;;0CACf,6LAAC;0EAAc;;;;;;;;;;;;kCAIjB,6LAAC;kEAAc;kCACb,cAAA,6LAAC;sEAAc;;8CAEb,6LAAC;8EAAe,CAAC,gEAAgE,EAC/E,YAAY,8BAA8B,4BAC1C;;sDAEA,6LAAC;sFAAc;sDACb,cAAA,6LAAC;0FAAc;;kEACb,6LAAC,6MAAA,CAAA,WAAQ;wDAAC,WAAU;wDAAkB,MAAM;;;;;;kEAC5C,6LAAC;kGAAe;kEAA4D;;;;;;kEAG5E,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;wDAAkB,MAAM;;;;;;;;;;;;;;;;;sDAK5C,6LAAC;sFAAa;;8DACZ,6LAAC;8FAAe;8DAAwB;;;;;;8DACxC,6LAAC;8FAAe;8DAAmH;;;;;;8DAGnI,6LAAC;8FAAe;8DAA2H;;;;;;;;;;;;sDAM7I,6LAAC;sFAAY;;gDAAmF;8DACnF,6LAAC;8FAAe;8DAA2B;;;;;;gDAAiB;8DACvE,6LAAC;8FAAe;8DAAgC;;;;;;gDAAwB;;;;;;;sDAG1E,6LAAC;sFAAY;sDAAwE;;;;;;sDAMrF,6LAAC;sFAAc;;8DACb,6LAAC;oDACC,SAAS;8FACC;;sEAEV,6LAAC;sGAAc;;;;;;sEACf,6LAAC;sGAAe;sEAAgB;;;;;;sEAChC,6LAAC,mNAAA,CAAA,YAAS;4DAAC,WAAU;;;;;;;;;;;;8DAGvB,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;;sEAEV,6LAAC;sGAAc;;;;;;sEACf,6LAAC;sGAAe;sEAAgB;;;;;;sEAChC,6LAAC,yNAAA,CAAA,eAAY;4DAAC,WAAU;;;;;;;;;;;;;;;;;;sDAK5B,6LAAC;sFAAc;sDACZ;gDACC;oDAAE,MAAM,yMAAA,CAAA,SAAM;oDAAE,MAAM;oDAAqB,OAAO;gDAAgB;gDAClE;oDAAE,MAAM,uMAAA,CAAA,QAAK;oDAAE,MAAM;oDAAqB,OAAO;gDAAkB;gDACnE;oDAAE,MAAM,yMAAA,CAAA,SAAM;oDAAE,MAAM;oDAAkB,OAAO;gDAAgB;gDAC/D;oDAAE,MAAM,uMAAA,CAAA,QAAK;oDAAE,MAAM;oDAAwB,OAAO;gDAAkB;6CACvE,CAAC,GAAG,CAAC,CAAC,MAAM,sBACX,6LAAC;8FAEY,CAAC,kJAAkJ,EAC5J,kBAAkB,QAAQ,gCAAgC,IAC1D;;sEAEF,6LAAC,KAAK,IAAI;4DAAC,WAAW,GAAG,KAAK,KAAK,CAAC,wDAAwD,CAAC;4DAAE,MAAM;;;;;;sEACrG,6LAAC;sGAAe;sEAAkC,KAAK,IAAI;;;;;;;mDANtD;;;;;;;;;;;;;;;;8CAab,6LAAC;8EAAe,CAAC,0DAA0D,EACzE,YAAY,8BAA8B,4BAC1C;8CACA,cAAA,6LAAC;kFAAc;;0DAEb,6LAAC;0FAAc;;kEACb,6LAAC,gIAAA,CAAA,UAAK;wDACJ,KAAI;wDACJ,KAAI;wDACJ,IAAI;wDACJ,WAAU;;;;;;kEAEZ,6LAAC;kGAAc;;;;;;kEAGf,6LAAC;kGAAc;kEACb,cAAA,6LAAC;sGAAiB;sEAChB,cAAA,6LAAC,qMAAA,CAAA,OAAI;gEAAC,WAAU;gEAAkB,MAAM;;;;;;;;;;;;;;;;;;;;;;0DAM9C,6LAAC;0FAAc;;kEACb,6LAAC;kGAAc;kEAA0C;;;;;;kEACzD,6LAAC;kGAAc;kEAAwB;;;;;;;;;;;;0DAGzC,6LAAC;0FAAc;;kEACb,6LAAC;kGAAc;kEAA0C;;;;;;kEACzD,6LAAC;kGAAc;kEAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQjD,6LAAC;kEAAc;kCACb,cAAA,6LAAC;sEAAe,CAAC,uFAAuF,EACtG,YAAY,8BAA8B,4BAC1C;sCACC;gCACC;oCAAE,QAAQ;oCAAO,OAAO;oCAAmB,OAAO;gCAA4B;gCAC9E;oCAAE,QAAQ;oCAAQ,OAAO;oCAAgB,OAAO;gCAAgC;gCAChF;oCAAE,QAAQ;oCAAO,OAAO;oCAAkB,OAAO;gCAAgC;gCACjF;oCAAE,QAAQ;oCAAO,OAAO;oCAAgB,OAAO;gCAA4B;6BAC5E,CAAC,GAAG,CAAC,CAAC,MAAM,sBACX,6LAAC;8EAEW;;sDAEV,6LAAC;sFAAe,CAAC,gDAAgD,EAAE,KAAK,KAAK,CAAC,2FAA2F,CAAC;sDACvK,KAAK,MAAM;;;;;;sDAEd,6LAAC;sFAAc;sDAAqC,KAAK,KAAK;;;;;;;mCANzD;;;;;;;;;;;;;;;kCAab,6LAAC;kEAAc;kCACb,cAAA,6LAAC;4BACC,SAAS;4BAET,cAAW;sEADD;sCAGV,cAAA,6LAAC,mNAAA,CAAA,YAAS;gCAAC,WAAU;gCAAyE,MAAM;;;;;;;;;;;;;;;;;;;;;;0BAM1G,6LAAC;gBAAI,IAAG;;;;;;;0BAGR,6LAAC,kKAAA,CAAA,UAAc;gBAAC,SAAS,+HAAA,CAAA,eAAY,CAAC,EAAE;;;;;;0BAGxC,6LAAC,oKAAA,CAAA,UAAsB;;;;;0BAGvB,6LAAC,sKAAA,CAAA,UAAwB;;;;;;;;;;;AAwD/B;GAjVwB;KAAA", "debugId": null}}]}