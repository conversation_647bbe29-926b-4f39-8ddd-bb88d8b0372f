(()=>{var e={};e.id=289,e.ids=[289],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},16023:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]])},18212:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>m,tree:()=>c});var a=t(65239),r=t(48088),l=t(88170),i=t.n(l),n=t(30893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);t.d(s,d);let c={children:["",{children:["dashboard",{children:["news",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,30541)),"D:\\fwu_incubation(react+laravel)\\incubation\\frontend\\src\\app\\dashboard\\news\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,63144)),"D:\\fwu_incubation(react+laravel)\\incubation\\frontend\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,53862)),"D:\\fwu_incubation(react+laravel)\\incubation\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54413)),"D:\\fwu_incubation(react+laravel)\\incubation\\frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["D:\\fwu_incubation(react+laravel)\\incubation\\frontend\\src\\app\\dashboard\\news\\page.tsx"],x={require:t,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/dashboard/news/page",pathname:"/dashboard/news",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},22518:(e,s,t)=>{Promise.resolve().then(t.bind(t,51164))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30541:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\fwu_incubation(react+laravel)\\\\incubation\\\\frontend\\\\src\\\\app\\\\dashboard\\\\news\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\fwu_incubation(react+laravel)\\incubation\\frontend\\src\\app\\dashboard\\news\\page.tsx","default")},33873:e=>{"use strict";e.exports=require("path")},37360:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]])},48730:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},51164:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>er});var a=t(60687),r=t(43210),l=t(25541),i=t(10022),n=t(41312),d=t(37360),c=t(58869),o=t(40228),x=t(13861),m=t(75034),h=t(88233),u=t(30474),p=t(11860),g=t(9005);let b=({item:e,isModalOpen:s,setIsModalOpen:t})=>{console.log(e,"this is items from view details"),console.log(e,"this is item from view details");let r={news_id:e?.news_id,title:e?.title,description:e?.description,news_photo:`http://127.0.0.1:8000/storage/news/${e?.news_photo}`,category:e?.category,added_by:e?.added_by,created_at:e?.created_at,updated_at:e?.updated_at,admin:{id:e?.admin?.id,name:e?.admin?.name,email:e?.admin?.email,profile_image:e?.admin?.profile_image,role:e?.admin?.role,created_at:e?.admin?.created_at,updated_at:e?.admin?.updated_at,email_verified:e?.admin?.email_verified}},l=e=>new Date(e).toLocaleString("en-US",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"}),i="";return console.log(i="string"==typeof r?.news_photo?r?.news_photo:"","this is image url"),(0,a.jsx)("div",{className:"p-8",children:s&&(0,a.jsx)("div",{className:"fixed overflow-x-hidden inset-0  bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,a.jsxs)("div",{className:"bg-white rounded-xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"News Details"}),(0,a.jsx)("button",{onClick:()=>t({select:!1,item:null}),className:"p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-all",children:(0,a.jsx)(p.A,{className:"w-5 h-5"})})]}),(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsx)("div",{className:"mb-6",children:r?.news_photo?(0,a.jsx)("div",{className:"relative h-64 w-full bg-gray-100 rounded-lg overflow-hidden",children:(0,a.jsx)("div",{className:"flex items-center justify-center h-full text-gray-400",children:(0,a.jsx)("div",{className:"text-center",children:""!=i?(0,a.jsx)(u.default,{src:i,alt:r.title,width:200,height:100,className:"w-full h-full object-cover"}):(0,a.jsx)("div",{className:"w-full h-full flex items-center justify-center text-gray-400",children:(0,a.jsx)(g.A,{className:"w-12 h-12"})})})})}):(0,a.jsx)("div",{className:"h-64 w-full bg-gray-100 rounded-lg flex items-center justify-center",children:(0,a.jsx)(g.A,{className:"w-12 h-12 text-gray-400"})})}),(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-4",children:r.title}),(0,a.jsx)("div",{className:"mb-4",children:(0,a.jsxs)("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800",children:[(0,a.jsx)(d.A,{className:"w-3 h-3 mr-1"}),r.category]})}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Description"}),(0,a.jsx)("p",{className:"text-gray-700 leading-relaxed",children:r.description})]}),(0,a.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4 mb-6",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-3 flex items-center",children:[(0,a.jsx)(c.A,{className:"w-5 h-5 mr-2"}),"Added By"]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-gray-200 rounded-full overflow-hidden",children:r.admin.profile_image?(0,a.jsx)(u.default,{width:40,height:40,src:r.admin.profile_image,alt:r.admin.name,className:"w-full h-full object-cover"}):(0,a.jsx)("div",{className:"w-full h-full flex items-center justify-center text-gray-400",children:(0,a.jsx)(c.A,{className:"w-6 h-6"})})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-semibold text-gray-900",children:r.admin.name}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:r.admin.email}),(0,a.jsx)("span",{className:"inline-block px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full mt-1",children:r.admin.role})]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"bg-blue-50 rounded-lg p-4",children:[(0,a.jsxs)("h4",{className:"font-semibold text-gray-900 mb-2 flex items-center",children:[(0,a.jsx)(o.A,{className:"w-4 h-4 mr-2"}),"Created At"]}),(0,a.jsx)("p",{className:"text-sm text-gray-700",children:l(r.created_at)})]}),(0,a.jsxs)("div",{className:"bg-green-50 rounded-lg p-4",children:[(0,a.jsxs)("h4",{className:"font-semibold text-gray-900 mb-2 flex items-center",children:[(0,a.jsx)(o.A,{className:"w-4 h-4 mr-2"}),"Updated At"]}),(0,a.jsx)("p",{className:"text-sm text-gray-700",children:l(r.updated_at)})]})]}),(0,a.jsx)("div",{className:"mt-6 pt-4 border-t border-gray-200",children:(0,a.jsxs)("div",{className:"flex flex-wrap gap-4 text-sm text-gray-600",children:[(0,a.jsxs)("span",{children:["News ID: ",(0,a.jsxs)("span",{className:"font-semibold",children:["#",r.news_id]})]}),(0,a.jsxs)("span",{children:["Admin ID: ",(0,a.jsxs)("span",{className:"font-semibold",children:["#",r.admin.id]})]}),(0,a.jsxs)("span",{children:["Email Verified:",(0,a.jsx)("span",{className:`ml-1 px-2 py-1 rounded-full text-xs ${r.admin.email_verified?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:r.admin.email_verified?"Yes":"No"})]})]})})]}),(0,a.jsx)("div",{className:"flex justify-end p-6 border-t border-gray-200",children:(0,a.jsx)("button",{onClick:()=>t({select:!1,item:null}),className:"px-6 cursor-pointer py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"Close"})})]})})})};var f=t(16023),j=t(62688);let y=(0,j.A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]]);var v=t(8693),N=t(51060),w=t(52581);let A=({item:e,isEditModelOpen:s,setIsEditModelOpen:t})=>{let[l,n]=(0,r.useState)({title:"",description:"",category:"",news_photo:null}),[o,x]=(0,r.useState)(""),[m,h]=(0,r.useState)(!1),b=(0,v.jE)();(0,r.useEffect)(()=>{e&&(n({title:e.title||"",description:e.description||"",category:e.category||"",news_photo:e.news_photo instanceof File?e.news_photo:null}),x(e.news_photo?`http://127.0.0.1:8000/storage/news/${e.news_photo}`:""))},[e]);let j=e=>{let{name:s,value:t}=e.target;n(e=>({...e,[s]:t}))},A=e=>{let s=e.target.files?.[0];if(s){let e=new FileReader;e.onloadend=()=>{x(e.result),n(e=>({...e,news_photo:s}))},e.readAsDataURL(s)}},_=async()=>{h(!0);try{let s=new FormData;l.news_photo instanceof File&&s.append("news_photo",l.news_photo),s.append("title",l.title),s.append("description",l.description),s.append("category",l.category),s.append("_method","PUT"),await N.A.post(`http://localhost:8000/api/news/${e.news_id}`,s,{headers:{"Content-Type":"multipart/form-data",Accept:"application/json"}}),await b.invalidateQueries({queryKey:["news"]}),w.oR.success("News updated successfully")}catch(e){console.error("Error details:",e),w.oR.error("Failed to update news")}finally{h(!1),t({select:!1,item:null})}},k=()=>{t({select:!1,item:null})};return(0,a.jsx)("div",{className:"p-8",children:s?.select&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,a.jsxs)("div",{className:"bg-white rounded-xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-indigo-50",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"p-2 bg-blue-100 rounded-lg",children:(0,a.jsx)(i.A,{className:"w-6 h-6 text-blue-600"})}),(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Edit News Article"})]}),(0,a.jsx)("button",{onClick:k,className:"p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-all",children:(0,a.jsx)(p.A,{className:"w-5 h-5"})})]}),(0,a.jsxs)("div",{className:"p-6 space-y-6",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("label",{className:"block text-sm font-semibold text-gray-700",children:"News Image"}),(0,a.jsx)("div",{className:"flex flex-col space-y-4",children:o?(0,a.jsxs)("div",{className:"relative h-48 w-full bg-gray-100 rounded-lg overflow-hidden",children:[(0,a.jsx)(u.default,{src:o,alt:"News preview",fill:!0,className:"object-cover"}),(0,a.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-20 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity",children:(0,a.jsxs)("label",{className:"bg-white px-4 py-2 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors",children:[(0,a.jsx)(f.A,{className:"w-4 h-4 inline mr-2"}),"Change Image",(0,a.jsx)("input",{type:"file",accept:"image/*",onChange:A,className:"hidden"})]})})]}):(0,a.jsx)("div",{className:"h-48 w-full border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center hover:border-blue-400 transition-colors",children:(0,a.jsxs)("label",{className:"cursor-pointer text-center",children:[(0,a.jsx)(g.A,{className:"w-12 h-12 text-gray-400 mx-auto mb-2"}),(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"Click to upload image"}),(0,a.jsx)("input",{type:"file",accept:"image/*",onChange:A,className:"hidden"})]})})})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("label",{htmlFor:"title",className:"block text-sm font-semibold text-gray-700",children:"News Title *"}),(0,a.jsx)("input",{type:"text",id:"title",name:"title",value:l.title,onChange:j,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all",placeholder:"Enter news title...",required:!0})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("label",{htmlFor:"category",className:"block text-sm font-semibold text-gray-700",children:"Category *"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(d.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400"}),(0,a.jsxs)("select",{id:"category",name:"category",value:l.category,onChange:j,className:"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all appearance-none bg-white",required:!0,children:[(0,a.jsx)("option",{value:"",children:"Select a category"}),["event","announcement","research","startup","seminar","funding","achievement","notice","workshop"].map(e=>(0,a.jsx)("option",{value:e,children:e},e))]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("label",{htmlFor:"description",className:"block text-sm font-semibold text-gray-700",children:"Description *"}),(0,a.jsx)("textarea",{id:"description",name:"description",value:l.description,onChange:j,rows:6,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all resize-vertical",placeholder:"Enter news description...",required:!0}),(0,a.jsxs)("div",{className:"text-right text-sm text-gray-500",children:[l.description.length," characters"]})]}),(0,a.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,a.jsxs)("h3",{className:"text-sm font-semibold text-gray-700 mb-3 flex items-center",children:[(0,a.jsx)(c.A,{className:"w-4 h-4 mr-2"}),"Author Information"]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-10 h-10 bg-gray-200 rounded-full overflow-hidden",children:e?.admin?.profile_image?(0,a.jsx)(u.default,{src:e.admin.profile_image,alt:e.admin.name,width:40,height:40,className:"w-full h-full object-cover"}):(0,a.jsx)("div",{className:"w-full h-full flex items-center justify-center text-gray-400",children:(0,a.jsx)(c.A,{className:"w-5 h-5"})})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900",children:e?.admin?.name}),(0,a.jsx)("p",{className:"text-xs text-gray-600",children:e?.admin?.email})]})]})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between p-6 border-t border-gray-200 bg-gray-50",children:[(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:["Last updated: ",new Date(e?.updated_at).toLocaleDateString()]}),(0,a.jsxs)("div",{className:"flex space-x-3",children:[(0,a.jsx)("button",{onClick:k,className:"px-6 py-2 cursor-pointer border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors",disabled:m,children:"Cancel"}),(0,a.jsx)("button",{onClick:_,disabled:m||!l.title||!l.description||!l.category,className:"px-6 py-2 cursor-pointer bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors flex items-center space-x-2",children:m?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}),(0,a.jsx)("span",{children:"Saving..."})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(y,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Save Changes"})]})})]})]})]})})})},_=(0,j.A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]),k=({item:e,isDeleteModelOpen:s,setIsDeleteModelOpen:t})=>{let[l,i]=(0,r.useState)(!1),[n,x]=(0,r.useState)(""),m=(0,v.jE)(),g=async()=>{i(!0);try{let s=await N.A.delete(`http://localhost:8000/api/news/${e.news_id}`,{headers:{Accept:"application/json"},params:{news_photo:e.news_photo},method:"DELETE"});console.log(s,"this is response"),w.oR.success("News deleted successfully"),m.invalidateQueries({queryKey:["news"]})}catch(e){w.oR.error("Delete failed "+e)}finally{i(!1),t({select:!1,item:null}),x("")}},b=()=>{t({select:!1,item:null}),x("")},f="delete"===n.toLowerCase(),j="";return j="string"==typeof e?.news_photo?`http://127.0.0.1:8000/storage/news/${e.news_photo}`:"",(0,a.jsx)("div",{className:"p-8",children:s.select&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,a.jsxs)("div",{className:"bg-white rounded-xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200 bg-gradient-to-r from-red-50 to-pink-50",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"p-2 bg-red-100 rounded-lg",children:(0,a.jsx)(_,{className:"w-6 h-6 text-red-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Delete News Article"}),(0,a.jsx)("p",{className:"text-sm text-red-600 mt-1",children:"This action cannot be undone"})]})]}),(0,a.jsx)("button",{onClick:b,className:"p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-all",disabled:l,children:(0,a.jsx)(p.A,{className:"w-5 h-5"})})]}),(0,a.jsxs)("div",{className:"p-6 space-y-6",children:[(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)(_,{className:"w-5 h-5 text-red-500 mt-0.5 flex-shrink-0"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-sm font-semibold text-red-800",children:"Warning"}),(0,a.jsx)("p",{className:"text-sm text-red-700 mt-1",children:"You are about to permanently delete this news article. This action cannot be undone and will remove all associated data."})]})]})}),(0,a.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4 bg-gray-50",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Article to be deleted:"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex space-x-4",children:[(0,a.jsx)("div",{className:"w-24 h-24 bg-gray-200 rounded-lg overflow-hidden flex-shrink-0",children:e?.news_photo?(0,a.jsx)(u.default,{src:j,alt:e.title,width:96,height:96,className:"w-full h-full object-cover"}):(0,a.jsx)("div",{className:"w-full h-full flex items-center justify-center text-gray-400",children:(0,a.jsx)(h.A,{className:"w-8 h-8"})})}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-gray-900 truncate",children:e?.title}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 mt-2",children:[(0,a.jsxs)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800",children:[(0,a.jsx)(d.A,{className:"w-3 h-3 mr-1"}),e?.category]}),(0,a.jsxs)("span",{className:"text-xs text-gray-500",children:["ID: #",e?.news_id]})]})]})]}),(0,a.jsx)("div",{children:(0,a.jsx)("p",{className:"text-sm text-gray-600 line-clamp-3",children:e?.description})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 pt-4 border-t border-gray-200",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(c.A,{className:"w-4 h-4 text-gray-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-xs text-gray-500",children:"Added by"}),(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900",children:e?.admin?.name})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(o.A,{className:"w-4 h-4 text-gray-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-xs text-gray-500",children:"Created"}),(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900",children:new Date(e?.created_at).toLocaleString("en-US",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"})})]})]})]})]})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("label",{htmlFor:"confirmDelete",className:"block text-sm font-semibold text-gray-700",children:["To confirm deletion, type ",(0,a.jsx)("span",{className:"font-mono bg-gray-100 px-1 py-0.5 rounded",children:"DELETE"})," below:"]}),(0,a.jsx)("input",{type:"text",id:"confirmDelete",value:n,onChange:e=>x(e.target.value),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition-all",placeholder:"Type DELETE to confirm",disabled:l})]}),(0,a.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)(_,{className:"w-5 h-5 text-yellow-500 mt-0.5 flex-shrink-0"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-semibold text-yellow-800",children:"What will be deleted:"}),(0,a.jsxs)("ul",{className:"text-sm text-yellow-700 mt-2 space-y-1",children:[(0,a.jsx)("li",{children:"• News article content and metadata"}),(0,a.jsx)("li",{children:"• Associated images and media files"}),(0,a.jsx)("li",{children:"• View statistics and engagement data"}),(0,a.jsx)("li",{children:"• All comments and user interactions"})]})]})]})})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between p-6 border-t border-gray-200 bg-gray-50",children:[(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:["Article ID: #",e?.news_id]}),(0,a.jsxs)("div",{className:"flex space-x-3",children:[(0,a.jsx)("button",{onClick:b,className:"px-6 cursor-pointer py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors",disabled:l,children:"Cancel"}),(0,a.jsx)("button",{onClick:g,disabled:!f||l,className:"px-6 py-2 cursor-pointer bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors flex items-center space-x-2",children:l?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}),(0,a.jsx)("span",{children:"Deleting..."})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(h.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Delete Article"})]})})]})]})]})})})},C=({news:e})=>{let[s,t]=(0,r.useState)({select:!1,item:null}),[l,i]=(0,r.useState)({select:!1,item:null}),[n,p]=(0,r.useState)({select:!1,item:null});return(console.log(e,"this is news from news table"),s?.select&&s.item)?(0,a.jsx)(b,{item:s.item,isModalOpen:s,setIsModalOpen:t}):l?.select&&l.item?(0,a.jsx)(A,{item:l.item,isEditModelOpen:l,setIsEditModelOpen:i}):n?.select&&n.item?(0,a.jsx)(k,{item:n.item,isDeleteModelOpen:n,setIsDeleteModelOpen:p}):(0,a.jsx)("div",{className:"bg-white rounded-2xl shadow-lg overflow-hidden border border-gray-100",children:(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full",children:[(0,a.jsx)("thead",{className:"bg-gray-50 border-b border-gray-200",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider",children:"News"}),(0,a.jsx)("th",{className:"px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider",children:"Category"}),(0,a.jsx)("th",{className:"px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider",children:"Author"}),(0,a.jsx)("th",{className:"px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider",children:"Date"}),(0,a.jsx)("th",{className:"px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider",children:"Actions"})]})}),(0,a.jsx)("tbody",{className:"divide-y divide-gray-200",children:e.map(e=>(0,a.jsxs)("tr",{className:"hover:bg-gray-50 transition-colors group",children:[(0,a.jsx)("td",{className:"px-6 py-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)(u.default,{src:`http://127.0.0.1:8000/storage/news/${e.news_photo}`,alt:e?.title,width:48,height:48,className:"h-12 border w-12 object-contain rounded-[50%] "}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("h4",{className:"text-sm font-semibold text-gray-900 truncate group-hover:text-blue-600 transition-colors",children:e.title}),(0,a.jsx)("p",{className:"text-sm text-gray-500 truncate max-w-xs",children:e.description})]})]})}),(0,a.jsx)("td",{className:"px-6 py-4",children:(0,a.jsxs)("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 capitalize",children:[(0,a.jsx)(d.A,{className:"w-3 h-3 mr-1"}),e.category]})}),(0,a.jsx)("td",{className:"px-6 py-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-gradient-to-br from-green-500 to-teal-500 rounded-full flex items-center justify-center",children:(0,a.jsx)(c.A,{className:"w-4 h-4 text-white"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900",children:e.admin.name}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:e.admin.role})]})]})}),(0,a.jsx)("td",{className:"px-6 py-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-gray-600",children:[(0,a.jsx)(o.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:new Date(e.created_at).toLocaleDateString()})]})}),(0,a.jsx)("td",{className:"px-6 py-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("button",{onClick:()=>t({select:!0,item:e}),className:"p-2 cursor-pointer text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-all",title:"View",children:(0,a.jsx)(x.A,{className:"w-4 h-4"})}),(0,a.jsx)("button",{onClick:()=>i({select:!0,item:e}),className:"p-2 cursor-pointer text-gray-600 hover:text-green-600 hover:bg-green-50 rounded-lg transition-all",title:"Edit",children:(0,a.jsx)(m.A,{className:"w-4 h-4"})}),(0,a.jsx)("button",{onClick:()=>p({select:!0,item:e}),className:"p-2 cursor-pointer text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-lg transition-all",title:"Delete",children:(0,a.jsx)(h.A,{className:"w-4 h-4"})})]})})]},e.news_id))})]})})})};var D=t(99270),q=t(80462),S=t(31158),E=t(96474),M=t(26134),P=t(4780);function z({...e}){return(0,a.jsx)(M.bL,{"data-slot":"dialog",...e})}function F({...e}){return(0,a.jsx)(M.l9,{"data-slot":"dialog-trigger",...e})}function T({...e}){return(0,a.jsx)(M.ZL,{"data-slot":"dialog-portal",...e})}function L({className:e,...s}){return(0,a.jsx)(M.hJ,{"data-slot":"dialog-overlay",className:(0,P.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...s})}function I({className:e,children:s,showCloseButton:t=!0,...r}){return(0,a.jsxs)(T,{"data-slot":"dialog-portal",children:[(0,a.jsx)(L,{}),(0,a.jsxs)(M.UC,{"data-slot":"dialog-content",className:(0,P.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",e),...r,children:[s,t&&(0,a.jsxs)(M.bm,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,a.jsx)(p.A,{}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function $({className:e,...s}){return(0,a.jsx)("div",{"data-slot":"dialog-header",className:(0,P.cn)("flex flex-col gap-2 text-center sm:text-left",e),...s})}function O({className:e,...s}){return(0,a.jsx)(M.hE,{"data-slot":"dialog-title",className:(0,P.cn)("text-lg leading-none font-semibold",e),...s})}function V({className:e,...s}){return(0,a.jsx)(M.VY,{"data-slot":"dialog-description",className:(0,P.cn)("text-muted-foreground text-sm",e),...s})}function R({open:e,setOpen:s}){let[t,l]=(0,r.useState)({title:"",category:"",description:"",added_by:"1"}),[i,n]=(0,r.useState)(null),[d,c]=(0,r.useState)(!1),o=(0,v.jE)(),x=e=>{let{name:s,value:t}=e.target;l(e=>({...e,[s]:t}))},m=async a=>{a.preventDefault(),c(!0);let r=new FormData;Object.entries(t).forEach(([e,s])=>{r.append(e,s)}),i&&r.append("news_photo",i);try{let e=await N.A.post("http://localhost:8000/api/news",r,{headers:{"Content-Type":"multipart/form-data"}});console.log(e,"this is response")}catch(e){console.error(e),alert("Error creating news.")}finally{c(!1),s(!e),await o.invalidateQueries({queryKey:["news"]})}};return(0,a.jsxs)("form",{onSubmit:m,className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Title *"}),(0,a.jsx)("input",{type:"text",name:"title",value:t.title,onChange:x,required:!0,className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",placeholder:"Enter  news title"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Category *"}),(0,a.jsxs)("select",{name:"category",value:t.category,onChange:x,required:!0,className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"",children:"Select Category"}),["event","announcement","research","startup","seminar","funding","training","achievement","notice","workshop"].map(e=>(0,a.jsx)("option",{value:e,children:e},e))]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Photo"}),(0,a.jsx)("input",{type:"file",name:"news_photo",onChange:e=>{n(e.target.files&&e.target.files[0]?e.target.files[0]:null)},accept:"image/*",className:"w-full px-4 py-2 border border-gray-300 rounded-lg"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Description *"}),(0,a.jsx)("textarea",{name:"description",value:t.description,onChange:x,rows:6,required:!0,className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",placeholder:"Enter news description..."})]}),(0,a.jsxs)("div",{className:"flex justify-end gap-4 pt-4 border-t mt-6",children:[(0,a.jsx)("button",{type:"button",onClick:()=>{s(!e)},className:"px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-100",children:"Cancel"}),(0,a.jsx)("button",{type:"submit",disabled:d,className:"px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg",children:d?"Creating...":"Create News"})]})]})}let U=function({searchTerm:e,setSearchTerm:s,selectedCategory:t,setSelectedCategory:l,categories:i}){let[n,d]=(0,r.useState)(!1);return(0,a.jsx)(a.Fragment,{children:(0,a.jsx)("div",{className:"bg-white rounded-2xl p-6 shadow-lg mb-8 border border-gray-100",children:(0,a.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0",children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4 flex-1",children:[(0,a.jsxs)("div",{className:"relative flex-1 max-w-md",children:[(0,a.jsx)(D.A,{className:"absolute left-3 top-3 w-5 h-5 text-gray-400"}),(0,a.jsx)("input",{type:"text",placeholder:"Search news...",value:e,onChange:e=>s(e.target.value),className:"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"})]}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(q.A,{className:"absolute left-3 top-3 w-5 h-5 text-gray-400"}),(0,a.jsx)("select",{value:t,onChange:e=>l(e.target.value),className:"pl-10 pr-8 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all appearance-none bg-white min-w-40",children:i?.map(e=>(0,a.jsx)("option",{value:e,children:"all"===e?"All Categories":e.charAt(0).toUpperCase()+e.slice(1)},e))})]})]}),(0,a.jsxs)("div",{className:"flex space-x-4",children:[(0,a.jsxs)("button",{className:"flex items-center space-x-2 px-4 py-3 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-xl transition-all",children:[(0,a.jsx)(S.A,{className:"w-5 h-5"}),(0,a.jsx)("span",{children:"Export"})]}),(0,a.jsxs)(z,{open:n,onOpenChange:d,children:[(0,a.jsx)(F,{asChild:!0,children:(0,a.jsxs)("button",{className:"flex cursor-pointer items-center space-x-2 px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white rounded-xl transition-all shadow-lg hover:shadow-xl",children:[(0,a.jsx)(E.A,{className:"w-5 h-5"}),(0,a.jsx)("span",{children:"Add News"})]})}),(0,a.jsx)(I,{children:(0,a.jsxs)($,{children:[(0,a.jsx)(O,{children:"Create News"}),(0,a.jsx)(V,{children:(0,a.jsx)(R,{open:n,setOpen:d})})]})})]})]})]})})})};var G=t(47033),H=t(14952);let K=({currentPage:e,totalPages:s,onPageChange:t,mockNewsData:r})=>(0,a.jsxs)("div",{className:"flex items-center justify-between bg-white px-6 py-4 rounded-2xl shadow-lg border border-gray-100",children:[(0,a.jsx)("div",{className:"flex items-center space-x-2 text-sm text-gray-600",children:(0,a.jsxs)("span",{children:["Showing 1 to ",r.data.length," of ",r.total," results"]})}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("button",{onClick:()=>t(e-1),disabled:1===e,className:"p-2 rounded-lg border border-gray-300 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-all",children:(0,a.jsx)(G.A,{className:"w-5 h-5"})}),(0,a.jsx)("span",{className:"px-4 py-2 bg-blue-600 text-white rounded-lg font-medium",children:e}),(0,a.jsx)("button",{onClick:()=>t(e+1),disabled:e===s,className:"p-2 rounded-lg border border-gray-300 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-all",children:(0,a.jsx)(H.A,{className:"w-5 h-5"})})]})]});var Q=t(51423);let W=(0,j.A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]),Y=(0,j.A)("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]]),B=(0,j.A)("server",[["rect",{width:"20",height:"8",x:"2",y:"2",rx:"2",ry:"2",key:"ngkwjq"}],["rect",{width:"20",height:"8",x:"2",y:"14",rx:"2",ry:"2",key:"iecqi9"}],["line",{x1:"6",x2:"6.01",y1:"6",y2:"6",key:"16zg32"}],["line",{x1:"6",x2:"6.01",y1:"18",y2:"18",key:"nzw8ys"}]]),J=(0,j.A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]);var X=t(78122),Z=t(48730);function ee({type:e="spinner",message:s="Loading...",progress:t=0,size:l="medium",color:i="blue"}){let[n,d]=(0,r.useState)(""),[c,o]=(0,r.useState)(0);console.log(c);let x={small:"w-4 h-4",medium:"w-6 h-6",large:"w-8 h-8",xl:"w-12 h-12"};return"spinner"===e?(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center p-8",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(W,{className:`${x[l]} ${{blue:"text-blue-600",purple:"text-purple-600",green:"text-green-600",orange:"text-orange-600",red:"text-red-600"}[i]} animate-spin`}),(0,a.jsx)("div",{className:`absolute inset-0 ${x[l]} border-2 border-transparent border-t-blue-200 rounded-full animate-spin`,style:{animationDirection:"reverse",animationDuration:"1.5s"}})]}),(0,a.jsxs)("p",{className:"mt-4 text-gray-600 font-medium",children:[s,n]})]}):"database"===e?(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center p-8",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl p-4 animate-pulse",children:(0,a.jsx)(Y,{className:"w-8 h-8 text-white"})}),(0,a.jsx)("div",{className:"absolute -top-1 -right-1 w-3 h-3 bg-green-400 rounded-full animate-ping"})]}),(0,a.jsxs)("p",{className:"mt-4 text-gray-700 font-medium",children:["Fetching data from database",n]}),(0,a.jsxs)("div",{className:"mt-2 flex items-center space-x-2 text-sm text-gray-500",children:[(0,a.jsx)(B,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Connecting to server"})]})]}):"progress"===e?(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center p-8 w-full max-w-md mx-auto",children:[(0,a.jsxs)("div",{className:"relative mb-6",children:[(0,a.jsx)("div",{className:"w-16 h-16 border-4 border-gray-200 rounded-full",children:(0,a.jsx)("div",{className:"w-16 h-16 border-4 border-blue-600 rounded-full border-t-transparent animate-spin",style:{animationDuration:"1s"}})}),(0,a.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,a.jsxs)("span",{className:"text-sm font-bold text-blue-600",children:[t,"%"]})})]}),(0,a.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2 mb-2",children:(0,a.jsx)("div",{className:"bg-gradient-to-r from-blue-600 to-purple-600 h-2 rounded-full transition-all duration-300 ease-out",style:{width:`${t}%`}})}),(0,a.jsx)("p",{className:"text-gray-600 font-medium",children:s})]}):"skeleton"===e?(0,a.jsx)("div",{className:"p-6 space-y-4",children:(0,a.jsxs)("div",{className:"animate-pulse",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4 mb-6",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-gray-300 rounded-full"}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("div",{className:"h-4 bg-gray-300 rounded w-1/4 mb-2"}),(0,a.jsx)("div",{className:"h-3 bg-gray-200 rounded w-1/2"})]})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("div",{className:"h-4 bg-gray-300 rounded"}),(0,a.jsx)("div",{className:"h-4 bg-gray-300 rounded w-5/6"}),(0,a.jsx)("div",{className:"h-4 bg-gray-300 rounded w-4/6"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-4 mt-6",children:[(0,a.jsx)("div",{className:"h-20 bg-gray-300 rounded-lg"}),(0,a.jsx)("div",{className:"h-20 bg-gray-300 rounded-lg"}),(0,a.jsx)("div",{className:"h-20 bg-gray-300 rounded-lg"})]})]})}):"card"===e?(0,a.jsxs)("div",{className:"bg-white rounded-xl shadow-lg border border-gray-100 p-6",children:[(0,a.jsx)("div",{className:"flex items-center justify-center h-32",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"w-16 h-16 border-4 border-blue-100 rounded-full animate-spin",children:(0,a.jsx)("div",{className:"w-16 h-16 border-4 border-blue-600 rounded-full border-r-transparent animate-pulse"})}),(0,a.jsx)(J,{className:"w-6 h-6 text-blue-600 absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"})]})}),(0,a.jsxs)("div",{className:"text-center mt-4",children:[(0,a.jsx)("p",{className:"text-gray-700 font-medium",children:s}),(0,a.jsx)("div",{className:"flex items-center justify-center mt-2 space-x-1",children:[0,1,2].map(e=>(0,a.jsx)("div",{className:"w-2 h-2 bg-blue-600 rounded-full animate-bounce",style:{animationDelay:`${.1*e}s`}},e))})]})]}):"pulse"===e?(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center p-8",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"w-20 h-20 bg-gradient-to-br from-blue-400 to-purple-600 rounded-full animate-pulse"}),(0,a.jsx)("div",{className:"absolute inset-0 w-20 h-20 bg-gradient-to-br from-blue-400 to-purple-600 rounded-full animate-ping opacity-30"}),(0,a.jsx)(X.A,{className:"w-8 h-8 text-white absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 animate-spin"})]}),(0,a.jsx)("p",{className:"mt-6 text-gray-700 font-medium text-lg",children:s}),(0,a.jsxs)("div",{className:"mt-2 flex items-center space-x-2 text-sm text-gray-500",children:[(0,a.jsx)(Z.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Please wait a moment"})]})]}):(0,a.jsxs)("div",{className:"flex items-center justify-center p-4",children:[(0,a.jsx)(W,{className:"w-6 h-6 text-blue-600 animate-spin mr-2"}),(0,a.jsx)("span",{className:"text-gray-600",children:s})]})}let es=function({type:e="spinner"}){return(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 p-6",children:(0,a.jsx)(ee,{type:e,message:"Saving changes"})})};var et=t(48927);let ea=({icon:e,title:s,value:t,change:r,color:i})=>(0,a.jsx)("div",{className:"bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 group",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-gray-600 text-sm font-medium",children:s}),(0,a.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mt-1",children:t}),void 0!==r&&(0,a.jsxs)("p",{className:`text-sm mt-2 flex items-center ${r>0?"text-green-600":"text-red-600"}`,children:[(0,a.jsx)(l.A,{className:"w-4 h-4 mr-1"}),r>0?"+":"",r,"% from last month"]})]}),(0,a.jsx)("div",{className:`p-3 rounded-xl ${i} group-hover:scale-110 transition-transform`,children:(0,a.jsx)(e,{className:"w-6 h-6 text-white"})})]})}),er=()=>{let[e,s]=(0,r.useState)(""),[t,c]=(0,r.useState)("all"),[o,x]=(0,r.useState)(1),{data:m,isLoading:h,error:u}=(0,Q.I)({queryKey:["news"],queryFn:async()=>(await N.A.get("http://localhost:8000/api/news")).data,select:e=>e,refetchOnWindowFocus:!1});if(h)return(0,a.jsx)(es,{type:"spinner"});if(u)return(0,a.jsx)(es,{type:"error"});let p=m?.data.filter(s=>{let a=s.title.toLowerCase().includes(e.toLowerCase())||s.description.toLowerCase().includes(e.toLowerCase()),r="all"===t||s.category===t;return a&&r});return(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,a.jsx)(et.A,{pageTitle:"Incubation Center - Official News",pageSubtitle:" Stay updated with the latest announcements, programs, and opportunities from our innovation hub. Access important documents and guidelines for startups and entrepreneurs.",breadcrumb:["Home","Incubation Center","News"],showStats:!0}),(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-6 py-8",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[(0,a.jsx)(ea,{icon:i.A,title:"Total News",value:m?.total??0,change:12,color:"bg-gradient-to-br from-blue-500 to-blue-600"}),(0,a.jsx)(ea,{icon:n.A,title:"Active Authors",value:"3",change:5,color:"bg-gradient-to-br from-green-500 to-green-600"}),(0,a.jsx)(ea,{icon:d.A,title:"Categories",value:"4",change:0,color:"bg-gradient-to-br from-purple-500 to-purple-600"}),(0,a.jsx)(ea,{icon:l.A,title:"This Month",value:"8",change:25,color:"bg-gradient-to-br from-orange-500 to-orange-600"})]}),(0,a.jsx)(U,{searchTerm:e,setSearchTerm:s,selectedCategory:t,setSelectedCategory:c,categories:["event","announcement","research","startup","seminar","funding","achievement","notice","workshop"]}),(0,a.jsx)("div",{className:"mb-8",children:(0,a.jsx)(C,{news:p??[]})}),(0,a.jsx)(K,{mockNewsData:m??{current_page:1,data:[],total:0,per_page:10,last_page:1},currentPage:o,totalPages:m?.last_page??1,onPageChange:x})]})]})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},59470:(e,s,t)=>{Promise.resolve().then(t.bind(t,30541))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")},96474:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[447,693,585,208,464,935,171,884,927],()=>t(18212));module.exports=a})();