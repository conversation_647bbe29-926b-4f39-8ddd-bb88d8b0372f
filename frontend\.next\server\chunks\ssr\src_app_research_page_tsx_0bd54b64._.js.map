{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/fwu_incubation%28react%2Blaravel%29/incubation/frontend/src/app/research/page.tsx"], "sourcesContent": ["\"use client\"\r\nimport { useState, useEffect, useRef } from 'react';\r\nimport Image from 'next/image';\r\nimport Link from 'next/link';\r\nimport { ArrowRight, ExternalLink, Search, FlaskRound, Book, Building2, GraduationCap, Handshake, User } from 'lucide-react';\r\n\r\n// Research Project Type\r\ninterface ResearchProject {\r\n  id: string;\r\n  title: string;\r\n  description: string;\r\n  category: string;\r\n  imageUrl: string;\r\n  researchers: string[];\r\n  status: 'ongoing' | 'completed';\r\n  year: string;\r\n}\r\n\r\n// Publication Type\r\ninterface Publication {\r\n  id: string;\r\n  title: string;\r\n  authors: string[];\r\n  journal: string;\r\n  year: string;\r\n  abstract: string;\r\n  link: string;\r\n  imageUrl: string;\r\n}\r\n\r\n// Research Center Type\r\ninterface ResearchCenter {\r\n  id: string;\r\n  name: string;\r\n  description: string;\r\n  focus: string[];\r\n  imageUrl: string;\r\n  contact: string;\r\n}\r\n\r\nexport default function ResearchPage() {\r\n  const [isVisible, setIsVisible] = useState(false);\r\n  const [activeTab, setActiveTab] = useState('all');\r\n  const [searchQuery, setSearchQuery] = useState('');\r\n  const heroRef = useRef(null);\r\n\r\n  useEffect(() => {\r\n    const observer = new IntersectionObserver(\r\n      ([entry]) => {\r\n        if (entry.isIntersecting) {\r\n          setIsVisible(true);\r\n          observer.disconnect();\r\n        }\r\n      },\r\n      { threshold: 0.1 }\r\n    );\r\n\r\n    // Store current ref value in a variable to use in cleanup\r\n    const currentRef = heroRef.current;\r\n\r\n    if (currentRef) {\r\n      observer.observe(currentRef);\r\n    }\r\n\r\n    return () => {\r\n      // Use the stored ref value in cleanup\r\n      if (currentRef) {\r\n        observer.unobserve(currentRef);\r\n      }\r\n    };\r\n  }, []);\r\n\r\n  // Sample Research Projects Data\r\n  const researchProjects: ResearchProject[] = [\r\n    {\r\n      id: 'rp1',\r\n      title: 'Sustainable Agricultural Practices in Far Western Nepal',\r\n      description: 'This research project focuses on developing sustainable agricultural practices suitable for the unique geographical and climatic conditions of Far Western Nepal. The project aims to improve crop yields, reduce environmental impact, and enhance the livelihoods of local farmers.',\r\n      category: 'Agriculture',\r\n      imageUrl: 'https://images.unsplash.com/photo-1500382017468-9049fed747ef?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2000&q=80',\r\n      researchers: ['Dr. Rajendra Sharma', 'Dr. Anita Poudel', 'Prof. Binod Thapa'],\r\n      status: 'ongoing',\r\n      year: '2024'\r\n    },\r\n    {\r\n      id: 'rp2',\r\n      title: 'Biodiversity Conservation in Shuklaphanta National Park',\r\n      description: 'This project studies the biodiversity of Shuklaphanta National Park and develops conservation strategies for endangered species. The research includes wildlife monitoring, habitat assessment, and community-based conservation approaches.',\r\n      category: 'Environmental Science',\r\n      imageUrl: 'https://images.unsplash.com/photo-1469474968028-56623f02e42e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2000&q=80',\r\n      researchers: ['Dr. Sarita Joshi', 'Prof. Kamal Thapa', 'Dr. Nirmala Bhatta'],\r\n      status: 'ongoing',\r\n      year: '2023'\r\n    },\r\n    {\r\n      id: 'rp3',\r\n      title: 'Renewable Energy Solutions for Rural Communities',\r\n      description: 'This research explores affordable and sustainable renewable energy solutions for rural communities in Far Western Nepal. The project focuses on solar, micro-hydro, and biomass energy technologies adapted to local conditions and needs.',\r\n      category: 'Engineering',\r\n      imageUrl: 'https://images.unsplash.com/photo-1509391366360-2e959784a276?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2000&q=80',\r\n      researchers: ['Prof. Ramesh Bhatt', 'Dr. Sunita Chaudhary', 'Dr. Prakash Joshi'],\r\n      status: 'ongoing',\r\n      year: '2024'\r\n    },\r\n    {\r\n      id: 'rp4',\r\n      title: 'Traditional Knowledge and Medicinal Plants of Far Western Nepal',\r\n      description: 'This project documents and studies the traditional knowledge of medicinal plants used by indigenous communities in Far Western Nepal. The research aims to preserve this knowledge and explore potential applications in modern medicine.',\r\n      category: 'Health Sciences',\r\n      imageUrl: 'https://images.unsplash.com/photo-1540573133985-87b6da6d54a9?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2000&q=80',\r\n      researchers: ['Dr. Meena Bhandari', 'Prof. Hari Prasad Sharma', 'Dr. Laxmi Rawat'],\r\n      status: 'completed',\r\n      year: '2022'\r\n    },\r\n    {\r\n      id: 'rp5',\r\n      title: 'Climate Change Adaptation Strategies for Local Communities',\r\n      description: 'This research develops and evaluates climate change adaptation strategies for vulnerable communities in Far Western Nepal. The project focuses on water resource management, agricultural adaptations, and disaster risk reduction.',\r\n      category: 'Environmental Science',\r\n      imageUrl: 'https://images.unsplash.com/photo-1470071459604-3b5ec3a7fe05?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2000&q=80',\r\n      researchers: ['Prof. Dipak Bhatta', 'Dr. Sabita Ranabhat', 'Dr. Mohan Singh Dhami'],\r\n      status: 'completed',\r\n      year: '2023'\r\n    },\r\n    {\r\n      id: 'rp6',\r\n      title: 'Educational Technology for Remote Learning in Rural Areas',\r\n      description: 'This project develops and tests educational technology solutions for remote learning in rural areas of Far Western Nepal. The research addresses challenges such as limited internet connectivity, power supply, and digital literacy.',\r\n      category: 'Education',\r\n      imageUrl: 'https://images.unsplash.com/photo-1503676260728-1c00da094a0b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2000&q=80',\r\n      researchers: ['Dr. Bhawana Joshi', 'Prof. Narendra Joshi', 'Dr. Prabha Sharma'],\r\n      status: 'ongoing',\r\n      year: '2024'\r\n    }\r\n  ];\r\n\r\n  // Sample Publications Data\r\n  const publications: Publication[] = [\r\n    {\r\n      id: 'pub1',\r\n      title: 'Biodiversity Status and Conservation Challenges in Shuklaphanta National Park, Nepal',\r\n      authors: ['Joshi, S.', 'Thapa, K.', 'Bhatta, N.'],\r\n      journal: 'Journal of Biodiversity Conservation',\r\n      year: '2023',\r\n      abstract: 'This paper presents a comprehensive assessment of biodiversity status in Shuklaphanta National Park, Nepal, and discusses the challenges and strategies for conservation. The study includes surveys of flora and fauna, analysis of threats, and evaluation of current conservation measures.',\r\n      link: '#',\r\n      imageUrl: 'https://images.unsplash.com/photo-1470770841072-f978cf4d019e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2000&q=80'\r\n    },\r\n    {\r\n      id: 'pub2',\r\n      title: 'Traditional Medicinal Plants Used by Indigenous Communities in Far Western Nepal',\r\n      authors: ['Bhandari, M.', 'Sharma, H. P.', 'Rawat, L.'],\r\n      journal: 'Journal of Ethnopharmacology',\r\n      year: '2022',\r\n      abstract: 'This study documents the traditional medicinal plants used by indigenous communities in Far Western Nepal. The research identifies 120 plant species used for various medicinal purposes and analyzes their pharmacological properties, conservation status, and potential applications in modern medicine.',\r\n      link: '#',\r\n      imageUrl: 'https://images.unsplash.com/photo-1516339901601-2e1b62dc0c45?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2000&q=80'\r\n    },\r\n    {\r\n      id: 'pub3',\r\n      title: 'Renewable Energy Potential and Implementation Challenges in Rural Nepal',\r\n      authors: ['Bhatt, R.', 'Chaudhary, S.', 'Joshi, P.'],\r\n      journal: 'Renewable Energy',\r\n      year: '2024',\r\n      abstract: 'This paper assesses the potential for renewable energy development in rural areas of Nepal, with a focus on solar, micro-hydro, and biomass technologies. The study analyzes technical, economic, and social factors affecting implementation and proposes strategies to overcome challenges.',\r\n      link: '#',\r\n      imageUrl: 'https://images.unsplash.com/photo-1466611653911-95081537e5b7?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2000&q=80'\r\n    },\r\n    {\r\n      id: 'pub4',\r\n      title: 'Climate Change Impacts on Water Resources in Far Western Nepal',\r\n      authors: ['Bhatta, D.', 'Ranabhat, S.', 'Dhami, M. S.'],\r\n      journal: 'Journal of Hydrology',\r\n      year: '2023',\r\n      abstract: 'This research investigates the impacts of climate change on water resources in Far Western Nepal. The study analyzes historical climate data, models future scenarios, and assesses implications for water availability, agriculture, and community livelihoods.',\r\n      link: '#',\r\n      imageUrl: 'https://images.unsplash.com/photo-1455218873509-8097305ee378?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2000&q=80'\r\n    }\r\n  ];\r\n\r\n  // Sample Research Centers Data\r\n  const researchCenters: ResearchCenter[] = [\r\n    {\r\n      id: 'rc1',\r\n      name: 'Center for Environmental Research and Conservation',\r\n      description: 'The Center for Environmental Research and Conservation focuses on biodiversity conservation, ecosystem management, and environmental sustainability in Far Western Nepal. The center conducts research, provides training, and engages with local communities to address environmental challenges.',\r\n      focus: ['Biodiversity Conservation', 'Ecosystem Management', 'Climate Change Adaptation'],\r\n      imageUrl: 'https://images.unsplash.com/photo-1500382017468-9049fed747ef?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2000&q=80',\r\n      contact: '<EMAIL>'\r\n    },\r\n    {\r\n      id: 'rc2',\r\n      name: 'Agricultural Innovation and Development Center',\r\n      description: 'The Agricultural Innovation and Development Center conducts research on sustainable agriculture, crop improvement, and agricultural technology. The center works with farmers, agricultural extension services, and industry partners to develop and disseminate innovative solutions.',\r\n      focus: ['Sustainable Agriculture', 'Crop Improvement', 'Agricultural Technology'],\r\n      imageUrl: 'https://images.unsplash.com/photo-1500382017468-9049fed747ef?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2000&q=80',\r\n      contact: '<EMAIL>'\r\n    },\r\n    {\r\n      id: 'rc3',\r\n      name: 'Center for Renewable Energy Research',\r\n      description: 'The Center for Renewable Energy Research focuses on developing and adapting renewable energy technologies for the specific needs and conditions of Far Western Nepal. The center conducts research on solar, micro-hydro, wind, and biomass energy systems.',\r\n      focus: ['Solar Energy', 'Micro-hydro Power', 'Biomass Energy'],\r\n      imageUrl: 'https://images.unsplash.com/photo-1509391366360-2e959784a276?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2000&q=80',\r\n      contact: '<EMAIL>'\r\n    },\r\n    {\r\n      id: 'rc4',\r\n      name: 'Health and Medical Research Institute',\r\n      description: 'The Health and Medical Research Institute conducts research on public health, traditional medicine, and healthcare delivery in Far Western Nepal. The institute collaborates with healthcare providers, government agencies, and international partners to improve health outcomes in the region.',\r\n      focus: ['Public Health', 'Traditional Medicine', 'Healthcare Delivery'],\r\n      imageUrl: 'https://images.unsplash.com/photo-1576091160550-2173dba999ef?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2000&q=80',\r\n      contact: '<EMAIL>'\r\n    }\r\n  ];\r\n\r\n  // Filter projects based on active tab and search query\r\n  const filteredProjects = researchProjects.filter(project => {\r\n    const matchesTab = activeTab === 'all' ||\r\n                      (activeTab === 'ongoing' && project.status === 'ongoing') ||\r\n                      (activeTab === 'completed' && project.status === 'completed');\r\n\r\n    const matchesSearch = searchQuery === '' ||\r\n                         project.title.toLowerCase().includes(searchQuery.toLowerCase()) ||\r\n                         project.description.toLowerCase().includes(searchQuery.toLowerCase()) ||\r\n                         project.category.toLowerCase().includes(searchQuery.toLowerCase());\r\n\r\n    return matchesTab && matchesSearch;\r\n  });\r\n\r\n  return (\r\n    <main className=\"bg-white\">\r\n      {/* Hero Section */}\r\n      <div\r\n        ref={heroRef}\r\n        className=\"relative py-20 md:py-28 bg-gradient-to-r from-indigo-900 via-indigo-800 to-blue-900 text-white overflow-hidden\"\r\n      >\r\n        {/* Background elements */}\r\n        <div className=\"absolute inset-0 overflow-hidden\">\r\n          <div className=\"absolute top-20 left-10 w-64 h-64 rounded-full bg-indigo-500 opacity-10 animate-float-slow\"></div>\r\n          <div className=\"absolute bottom-20 right-10 w-96 h-96 rounded-full bg-blue-500 opacity-10 animate-float-reverse\"></div>\r\n          <div className=\"absolute top-1/2 left-1/4 w-32 h-32 rounded-full bg-purple-500 opacity-5 animate-pulse\"></div>\r\n          <div\r\n            className=\"absolute inset-0 opacity-10\"\r\n            style={{\r\n              backgroundImage: 'radial-gradient(circle, rgba(255,255,255,0.1) 1px, transparent 1px)',\r\n              backgroundSize: '30px 30px'\r\n            }}\r\n          ></div>\r\n        </div>\r\n\r\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 relative z-10\">\r\n          <div className={`max-w-4xl mx-auto text-center transition-all duration-1000 transform ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>\r\n            <div className=\"inline-block mb-6 p-2 bg-indigo-800/30 rounded-full\">\r\n              <div className=\"px-4 py-1 bg-indigo-700/50 rounded-full\">\r\n                <span className=\"text-indigo-100 font-medium\">Far Western University</span>\r\n              </div>\r\n            </div>\r\n\r\n            <h1 className=\"text-4xl sm:text-5xl md:text-6xl font-bold mb-6 leading-tight\">\r\n              Research & <span className=\"text-transparent bg-clip-text bg-gradient-to-r from-blue-300 to-indigo-300\">Innovation</span>\r\n            </h1>\r\n\r\n            <p className=\"text-xl text-indigo-100 max-w-3xl mx-auto mb-10 leading-relaxed\">\r\n              Exploring new frontiers of knowledge through cutting-edge research and innovation at Far Western University.\r\n            </p>\r\n\r\n            {/* Search Bar */}\r\n            <div className=\"max-w-2xl mx-auto mt-8 relative\">\r\n              <div className=\"relative\">\r\n                <input\r\n                  type=\"text\"\r\n                  placeholder=\"Search for research projects, publications, or topics...\"\r\n                  value={searchQuery}\r\n                  onChange={(e) => setSearchQuery(e.target.value)}\r\n                  className=\"w-full py-4 px-6 pl-12 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 text-white placeholder-indigo-200 focus:outline-none focus:ring-2 focus:ring-indigo-400 focus:border-transparent transition-all duration-300\"\r\n                />\r\n                <Search className=\"absolute left-4 top-1/2 transform -translate-y-1/2 text-indigo-300 text-xl\" />\r\n                {searchQuery && (\r\n                  <button\r\n                    onClick={() => setSearchQuery('')}\r\n                    className=\"absolute right-4 top-1/2 transform -translate-y-1/2 text-indigo-300 hover:text-white transition-colors\"\r\n                  >\r\n                    ✕\r\n                  </button>\r\n                )}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Wave divider */}\r\n        <div className=\"absolute bottom-0 left-0 right-0\">\r\n          <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 1440 120\" className=\"w-full h-auto\">\r\n            <path\r\n              fill=\"#ffffff\"\r\n              fillOpacity=\"1\"\r\n              d=\"M0,64L80,69.3C160,75,320,85,480,80C640,75,800,53,960,48C1120,43,1280,53,1360,58.7L1440,64L1440,120L1360,120C1280,120,1120,120,960,120C800,120,640,120,480,120C320,120,160,120,80,120L0,120Z\"\r\n            ></path>\r\n          </svg>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Research Highlights Section */}\r\n      <section className=\"py-16 md:py-24\">\r\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\r\n          <div className=\"max-w-3xl mx-auto text-center mb-16\">\r\n            <div className=\"inline-block mb-4\">\r\n              <div className=\"flex items-center justify-center w-16 h-16 mx-auto bg-indigo-100 rounded-full mb-4\">\r\n                <FlaskRound className=\"h-8 w-8 text-indigo-600\" />\r\n              </div>\r\n            </div>\r\n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">Research Highlights</h2>\r\n            <div className=\"w-24 h-1 bg-indigo-600 mx-auto mb-6 rounded-full\"></div>\r\n            <p className=\"text-xl text-gray-600 leading-relaxed\">\r\n              Discover our innovative research projects addressing local and global challenges across various disciplines.\r\n            </p>\r\n          </div>\r\n\r\n          {/* Filter Tabs */}\r\n          <div className=\"flex flex-wrap justify-center mb-12 gap-4\">\r\n            <button\r\n              onClick={() => setActiveTab('all')}\r\n              className={`px-6 py-2 rounded-full font-medium transition-colors ${\r\n                activeTab === 'all'\r\n                  ? 'bg-indigo-600 text-white'\r\n                  : 'bg-white border border-gray-200 text-gray-700 hover:bg-indigo-50'\r\n              }`}\r\n            >\r\n              All Projects\r\n            </button>\r\n            <button\r\n              onClick={() => setActiveTab('ongoing')}\r\n              className={`px-6 py-2 rounded-full font-medium transition-colors ${\r\n                activeTab === 'ongoing'\r\n                  ? 'bg-indigo-600 text-white'\r\n                  : 'bg-white border border-gray-200 text-gray-700 hover:bg-indigo-50'\r\n              }`}\r\n            >\r\n              Ongoing\r\n            </button>\r\n            <button\r\n              onClick={() => setActiveTab('completed')}\r\n              className={`px-6 py-2 rounded-full font-medium transition-colors ${\r\n                activeTab === 'completed'\r\n                  ? 'bg-indigo-600 text-white'\r\n                  : 'bg-white border border-gray-200 text-gray-700 hover:bg-indigo-50'\r\n              }`}\r\n            >\r\n              Completed\r\n            </button>\r\n          </div>\r\n\r\n          {/* Research Projects Grid */}\r\n          {filteredProjects.length > 0 ? (\r\n            <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-8\">\r\n              {filteredProjects.map((project) => (\r\n                <div\r\n                  key={project.id}\r\n                  className=\"bg-white rounded-xl shadow-lg overflow-hidden border border-gray-100 hover:border-indigo-100 transition-all duration-300 hover:shadow-indigo-100/20 group h-full flex flex-col\"\r\n                >\r\n                  <div className=\"relative h-48 overflow-hidden\">\r\n                    <Image\r\n                      src={project.imageUrl}\r\n                      alt={project.title}\r\n                      fill\r\n                      className=\"object-cover transition-transform duration-700 group-hover:scale-110\"\r\n                    />\r\n                    <div className=\"absolute inset-0 bg-gradient-to-t from-indigo-900/80 to-transparent\"></div>\r\n                    <div className=\"absolute top-4 right-4\">\r\n                      <span className={`text-xs font-bold px-3 py-1 rounded-full ${\r\n                        project.status === 'ongoing'\r\n                          ? 'bg-green-500/90 text-white'\r\n                          : 'bg-blue-500/90 text-white'\r\n                      }`}>\r\n                        {project.status === 'ongoing' ? 'Ongoing' : 'Completed'}\r\n                      </span>\r\n                    </div>\r\n                    <div className=\"absolute bottom-4 left-4\">\r\n                      <span className=\"text-xs font-medium px-3 py-1 rounded-full bg-white/90 text-indigo-600\">\r\n                        {project.category}\r\n                      </span>\r\n                    </div>\r\n                  </div>\r\n\r\n                  <div className=\"p-6 flex-grow\">\r\n                    <h3 className=\"text-xl font-bold text-gray-900 mb-3 group-hover:text-indigo-600 transition-colors\">\r\n                      {project.title}\r\n                    </h3>\r\n                    <p className=\"text-gray-600 mb-4 text-sm line-clamp-3\">\r\n                      {project.description}\r\n                    </p>\r\n                  </div>\r\n\r\n                  <div className=\"px-6 pb-6\">\r\n                    <div className=\"flex flex-wrap gap-1 mb-4\">\r\n                      {project.researchers.slice(0, 2).map((researcher, index) => (\r\n                        <span key={index} className=\"text-xs bg-indigo-50 text-indigo-600 px-2 py-1 rounded\">\r\n                          {researcher}\r\n                        </span>\r\n                      ))}\r\n                      {project.researchers.length > 2 && (\r\n                        <span className=\"text-xs bg-indigo-50 text-indigo-600 px-2 py-1 rounded\">\r\n                          +{project.researchers.length - 2} more\r\n                        </span>\r\n                      )}\r\n                    </div>\r\n\r\n                    <div className=\"flex justify-between items-center\">\r\n                      <span className=\"text-sm text-gray-500\">{project.year}</span>\r\n                      <button className=\"text-indigo-600 font-medium text-sm flex items-center group/btn\">\r\n                        Learn More\r\n                        <ArrowRight className=\"ml-1 group-hover/btn:translate-x-1 transition-transform duration-300\" />\r\n                      </button>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              ))}\r\n            </div>\r\n          ) : (\r\n            <div className=\"text-center py-12 bg-gray-50 rounded-xl\">\r\n              <div className=\"w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-4\">\r\n                <Search className=\"h-8 w-8 text-indigo-400\" />\r\n              </div>\r\n              <h3 className=\"text-xl font-bold text-gray-900 mb-2\">No Projects Found</h3>\r\n              <p className=\"text-gray-600 mb-6\">\r\n                We could not  find any research projects matching your criteria.\r\n              </p>\r\n              <button\r\n                onClick={() => {\r\n                  setActiveTab('all');\r\n                  setSearchQuery('');\r\n                }}\r\n                className=\"px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors\"\r\n              >\r\n                View All Projects\r\n              </button>\r\n            </div>\r\n          )}\r\n\r\n          {/* View All Projects Button */}\r\n          {filteredProjects.length > 0 && filteredProjects.length < researchProjects.length && (\r\n            <div className=\"text-center mt-12\">\r\n              <button\r\n                onClick={() => {\r\n                  setActiveTab('all');\r\n                  setSearchQuery('');\r\n                }}\r\n                className=\"inline-flex items-center bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-3 px-8 rounded-xl shadow-md transition-all duration-300 hover:-translate-y-1\"\r\n              >\r\n                View All Projects\r\n                <ArrowRight className=\"ml-2\" />\r\n              </button>\r\n            </div>\r\n          )}\r\n        </div>\r\n      </section>\r\n\r\n      {/* Research Centers Section */}\r\n      <section className=\"py-20 bg-gradient-to-b from-indigo-50 to-white relative overflow-hidden\">\r\n        {/* Background decorative elements */}\r\n        <div className=\"absolute top-0 right-0 w-[600px] h-[600px] bg-indigo-100 rounded-full opacity-30 blur-3xl -translate-x-1/3 -translate-y-1/3\"></div>\r\n        <div className=\"absolute bottom-0 left-0 w-[500px] h-[500px] bg-blue-100 rounded-full opacity-30 blur-3xl translate-x-1/4 translate-y-1/4\"></div>\r\n\r\n        {/* Subtle pattern overlay */}\r\n        <div className=\"absolute inset-0 opacity-5\"\r\n          style={{\r\n            backgroundImage: 'radial-gradient(#4338ca 1px, transparent 1px)',\r\n            backgroundSize: '40px 40px'\r\n          }}>\r\n        </div>\r\n\r\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 relative z-10\">\r\n          <div className=\"max-w-3xl mx-auto text-center mb-16\">\r\n            <div className=\"inline-block mb-4\">\r\n              <div className=\"flex items-center justify-center w-16 h-16 mx-auto bg-indigo-100 rounded-full mb-4\">\r\n                <Building2 className=\"h-8 w-8 text-indigo-600\" />\r\n              </div>\r\n            </div>\r\n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">Research Centers</h2>\r\n            <div className=\"w-24 h-1 bg-indigo-600 mx-auto mb-6 rounded-full\"></div>\r\n            <p className=\"text-xl text-gray-600 leading-relaxed\">\r\n              Our specialized research centers drive innovation and address critical challenges through focused research initiatives.\r\n            </p>\r\n          </div>\r\n\r\n          <div className=\"grid md:grid-cols-2 gap-8\">\r\n            {researchCenters.map((center) => (\r\n              <div\r\n                key={center.id}\r\n                className=\"bg-white rounded-xl shadow-lg overflow-hidden border border-gray-100 hover:border-indigo-100 transition-all duration-300 hover:shadow-indigo-100/20 group\"\r\n              >\r\n                <div className=\"md:flex\">\r\n                  <div className=\"md:w-2/5 relative h-48 md:h-auto overflow-hidden\">\r\n                    <Image\r\n                      src={center.imageUrl}\r\n                      alt={center.name}\r\n                      fill\r\n                      className=\"object-cover transition-transform duration-700 group-hover:scale-110\"\r\n                    />\r\n                    <div className=\"absolute inset-0 bg-gradient-to-r from-indigo-900/70 to-indigo-900/30 md:bg-gradient-to-t\"></div>\r\n                    <div className=\"absolute inset-0 flex items-center justify-center p-6 md:hidden\">\r\n                      <h3 className=\"text-xl font-bold text-white text-center\">\r\n                        {center.name}\r\n                      </h3>\r\n                    </div>\r\n                  </div>\r\n\r\n                  <div className=\"md:w-3/5 p-6\">\r\n                    <h3 className=\"text-xl font-bold text-gray-900 mb-3 hidden md:block\">\r\n                      {center.name}\r\n                    </h3>\r\n                    <p className=\"text-gray-600 mb-4 text-sm\">\r\n                      {center.description}\r\n                    </p>\r\n                    <div className=\"mb-4\">\r\n                      <h4 className=\"text-sm font-semibold text-gray-900 mb-2\">Research Focus:</h4>\r\n                      <div className=\"flex flex-wrap gap-2\">\r\n                        {center.focus.map((area, index) => (\r\n                          <span key={index} className=\"text-xs bg-indigo-50 text-indigo-600 px-2 py-1 rounded\">\r\n                            {area}\r\n                          </span>\r\n                        ))}\r\n                      </div>\r\n                    </div>\r\n                    <div className=\"flex justify-between items-center\">\r\n                      <a href={`mailto:${center.contact}`} className=\"text-sm text-indigo-600 hover:underline\">\r\n                        {center.contact}\r\n                      </a>\r\n                      <button className=\"text-indigo-600 font-medium text-sm flex items-center group/btn\">\r\n                        Learn More\r\n                        <ArrowRight className=\"ml-1 group-hover/btn:translate-x-1 transition-transform duration-300\" />\r\n                      </button>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Publications Section */}\r\n      <section className=\"py-20 bg-white\">\r\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\r\n          <div className=\"max-w-3xl mx-auto text-center mb-16\">\r\n            <div className=\"inline-block mb-4\">\r\n              <div className=\"flex items-center justify-center w-16 h-16 mx-auto bg-indigo-100 rounded-full mb-4\">\r\n                <Book className=\"h-8 w-8 text-indigo-600\" />\r\n              </div>\r\n            </div>\r\n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">Featured Publications</h2>\r\n            <div className=\"w-24 h-1 bg-indigo-600 mx-auto mb-6 rounded-full\"></div>\r\n            <p className=\"text-xl text-gray-600 leading-relaxed\">\r\n              Explore our latest research publications contributing to knowledge advancement in various fields.\r\n            </p>\r\n          </div>\r\n\r\n          <div className=\"grid md:grid-cols-2 gap-8\">\r\n            {publications.map((publication) => (\r\n              <div\r\n                key={publication.id}\r\n                className=\"bg-white rounded-xl shadow-lg overflow-hidden border border-gray-100 hover:border-indigo-100 transition-all duration-300 hover:shadow-indigo-100/20 group\"\r\n              >\r\n                <div className=\"md:flex\">\r\n                  <div className=\"md:w-2/5 relative h-48 md:h-auto overflow-hidden\">\r\n                    <Image\r\n                      src={publication.imageUrl}\r\n                      alt={publication.title}\r\n                      fill\r\n                      className=\"object-cover transition-transform duration-700 group-hover:scale-110\"\r\n                    />\r\n                    <div className=\"absolute inset-0 bg-gradient-to-r from-indigo-900/70 to-indigo-900/30 md:bg-gradient-to-t\"></div>\r\n                    <div className=\"absolute bottom-0 left-0 right-0 p-4\">\r\n                      <div className=\"bg-white/90 text-indigo-600 text-xs font-bold px-3 py-1 rounded-full inline-block\">\r\n                        {publication.year}\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n\r\n                  <div className=\"md:w-3/5 p-6\">\r\n                    <h3 className=\"text-lg font-bold text-gray-900 mb-2 line-clamp-2\">\r\n                      {publication.title}\r\n                    </h3>\r\n                    <p className=\"text-sm text-gray-500 mb-3\">\r\n                      {publication.authors.join(', ')} | {publication.journal}\r\n                    </p>\r\n                    <p className=\"text-gray-600 mb-4 text-sm line-clamp-3\">\r\n                      {publication.abstract}\r\n                    </p>\r\n                    <div className=\"flex justify-between items-center\">\r\n                      <a\r\n                        href={publication.link}\r\n                        target=\"_blank\"\r\n                        rel=\"noopener noreferrer\"\r\n                        className=\"text-indigo-600 font-medium text-sm flex items-center group/btn\"\r\n                      >\r\n                        Read Full Paper\r\n                        <ExternalLink className=\"ml-1 group-hover/btn:translate-x-1 transition-transform duration-300\" />\r\n                      </a>\r\n                      <button className=\"text-xs bg-indigo-50 text-indigo-600 px-2 py-1 rounded\">\r\n                        Download PDF\r\n                      </button>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n\r\n          <div className=\"text-center mt-12\">\r\n            <Link\r\n              href=\"#\"\r\n              className=\"inline-flex items-center bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-3 px-8 rounded-xl shadow-md transition-all duration-300 hover:-translate-y-1\"\r\n            >\r\n              View All Publications\r\n              <ArrowRight className=\"ml-2\" />\r\n            </Link>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Research Opportunities Section */}\r\n      <section className=\"py-20 bg-gradient-to-b from-white to-indigo-50\">\r\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\r\n          <div className=\"max-w-3xl mx-auto text-center mb-16\">\r\n            <div className=\"inline-block mb-4\">\r\n              <div className=\"flex items-center justify-center w-16 h-16 mx-auto bg-indigo-100 rounded-full mb-4\">\r\n                <GraduationCap className=\"h-8 w-8 text-indigo-600\" />\r\n              </div>\r\n            </div>\r\n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">Research Opportunities</h2>\r\n            <div className=\"w-24 h-1 bg-indigo-600 mx-auto mb-6 rounded-full\"></div>\r\n            <p className=\"text-xl text-gray-600 leading-relaxed\">\r\n              Join our research community and contribute to innovative projects addressing local and global challenges.\r\n            </p>\r\n          </div>\r\n\r\n          <div className=\"grid md:grid-cols-3 gap-8\">\r\n            {/* For Students */}\r\n            <div className=\"bg-white rounded-xl shadow-lg overflow-hidden border border-gray-100 hover:border-indigo-100 transition-all duration-300 hover:shadow-indigo-100/20 group\">\r\n              <div className=\"p-6\">\r\n                <div className=\"w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mb-6 group-hover:bg-indigo-200 transition-colors\">\r\n                  <GraduationCap className=\"h-8 w-8 text-indigo-600\" />\r\n                </div>\r\n                <h3 className=\"text-xl font-bold text-gray-900 mb-4\">For Students</h3>\r\n                <ul className=\"space-y-3 mb-6\">\r\n                  <li className=\"flex items-start\">\r\n                    <div className=\"flex-shrink-0 w-5 h-5 rounded-full bg-indigo-100 flex items-center justify-center mt-0.5 mr-3\">\r\n                      <div className=\"w-2 h-2 rounded-full bg-indigo-600\"></div>\r\n                    </div>\r\n                    <p className=\"text-gray-600 text-sm\">Research assistantships with faculty members</p>\r\n                  </li>\r\n                  <li className=\"flex items-start\">\r\n                    <div className=\"flex-shrink-0 w-5 h-5 rounded-full bg-indigo-100 flex items-center justify-center mt-0.5 mr-3\">\r\n                      <div className=\"w-2 h-2 rounded-full bg-indigo-600\"></div>\r\n                    </div>\r\n                    <p className=\"text-gray-600 text-sm\">Undergraduate research programs</p>\r\n                  </li>\r\n                  <li className=\"flex items-start\">\r\n                    <div className=\"flex-shrink-0 w-5 h-5 rounded-full bg-indigo-100 flex items-center justify-center mt-0.5 mr-3\">\r\n                      <div className=\"w-2 h-2 rounded-full bg-indigo-600\"></div>\r\n                    </div>\r\n                    <p className=\"text-gray-600 text-sm\">Master&#39;s and PhD research opportunities</p>\r\n                  </li>\r\n                  <li className=\"flex items-start\">\r\n                    <div className=\"flex-shrink-0 w-5 h-5 rounded-full bg-indigo-100 flex items-center justify-center mt-0.5 mr-3\">\r\n                      <div className=\"w-2 h-2 rounded-full bg-indigo-600\"></div>\r\n                    </div>\r\n                    <p className=\"text-gray-600 text-sm\">Summer research internships</p>\r\n                  </li>\r\n                </ul>\r\n                <Link\r\n                  href=\"#\"\r\n                  className=\"inline-flex items-center text-indigo-600 font-medium hover:text-indigo-800 transition-colors group/btn\"\r\n                >\r\n                  Learn More\r\n                  <ArrowRight className=\"ml-1 group-hover/btn:translate-x-1 transition-transform duration-300\" />\r\n                </Link>\r\n              </div>\r\n            </div>\r\n\r\n            {/* For Faculty */}\r\n            <div className=\"bg-white rounded-xl shadow-lg overflow-hidden border border-gray-100 hover:border-indigo-100 transition-all duration-300 hover:shadow-indigo-100/20 group\">\r\n              <div className=\"p-6\">\r\n                <div className=\"w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mb-6 group-hover:bg-indigo-200 transition-colors\">\r\n                  <User className=\"h-8 w-8 text-indigo-600\" />\r\n                </div>\r\n                <h3 className=\"text-xl font-bold text-gray-900 mb-4\">For Faculty</h3>\r\n                <ul className=\"space-y-3 mb-6\">\r\n                  <li className=\"flex items-start\">\r\n                    <div className=\"flex-shrink-0 w-5 h-5 rounded-full bg-indigo-100 flex items-center justify-center mt-0.5 mr-3\">\r\n                      <div className=\"w-2 h-2 rounded-full bg-indigo-600\"></div>\r\n                    </div>\r\n                    <p className=\"text-gray-600 text-sm\">Internal research grants</p>\r\n                  </li>\r\n                  <li className=\"flex items-start\">\r\n                    <div className=\"flex-shrink-0 w-5 h-5 rounded-full bg-indigo-100 flex items-center justify-center mt-0.5 mr-3\">\r\n                      <div className=\"w-2 h-2 rounded-full bg-indigo-600\"></div>\r\n                    </div>\r\n                    <p className=\"text-gray-600 text-sm\">Collaborative research initiatives</p>\r\n                  </li>\r\n                  <li className=\"flex items-start\">\r\n                    <div className=\"flex-shrink-0 w-5 h-5 rounded-full bg-indigo-100 flex items-center justify-center mt-0.5 mr-3\">\r\n                      <div className=\"w-2 h-2 rounded-full bg-indigo-600\"></div>\r\n                    </div>\r\n                    <p className=\"text-gray-600 text-sm\">Research leave opportunities</p>\r\n                  </li>\r\n                  <li className=\"flex items-start\">\r\n                    <div className=\"flex-shrink-0 w-5 h-5 rounded-full bg-indigo-100 flex items-center justify-center mt-0.5 mr-3\">\r\n                      <div className=\"w-2 h-2 rounded-full bg-indigo-600\"></div>\r\n                    </div>\r\n                    <p className=\"text-gray-600 text-sm\">Publication and conference support</p>\r\n                  </li>\r\n                </ul>\r\n                <Link\r\n                  href=\"#\"\r\n                  className=\"inline-flex items-center text-indigo-600 font-medium hover:text-indigo-800 transition-colors group/btn\"\r\n                >\r\n                  Learn More\r\n                  <ArrowRight className=\"ml-1 group-hover/btn:translate-x-1 transition-transform duration-300\" />\r\n                </Link>\r\n              </div>\r\n            </div>\r\n\r\n            {/* For External Partners */}\r\n            <div className=\"bg-white rounded-xl shadow-lg overflow-hidden border border-gray-100 hover:border-indigo-100 transition-all duration-300 hover:shadow-indigo-100/20 group\">\r\n              <div className=\"p-6\">\r\n                <div className=\"w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mb-6 group-hover:bg-indigo-200 transition-colors\">\r\n                  <Handshake className=\"h-8 w-8 text-indigo-600\" />\r\n                </div>\r\n                <h3 className=\"text-xl font-bold text-gray-900 mb-4\">For External Partners</h3>\r\n                <ul className=\"space-y-3 mb-6\">\r\n                  <li className=\"flex items-start\">\r\n                    <div className=\"flex-shrink-0 w-5 h-5 rounded-full bg-indigo-100 flex items-center justify-center mt-0.5 mr-3\">\r\n                      <div className=\"w-2 h-2 rounded-full bg-indigo-600\"></div>\r\n                    </div>\r\n                    <p className=\"text-gray-600 text-sm\">Industry-academic partnerships</p>\r\n                  </li>\r\n                  <li className=\"flex items-start\">\r\n                    <div className=\"flex-shrink-0 w-5 h-5 rounded-full bg-indigo-100 flex items-center justify-center mt-0.5 mr-3\">\r\n                      <div className=\"w-2 h-2 rounded-full bg-indigo-600\"></div>\r\n                    </div>\r\n                    <p className=\"text-gray-600 text-sm\">Collaborative research projects</p>\r\n                  </li>\r\n                  <li className=\"flex items-start\">\r\n                    <div className=\"flex-shrink-0 w-5 h-5 rounded-full bg-indigo-100 flex items-center justify-center mt-0.5 mr-3\">\r\n                      <div className=\"w-2 h-2 rounded-full bg-indigo-600\"></div>\r\n                    </div>\r\n                    <p className=\"text-gray-600 text-sm\">Consulting services</p>\r\n                  </li>\r\n                  <li className=\"flex items-start\">\r\n                    <div className=\"flex-shrink-0 w-5 h-5 rounded-full bg-indigo-100 flex items-center justify-center mt-0.5 mr-3\">\r\n                      <div className=\"w-2 h-2 rounded-full bg-indigo-600\"></div>\r\n                    </div>\r\n                    <p className=\"text-gray-600 text-sm\">Technology transfer opportunities</p>\r\n                  </li>\r\n                </ul>\r\n                <Link\r\n                  href=\"#\"\r\n                  className=\"inline-flex items-center text-indigo-600 font-medium hover:text-indigo-800 transition-colors group/btn\"\r\n                >\r\n                  Learn More\r\n                  <ArrowRight className=\"ml-1 group-hover/btn:translate-x-1 transition-transform duration-300\" />\r\n                </Link>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* CTA Section */}\r\n      <section className=\"py-16 bg-gradient-to-r from-indigo-900 to-blue-900 text-white relative overflow-hidden\">\r\n        {/* Background elements */}\r\n        <div className=\"absolute inset-0 overflow-hidden\">\r\n          <div className=\"absolute top-0 right-0 w-64 h-64 rounded-full bg-indigo-500 opacity-10\"></div>\r\n          <div className=\"absolute bottom-0 left-0 w-96 h-96 rounded-full bg-blue-500 opacity-10\"></div>\r\n        </div>\r\n\r\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 relative z-10\">\r\n          <div className=\"max-w-4xl mx-auto text-center\">\r\n            <h2 className=\"text-3xl md:text-4xl font-bold mb-6\">Ready to Collaborate on Research?</h2>\r\n            <p className=\"text-xl text-indigo-100 mb-10 leading-relaxed\">\r\n              Join our research community at Far Western University and contribute to innovative projects addressing local and global challenges.\r\n            </p>\r\n            <div className=\"flex flex-wrap justify-center gap-6\">\r\n              <Link\r\n                href=\"/contact\"\r\n                className=\"bg-white text-indigo-900 hover:bg-indigo-50 font-bold py-4 px-8 rounded-lg shadow-lg transition-all duration-300 hover:scale-105 transform\"\r\n              >\r\n                Contact Research Office\r\n              </Link>\r\n              <Link\r\n                href=\"#\"\r\n                className=\"bg-transparent border-2 border-white text-white hover:bg-white/10 font-bold py-4 px-8 rounded-lg shadow-lg transition-all duration-300 hover:scale-105 transform\"\r\n              >\r\n                Download Research Brochure\r\n              </Link>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n    </main>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;;AAwCe,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,IAAI,qBACnB,CAAC,CAAC,MAAM;YACN,IAAI,MAAM,cAAc,EAAE;gBACxB,aAAa;gBACb,SAAS,UAAU;YACrB;QACF,GACA;YAAE,WAAW;QAAI;QAGnB,0DAA0D;QAC1D,MAAM,aAAa,QAAQ,OAAO;QAElC,IAAI,YAAY;YACd,SAAS,OAAO,CAAC;QACnB;QAEA,OAAO;YACL,sCAAsC;YACtC,IAAI,YAAY;gBACd,SAAS,SAAS,CAAC;YACrB;QACF;IACF,GAAG,EAAE;IAEL,gCAAgC;IAChC,MAAM,mBAAsC;QAC1C;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,UAAU;YACV,UAAU;YACV,aAAa;gBAAC;gBAAuB;gBAAoB;aAAoB;YAC7E,QAAQ;YACR,MAAM;QACR;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,UAAU;YACV,UAAU;YACV,aAAa;gBAAC;gBAAoB;gBAAqB;aAAqB;YAC5E,QAAQ;YACR,MAAM;QACR;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,UAAU;YACV,UAAU;YACV,aAAa;gBAAC;gBAAsB;gBAAwB;aAAoB;YAChF,QAAQ;YACR,MAAM;QACR;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,UAAU;YACV,UAAU;YACV,aAAa;gBAAC;gBAAsB;gBAA4B;aAAkB;YAClF,QAAQ;YACR,MAAM;QACR;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,UAAU;YACV,UAAU;YACV,aAAa;gBAAC;gBAAsB;gBAAuB;aAAwB;YACnF,QAAQ;YACR,MAAM;QACR;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,UAAU;YACV,UAAU;YACV,aAAa;gBAAC;gBAAqB;gBAAwB;aAAoB;YAC/E,QAAQ;YACR,MAAM;QACR;KACD;IAED,2BAA2B;IAC3B,MAAM,eAA8B;QAClC;YACE,IAAI;YACJ,OAAO;YACP,SAAS;gBAAC;gBAAa;gBAAa;aAAa;YACjD,SAAS;YACT,MAAM;YACN,UAAU;YACV,MAAM;YACN,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;gBAAC;gBAAgB;gBAAiB;aAAY;YACvD,SAAS;YACT,MAAM;YACN,UAAU;YACV,MAAM;YACN,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;gBAAC;gBAAa;gBAAiB;aAAY;YACpD,SAAS;YACT,MAAM;YACN,UAAU;YACV,MAAM;YACN,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;gBAAC;gBAAc;gBAAgB;aAAe;YACvD,SAAS;YACT,MAAM;YACN,UAAU;YACV,MAAM;YACN,UAAU;QACZ;KACD;IAED,+BAA+B;IAC/B,MAAM,kBAAoC;QACxC;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,OAAO;gBAAC;gBAA6B;gBAAwB;aAA4B;YACzF,UAAU;YACV,SAAS;QACX;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,OAAO;gBAAC;gBAA2B;gBAAoB;aAA0B;YACjF,UAAU;YACV,SAAS;QACX;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,OAAO;gBAAC;gBAAgB;gBAAqB;aAAiB;YAC9D,UAAU;YACV,SAAS;QACX;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,OAAO;gBAAC;gBAAiB;gBAAwB;aAAsB;YACvE,UAAU;YACV,SAAS;QACX;KACD;IAED,uDAAuD;IACvD,MAAM,mBAAmB,iBAAiB,MAAM,CAAC,CAAA;QAC/C,MAAM,aAAa,cAAc,SACd,cAAc,aAAa,QAAQ,MAAM,KAAK,aAC9C,cAAc,eAAe,QAAQ,MAAM,KAAK;QAEnE,MAAM,gBAAgB,gBAAgB,MACjB,QAAQ,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OAC5D,QAAQ,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OAClE,QAAQ,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW;QAEpF,OAAO,cAAc;IACvB;IAEA,qBACE,8OAAC;QAAK,WAAU;;0BAEd,8OAAC;gBACC,KAAK;gBACL,WAAU;;kCAGV,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCACC,WAAU;gCACV,OAAO;oCACL,iBAAiB;oCACjB,gBAAgB;gCAClB;;;;;;;;;;;;kCAIJ,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAW,CAAC,qEAAqE,EAAE,YAAY,8BAA8B,4BAA4B;;8CAC5J,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDAA8B;;;;;;;;;;;;;;;;8CAIlD,8OAAC;oCAAG,WAAU;;wCAAgE;sDACjE,8OAAC;4CAAK,WAAU;sDAA6E;;;;;;;;;;;;8CAG1G,8OAAC;oCAAE,WAAU;8CAAkE;;;;;;8CAK/E,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,MAAK;gDACL,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gDAC9C,WAAU;;;;;;0DAEZ,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CACjB,6BACC,8OAAC;gDACC,SAAS,IAAM,eAAe;gDAC9B,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAUX,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,OAAM;4BAA6B,SAAQ;4BAAe,WAAU;sCACvE,cAAA,8OAAC;gCACC,MAAK;gCACL,aAAY;gCACZ,GAAE;;;;;;;;;;;;;;;;;;;;;;0BAOV,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;;;;;;8CAG1B,8OAAC;oCAAG,WAAU;8CAAoD;;;;;;8CAClE,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAE,WAAU;8CAAwC;;;;;;;;;;;;sCAMvD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS,IAAM,aAAa;oCAC5B,WAAW,CAAC,qDAAqD,EAC/D,cAAc,QACV,6BACA,oEACJ;8CACH;;;;;;8CAGD,8OAAC;oCACC,SAAS,IAAM,aAAa;oCAC5B,WAAW,CAAC,qDAAqD,EAC/D,cAAc,YACV,6BACA,oEACJ;8CACH;;;;;;8CAGD,8OAAC;oCACC,SAAS,IAAM,aAAa;oCAC5B,WAAW,CAAC,qDAAqD,EAC/D,cAAc,cACV,6BACA,oEACJ;8CACH;;;;;;;;;;;;wBAMF,iBAAiB,MAAM,GAAG,kBACzB,8OAAC;4BAAI,WAAU;sCACZ,iBAAiB,GAAG,CAAC,CAAC,wBACrB,8OAAC;oCAEC,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,6HAAA,CAAA,UAAK;oDACJ,KAAK,QAAQ,QAAQ;oDACrB,KAAK,QAAQ,KAAK;oDAClB,IAAI;oDACJ,WAAU;;;;;;8DAEZ,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAW,CAAC,yCAAyC,EACzD,QAAQ,MAAM,KAAK,YACf,+BACA,6BACJ;kEACC,QAAQ,MAAM,KAAK,YAAY,YAAY;;;;;;;;;;;8DAGhD,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;kEACb,QAAQ,QAAQ;;;;;;;;;;;;;;;;;sDAKvB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DACX,QAAQ,KAAK;;;;;;8DAEhB,8OAAC;oDAAE,WAAU;8DACV,QAAQ,WAAW;;;;;;;;;;;;sDAIxB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;wDACZ,QAAQ,WAAW,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,YAAY,sBAChD,8OAAC;gEAAiB,WAAU;0EACzB;+DADQ;;;;;wDAIZ,QAAQ,WAAW,CAAC,MAAM,GAAG,mBAC5B,8OAAC;4DAAK,WAAU;;gEAAyD;gEACrE,QAAQ,WAAW,CAAC,MAAM,GAAG;gEAAE;;;;;;;;;;;;;8DAKvC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAyB,QAAQ,IAAI;;;;;;sEACrD,8OAAC;4DAAO,WAAU;;gEAAkE;8EAElF,8OAAC,kNAAA,CAAA,aAAU;oEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;mCAtDvB,QAAQ,EAAE;;;;;;;;;iDA8DrB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;;;;;;8CAEpB,8OAAC;oCAAG,WAAU;8CAAuC;;;;;;8CACrD,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAGlC,8OAAC;oCACC,SAAS;wCACP,aAAa;wCACb,eAAe;oCACjB;oCACA,WAAU;8CACX;;;;;;;;;;;;wBAOJ,iBAAiB,MAAM,GAAG,KAAK,iBAAiB,MAAM,GAAG,iBAAiB,MAAM,kBAC/E,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,SAAS;oCACP,aAAa;oCACb,eAAe;gCACjB;gCACA,WAAU;;oCACX;kDAEC,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQhC,8OAAC;gBAAQ,WAAU;;kCAEjB,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCAGf,8OAAC;wBAAI,WAAU;wBACb,OAAO;4BACL,iBAAiB;4BACjB,gBAAgB;wBAClB;;;;;;kCAGF,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,gNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;;;;;;;;;;;kDAGzB,8OAAC;wCAAG,WAAU;kDAAoD;;;;;;kDAClE,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAE,WAAU;kDAAwC;;;;;;;;;;;;0CAKvD,8OAAC;gCAAI,WAAU;0CACZ,gBAAgB,GAAG,CAAC,CAAC,uBACpB,8OAAC;wCAEC,WAAU;kDAEV,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,6HAAA,CAAA,UAAK;4DACJ,KAAK,OAAO,QAAQ;4DACpB,KAAK,OAAO,IAAI;4DAChB,IAAI;4DACJ,WAAU;;;;;;sEAEZ,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAG,WAAU;0EACX,OAAO,IAAI;;;;;;;;;;;;;;;;;8DAKlB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEACX,OAAO,IAAI;;;;;;sEAEd,8OAAC;4DAAE,WAAU;sEACV,OAAO,WAAW;;;;;;sEAErB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAG,WAAU;8EAA2C;;;;;;8EACzD,8OAAC;oEAAI,WAAU;8EACZ,OAAO,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,sBACvB,8OAAC;4EAAiB,WAAU;sFACzB;2EADQ;;;;;;;;;;;;;;;;sEAMjB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAE,MAAM,CAAC,OAAO,EAAE,OAAO,OAAO,EAAE;oEAAE,WAAU;8EAC5C,OAAO,OAAO;;;;;;8EAEjB,8OAAC;oEAAO,WAAU;;wEAAkE;sFAElF,8OAAC,kNAAA,CAAA,aAAU;4EAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uCA1CzB,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;0BAsDxB,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;;;;;;8CAGpB,8OAAC;oCAAG,WAAU;8CAAoD;;;;;;8CAClE,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAE,WAAU;8CAAwC;;;;;;;;;;;;sCAKvD,8OAAC;4BAAI,WAAU;sCACZ,aAAa,GAAG,CAAC,CAAC,4BACjB,8OAAC;oCAEC,WAAU;8CAEV,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,6HAAA,CAAA,UAAK;wDACJ,KAAK,YAAY,QAAQ;wDACzB,KAAK,YAAY,KAAK;wDACtB,IAAI;wDACJ,WAAU;;;;;;kEAEZ,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;sEACZ,YAAY,IAAI;;;;;;;;;;;;;;;;;0DAKvB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEACX,YAAY,KAAK;;;;;;kEAEpB,8OAAC;wDAAE,WAAU;;4DACV,YAAY,OAAO,CAAC,IAAI,CAAC;4DAAM;4DAAI,YAAY,OAAO;;;;;;;kEAEzD,8OAAC;wDAAE,WAAU;kEACV,YAAY,QAAQ;;;;;;kEAEvB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEACC,MAAM,YAAY,IAAI;gEACtB,QAAO;gEACP,KAAI;gEACJ,WAAU;;oEACX;kFAEC,8OAAC,sNAAA,CAAA,eAAY;wEAAC,WAAU;;;;;;;;;;;;0EAE1B,8OAAC;gEAAO,WAAU;0EAAyD;;;;;;;;;;;;;;;;;;;;;;;;mCAvC5E,YAAY,EAAE;;;;;;;;;;sCAiDzB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;oCACX;kDAEC,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO9B,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,wNAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;;;;;;;;;;;8CAG7B,8OAAC;oCAAG,WAAU;8CAAoD;;;;;;8CAClE,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAE,WAAU;8CAAwC;;;;;;;;;;;;sCAKvD,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,wNAAA,CAAA,gBAAa;oDAAC,WAAU;;;;;;;;;;;0DAE3B,8OAAC;gDAAG,WAAU;0DAAuC;;;;;;0DACrD,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAI,WAAU;;;;;;;;;;;0EAEjB,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;kEAEvC,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAI,WAAU;;;;;;;;;;;0EAEjB,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;kEAEvC,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAI,WAAU;;;;;;;;;;;0EAEjB,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;kEAEvC,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAI,WAAU;;;;;;;;;;;0EAEjB,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;;;;;;;0DAGzC,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;;oDACX;kEAEC,8OAAC,kNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;8CAM5B,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;0DAElB,8OAAC;gDAAG,WAAU;0DAAuC;;;;;;0DACrD,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAI,WAAU;;;;;;;;;;;0EAEjB,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;kEAEvC,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAI,WAAU;;;;;;;;;;;0EAEjB,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;kEAEvC,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAI,WAAU;;;;;;;;;;;0EAEjB,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;kEAEvC,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAI,WAAU;;;;;;;;;;;0EAEjB,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;;;;;;;0DAGzC,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;;oDACX;kEAEC,8OAAC,kNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;8CAM5B,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,4MAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;;;;;;0DAEvB,8OAAC;gDAAG,WAAU;0DAAuC;;;;;;0DACrD,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAI,WAAU;;;;;;;;;;;0EAEjB,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;kEAEvC,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAI,WAAU;;;;;;;;;;;0EAEjB,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;kEAEvC,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAI,WAAU;;;;;;;;;;;0EAEjB,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;kEAEvC,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAI,WAAU;;;;;;;;;;;0EAEjB,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;;;;;;;0DAGzC,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;;oDACX;kEAEC,8OAAC,kNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASlC,8OAAC;gBAAQ,WAAU;;kCAEjB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;;;;;;;kCAGjB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAsC;;;;;;8CACpD,8OAAC;oCAAE,WAAU;8CAAgD;;;;;;8CAG7D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;sDAGD,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf", "debugId": null}}]}