<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AdminController;
use App\Http\Controllers\NewsController;
use App\Http\Controllers\NoticeController;
// use App\Http\Controllers\NoticeController;
// use App\Http\Controllers\ApplicationController;
// use App\Http\Controllers\ApplicantController;
// use App\Http\Controllers\CommitteeController;
// use App\Http\Controllers\PhotoGalleryController;
// use App\Http\Controllers\GalleryImageController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:sanctum');

//admin signup API (returns JSON)
Route::post('/signup', [AdminController::class, 'register'])->name('api.signup');

// Admin login API (returns JSON)
Route::post('/login', [AdminController::class, 'login'])->name('api.login');

// News CRUD API routes
Route::get('/news', [NewsController::class, 'index'])->name('api.news');
Route::post('/news', [NewsController::class, 'store'])->name('api.news');
Route::put('/news/{id}', [NewsController::class, 'update'])->name('api.news.update');
Route::delete('/news/{id}', [NewsController::class, 'destroy'])->name('api.news.destroy');
Route::get('/news/show/{id}', [NewsController::class, 'show'])->name('api.news.show');

//Notice CRUD API routes
Route::get('/notice', [NoticeController::class, 'index'])->name('api.notice');
Route::post('/notice', [NoticeController::class, 'store'])->name('api.notice');
Route::put('/notice/{id}', [NoticeController::class, 'update'])->name('api.notice.update');
Route::delete('/notice/{id}', [NoticeController::class, 'destroy'])->name('api.notice.destroy');

